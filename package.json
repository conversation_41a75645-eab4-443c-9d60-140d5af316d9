{"name": "circo-admin", "private": true, "version": "0.0.0", "type": "module", "scripts": {"dev": "vite --host", "build": "tsc && vite build", "lint": "eslint . --ext ts,tsx --report-unused-disable-directives --max-warnings 0", "preview": "vite preview", "deploy-dev": "npx tsc && vite build --mode dev && firebase deploy --only hosting:dev", "deploy-stage": "npx tsc && vite build --mode stage && firebase deploy --only hosting:stage", "deploy-prod": "npx tsc && vite build --mode prod && firebase deploy --only hosting:prod", "deploy-qa": "npx tsc && vite build --mode prod && firebase deploy --only hosting:qa"}, "dependencies": {"@emotion/react": "^11.13.0", "@emotion/styled": "^11.13.0", "@hookform/resolvers": "^3.3.4", "@mdxeditor/editor": "^3.11.2", "@mui/icons-material": "^5.15.10", "@mui/lab": "^5.0.0-alpha.166", "@mui/material": "^5.16.6", "@mui/x-data-grid": "^6.19.5", "@mui/x-date-pickers": "^7.12.0", "@react-google-maps/api": "^2.19.3", "@react-map/india": "^1.0.14", "@tanstack/react-query": "^5.17.19", "@tanstack/react-query-devtools": "^5.17.21", "@tmcw/togeojson": "^6.0.1", "apexcharts": "^3.46.0", "axios": "^1.6.7", "date-fns": "^3.6.0", "dayjs": "^1.11.9", "leaflet": "^1.9.4", "moment": "^2.30.1", "path-to-regexp": "^7.1.0", "react": "^18.2.0", "react-apexcharts": "^1.4.1", "react-copy-to-clipboard": "^5.1.0", "react-datamaps-india": "^0.6.0", "react-dom": "^18.2.0", "react-drag-drop-files": "^2.3.10", "react-hook-form": "^7.51.1", "react-jvectormap": "^0.0.16", "react-leaflet": "^4.2.1", "react-markdown": "^9.0.1", "react-number-format": "^5.4.0", "react-otp-input": "^3.1.1", "react-pdf": "^9.1.0", "react-player": "^2.16.0", "react-responsive-carousel": "^3.2.23", "react-router-dom": "^6.21.3", "react-simple-maps": "^3.0.0", "react-toastify": "^10.0.5", "remark-breaks": "^4.0.0", "remark-gfm": "^4.0.0", "vite-tsconfig-paths": "^4.3.2", "yup": "^1.4.0", "zustand": "^4.5.0"}, "devDependencies": {"@tanstack/eslint-plugin-query": "^5.17.22", "@types/leaflet": "^1.9.16", "@types/node": "^22.10.7", "@types/react": "^18.2.43", "@types/react-dom": "^18.2.17", "@typescript-eslint/eslint-plugin": "^6.14.0", "@typescript-eslint/parser": "^6.14.0", "@vitejs/plugin-react-swc": "^3.5.0", "eslint": "^8.55.0", "eslint-plugin-react-hooks": "^4.6.0", "eslint-plugin-react-refresh": "^0.4.5", "typescript": "^5.2.2", "vite": "^5.0.8"}}