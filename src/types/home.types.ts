import { IFileData, ImageURL } from '@/interfaces'
import { StageStatus } from '@/utils/constant'
import { Nullable } from './generic.types'

export interface IQuantityData {
	approvedSinkCarbonCredits?: number,
	pendingSinkCarbonCredits?: number,
	totalBiocharQuantity?: number
	mixedBiocharQuantity?: number
	totalCarbonCredits?: number
	stockCarbonCredits?: number
	rejectedCarbonCredits?: number
	rejectedBiocharQuantity?: number
	pendingApplicationCarbonCredits?: number
	pendingRegistrationCarbonCredits?: number
	compensatedCarbonCredits?: number
	mixedBiocharQuantityInTonnes?: number
	totalBiocharQuantityinTonnes?: number
	rejectedBiocharQuantityInTonnes?: number
}

export interface SiteKilnResponse {
	siteKilns: SiteKiln[]
}

export interface SiteKiln {
	siteId: Nullable<string>
	siteName: Nullable<string>
	siteAddress: Nullable<string>
	siteLocation: Nullable<string>
	artisanProId: Nullable<string>
	artisanProName: Nullable<string>
	kilnId: Nullable<string>
	kilnName: Nullable<string>
	kilnAddress: Nullable<string>
	kilnLocation: Nullable<string>
	csinkNetworkId: Nullable<string>
	csinkNetworkName: Nullable<string>
	isArtisan: boolean
}

export interface SitesResponse {
	sites: Site[]
}
export interface Site {
	id: Nullable<string>
	shortName: string
	name: Nullable<string>
	address: Nullable<string>
	siteLocation: Nullable<string>
}
export interface GetArtisanProsAndCSinkNetworksResponse {
	count: number
	networks: NetworkForHomePage[]
}

export interface NetworkForHomePage {
	id: string
	name: string
	shortName: string
	isArtisan: boolean
	location: string
}

export enum NetworkEnumForHomePage {
	artisanPro = 'artisanPro',
	network = 'network',
	all = 'all',
}

export type CompanyResponse = {
	id: string
	name: string
	countryCode: string
	phoneNumber: string
	email: string
	state: string
	pinCode: string
	companyLogoId: string
	createdAt: string
	companyLogo: {
		id: string
		url: string
		path: string
		deletionStatus: string | null
		deletionReason: string | null
		fileType: string
	}
	coiDocumentId: string
	coiDocument: {
		id: string
		url: string
		path: string
		deletionStatus: string | null
		deletionReason: string | null
		fileType: string
	}
	admins:
		| {
				name: string
				countryCode: string
				phoneNumber: string
				email: string
		  }[]
		| null
}

export type CompanyListResponse = {
	count: number
	requests: CompanyResponse[]
}

export type CompanyDetailsResponse = {
	id: string
	name: string
	countryCode: string
	phoneNumber: string
	email: string
	state: string
	pinCode: string
	country: string
	companyLogo: DocumentInfo
	coiDocument: DocumentInfo
	ndaDocument: DocumentInfo | null
	commercialDocument: DocumentInfo | null
	serviceType: string
	registry: string
	isTEAEnabled: boolean
	isLCAEnabled: boolean
	isFinanceToolEnabled: boolean
	admins:
		| {
				name: string
				countryCode: string
				phoneNumber: string
				email: string
		  }[]
		| null
	createdAt: string
	stages: Stage[]
	stage: string
	status: StageStatus
}

export type DocumentInfo = {
	id: string
	url: string
	path: string
	deletionStatus: string | null
	deletionReason: string | null
	fileType: string
}

export type Stage = {
	stage: string
	createdAt: string | null
	status: StageStatus
	isCompleted: boolean
	assessedAt: string | null
	assessedBy: AssessedBy
}
export interface CompanyListResponseV2 {
	// This name is only Temporary, will be changed later to `CompanyListResponse`
	count: number
	companies: Company[]
}

export interface Company {
	id: string
	requestId: string
	name: string
	countryCode: string
	phoneNumber: string
	email: string
	state: string
	pinCode: string
	country: string
	address: string
	status: string
	stage: string
	stageStatus: string
	companyLogoId: string
	companyLogo: IFileData
	coiDocument: IFileData
	coiDocumentId: string
	serviceType: string
	registry: string
	admins: Admin[]
	createdAt: string
	isRequestApproved: boolean
}

export interface CompanyDetailsMedia extends ImageURL {
	fileType: string
}

export interface Admin {
	id: string
	name: string
	countryCode: string
	phoneNumber: string
	email: string
	profileImageId?: string
	profileImage?: CompanyDetailsMedia
}

export type Kiln = {
	id: string
	name: string
	address: string
	coordinate: {
		x: number
		y: number
	}
	biocharQuantity: number
	biocharQuantityInTonnes: number
}

export type KilnsResponse = {
	kilns: Kiln[]
}
export interface ICompanyDetailsAccountManagement {
	id: string
	name: string
	countryCode: string
	phoneNumber: string
	email: string
	state: string
	pinCode: string
	country: string
	companyLogo: IFileData
	coiDocument: IFileData
	ndaSignedDocument: IFileData
	serviceType: string
	registry: string
	ndaDocument: IFileData
	commercialDocument: IFileData
	isTEAEnabled: boolean
	isLCAEnabled: boolean
	isFinanceToolEnabled: boolean
	admins: Admin[]
	createdAt: string
	stages: Stage[]
	address: string
	status: string
}

export interface AssessedBy {
	id?: string
	name?: string
}
export interface ICompanyCommonInfo {
	phoneNumber: string
	email: string
	state: string
	pinCode: string
	country: string
	address: string
	serviceType: string
	registry: string
	name: string
	coiDocument: IFileData
	status: string
	admins: Admin[]
	companyLogo: IFileData
}
