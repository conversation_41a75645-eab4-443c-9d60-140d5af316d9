import { BaseTextFieldProps, TextField } from '@mui/material'
import { forwardRef } from 'react'
import { UseFormWatch } from 'react-hook-form'
import { isFormFieldRequired } from '../helper/isFieldRequired'
import { AnyObjectSchema } from 'yup'

interface IProps extends BaseTextFieldProps {
	watch?: UseFormWatch<any>
	hideNumberArrows?: boolean
	asterikRequired?: boolean
	schema?: AnyObjectSchema
	InputProps?: any // autocomplete props
	onChange?:
		| React.ChangeEventHandler<HTMLInputElement | HTMLTextAreaElement>
		| undefined
}
export const CustomTextField = forwardRef<HTMLInputElement, IProps>(
	(
		{
			watch,
			schema,
			hideNumberArrows = false,
			asterikRequired = true,
			...props
		},
		ref
	) => {
		const shrink = !!watch?.(props?.name ?? '')

		return (
			<TextField
				{...props} // Spread all props provided to CustomTextField
				InputProps={{
					...props?.InputProps, // Ensure existing InputProps from `params` are not overridden

					...(hideNumberArrows
						? {
								sx: {
									'& input::-webkit-outer-spin-button, & input::-webkit-inner-spin-button':
										{
											display: 'none',
										},
									'& input[type=number]': {
										MozAppearance: 'textfield',
									},
									'.MuiInputAdornment-root': {
										'& fieldset': {
											border: 'none',
										},
									},
								},
						  }
						: {}),
				}}
				InputLabelProps={{
					...props.InputLabelProps, // Ensure existing InputLabelProps from `params` are preserved
					...(shrink && { shrink }),
					...(asterikRequired &&
						schema && {
							required: isFormFieldRequired({
								schema,
								name: props?.name ?? '',
							}),
						}),
				}}
				ref={ref} // Forward ref
			/>
		)
	}
)
