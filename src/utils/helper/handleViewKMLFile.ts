import { ILocation } from '@/interfaces'
import { NavigateFunction } from 'react-router-dom'

export const handleViewKMLFile = ({
	farmCoordinates,
	center,
	navigate,
	networkId,
	setFarmCoordinates,
	setShowMap,
}: {
	farmCoordinates: ILocation[]
	center: { x?: string; y?: string }
	networkId: string
	navigate: NavigateFunction
	setShowMap: () => void
	setFarmCoordinates: (
		coodinates: google.maps.LatLng[] | google.maps.LatLngLiteral[]
	) => void
}) => {
	const formattedCoordinates = farmCoordinates?.map((coordinate) => ({
		lat: coordinate.x,
		lng: coordinate.y,
	}))
	const searchParams = new URLSearchParams(window.location.search)
	searchParams.set('lat', center.x??"")
	searchParams.set('long', center.y??"")
	searchParams.set('networkId', networkId)
	navigate(`?${searchParams.toString()}`, { replace: true })
	setFarmCoordinates(formattedCoordinates)
	setShowMap()
}
