export function generateThumbnail(
	videoUrl: string,
	seekTime = 2
): Promise<{ blob: Blob | null; thumbnailBlobUrl: string }> {
	if (!videoUrl || typeof videoUrl !== 'string') {
		return Promise.reject(new Error('Invalid video URL provided'))
	}

	return new Promise<{ blob: Blob | null; thumbnailBlobUrl: string }>(
		(resolve, reject) => {
			const video = document.createElement('video')

			const cleanup = () => {
				video.remove()
				video.src = ''
			}

			video.src = videoUrl
			video.crossOrigin = 'anonymous'
			video.muted = true
			video.currentTime = seekTime

			video.onloadeddata = () => {
				const canvas = document.createElement('canvas')
				canvas.width = video.videoWidth
				canvas.height = video.videoHeight

				const ctx = canvas.getContext('2d')
				if (!ctx) {
					cleanup()
					reject(new Error('Failed to get canvas context'))
					return
				}

				ctx.drawImage(video, 0, 0, canvas.width, canvas.height)
				canvas.toBlob((blob) => {
					if (blob) {
						const thumbnailUrl = URL.createObjectURL(blob)
						cleanup()
						resolve({ blob, thumbnailBlobUrl: thumbnailUrl })
					} else {
						cleanup()
						reject(new Error('Failed to generate thumbnail blob'))
					}
				}, 'image/jpeg')
			}

			video.onerror = (error) => {
				cleanup()
				reject(error)
			}
		}
	)
}
