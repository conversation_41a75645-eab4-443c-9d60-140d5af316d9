import { AnyObjectSchema } from 'yup'

interface IProps {
	schema: AnyObjectSchema | any
	name: string
}
export const isFormFieldRequired = ({ schema, name }: IProps) => {
	if (!schema || !name) return false
	
	let field = schema.fields?.[name]
	// Handle nested fields (e.g., managerDetails.email)
	if (name?.includes('.')) {
		const subName = name.split('.')
		field =
			schema.fields?.[subName?.[0]]?.describe()?.fields?.[
				subName?.reverse()?.[0]
			]

		if (field?.tests?.some((test: any) => test.name === 'required'||test.name=== 'min' || test.name === 'max')) {
			return true
		}
		return false
	}
	if (!field) return false

	const description = field?.describe()

	if (description?.tests?.some((test: any) => test?.name === 'required' ||test.name=== 'min' || test.name === 'max')) {
		return true
	}

	// if (!description?.optional) return true

	// Handle `.when()` conditions, but only if explicitly marked as required in certain cases
	if (description?.tests.some((test: any) => test.name === 'when')) {
		return true // Assuming conditional fields with `.when()` might be required
	}

	return false
}
