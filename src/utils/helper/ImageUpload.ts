import { ContentType, authAxios, publicAxios } from '@/contexts'
import { toast } from 'react-toastify'

export const handleImageUpload = async (
	file: File | undefined,
	type?: string,
	publicRoute: boolean = false
) => {
	if (!file) return

	if (file?.size / 10 ** 6 > 5) {
		toast.error('Please upload the file less than 5 MB', {
			autoClose: 3000,
		})
		return
	}

	const formData = new FormData()
	formData.append('file', file)
	formData.append('type', type ?? 'misc')

	const axiosInstance = publicRoute ? publicAxios : authAxios
	const endpoint = publicRoute ? '/public-upload' : '/upload'

	const { data } = await axiosInstance.post(endpoint, formData, {
		headers: { 'Content-Type': ContentType.FormData },
	})

	return data
}

export const handleVideoUpload = async (
	file: File | undefined,
	type?: string
) => {
	try {
		if (!file) return
		const formData = new FormData()
		formData.append('file', file)
		formData.append('type', type ?? 'misc')
		const { data } = await authAxios.post('/upload', formData, {
			headers: { 'Content-Type': ContentType.FormData },
		})
		return data
	} catch (err) {
		throw err
	}
}
