import {
	GridRenderCellParams,
	GridTreeNodeWithRender,
	GridValidRowModel,
} from '@mui/x-data-grid'

export const FormatNumber = (num: number) => {
	if (num >= 1000000) {
		return (num / 1000000).toFixed(1) + 'M'
	} else if (num >= 1000) {
		return (num / 1000).toFixed(1) + 'K'
	} else {
		return num.toString()
	}
}

export const formatDivision = (
	numerator: number,
	denominator: number,
	placeNumber: number = 3
) => {
	const result = numerator / denominator
	const fixedResult = result.toFixed(placeNumber)
	const formattedResult = parseFloat(fixedResult)
	return formattedResult
}

export const getSerialNumber = (
	params: GridRenderCellParams<
		GridValidRowModel,
		any,
		any,
		GridTreeNodeWithRender
	>,
	total?: Number
) => {
	if (!params) return
	const searchParams = new URLSearchParams(window.location.search)
	const page = Number(searchParams?.get('page') ?? 0)
	const pageSize = Number(total ?? params?.api?.getAllRowIds()?.length)
	const indexInPage = params?.api?.getAllRowIds()?.indexOf(params.id)
	return page * pageSize + indexInPage + 1
}

export const convertToMMonBoolean = (length: number, isMM: boolean) => {
	return isMM ? length * 0.001 : length
}
