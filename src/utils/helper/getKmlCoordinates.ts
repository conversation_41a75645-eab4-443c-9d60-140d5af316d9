import { kml } from '@tmcw/togeojson'
import { FeatureCollection, GeoJsonProperties, Geometry } from 'geojson'

async function parseKML(file: File) {
	const text = await file.text() // Read file as text
	const dom = new DOMParser().parseFromString(text, 'text/xml') // Parse as XML
	const geojson = kml(dom) // Convert to GeoJSON

	return geojson
}
function extractCoordinates(
	geojson: FeatureCollection<Geometry | null, GeoJsonProperties>
) {
	return geojson.features.flatMap((feature) => {
		const { geometry } = feature

		if (geometry?.type === 'LineString' || geometry?.type === 'Polygon') {
			return geometry.coordinates.flat(1)
		}

		return []
	})
}
export const getKmlCoordinates = async (file: File) => {
	if (file) {
		const geojson = await parseKML(file)
		return extractCoordinates(geojson)
	}
	return []
}
