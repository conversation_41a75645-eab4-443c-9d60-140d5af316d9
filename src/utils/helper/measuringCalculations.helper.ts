import { measuringUnitEnum } from '../constant'
import { roundNumber } from './roundNumber.helper'

export const convertMillimeterToMeter = (value: number) => value / 1000

export const convertMillimeterToCentimeter = (milometer: number): number =>
	milometer / 10

export const convertMeterToMillimeter = (meter: number) => meter * 1000

export const convertMeterToCentimeter = (meter: number): number => meter * 100

export const convertCentimeterToMillimeter = (cm: number) => cm * 10

export const convertCentimeterToMeter = (centimeter: number): number =>
	centimeter / 100

export const convertMillimeterCubetoLitre = (mm: number) =>
	Math.floor(mm / 10 ** 6 || 0)

export const convertLitreToMeterCube = (litre: number) => litre / 1000

export const convertLitreToMilliMeterCube = (litre: number) => litre * 10 ** 6
export const convertKgToTon = (value: number = 0, place?: number) =>
	roundNumber(value / 1000, place)

export const convertCubicCentimeterToLiter = (
	cubicCentimeter: number
): number => cubicCentimeter / 1000

export const convertCubicMillimeterToLt = (value: number) => value / 10 ** 6
export const convertCubicMeterToLiter = (cubicCentimeter: number): number =>
	cubicCentimeter * 1000

export const valueWithFixedDecimalPlaces = (value: number, place: number) => {
	const valueInString = String(value)
	if (!valueInString.includes('.')) return value
	const decimalPlaceValue = valueInString.split('.')?.[1]?.slice(0, place)
	const integerValue = valueInString.split('.')?.[0]
	return Number(`${integerValue}.${decimalPlaceValue}`)
}

export const valueUpToThreeDecimalPlaces = ({
	value,
	measuringUnit,
}: {
	value: string
	measuringUnit: string
}) => {
	const regexForSingleDecPlace = /([0-9]*[.|,]{0,1}[0-9]{0,1})/s
	const regexForThreeDecPlace = /([0-9]*[.|,]{0,1}[0-9]{0,3})/s

	return Number(
		value.match(
			measuringUnit === 'm' ? regexForThreeDecPlace : regexForSingleDecPlace
		)?.[0] || ''
	)
}

export const calculateVolumePyramidalKiln = (
	length: number,
	width: number,
	depth: number
) => {
	const lengthSquare = length ** 2
	const widthSquare = width ** 2
	const volume =
		(depth / 3) *
		(lengthSquare + widthSquare + Math.sqrt(lengthSquare * widthSquare))

	return volume
}
export const calculateVolumeRectangularKiln = (
	length: number,
	depth: number,
	shortBase: number,
	longBase: number
) => {
	const s = (longBase + shortBase) / 2
	const volume = s * depth * length

	return volume
}

export const calculateVolumeConicalKiln = (
	d1: number,
	d2: number,
	depth: number
) => {
	const r1 = d1 / 2
	const r1Square = r1 ** 2
	const r2 = d2 / 2
	const r2Square = r2 ** 2

	const volume = (Math.PI * depth * (r1Square + r2Square + r1 * r2)) / 3

	return volume
}

export const calculateVolumeCylindricalKiln = (
	diameter: number,
	height: number
) => {
	const radius = diameter / 2
	const volume = Math.PI * (radius * radius) * height
	return volume
}

export const convertUnit = (
	value: number,
	from: measuringUnitEnum,
	to: measuringUnitEnum = measuringUnitEnum.mm
) => {
	if (from === to) {
		return value
	}
	if (from === 'cm' && to === 'mm') {
		return convertCentimeterToMillimeter(value)
	}
	if (from === 'cm' && to === 'm') {
		return convertCentimeterToMeter(value)
	}
	if (from === 'mm' && to === 'cm') {
		return convertMillimeterToCentimeter(value)
	}
	if (from === 'mm' && to === 'm') {
		return convertMillimeterToMeter(value)
	}
	if (from === 'm' && to === 'cm') {
		return convertMeterToCentimeter(value)
	}
	if (from === 'm' && to === 'mm') {
		return convertMeterToMillimeter(value)
	}

	return value
}

export const convertUnitToLt = (
	value: number,
	from: 'cm3' | 'mm3' | 'm3'
): number => {
	if (from === 'cm3') {
		return convertCubicCentimeterToLiter(value)
	}
	if (from === 'mm3') {
		return convertCubicMillimeterToLt(value)
	}
	if (from === 'm3') {
		return convertCubicMeterToLiter(value)
	}
	return value
}
