import { format, differenceInMinutes, differenceInSeconds } from 'date-fns'

export function formatProductionTime(
	createdAtRaw: string | undefined,
	endTimeRaw: string | undefined
): {
	dateString: string
	timingDifference: string | null
	times: string | null
	inProgress?: boolean
} {
	const createdAt = createdAtRaw ? new Date(createdAtRaw) : new Date()
	const endTime = endTimeRaw ? new Date(endTimeRaw) : null

	if (endTime) {
		const sameDate =
			format(createdAt, 'dd/MM/yyyy') === format(endTime, 'dd/MM/yyyy')

		if (sameDate) {
			const diffMinutes = differenceInMinutes(endTime, createdAt)
			const diffSeconds = differenceInSeconds(endTime, createdAt)

			let timingDifference = ''
			if (diffMinutes > 0) {
				timingDifference += `${diffMinutes} ${
					diffMinutes == 1 ? 'min' : 'mins'
				}`
			} else if (diffSeconds > 0) {
				timingDifference += `${diffSeconds} ${
					diffSeconds == 1 ? 'sec' : 'secs'
				}`
			}

			return {
				dateString: format(createdAt, 'dd/MM/yyyy'),
				timingDifference: timingDifference ? `(${timingDifference})` : null,
				times: `(${format(createdAt, 'HH:mm')} - ${format(endTime, 'HH:mm')})`,
			}
		} else {
			return {
				dateString: `${format(createdAt, 'dd/MM/yyyy (HH:mm)')} - ${format(
					endTime,
					'dd/MM/yyyy (HH:mm)'
				)}`,
				timingDifference: null,
				times: null,
			}
		}
	}

	return {
		dateString: `${format(createdAt, 'dd/MM/yyyy (HH:mm)')} `,
		timingDifference: null,
		inProgress: true,
		times: null,
	}
}
