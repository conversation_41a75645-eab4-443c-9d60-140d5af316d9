import { IImage, IKilnWithDimensionImages } from "@/interfaces";

export function getKlinImagesList(data: IKilnWithDimensionImages): IImage[] {
    const imageKeys = [
        "imageURLs",
        "upper_surface_images",
        "lower_surface_images",
        "depth_images",
        "lower_side_images",
        "upper_side_images",
        "diameter_images",
        "frustum_length_images",
        "short_base_images",
        "long_base_images",
    ];

    const allImages: IImage[] = [];

    for (const key of imageKeys) {
        const images = (data as IKilnWithDimensionImages)[key as keyof IKilnWithDimensionImages] as IImage[] | undefined;
        if (images) {
            allImages.push(...images);
        }
    }

    return allImages;
}