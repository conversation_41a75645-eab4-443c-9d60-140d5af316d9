import { IImage, IContainerWithDimensionImages } from "@/interfaces";

export function getContainerImagesList(data: IContainerWithDimensionImages): IImage[] {
    const imageKeys: (keyof IContainerWithDimensionImages)[] = [
        "imageURLs",
        "heightImages",
        "lengthImages",
        "breadthImages",
        "diameterImages",
        "upperSurfaceDiameterImages",
        "lowerSurfaceDiameterImages",
        "lowerBaseImages",
        "upperBaseImages",
    ];

    const allImages: IImage[] = [];

    for (const key of imageKeys) {
        const images = data[key] as IImage[] | null | undefined;
        if (images) {
            allImages.push(...images);
        }
    }

    return allImages;
}
