import { useEffect, useState } from 'react'

export const useScrollHeight = () => {
	const [scrolledHeight, setScrolledHeight] = useState(0)

	useEffect(() => {
		const handleScroll = () => {
			const scrolledHeight = window.scrollY
			setScrolledHeight(scrolledHeight)
		}

		window.addEventListener('scroll', handleScroll)

		return () => {
			window.removeEventListener('scroll', handleScroll)
		}
	}, [])

	return { scrolledHeight }
}
