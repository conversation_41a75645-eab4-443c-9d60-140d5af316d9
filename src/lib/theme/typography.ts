import { ThemeOptions } from '@mui/material'
// Overlock;
// Overpass;
// OverpassMono;
export const typography: ThemeOptions['typography'] = () => ({
	fontFamily: 'Overpass, sans-serif',
	fontSize: 16,

	h1: {
		fontFamily: 'Overlock, sans-serif',
		fontSize: '40px',
		fontWeight: 900,
		lineHeight: '49px',
		letterSpacing: '0px',
		textAlign: 'left',
	},
	h2: {
		fontFamily: 'Overpass, sans-serif',
		fontSize: '36px',
		fontWeight: 700,
		lineHeight: '49px',
		letterSpacing: '0em',
		textAlign: 'left',
	},
	h3: {
		fontFamily: 'Overpass, sans-serif',
		fontSize: '32px',
		fontWeight: 700,
		lineHeight: '38px',
		letterSpacing: '0em',
		textAlign: 'left',
	},
	h4: {
		fontFamily: 'Overpass, sans-serif',
		fontSize: '24px',
		fontWeight: 700,
		lineHeight: '29px',
		letterSpacing: '0em',
		textAlign: 'left',
	},

	h5: {
		fontFamily: 'Overpass, sans-serif',
		fontSize: '20px',
		fontWeight: 700,
		lineHeight: '24px',
		letterSpacing: '0em',
		textAlign: 'left',
	},

	h6: {
		fontFamily: 'Overpass, sans-serif',
		fontSize: '20px',
		fontWeight: 400,
		lineHeight: '24px',
		letterSpacing: '0em',
		textAlign: 'left',
	},

	body1: {
		fontFamily: 'Overpass, sans-serif',
		fontSize: '16px',
		fontWeight: 400,
		lineHeight: '22px',
		letterSpacing: '0em',
		textAlign: 'left',
	},
	body2: {
		fontFamily: 'Overpass, sans-serif',
		fontSize: '16px',
		fontWeight: 700,
		lineHeight: '19px',
		letterSpacing: '0em',
		textAlign: 'left',
	},
	subtitle1: {
		fontFamily: 'Overpass, sans-serif',
		fontSize: '14px',
		fontWeight: 400,
		lineHeight: '20px',
		letterSpacing: '0em',
		textAlign: 'left',
	},
	subtitle2: {
		fontFamily: 'Overpass, sans-serif',
		fontSize: '14px',
		fontWeight: 700,
		lineHeight: '20px',
		letterSpacing: '0em',
		textAlign: 'left',
	},

	caption: {
		fontFamily: 'Overpass, sans-serif',
		fontSize: '12px',
		fontWeight: 600,
		lineHeight: '17px',
		letterSpacing: '0em',
		textAlign: 'left',
	},

	overline: {
		fontFamily: 'Overpass, sans-serif',
		fontSize: '12px',
		fontWeight: 400,
		lineHeight: '17px',
		letterSpacing: '0em',
		textAlign: 'left',
	},
})
