declare module '@mui/material/styles' {
	interface Palette {
		custom: {
			// add new colors here in future is needed to excellent palette support
			lightest: {
				primary: string
				approvedStatus: string
				pendingStatus: string
				rejectedStatus: string
			}
			red: {
				300: string
				400: string
				500: string
				600: string
				700: string
			}
			green: {
				100: string
				200: string
				300: string
				400: string
				500: string
				600: string
				700: string
				800: string
				900: string
			}
			yellow: {
				100: string
				200: string
				300: string
				400: string
				500: string
				600: string
			}
			blue: {
				100: string
				200: string
				400: string
				500: string
				600: string
			}
			grey: {
				100: string
				200: string
				300: string
				800: string
			}
		}
	}
	interface PaletteOptions {
		custom?: {
			// add new colors here in future is needed to excellent palette support
		}
	}
}
export {}
