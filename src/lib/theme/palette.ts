import { alpha, PaletteOptions } from '@mui/material'
import { common } from '@mui/material/colors'
import { error, info, neutral, primary, success, warning } from './colors'

export const palette: PaletteOptions = {
	action: {
		active: neutral[500],
		disabled: alpha(neutral[900], 0.38),
		disabledBackground: alpha(neutral[900], 0.12),
		focus: alpha(neutral[900], 0.16),
		hover: alpha(neutral[900], 0.04),
		selected: alpha(neutral[900], 0.12),
	},
	background: {
		default: common.white,
		paper: common.white,
	},
	divider: '#F2F4F7',
	error,
	info,
	mode: 'light',
	neutral,
	primary,
	success,
	text: {
		primary: neutral[900],
		secondary: neutral[500],
		disabled: alpha(neutral[900], 0.38),
	},
	warning,
	custom: {
		lightest: {
			primary: '#F7E6E4',
			pendingStatus: '#F9A825',
			approvedStatus: '#00C853',
			rejectedStatus: '#D32F2F'
		},
		red: {
			300: '#FBF7F6',
			400: '#C44736',
			500: '#D06C5E',
			600: '#FFEDD5',
			700: '#9A3412',
		},
		green: {
			100: '#ECFDF5',
			200: '#4CAF50',
			300: '#F0FDF4',
			400: '#BBF7D0',
			500: '#D0E6A6',
			600: '#DCFCE7',
			700: '#8CB43D',
			800: '#22C55E',
			900: '#15803D',
		},
		yellow: {
			100: '#FFF7ED',
			200: '#FFE0BE',
			300: '#FFEDD5',
			400: '#FDE68A',
			500: '#FFCC40',
			600: '#F59E0B'
		},
		blue: {
			100: '#EEF2FF',
			200: '#E8F4FF',
			400: '#BFDBFE',
			500: '#30A6FF',
			600: '#3B82F6'

		},
		grey: {
			100: '#E9E8E8',
			200: '#292424CC',
			300: '#D9D9D9',
			800: '#747474',
		},
		pink: {
			A100: '#FCD7C2',
			A500: '#F4A0A0',
			lightPink: '#FBF7F6',
		},
	},
}
