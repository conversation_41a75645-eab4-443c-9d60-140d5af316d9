declare module '@mui/material/styles' {
	interface Palette {
		neutral: {
			10: string
			20: string
			50: string
			100: string
			200: string
			300: string
			400: string
			500: string
			600: string
			700: string
			800: string
			900: string
		}
	}

	interface PaletteColorOptions {
		lightest: string
		light: string
		main: string
		dark: string
		darkest: string
		alpha4?: string
		alpha8?: string
		alpha12?: string
		alpha30?: string
		alpha50?: string
		contrastText: string
	}
	interface PaletteOptions {
		neutral: {
			50: string
			100: string
			200: string
			300: string
			400: string
			500: string
			600: string
			700: string
			800: string
			900: string
		}
	}
}
export {}
