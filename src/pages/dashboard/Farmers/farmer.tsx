import { CustomDataGrid, CustomHeader } from '@/components'
import DownloadIcon from '@/assets/icons/download.svg'
import {
	Box,
	Chip,
	FormControl,
	IconButton,
	InputLabel,
	MenuItem,
	Select,
	Stack,
	Tab,
	TextField,
	Typography,
	alpha,
	styled,
	useTheme,
} from '@mui/material'
import {
	GridColDef,
	GridEventListener,
	GridRenderCellParams,
	GridSearchIcon,
	GridValidRowModel,
} from '@mui/x-data-grid'
import totalAcresIcon from '@/assets/icons/c-sink.svg'
import totalAreaIcon from '@/assets/icons/fileIcon.svg'
import { filterValue } from '@/utils/constant'
import ReactApexChart from 'react-apexcharts'
// import { FarmData, FarmerData, dummyImageList } from '../dummyData'
import { dummyImageList } from '../dummyData'
import { ApexOptions } from 'apexcharts'
import { MapComponent } from './Component'
import { MultipleAvatar } from '@/components/MultipleAvatar.tsx'
import { SyntheticEvent, useCallback, useMemo, useState } from 'react'
import { useNavigate } from 'react-router-dom'
import { TabContext, TabList, TabPanel } from '@mui/lab'
import {
	CloseRounded,
	Done,
	MoreVert,
	QueryBuilderRounded,
} from '@mui/icons-material'
import { useFarmer } from './useFarmer'

export const Farmer = () => {
	const theme = useTheme()
	const navigate = useNavigate()
	const {
		farmDetails,
		farmerDetails,
		setSearchParams,
		isLoadingFarmDetails,
		isLoadingFarmerDetails,
		tab,
		cropStage,
	} = useFarmer()
	const [filterData, setFilterData] = useState({
		crop: '',
		stage: '',
		status: '',
		network: '',
		aggregator: '',
	})

	const handleFilterTabChange = useCallback(
		(_: SyntheticEvent | null, newValue: string) => {
			setSearchParams((prev) => ({
				...prev,
				tab: newValue,
				page: 0,
				limit: 10,
			}))
		},
		[setSearchParams]
	)

	const handleCropState = useCallback(
		(cropStage: string) => {
			setSearchParams((prev) => ({
				...prev,
				cropStage,
				page: 0,
				limit: 10,
			}))
		},
		[setSearchParams]
	)

	const handleFarmerRowClick: GridEventListener<'rowClick'> = useCallback(
		(params) => {
			navigate(`${params?.row?.id}/details`)
		},
		[navigate]
	)

	const handleFarmRowClick: GridEventListener<'rowClick'> = useCallback(
		(params) => {
			navigate(`${params?.row?.id}/farm-details`)
		},
		[navigate]
	)

	const StatusChart = useMemo(
		() => ({
			series: [4000, 3000, 1000],
			options: {
				labels: ['Accepted', 'Pending', 'Rejected'],
				legend: {
					show: true,
					markers: {
						fillColors: [
							theme.palette.success.main,
							theme.palette.warning.main,
							theme.palette.error.light,
						],
					},
				},
				dataLabels: {
					enabled: true,
					textAnchor: 'middle',
					style: {
						fontSize: '12px',
						fontWeight: '700',
						colors: [
							theme.palette.neutral['900'],
							theme.palette.neutral['900'],
							theme.palette.neutral['900'],
						],
					},
				},
				tooltip: { enabled: false },
				responsive: [
					{
						breakpoint: 680,
						options: {
							chart: {
								width: 280,
							},
						},
					},
					{
						breakpoint: 1440,
						options: {
							chart: {
								width: 300,
							},
						},
					},
				],
				chart: {
					type: 'donut',
					width: 100,
				},
				plotOptions: {
					pie: {
						donut: {
							size: '70%',
						},
					},
				},
				fill: {
					colors: [
						theme.palette.success.main,
						theme.palette.warning.main,
						theme.palette.error.light,
					],
				},
				states: {
					hover: { filter: { type: 'darken', value: 0.7 } },
					active: { filter: { type: 'none', value: 0 } },
				},
				stroke: { width: 0 },
			},
		}),
		[theme.palette]
	)

	const Status = useMemo(
		() => ({
			accepted: {
				className: 'success-chip',
				icon: <Done color='success' fontSize='small' />,
			},
			pending: {
				className: 'warning-chip',
				icon: <QueryBuilderRounded color='warning' fontSize='small' />,
			},
			rejected: {
				className: 'error-chip',
				icon: <CloseRounded color='error' fontSize='small' />,
			},
		}),
		[]
	)
	const CroppingStage = useMemo(
		() => ({
			cropping: {
				className: 'success-chip',
			},
			harversting: {
				className: 'success-chip',
			},
			sundrying: {
				className: 'success-chip',
			},
			transport: {
				className: 'success-chip',
			},
			production: {
				className: 'success-chip',
			},
			distribution: {
				className: 'success-chip',
			},
		}),
		[]
	)

	const filterArray = useMemo(
		() => [
			{
				id: 'crop',
				label: 'Crop',
				children: [
					{
						value: 'grain',
						label: 'Grain',
					},
				],
			},
			{
				id: 'stage',
				label: 'Stage',
				children: [
					{
						value: 'staring',
						label: 'Starting',
					},
				],
			},
			{
				id: 'status',
				label: 'Status',
				children: [
					{
						value: 'test1',
						label: 'Test1',
					},
				],
			},
			{
				id: 'network',
				label: 'Network',
				children: [
					{
						value: 'test_network1',
						label: 'Test Network 1',
					},
				],
			},
			{
				id: 'aggregator',
				label: 'Aggregator',
				children: [
					{
						value: 'test_aggregator',
						label: 'Text Aggregator 1',
					},
				],
			},
		],
		[]
	)

	const HeaderEndButtons = () => (
		<Stack direction='row' spacing={2}>
			<IconButton
				onClick={() => console.log('hi')}
				sx={{
					border: `${theme.spacing(0.125)} solid ${
						theme.palette.neutral['100']
					}`,
					borderRadius: theme.spacing(1.25),
				}}>
				<Box component='img' src={DownloadIcon} height={20} width={20} />
			</IconButton>
		</Stack>
	)

	const FarmerColumns: GridColDef<GridValidRowModel>[] = useMemo(
		() => [
			{
				field: 'name',
				headerName: 'Farmer Name',
				minWidth: 160,
				flex: 1,
			},
			{
				field: 'BAName',
				headerName: 'BA Name',
				minWidth: 160,
				flex: 1,
				renderCell: (params: GridRenderCellParams) => (
					<Typography variant='subtitle1'>
						{params.row.BAName || '-'}
					</Typography>
				),
			},
			{
				field: 'cSinkNetworkName',
				headerName: 'C-sink Network/Artisan Pro',
				minWidth: 160,
				flex: 1,
				renderCell: (params: GridRenderCellParams) => (
					<Typography variant='subtitle1'>
						{params.row.cSinkNetworkName || params.row.artisanProName}
					</Typography>
				),
			},
			{
				field: 'farmsCount',
				headerName: 'No of Farms',
				minWidth: 100,
				flex: 1,
			},
			{
				field: 'totalFarmArea',
				headerName: 'Total Size',
				minWidth: 100,
				flex: 1,
			},
			{
				field: 'biomassQty',
				headerName: 'Total Biomass quantity (Kg) ',
				minWidth: 100,
				flex: 1,
			},
		],
		[]
	)

	const FarmColumns: GridColDef<GridValidRowModel>[] = useMemo(
		() => [
			{
				field: 'landmark',
				headerName: 'Farm',
				minWidth: 160,
				flex: 1,
			},
			{
				field: 'farmerName',
				headerName: 'Farmer Name',
				minWidth: 160,
				flex: 1,
			},
			{
				field: 'cropName',
				headerName: 'Crop',
				minWidth: 160,
				flex: 1,
			},
			{
				field: 'cropStage',
				headerName: 'Stage',
				minWidth: 120,
				flex: 1,
				renderCell: (params: GridRenderCellParams) => {
					const key: keyof typeof CroppingStage = params.row.cropStage
					return (
						<Chip
							label={key ?? ''}
							size='small'
							variant='filled'
							className={`chip ${CroppingStage[`${key}`]?.className}`}
						/>
					)
				},
			},
			{
				field: 'farmerStatus',
				headerName: 'Status',
				minWidth: 120,
				flex: 1,
				renderCell: (params: GridRenderCellParams) => {
					const key: keyof typeof Status = params.row.farmerStatus
					return (
						<Chip
							label={key ?? ''}
							icon={Status[`${key}`]?.icon}
							size='small'
							variant='filled'
							className={`chip ${Status[`${key}`]?.className}`}
						/>
					)
				},
			},
			{
				field: 'cSinkNetworkName',
				headerName: 'C Sink Network/Artisan Pro',
				minWidth: 120,
				flex: 1,
				renderCell: (params: GridRenderCellParams) => (
					<Typography variant='subtitle1'>
						{params.row.cSinkNetworkName ?? params.row.artisanProName}
					</Typography>
				),
			},
			{
				field: 'biomassAggregatorName',
				headerName: 'Aggregator',
				minWidth: 120,
				flex: 1,
			},
			{
				field: 'landmark',
				headerName: 'Location',
				minWidth: 120,
				flex: 1,
			},
			{
				field: 'biomassQuantity',
				headerName: 'Biomass Quantity (Kg)',
				minWidth: 120,
				flex: 1,
				renderCell: (params: GridRenderCellParams) => (
					<Typography variant='subtitle1'>
						{params.row.biomassQuantity ?? '-'}
					</Typography>
				),
			},
			{
				field: 'fieldSize',
				headerName: 'Size',
				minWidth: 120,
				flex: 1,
				renderCell: (params: GridRenderCellParams) => (
					<Typography variant='subtitle1'>
						{params.row.fieldSize} hectare
					</Typography>
				),
			},
			{
				field: 'action',
				headerName: '',
				minWidth: 60,
				flex: 1,
				renderCell: () => (
					<IconButton onClick={() => console.log('hi')}>
						<MoreVert />
					</IconButton>
				),
			},
		],
		[CroppingStage, Status]
	)

	const CropStagesArray = useMemo(
		() => [
			{
				value: 'cropping',
				label: 'Cropping',
				count: 5,
				imageList: dummyImageList,
			},
			{
				value: 'harvesting',
				label: 'Harvesting',
				count: 5,
				imageList: dummyImageList,
			},
			{
				value: 'sun_drying',
				label: 'Sundrying',
				count: 5,
				imageList: dummyImageList,
			},
			{
				value: 'transportation',
				label: 'Transport',
				count: 5,
				imageList: dummyImageList,
			},
			{
				value: 'production',
				label: 'Production',
				count: 5,
				imageList: dummyImageList,
			},
			{
				value: 'distribution',
				label: 'Distribution',
				count: 5,
				imageList: dummyImageList,
			},
		],
		[]
	)

	return (
		<StyledContained>
			<Stack className='header' gap={3}>
				<CustomHeader
					showBottomBorder={true}
					heading='Farmers'
					showButton={false}
					endComponent={<HeaderEndButtons />}
				/>
				<Stack className='header-filters' gap={1}>
					<TextField
						name='search'
						label='Search'
						variant='outlined'
						className='search-textFiled'
						InputProps={{
							startAdornment: <GridSearchIcon />,
						}}
					/>
					{filterArray.map(({ id, label, children }) => (
						<FormControl key={id} className='form-controller'>
							<InputLabel id='manager'>{label}</InputLabel>
							<Select
								labelId='manager'
								label={label}
								value={filterData[id as keyof typeof filterData] || ''}
								onChange={(e) => {
									setFilterData((prev) => ({
										...prev,
										[`${id}`]: e.target.value as string,
									}))
								}}>
								{children.map(
									({ label, value }: { label: string; value: string }) => (
										<MenuItem key={value} value={value}>
											{label}
										</MenuItem>
									)
								)}
							</Select>
						</FormControl>
					))}
				</Stack>
			</Stack>
			<Stack className='details-hero-container'>
				<Stack className='details-container' flexWrap='wrap' gap={1}>
					<Stack direction='column' spacing={2}>
						<Stack direction='row' className='details-card'>
							<Stack className='image-container light-primary'>
								<Box
									component='img'
									src={totalAcresIcon}
									alt='icon'
									height={24}
									width={24}
									sx={{
										filter: filterValue,
									}}
								/>
							</Stack>
							<Stack>
								<Typography variant='subtitle1' className='text-grey'>
									Total Farmers
								</Typography>
								<Typography variant='h4'>
									{farmerDetails?.totalFarmersAndFarms?.totalFarmers}
								</Typography>
							</Stack>
						</Stack>
						<Stack direction='row' className='details-card'>
							<Stack className='image-container light-success '>
								<Box
									component='img'
									src={totalAreaIcon}
									alt='icon'
									height={24}
									width={24}
								/>
							</Stack>
							<Stack>
								<Typography variant='subtitle1' className='text-grey'>
									Total Area
								</Typography>
								<Typography variant='h4'>
									{farmerDetails?.totalFarmersAndFarms?.totalFarms} Ha
								</Typography>
							</Stack>
						</Stack>
					</Stack>
					<Stack className='crop-details-card'>
						<Typography variant='body2' ml={4}>
							Status
						</Typography>
						<Stack className='chart'>
							<ReactApexChart
								options={StatusChart.options as ApexOptions}
								series={StatusChart.series}
								type='donut'
							/>
						</Stack>
					</Stack>
					<Stack className='crop-details-card'>
						<Typography variant='body2' ml={4}>
							Crops
						</Typography>
						<Stack className='chart'>
							<ReactApexChart
								options={StatusChart.options as ApexOptions}
								series={StatusChart.series}
								type='donut'
							/>
						</Stack>
					</Stack>
					<Stack className='crop-details-card'>
						<Typography variant='body2' ml={4}>
							Stage
						</Typography>
						<Stack className='chart'>
							<ReactApexChart
								options={StatusChart.options as ApexOptions}
								series={StatusChart.series}
								type='donut'
							/>
						</Stack>
					</Stack>
				</Stack>
				<Stack className='hero-section'>
					<Stack className='tabs-section'>
						{CropStagesArray.map(({ value, label, count, imageList }) => (
							<Stack
								className={`tab-container ${
									cropStage === value ? 'primary' : 'grey'
								}`}
								key={value}
								direction='row'
								justifyContent='space-between'
								onClick={() => {
									handleCropState(value)
								}}>
								<Stack>
									<Typography
										variant='subtitle2'
										color={
											cropStage === value
												? theme.palette.primary.light
												: theme.palette.neutral[500]
										}>
										{label}
									</Typography>
									<Typography
										variant='body1'
										color={
											cropStage === value
												? theme.palette.neutral[500]
												: theme.palette.neutral[300]
										}>
										{count}
									</Typography>
								</Stack>
								<Stack className='image-count-container' direction='row'>
									<MultipleAvatar imageList={imageList} MaxAvatar={2} />
									<Typography
										variant='caption'
										color={theme.palette.neutral[500]}>{`${
										imageList.length - 2 > 0 ? `+${imageList.length - 2}` : ''
									}`}</Typography>
								</Stack>
							</Stack>
						))}
					</Stack>
					<Stack className='map-container'>
						<Box className='map'>
							<MapComponent />
						</Box>
					</Stack>
				</Stack>
			</Stack>
			<Stack className='container'>
				<TabContext value={tab}>
					<Stack className='tab-container'>
						<TabList onChange={handleFilterTabChange}>
							<Tab label='By Farmer' value='farmer' />
							<Tab label='By Farm' value='farm' />
						</TabList>
					</Stack>
					<TabPanel className='tab-panel' value='farmer'>
						<CustomDataGrid
							onRowClick={handleFarmerRowClick}
							showPagination={true}
							rows={farmerDetails?.farmers ?? []}
							columns={FarmerColumns}
							rowCount={farmerDetails?.count ?? 0}
							sx={{
								border: `1px solid ${theme.palette.neutral['100']}`,
							}}
							loading={isLoadingFarmerDetails}
						/>
					</TabPanel>
					<TabPanel className='tab-panel' value='farm'>
						<CustomDataGrid
							onRowClick={handleFarmRowClick}
							showPagination={true}
							rows={farmDetails?.farms ?? []}
							columns={FarmColumns}
							rowCount={farmDetails?.count ?? 0}
							sx={{
								border: `1px solid ${theme.palette.neutral['100']}`,
							}}
							loading={isLoadingFarmDetails}
						/>
					</TabPanel>
				</TabContext>
			</Stack>
		</StyledContained>
	)
}

const StyledContained = styled(Stack)(({ theme }) => ({
	'.light-success': {
		background: alpha(theme.palette.success.main, 0.1),
	},
	'.light-primary': {
		background: theme.palette.neutral['20'],
	},
	'.text-grey': {
		color: theme.palette.neutral['300'],
	},
	'.chip': {
		...theme.typography.caption,
		textTransform: 'capitalize',
	},
	'.success-chip': {
		background: theme.palette.success.light,
		color: theme.palette.success.main,
	},
	'.warning-chip': {
		background: theme.palette.warning.light,
		color: theme.palette.warning.main,
	},
	'.error-chip': {
		background: theme.palette.error.light,
		color: theme.palette.error.main,
	},
	'.header': {
		padding: theme.spacing(2, 0, 0),
		borderBottom: `${theme.spacing(0.125)} solid ${
			theme.palette.neutral['100']
		}`,
		'.header-filters': {
			width: '100%',
			flexDirection: 'row',
			alignItems: 'center',
			padding: theme.spacing(2, 3),
			'.search-textFiled': {
				minWidth: theme.spacing(18.5),
				maxWidth: theme.spacing(20.5),
				width: '100%',
				'.MuiInputBase-root': {
					height: theme.spacing(4.5),
					borderRadius: theme.spacing(1.25),
				},
			},
			'.form-controller': {
				margin: theme.spacing(0.125),
				maxWidth: theme.spacing(20.5),
				width: '100%',
				'.MuiOutlinedInput-notchedOutline': {
					borderRadius: theme.spacing(1.25),
				},
			},
		},
	},
	'.grid-header-component': {
		width: '100%',
		'.form-controller': {
			margin: theme.spacing(0.125),
			minWidth: theme.spacing(18),
			width: '100%',
			'.MuiOutlinedInput-notchedOutline': {
				borderRadius: theme.spacing(1.25),
			},
		},
	},
	'.details-hero-container': {
		gap: theme.spacing(2),
		padding: theme.spacing(2, 0),
		background: theme.palette.neutral['50'],
		'.details-container': {
			flexDirection: 'row',
			padding: theme.spacing(0, 3),
			width: '100%',
			'.details-card': {
				width: theme.spacing(27.5),
				height: theme.spacing(15),
				alignItems: 'center',
				justifyContent: 'center',
				gap: theme.spacing(2),
				borderRadius: theme.spacing(2),
				boxShadow: `0 0 ${theme.spacing(0.25)} 0 ${alpha(
					theme.palette.common.black,
					0.25
				)}`,
				'.image-container': {
					height: 46,
					width: 46,
					borderRadius: theme.spacing(1),
					justifyContent: 'center',
					alignItems: 'center',
				},
			},

			'.crop-details-card': {
				justifyContent: 'center',
				width: '100%',
				maxWidth: theme.spacing(34.75),
				height: theme.spacing(32),
				borderRadius: theme.spacing(2),
				gap: theme.spacing(2),
				boxShadow: `0 0 ${theme.spacing(0.25)} 0 ${alpha(
					theme.palette.common.black,
					0.25
				)}`,
				padding: theme.spacing(4, 0, 2, 0),
				'.chart': {
					width: 290,
					height: '100%',
					marginLeft: theme.spacing(-1.5),
				},
			},
		},

		'.hero-section': {
			padding: theme.spacing(2, 3),
			flexDirection: 'row',
			gap: theme.spacing(2),
			'.tabs-section': {
				gap: theme.spacing(4),
				'.tab-container': {
					width: theme.spacing(27.5),
					height: theme.spacing(8),
					alignItems: 'center',
					borderRadius: theme.spacing(2),

					padding: theme.spacing(2),
					cursor: 'pointer',
					'.image-count-container': {
						alignItems: 'center',
					},
				},
				'.grey': {
					background: theme.palette.neutral['50'],
					border: `${theme.spacing(0.125)} solid ${
						theme.palette.neutral['200']
					}`,
				},
				'.primary': {
					background: alpha(theme.palette.primary.light, 0.2),
					border: `${theme.spacing(0.125)} solid ${alpha(
						theme.palette.primary.light,
						0.3
					)}`,
				},
			},
			'.map-container': {
				width: '100%',
				borderRadius: theme.spacing(2),
				boxShadow: `0 0 ${theme.spacing(0.25)} 0 ${alpha(
					theme.palette.common.black,
					0.25
				)}`,
				alignItems: 'center',
				'.map': {
					minHeight: 600,
					maxheight: 800,
					minWidth: 600,
					maxWidth: 800,
					position: 'relative',
				},
			},
		},
	},
	'.container': {
		'.tab-container': {
			background: theme.palette.neutral['50'],
			padding: theme.spacing(0, 3),
			borderBottom: `${theme.spacing(0.125)} solid ${
				theme.palette.neutral['100']
			}`,
		},
		'.tab-panel': {
			padding: theme.spacing(0, 3, 4),
			minHeight: theme.spacing(53),
		},
	},
}))
