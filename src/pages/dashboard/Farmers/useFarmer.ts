import { authAxios, useAuthContext } from '@/contexts'
import { IFarmData, IFarmerData } from '@/interfaces'
import { defaultLimit, defaultPage } from '@/utils/constant'
import { useQuery } from '@tanstack/react-query'
import { useCallback } from 'react'
import { useSearchParams } from 'react-router-dom'

export const useFarmer = () => {
	const [searchParams, setSearchParams] = useSearchParams()
	const { userDetails } = useAuthContext()
	const paramsLimit = searchParams.get('limit') || defaultLimit
	const paramsPage = searchParams.get('page') || defaultPage
	const tab = searchParams.get('tab') || 'farmer'
	const cropStage = searchParams.get('cropStage') || ''

	const getFamerData = useCallback(async () => {
		const { data } = await authAxios.get<IFarmerData>(
			`/new/farmers?biomassAggregatorId=${userDetails?.biomassAggregatorId}&cropStage=${cropStage}&limit=${paramsLimit}&page=${paramsPage}`
		)
		return data
	}, [cropStage, paramsLimit, paramsPage, userDetails?.biomassAggregatorId])

	const getFarmData = useCallback(async () => {
		const { data } = await authAxios.get<IFarmData>(
			`/new/farms?biomassAggregatorId=${userDetails?.biomassAggregatorId}&cropStage=${cropStage}&limit=${paramsLimit}&page=${paramsPage}`
		)
		return data
	}, [cropStage, paramsLimit, paramsPage, userDetails?.biomassAggregatorId])

	const farmerData = useQuery({
		queryKey: [
			'farmerDetails',
			userDetails?.biomassAggregatorId,
			paramsLimit,
			paramsPage,
			cropStage,
		],
		queryFn: getFamerData,
		enabled: !!userDetails?.biomassAggregatorId && tab === 'farmer',
	})

	const farmData = useQuery({
		queryKey: [
			'farmDetails',
			userDetails?.biomassAggregatorId,
			paramsLimit,
			paramsPage,
			cropStage,
		],
		queryFn: getFarmData,
		enabled: !!userDetails?.biomassAggregatorId && tab === 'farm',
	})

	return {
		farmerDetails: farmerData.data,
		isLoadingFarmerDetails: farmerData.isLoading,
		farmDetails: farmData.data,
		isLoadingFarmDetails: farmData.isLoading,
		setSearchParams,
		tab,
        cropStage
	}
}
