import { useTheme } from '@mui/material'
import DatamapsIndia from 'react-datamaps-india'

export const MapComponent = () => {
	const theme = useTheme()
	return (
		<DatamapsIndia
			regionData={{
				Maharashtra: {
					value: 10,
				},
				Punjab: {
					value: 0,
				},
				Gujarat: {
					value: 800,
				},
				Karnataka: {
					value: 700,
				},
				TamilNadu: {
					value: 190,
				},
				Kerala: {
					value: 890,
				},
			}}
			mapLayout={{
				// title: 'Map',
				startColor: theme.palette.primary.light,
				endColor: theme.palette.primary.dark,
				noDataColor: '#f5f5f5',
				borderColor: '#8D8D8D',
				hoverBorderColor: '#8D8D8D',
				hoverColor: theme.palette.primary.main,
				height: 50,
				weight: 30,
			}}
		/>
	)
}
