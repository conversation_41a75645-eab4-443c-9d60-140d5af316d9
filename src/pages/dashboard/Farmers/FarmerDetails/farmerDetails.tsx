import {
	Add,
	ArrowLeftRounded,
	ArrowRightRounded,
	Check,
	Circle,
	MoreVert,
} from '@mui/icons-material'
import {
	Avatar,
	Box,
	Button,
	Chip,
	FormControl,
	IconButton,
	InputLabel,
	MenuItem,
	Select,
	Stack,
	Tab,
	TextField,
	Typography,
	alpha,
	styled,
	useTheme,
} from '@mui/material'
import { useNavigate } from 'react-router-dom'
import DownloadIcon from '@/assets/icons/download.svg'
import EditIcon from '@/assets/icons/editIcon.svg'
import { useCallback, useMemo, useState } from 'react'
import { TabContext, TabList, TabPanel } from '@mui/lab'
import { MapComponent } from '../Component'
import {
	GridColDef,
	GridRenderCellParams,
	GridSearchIcon,
	GridValidRowModel,
} from '@mui/x-data-grid'
import { CustomDataGrid } from '@/components'
import { useFarmerDetails } from './useFarmDetails'
import { SingleFarmerData } from '../../dummyData'

export const FarmerDetails = () => {
	const theme = useTheme()
	const navigate = useNavigate()
	// commented some code for future use - currently pause this farmer screen
	const {
		farmerData,
		// farmDetails,
		// isFarmLoading,
		setSearchParams,
		tab,
		// cropStageDetails,
		// isCropStageLoading,
		croppingStage,
	} = useFarmerDetails()
	const [filterData, setFilterData] = useState({
		crop: '',
		status: '',
	})
	const handleChange = useCallback(
		(_: unknown, newValue: string) => {
			setSearchParams(
				(prev) => ({
					...prev,
					tab: newValue,
					page: 0,
					limit: 10,
				}),
				{
					replace: true,
				}
			)
		},
		[setSearchParams]
	)

	const handleCroppingState = useCallback(
		(croppingStage: string) => {
			setSearchParams(
				(prev) => ({
					...prev,
					croppingStage,
					page: 0,
					limit: 10,
				}),
				{
					replace: true,
				}
			)
		},
		[setSearchParams]
	)

	const networksDetails = useMemo(
		() => [
			{
				label: 'Network',
				value: farmerData?.CSinkNetwork,
				id: farmerData?.CSinkNetworkShortName,
			},
			{
				label: 'Biomass Aggregator',
				value: farmerData?.biomassAggregator,
				id: farmerData?.biomassAggregatorShortName,
			},
		],
		[farmerData]
	)

	const filterArray = [
		{
			id: 'crop',
			label: 'Crop',
			children: [
				{
					value: 'grain',
					label: 'Grain',
				},
			],
		},
		{
			id: 'status',
			label: 'Status',
			children: [
				{
					value: 'accepted',
					label: 'Accepted',
				},
			],
		},
	]

	const FarmerCropStatus = useMemo(
		() => [
			{
				id: 'cropping',
				label: 'Cropping',
				value: 4,
			},
			{
				id: 'harvesting',
				label: 'Harvesting',
				value: 0,
			},
			{
				id: 'sun_dryingg',
				label: 'Sundrying',
				value: 0,
			},
			{
				id: 'transportation',
				label: 'Transport',
				value: 0,
			},
			{
				id: 'production',
				label: 'Production',
				value: 0,
			},
			{ id: 'distribution', label: 'Distribution', value: 0 },
		],
		[]
	)

	const FarmerColumns: GridColDef<GridValidRowModel>[] = useMemo(
		() => [
			{
				field: 'farmerName',
				headerName: 'Farmer Name',
				minWidth: 120,
				flex: 1,
				renderCell: (params: GridRenderCellParams) => {
					return (
						<Typography
							variant='subtitle1'
							sx={{
								color: theme.palette.neutral['900'],
							}}>
							{params.row.farmerName ?? ''}
						</Typography>
					)
				},
			},
			{
				field: 'farm',
				headerName: 'Farm',
				minWidth: 120,
				flex: 1,
				renderCell: (params: GridRenderCellParams) => {
					return (
						<Typography
							variant='subtitle1'
							sx={{
								color: theme.palette.neutral['900'],
							}}>
							{params.row.farm ?? ''}
						</Typography>
					)
				},
			},
			{
				field: 'crop',
				headerName: 'Crop',
				minWidth: 120,
				flex: 1,
				renderCell: (params: GridRenderCellParams) => {
					return (
						<Typography
							variant='subtitle1'
							sx={{
								color: theme.palette.neutral['900'],
							}}>
							{params.row.crop ?? ''}
						</Typography>
					)
				},
			},
			{
				field: 'stage',
				headerName: 'Stage',
				minWidth: 180,
				flex: 1,
				renderCell: (params: GridRenderCellParams) => {
					return (
						<Chip
							label={params.row.stage}
							icon={<Check color='success' fontSize='small' />}
							sx={{
								background: theme.palette.success.light,
								color: theme.palette.success.main,
							}}
						/>
					)
				},
			},
			{
				field: 'status',
				headerName: 'Status',
				minWidth: 180,
				flex: 1,
				renderCell: (params: GridRenderCellParams) => {
					return (
						<Chip
							label={params.row.status}
							icon={<Check color='success' fontSize='small' />}
							sx={{
								background: theme.palette.success.light,
								color: theme.palette.success.main,
							}}
						/>
					)
				},
			},
			{
				field: 'cSinkNetwork',
				headerName: 'C-Sink Network',
				minWidth: 140,
				flex: 1,
				renderCell: (params: GridRenderCellParams) => {
					return (
						<Typography
							variant='subtitle1'
							sx={{
								color: theme.palette.neutral['900'],
							}}>
							{params.row.cSinkNetwork ?? ''}
						</Typography>
					)
				},
			},
			{
				field: 'aggregator',
				headerName: 'Aggregator',
				minWidth: 140,
				flex: 1,
				renderCell: (params: GridRenderCellParams) => {
					return (
						<Typography
							variant='subtitle1'
							sx={{
								color: theme.palette.neutral['900'],
							}}>
							{params.row.aggregator ?? ''}
						</Typography>
					)
				},
			},
			{
				field: 'location',
				headerName: 'Location',
				minWidth: 80,
				flex: 1,
				renderCell: (params: GridRenderCellParams) => {
					return (
						<Typography
							variant='subtitle1'
							sx={{
								color: theme.palette.neutral['900'],
							}}>
							{params.row.location ?? ''}
						</Typography>
					)
				},
			},
			{
				field: 'size',
				headerName: 'Size',
				minWidth: 80,
				flex: 1,
				renderCell: (params: GridRenderCellParams) => {
					return (
						<Typography
							variant='subtitle1'
							sx={{
								color: theme.palette.neutral['900'],
							}}>
							{params.row.size ?? ''}
						</Typography>
					)
				},
			},
			{
				field: 'biomass',
				headerName: 'Biomass',
				minWidth: 100,
				flex: 1,
				renderCell: (params: GridRenderCellParams) => {
					return (
						<Typography
							variant='subtitle1'
							sx={{
								color: theme.palette.neutral['900'],
							}}>
							{params.row.biomass ?? ''}
						</Typography>
					)
				},
			},
			{
				field: 'action',
				headerName: '',
				minWidth: 40,
				flex: 1,
				renderCell: () => {
					return (
						<IconButton>
							<MoreVert />
						</IconButton>
					)
				},
			},
		],
		[
			theme.palette.neutral,
			theme.palette.success.light,
			theme.palette.success.main,
		]
	)

	return (
		<StyledContainer>
			<Stack className='header'>
				<Stack direction='row' alignItems='center'>
					<Button
						className='light-grey'
						variant='text'
						startIcon={<ArrowLeftRounded className='light-grey' />}
						onClick={() => navigate(-1)}>
						Farmer
					</Button>
					<Typography variant='h6' color={theme.palette.neutral['500']}>
						/ Farmer Name
					</Typography>
				</Stack>
				<IconButton
					className='download-btn'
					onClick={() => console.log('download')}>
					<Box component='img' src={DownloadIcon} height={20} width={20} />
				</IconButton>
			</Stack>
			<Stack className='hero-section'>
				<Stack className='details-container' flexWrap='wrap'>
					<Stack className='farmer-details-container'>
						<Avatar src='' alt='farmer' sx={{ width: 118, height: 118 }} />
						<Stack justifyContent='center'>
							<Typography variant='h5'>{farmerData?.name}</Typography>
							<Stack direction='row' gap={2} alignItems='center'>
								<Typography variant='subtitle1'>ID: VS09F002(dummy)</Typography>
								<Typography variant='subtitle1'>
									<Circle className='circle-icon' />
									&nbsp;
									{farmerData?.address}
								</Typography>
							</Stack>
							<Chip
								icon={<Check color='success' />}
								label={'Trained'}
								className='success-chip'
							/>
						</Stack>
					</Stack>
					<Stack className='netwrok-details' gap={4}>
						{networksDetails.map(({ value, id, label }, index: number) => (
							<Stack key={index}>
								<Typography
									variant='subtitle2'
									color={theme.palette.neutral['300']}>
									{label}
								</Typography>
								<Stack direction='row' spacing={1}>
									<Typography
										variant='subtitle1'
										color={theme.palette.neutral['900']}>
										{value}
									</Typography>
									<Typography
										variant='subtitle1'
										color={theme.palette.neutral['900']}>
										({id})
									</Typography>
								</Stack>
							</Stack>
						))}
					</Stack>
					<Stack className='action-container'>
						<Button
							variant='outlined'
							className='edit-button'
							startIcon={
								<Box
									component='img'
									src={EditIcon}
									alt='edit-icon'
									height={12}
									width={12}
								/>
							}
							size='small'>
							Edit Network
						</Button>
						<Button variant='contained' startIcon={<Add />} size='small'>
							Add Farm
						</Button>
					</Stack>
				</Stack>
			</Stack>
			<TabContext value={tab}>
				<Stack className='tab-container'>
					<TabList onChange={handleChange}>
						<Tab value='cropStage' label='Crop Stages' />
						<Tab value='farms' label='Farms' />
					</TabList>
				</Stack>
				<Stack direction='row' className='filter-container'>
					<TextField
						name='search'
						label='Search'
						variant='outlined'
						className='search-textFiled'
						InputProps={{
							startAdornment: <GridSearchIcon />,
						}}
					/>
					{filterArray.map(({ id, label, children }) => (
						<FormControl key={id} className='form-controller'>
							<InputLabel id='manager'>{label}</InputLabel>
							<Select
								labelId='manager'
								label={label}
								value={filterData[id as keyof typeof filterData] || ''}
								onChange={(e) => {
									setFilterData((prev) => ({
										...prev,
										[id]: e.target.value as string,
									}))
								}}>
								{children.map(
									({ label, value }: { label: string; value: string }) => (
										<MenuItem key={value} value={value}>
											{label}
										</MenuItem>
									)
								)}
							</Select>
						</FormControl>
					))}
				</Stack>
				<Stack direction='row' className='map-data-container'>
					<Stack className='data-container'>
						{FarmerCropStatus.map(
							(
								{
									id,
									label,
									value,
								}: { id: string; label: string; value: number },
								index: number
							) => (
								<Stack direction='row' key={index}>
									<Stack
										className={`button ${
											croppingStage === id
												? 'primary-background'
												: 'grey-background'
										}`}
										onClick={() => handleCroppingState(id)}>
										<Typography
											variant='subtitle2'
											color={
												croppingStage === id
													? theme.palette.primary.main
													: theme.palette.neutral['500']
											}>
											{label}
										</Typography>
										<Typography
											variant='body1'
											color={
												croppingStage === id
													? theme.palette.neutral['500']
													: theme.palette.neutral['300']
											}>
											{value}
										</Typography>
									</Stack>
									{croppingStage === id && (
										<ArrowRightRounded color='primary' fontSize='large' />
									)}
								</Stack>
							)
						)}
					</Stack>
					<Stack className='map-container'>
						<Box className='map'>
							<MapComponent />
						</Box>
					</Stack>
				</Stack>
				<TabPanel value='cropStage'>
					<Stack className='data-farmer-container'>
						{/* <CustomDataGrid
							showPagination={true}
							rows={cropStageDetails ?? []}
							columns={FarmerColumns}
							rowCount={cropStageDetails?.length ?? 0}
							loading={isCropStageLoading}
							sx={{
								border: `1px solid ${theme.palette.neutral['100']}`,
							}}
						/> */}
						<CustomDataGrid
							showPagination={true}
							rows={SingleFarmerData}
							columns={FarmerColumns}
							rowCount={6}
							sx={{
								border: `1px solid ${theme.palette.neutral['100']}`,
							}}
						/>
					</Stack>
				</TabPanel>
				<TabPanel value='farms'>
					<Stack className='data-farmer-container'>
						{/* <CustomDataGrid
							showPagination={true}
							rows={farmDetails ?? []}
							columns={FarmerColumns}
							rowCount={farmDetails?.length ?? 0}
							loading={isFarmLoading}
							sx={{
								border: `1px solid ${theme.palette.neutral['100']}`,
							}}
						/> */}
						<CustomDataGrid
							showPagination={true}
							rows={SingleFarmerData}
							columns={FarmerColumns}
							rowCount={6}
							sx={{
								border: `1px solid ${theme.palette.neutral['100']}`,
							}}
						/>
					</Stack>
				</TabPanel>
			</TabContext>
		</StyledContainer>
	)
}

const StyledContainer = styled(Stack)(({ theme }) => ({
	'.light-grey': {
		color: theme.palette.neutral[300],
	},
	'.circle-icon': {
		fontSize: theme.spacing(1),
		color: theme.palette.neutral[100],
	},
	'.header': {
		flexDirection: 'row',
		alignItems: 'center',
		justifyContent: 'space-between',
		padding: theme.spacing(4, 2),
		borderBottom: `${theme.spacing(0.125)} solid ${
			theme.palette.neutral['100']
		}`,
		'.download-btn': {
			border: `${theme.spacing(0.125)} solid ${theme.palette.neutral['100']}`,
			borderRadius: theme.spacing(1.25),
		},
	},
	'.hero-section': {
		minHeight: theme.spacing(25.75),
		background: theme.palette.neutral['50'],
		padding: theme.spacing(2),
		'.details-container': {
			flexDirection: 'row',
			alignItems: 'flex-start',
			justifyContent: 'space-between',
			border: `${theme.spacing(0.125)} solid ${theme.palette.neutral['100']}`,
			borderRadius: theme.spacing(2),
			padding: theme.spacing(3, 2),
			background: theme.palette.common.white,
			gap: theme.spacing(2),
			'.farmer-details-container': {
				flexDirection: 'row',
				gap: theme.spacing(2),
				'.success-chip': {
					background: theme.palette.success.light,
					color: theme.palette.success.main,
					width: '80%',
				},
			},
			'.action-container': {
				flexDirection: 'row',
				gap: theme.spacing(2),
				'.edit-button': {
					color: theme.palette.neutral[300],
					borderColor: theme.palette.neutral[300],
					borderRadius: theme.spacing(1),
					textTransform: 'none',
					...theme.typography.subtitle2,
				},
			},
		},
	},
	'.tab-container': {
		background: theme.palette.neutral['50'],
		borderBottom: `${theme.spacing(0.125)} solid ${
			theme.palette.neutral['100']
		}`,
		padding: theme.spacing(0, 2),
	},
	'.filter-container': {
		padding: theme.spacing(4, 2),
		gap: theme.spacing(2),
		'.search-textFiled': {
			minWidth: theme.spacing(40.5),
			maxWidth: theme.spacing(20.5),
			width: '100%',
			'.MuiInputBase-root': {
				height: theme.spacing(4.5),
				borderRadius: theme.spacing(1.25),
			},
		},
		'.form-controller': {
			margin: theme.spacing(0.125),
			maxWidth: theme.spacing(20.5),
			width: '100%',
			'.MuiOutlinedInput-notchedOutline': {
				borderRadius: theme.spacing(1.25),
			},
		},
	},
	'.map-data-container': {
		padding: theme.spacing(2),
		'.data-container': {
			gap: theme.spacing(2),
			'.button': {
				width: theme.spacing(27.25),
				height: theme.spacing(8),
				justifyContent: 'center',
				padding: theme.spacing(2),
				border: `${theme.spacing(0.125)} solid ${theme.palette.neutral['100']}`,
				borderRadius: theme.spacing(2),
				cursor: 'pointer',
			},
			'.primary-background': {
				background: theme.palette.error.light,
			},
			'.grey-background': {
				background: theme.palette.neutral['50'],
			},
		},
		'.map-container': {
			width: '100%',
			borderRadius: theme.spacing(2),
			boxShadow: `0 0 ${theme.spacing(0.25)} 0 ${alpha(
				theme.palette.common.black,
				0.25
			)}`,
			alignItems: 'center',
			'.map': {
				minHeight: 600,
				maxheight: 800,
				minWidth: 600,
				maxWidth: 800,
				position: 'relative',
			},
		},
	},
	'.data-farmer-container': {
		padding: theme.spacing(2),
	},
}))
