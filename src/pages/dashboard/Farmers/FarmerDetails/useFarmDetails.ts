import { authAxios, useAuthContext } from '@/contexts'
import { IFarmerDetail } from '@/interfaces'
import { defaultLimit, defaultPage } from '@/utils/constant'
import { useQuery } from '@tanstack/react-query'
import { useCallback } from 'react'
import { useParams, useSearchParams } from 'react-router-dom'

export const useFarmerDetails = () => {
	const [searchParams, setSearchParams] = useSearchParams()
	const { userDetails } = useAuthContext()
	const { farmerId } = useParams()
	const paramsLimit = searchParams.get('limit') || defaultLimit
	const paramsPage = searchParams.get('page') || defaultPage
	const tab = searchParams.get('tab') || 'farms'
	const croppingStage = searchParams.get('croppingStage') || 'cropping'

	const getFamerData = useCallback(async () => {
		const { data } = await authAxios.get<IFarmerDetail>(`/farmers/${farmerId}`)
		return data
	}, [farmerId])

	const farmerData = useQuery({
		queryKey: ['farmerDetails', farmerId],
		queryFn: getFamerData,
		enabled: !!farmerId,
	})

	const getFarmDetails = useCallback(async () => {
		const { data } = await authAxios.get(
			`/farmers/${farmerId}/farm?cropStage=${croppingStage}&limit=${paramsLimit}&page=${paramsPage}`
		)
		return data
	}, [croppingStage, farmerId, paramsLimit, paramsPage])

	const farmDetails = useQuery({
		queryKey: [
			'farmDetails',
			userDetails?.biomassAggregatorId,
			paramsLimit,
			paramsPage,
			croppingStage,
		],
		queryFn: getFarmDetails,
		enabled: !!farmerId && tab === 'farms',
	})

	// const getCropStage = useCallback(async () => {
	// 	const { data } = await authAxios.get(
	// 		`/cs-network/${'csinknetworkId'}/farmers/${farmerId}/crop?cropStage=${croppingStage}&limit=${paramsLimit}&page=${paramsPage}`
	// 	)
	// 	return data
	// }, [croppingStage, farmerId, paramsLimit, paramsPage])

	// const cropStageDetails = useQuery({
	// 	queryKey: [
	// 		'farmDetails',
	// 		userDetails?.biomassAggregatorId,
	// 		paramsLimit,
	// 		paramsPage,
	// 		croppingStage,
	// 	],
	// 	queryFn: getCropStage,
	// 	enabled: !!farmerId && tab === 'cropStage',
	// })

	return {
		farmerData: farmerData.data,
		setSearchParams,
		farmDetails: farmDetails.data,
		isFarmLoading: farmDetails.isLoading,
		// cropStageDetails: cropStageDetails.data,
		// isCropStageLoading: cropStageDetails.isLoading,
		croppingStage,
		tab,
	}
}
