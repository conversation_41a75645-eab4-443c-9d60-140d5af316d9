import { Add, ArrowLeftRounded, Done, ToggleOn } from '@mui/icons-material'
import DownloadIcon from '@/assets/icons/download.svg'
import {
	Avatar,
	Box,
	Button,
	Chip,
	IconButton,
	Stack,
	Tab,
	Typography,
	styled,
	useTheme,
} from '@mui/material'
import { useNavigate } from 'react-router-dom'
import EditIcon from '@/assets/icons/editIcon.svg'
import { TabContext, TabList, TabPanel } from '@mui/lab'
import { SyntheticEvent, useMemo, useState } from 'react'
import { CustomDataGrid, StickyNavbar } from '@/components'
import {
	GridColDef,
	GridRenderCellParams,
	GridValidRowModel,
} from '@mui/x-data-grid'
import { FarmRowDetails } from '../../dummyData'
import { useScrollHeight } from '@/utils/helper'

export const FarmDetails = () => {
	const navigate = useNavigate()
	const theme = useTheme()
	const { scrolledHeight } = useScrollHeight()
	const [farmTabValue, setFarmTabValue] = useState<string>('crop-history')

	const handleChange = (_: SyntheticEvent | null, newValue: string) => {
		setFarmTabValue(newValue)
	}

	const CroppingStage = useMemo(
		() => ({
			cropping: {
				className: 'success-chip',
				icon: <Done color='success' fontSize='small' />,
			},
			harversting: {
				className: 'success-chip',
				icon: <Done color='success' fontSize='small' />,
			},
			sundrying: {
				className: 'success-chip',
				icon: <Done color='success' fontSize='small' />,
			},
			transport: {
				className: 'success-chip',
				icon: <Done color='success' fontSize='small' />,
			},
			production: {
				className: 'success-chip',
				icon: <Done color='success' fontSize='small' />,
			},
			distrubution: {
				className: 'success-chip',
				icon: <Done color='success' fontSize='small' />,
			},
			completed: {
				className: 'completed-chip',
				icon: (
					<ToggleOn
						fontSize='small'
						sx={{
							color: theme.palette.neutral['500'],
						}}
					/>
				),
			},
		}),
		[theme.palette]
	)

	const FarmColumns: GridColDef<GridValidRowModel>[] = useMemo(
		() => [
			{
				field: 'date',
				headerName: 'Date',
				minWidth: 160,
				flex: 1,
			},
			{
				field: 'cropStage',
				headerName: 'Crop Stage',
				minWidth: 120,
				flex: 1,
				renderCell: (params: GridRenderCellParams) => {
					const key: keyof typeof CroppingStage = params.row.cropStage

					return (
						<Chip
							label={key ?? ''}
							className={`chip ${CroppingStage[`${key}`]?.className}`}
							icon={CroppingStage[`${key}`]?.icon}
							size='small'
							variant='filled'
						/>
					)
				},
			},
			{
				field: 'crop',
				headerName: 'Crop',
				minWidth: 160,
				flex: 1,
			},

			{
				field: 'cSinkNetwork',
				headerName: 'C-sink Network',
				minWidth: 120,
				flex: 1,
			},
			{
				field: 'aggregator',
				headerName: 'Aggregator',
				minWidth: 120,
				flex: 1,
			},

			{
				field: 'farmer',
				headerName: 'Farmer',
				minWidth: 80,
				flex: 1,
			},
			{
				field: 'size',
				headerName: 'size',
				minWidth: 80,
				flex: 1,
			},
			{
				field: 'biomassQuantity',
				headerName: 'Biomass Quantity',
				minWidth: 80,
				flex: 1,
			},
			{
				field: 'address',
				headerName: 'Address',
				minWidth: 160,
				flex: 1,
			},
		],
		[CroppingStage, theme.typography.caption]
	)

	return (
		<StyledContainer>
			{scrolledHeight > 400 && (
				<StickyNavbar
					stickyComponent={
						<>
							<Stack direction='row' gap={4} alignItems='center'>
								<Stack direction='column'>
									<Typography variant='h5'>Network Name</Typography>
									<Stack direction='row' gap={2}>
										<Typography variant='subtitle1'>ID: VS09F002</Typography>
										<Typography variant='subtitle1'>Noida</Typography>
									</Stack>
								</Stack>
								<Stack>
									<Typography
										variant='subtitle2'
										color={theme.palette.neutral['300']}>
										Farmer
									</Typography>
									<Stack direction='row' alignItems='center' gap={2}>
										<Typography variant='subtitle1'>Farmer Name</Typography>
										<Avatar
											src={DownloadIcon}
											alt='farmer-image'
											sx={{
												height: 20,
												width: 20,
											}}
										/>
									</Stack>
								</Stack>
							</Stack>
							<Stack direction='row' spacing={2} alignItems='center'>
								<Button
									className='btn outlined'
									variant='outlined'
									startIcon={
										<Box
											component='img'
											src={EditIcon}
											alt='edit-icon'
											height={12}
											width={12}
										/>
									}>
									Edit Farm
								</Button>
								<Button
									className='btn'
									variant='contained'
									startIcon={<Add fontSize='small' />}>
									Add Crop
								</Button>
							</Stack>
						</>
					}
				/>
			)}
			<Stack className='header'>
				<Stack direction='row' alignItems='center' spacing={1}>
					<Button
						className='light-grey'
						variant='text'
						startIcon={<ArrowLeftRounded className='light-grey' />}
						onClick={() => navigate(-1)}>
						Farmer
					</Button>
					<Typography variant='body1' className='light-grey'>
						/ Farmer Name
					</Typography>
					<Typography variant='h6' color={theme.palette.neutral['500']}>
						/ Farm Name
					</Typography>
				</Stack>
				<IconButton
					className='download-btn'
					onClick={() => console.log('download')}>
					<Box component='img' src={DownloadIcon} height={20} width={20} />
				</IconButton>
			</Stack>
			<Stack className='farm-details-section'>
				<Stack className='details-container' gap={1}>
					<Box className='map' />
					<Stack className='details'>
						<Stack
							flexDirection='row'
							className='farmer-farm-details'
							gap={2}
							flexWrap='wrap'>
							<Stack spacing={1}>
								<Typography variant='h5'>Farm Name</Typography>
								<Stack direction='row' alignItems='center' gap={2}>
									<Typography variant='subtitle1'>Farm ID: VS09F002</Typography>
									<Chip
										icon={<Done fontSize='small' color='success' />}
										label='Verified'
										size='small'
										sx={{
											background: theme.palette.success.light,
											color: theme.palette.success.main,
										}}
									/>
								</Stack>
							</Stack>
							<Stack direction='row' spacing={2}>
								<Button
									className='btn outlined'
									variant='outlined'
									startIcon={
										<Box
											component='img'
											src={EditIcon}
											alt='edit-icon'
											height={12}
											width={12}
										/>
									}>
									Edit Farm
								</Button>
								<Button
									className='btn'
									variant='contained'
									startIcon={<Add fontSize='small' />}>
									Add Crop
								</Button>
							</Stack>
						</Stack>
						<Stack
							flexDirection='row'
							justifyContent='space-between'
							flexWrap='wrap'>
							<Stack spacing={4} className='network-details'>
								<Stack gap={1}>
									<Typography
										variant='subtitle2'
										color={theme.palette.neutral['300']}>
										Biomass Aggregator
									</Typography>
									<Typography variant='body1'>BA Name 1 (BA392kd43)</Typography>
								</Stack>
								<Stack gap={1}>
									<Typography
										variant='subtitle2'
										color={theme.palette.neutral['300']}>
										Farm Description
									</Typography>
									<Typography variant='body1'>Description</Typography>
								</Stack>
							</Stack>
							<Stack>
								<Stack spacing={4} className='network-details'>
									<Stack gap={1}>
										<Typography
											variant='subtitle2'
											color={theme.palette.neutral['300']}>
											Farmer
										</Typography>
										<Typography variant='body1'>Farmer Name</Typography>
									</Stack>
									<Stack gap={1}>
										<Typography
											variant='subtitle2'
											color={theme.palette.neutral['300']}>
											Farm Image
										</Typography>
										<Stack direction='row' spacing={4}>
											{new Array(4).fill(0).map((_, index: number) => (
												<Box
													key={index}
													component='img'
													src={DownloadIcon}
													alt='farm-image'
													height={72}
													width={72}
												/>
											))}
										</Stack>
									</Stack>
								</Stack>
							</Stack>
						</Stack>
					</Stack>
				</Stack>
			</Stack>
			<Stack className='tab-container'>
				<TabContext value={farmTabValue}>
					<Stack className='tabs'>
						<TabList onChange={handleChange}>
							<Tab label='Crop History' value='crop-history' />
						</TabList>
					</Stack>
					<TabPanel value='crop-history'>
						<CustomDataGrid
							showPagination={true}
							rows={FarmRowDetails}
							columns={FarmColumns}
							rowCount={4}
							sx={{
								border: `1px solid ${theme.palette.neutral['100']}`,
							}}
						/>
					</TabPanel>
				</TabContext>
			</Stack>
		</StyledContainer>
	)
}

const StyledContainer = styled(Stack)(({ theme }) => ({
	position: 'relative',
	width: '100%',
	'.chip': {
		...theme.typography.caption,
		textTransform: 'capitalize',
	},
	'.success-chip': {
		background: theme.palette.success.light,
		color: theme.palette.success.main,
	},
	'.warning-chip': {
		background: theme.palette.warning.light,
		color: theme.palette.warning.main,
	},
	'.error-chip': {
		background: theme.palette.error.light,
		color: theme.palette.error.main,
	},
	'.completed-chip': {
		background: theme.palette.neutral['20'],
		color: theme.palette.neutral['500'],
	},
	'.light-grey': {
		color: theme.palette.neutral[300],
	},
	'.btn': {
		height: theme.spacing(4.5),
		textTransform: 'none',
	},
	'.outlined': {
		borderColor: theme.palette.neutral['300'],
		color: theme.palette.neutral['300'],
	},
	'.header': {
		flexDirection: 'row',
		alignItems: 'center',
		justifyContent: 'space-between',
		padding: theme.spacing(4, 2),
		borderBottom: `${theme.spacing(0.125)} solid ${
			theme.palette.neutral['100']
		}`,
		'.download-btn': {
			border: `${theme.spacing(0.125)} solid ${theme.palette.neutral['100']}`,
			borderRadius: theme.spacing(1.25),
		},
	},
	'.farm-details-section': {
		background: theme.palette.neutral['50'],
		padding: theme.spacing(2, 3),
		'.details-container': {
			minHeight: theme.spacing(41.625),
			flexDirection: 'row',
			aliognItems: 'center',
			border: `${theme.spacing(0.125)} solid ${theme.palette.neutral['100']}`,
			borderRadius: theme.spacing(2),
			padding: theme.spacing(2),
			'.map': {
				height: theme.spacing(36.25),
				width: theme.spacing(47),
				background: theme.palette.neutral['100'],
				borderRadius: theme.spacing(2),
			},
			'.details': {
				width: '100%',
				'.farmer-farm-details': {
					width: '100%',
					justifyContent: 'space-between',
					borderBottom: `${theme.spacing(0.125)} solid ${
						theme.palette.neutral['100']
					}`,
					padding: theme.spacing(2),
				},
				'.network-details': {
					padding: theme.spacing(2),
				},
			},
		},
	},
	'.tab-container': {
		'.tabs': {
			padding: theme.spacing(0, 3),
			background: theme.palette.neutral['50'],
			borderBottom: `${theme.spacing(0.125)} solid ${
				theme.palette.neutral['100']
			}`,
		},
	},
}))
