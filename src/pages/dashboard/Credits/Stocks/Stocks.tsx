import { CustomDataGrid } from '@/components'
import { authAxios, publicAxios, useAuthContext } from '@/contexts'
import { IMixing, IProcessData, IStockDistribution, Site } from '@/interfaces'
import { theme } from '@/lib/theme/theme'
import { KilnsResponse } from '@/types'
import { dateFormats, defaultLimit, defaultPage, userRoles } from '@/utils/constant'
import {
	getFormattedDate,
	getGoogleMapLink,
	getSerialNumber,
	showAxiosErrorToast,
} from '@/utils/helper'
import { ArrowLeftRounded } from '@mui/icons-material'
import CloseIcon from '@mui/icons-material/Close'
import {
	alpha,
	Box,
	Button,
	Dialog,
	DialogContent,
	DialogTitle,
	Divider,
	Grid,
	IconButton,
	Menu,
	MenuItem,
	Paper,
	Stack,
	styled,
	Tooltip,
	Typography,
	TypographyProps,
} from '@mui/material'
import { GridColDef, GridEventListener,  GridValidRowModel } from '@mui/x-data-grid'
import { useQuery } from '@tanstack/react-query'
import { ReactNode, useCallback, useMemo, useState } from 'react'
import { useNavigate, useParams, useSearchParams } from 'react-router-dom'
import { BatchDetails } from '../../Production'
import { DocumentViewer } from '@/components/TrainingProofRenderer/DocumentViewer'
import { PdfPreviewDialog } from '@/components/TrainingProofRenderer/PdfPreviewDialog'
import { toast } from 'react-toastify'

const downloadFile = async (url: string, fileName: string) => {
	if (!url || !fileName) return
	try {
		const response = await publicAxios.get(url, { responseType: 'blob' })

		const blobUrl = URL.createObjectURL(response.data)
		const link = document.createElement('a')
		link.href = blobUrl
		link.download = fileName
		document.body.appendChild(link)
		link.click()
		document.body.removeChild(link)
		URL.revokeObjectURL(blobUrl)
	} catch (error) {
		toast('Error downloading file')
	}
}

export const Stocks = () => {
	const [anchorEl, setAnchorEl] = useState<null | HTMLElement>(null)
	const [searchParams] = useSearchParams()
	const { stockId } = useParams()
	const page = searchParams.get('page') || defaultPage
	const limit = searchParams.get('limit') || defaultLimit
	const navigate = useNavigate()
	const [showDialog, setShowDialog] = useState<string>('')
	const [showDocument, setShowDocument] = useState<boolean>(false)
	const { userDetails } = useAuthContext()

	const handleBack = () => {
		navigate(-1)
	}

	const handleRowClick:GridEventListener<'rowClick'> = useCallback((params) => {
		// Check if user is CERES Auditor and date is before 01-08-2024
		if (userDetails?.accountType === userRoles.ceres_auditor) {
			const rowDate = new Date(params?.row?.startDate)
			const cutoffDate = new Date('2024-08-01')
			
			if (rowDate < cutoffDate) {
				return // Prevent row click
			}
		}
		
		setShowDialog(params?.row?.id)
	}, [userDetails?.accountType])

	// Private Routes

	// const getBatchListQuery = useQuery({
	// 	queryKey: ['batchesQuery', page, limit, stockId],
	// 	queryFn: async () => {
	// 		try {
	// 			const { data } = await authAxios<IProcessData>(
	// 				`new/stocks/${stockId}/processes?limit=${limit}&page=${page}`
	// 			)
	// 			return data
	// 		} catch (error: any) {
	// 			toast(error?.response?.data?.messageToUser)
	// 			return null
	// 		}
	// 	},
	// })

	// const getMixingQuery = useQuery({
	// 	queryKey: ['getMixingQuery', stockId],
	// 	queryFn: async () => {
	// 		try {
	// 			const { data } = await authAxios<IMixing[]>(
	// 				`new/stocks/${stockId}/mixing?`
	// 			)
	// 			return data
	// 		} catch (error: any) {
	// 			toast(error?.response?.data?.messageToUser)
	// 			return null
	// 		}
	// 	},
	// })

	// const getDistributionQuery = useQuery({
	// 	queryKey: ['getDistributionQuery', stockId],
	// 	queryFn: async () => {
	// 		try {
	// 			const { data } = await authAxios<IStockDistribution[]>(
	// 				`new/stocks/${stockId}/distribution`
	// 			)
	// 			return data
	// 		} catch (error: any) {
	// 			toast(error?.response?.data?.messageToUser)
	// 			return null
	// 		}
	// 	},
	// })

	// public Routes

	const getBatchListQuery = useQuery({
		queryKey: ['BatchesQuery', page, limit, stockId],
		queryFn: async () => {
			try {
				const endPoint = `new/stocks/${stockId}/processes?limit=${limit}&page=${page}`
				const { data } = await authAxios.get<IProcessData>(endPoint)
				return data
			} catch (error: any) {
				showAxiosErrorToast(error)
				return null
			}
		},
	})

	const getMixingQuery = useQuery({
		queryKey: ['getMixingQuery', stockId],
		queryFn: async () => {
			try {
				const endPoint = `new/stocks/${stockId}/mixing`
				const { data } = await authAxios.get<IMixing[]>(endPoint)
				return data
			} catch (error: any) {
				showAxiosErrorToast(error)
				return null
			}
		},
	})

	const getDistributionQuery = useQuery({
		queryKey: ['getDistributionQuery', stockId],
		queryFn: async () => {
			try {
				const endPoint = `new/stocks/${stockId}/distribution`
				const { data } = await authAxios.get<IStockDistribution[]>(endPoint)
				return data
			} catch (error: any) {
				showAxiosErrorToast(error)
				return null
			}
		},
	})

	const getProductionQuery = useQuery({
		queryKey: ['getProductionQuery', stockId],
		queryFn: async () => {
			try {
				const endPoint = `new/stocks/${stockId}/production`

				const { data } = await authAxios.get<KilnsResponse>(endPoint)
				return data
			} catch (error: any) {
				showAxiosErrorToast(error)
				return null
			}
		},
	})

	const projectDetails = useMemo(
		() => [
			{
				label: 'Stock ID:',
				value: stockId,
			},
			{
				label: 'Project Name:',
				value: `${getBatchListQuery?.data?.projectDetail?.ProjectId} ${
					getBatchListQuery?.data?.projectDetail?.registryProjectName
						? `(${getBatchListQuery?.data?.projectDetail?.registryProjectName})`
						: ''
				}`,
			},
			{
				label: 'Total C-Sink(tCO2e)',
				value:
					getBatchListQuery?.data?.projectDetail?.totalCarbonCreditsInTonnes,
			},
			{
				label: 'Total SPC Fraction (tCO2e)',
				value: getBatchListQuery?.data?.projectDetail?.totalSPCFraction,
			},
		],
		[
			stockId,
			getBatchListQuery?.data?.projectDetail?.ProjectId,
			getBatchListQuery?.data?.projectDetail?.registryProjectName,
			getBatchListQuery?.data?.projectDetail?.totalCarbonCreditsInTonnes,
			getBatchListQuery?.data?.projectDetail?.totalSPCFraction,
		]
	)

	const networkDetails = useMemo(() => {
		const networsLength =
			getBatchListQuery?.data?.projectDetail?.networks?.length

		return [
			{
				key: 0,
				label: 'Network Names',
				value: (
					<Stack>
						<Typography
							variant='body2'
							sx={{
								fontWeight: theme.typography.body1.fontWeight,
							}}>
							{getBatchListQuery?.data?.projectDetail?.networks[0]?.name}
						</Typography>
						{(networsLength ?? 0) - 1 > 0 ? (
							<Tooltip
								title={getBatchListQuery?.data?.projectDetail?.networks?.map(
									(item, index) =>
										index > 1 && <Typography>{item.name}</Typography>
								)}>
								<Typography
									variant='body1'
									sx={{
										color: theme.palette.grey[600],
									}}>{`+${(networsLength ?? 0) - 1} more networks`}</Typography>
							</Tooltip>
						) : null}
					</Stack>
				),
			},
			{
				key: 1,
				label: 'C-Sink Manager',
				value: getBatchListQuery?.data?.projectDetail?.csinkManagerName,
			},
			{
				key: 2,
				label: (
					<Stack direction={'row'} gap={theme.spacing(1)} alignItems={'center'}>
						<Typography variant='caption' color={theme.palette.neutral[300]}>
							Feedstock Name
						</Typography>
						<Typography
							variant='caption'
							className='viewDocumentButton'
							onClick={() => setShowDocument(true)}>
							( View Document )
						</Typography>
					</Stack>
				),
				value: getBatchListQuery?.data?.projectDetail?.biomassName,
			},
		]
	}, [
		getBatchListQuery?.data?.projectDetail?.networks,
		getBatchListQuery?.data?.projectDetail?.csinkManagerName,
		getBatchListQuery?.data?.projectDetail?.biomassName,
	])

	const columns: GridColDef<GridValidRowModel>[] = useMemo(
		() => [
			{
				field: 'id',
				headerName: 'S.No',
				flex: 0.5,
				minWidth: 80,
				renderCell: (params) => (
					<Typography variant='subtitle1'>
						{getSerialNumber(params, Number(limit))}
					</Typography>
				),
			},
			{
				field: 'startDate',
				headerName: 'Date',
				flex: 1,
				minWidth: 150,
				renderCell: (params) => (
					<Typography variant='subtitle1'>
						{getFormattedDate(params?.value, dateFormats.yyyy_MM_dd)}
					</Typography>
				),
			},

			{
				field: 'biocharQuantity',
				headerName: 'Biochar Qty',
				minWidth: 100,
				flex: 1,
				renderCell: (params) => (
					<Typography variant='subtitle1'>
						{params?.value ?? 0} litres
					</Typography>
				),
			},
			{
				field: 'siteName',
				headerName: 'Site Name',
				minWidth: 100,
				flex: 1,
				renderCell: (params) => (
					<Typography variant='subtitle1'>
						{params?.row?.isSiteProcess
							? params?.row?.siteName
							: params?.row?.kilnName}
					</Typography>
				),
			},

			{
				field: 'cropName',
				headerName: 'Biomass Type',
				minWidth: 100,
				flex: 1,
				renderCell: (params) => (
					<Typography variant='subtitle1'>{params?.value}</Typography>
				),
			},

			{
				field: 'carbonCredits',
				headerName: 'Carbon credits',
				minWidth: 150,
				flex: 1,
				renderCell: (params) => (
					<Typography variant='subtitle1'>
						{params?.value ? `${params?.value} tCO2` : '-'}
					</Typography>
				),
			},
		],
		[limit]
	)

	const handleDownloadMonitoringReport = () => {
		const file = getBatchListQuery.data?.monitoringReport
		if (file?.url && file?.path) {
			const filename = decodeURIComponent(file.path.replace(/[{}]/g, '')) // Clean path like "{123.pdf}"
			downloadFile(file.url, filename)
		} else {
			toast('Monitoring report not available')
		}
	}

	const handleDownloadAnnexReports = () => {
		const annexReports = getBatchListQuery.data?.annexReport ?? []
		if (!annexReports.length) {
			toast('No annex reports available')
			return
		}
		annexReports.forEach((file) => {
			if (file?.url && file?.path) {
				downloadFile(file.url, file.path)
			} else {
				toast('Annex report not available')
			}
		})
	}

	return (
		<>
			<StyledContainer>
				<Stack className='header-navigation'>
					<Stack className='header-navigation-start'>
						<Button
							onClick={handleBack}
							className='batch-button'
							variant='text'
							startIcon={<ArrowLeftRounded fontSize='small' />}>
							Stock
						</Button>
						<Typography variant='body1' color={theme.palette.neutral['500']}>
							&nbsp;/ {stockId}{' '}
						</Typography>
					</Stack>
					<Stack paddingX={4}>
						<Typography
							color={theme.palette.primary.main}
							onClick={(event) => setAnchorEl(event.currentTarget)}
							variant='body1'
							sx={{ cursor: 'pointer' }}>
							Download Files
						</Typography>
						<Menu
							anchorEl={anchorEl}
							open={!!anchorEl}
							onClose={() => setAnchorEl(null)}>
							<StyledMenuItem onClick={handleDownloadAnnexReports}>
								Annex Report
							</StyledMenuItem>
							<StyledMenuItem onClick={handleDownloadMonitoringReport}>
								Monitoring Report
							</StyledMenuItem>
						</Menu>
					</Stack>
				</Stack>
				<Stack className='container' sx={{ margin: '0px' }}>
					<Box className='project-details'>
						{projectDetails.map((detail) => (
							<Stack key={detail.label} className='info-container'>
								<Typography className='label'>{detail.label}</Typography>
								<Typography className='value'>{detail.value || '-'}</Typography>
							</Stack>
						))}
					</Box>
					{/* Network details */}
					<Box className='project-network-details'>
						<Stack
							className='network-details-header'
							sx={{
								width: '100%',
								display: 'flex',
								flexDirection: 'row',
								justifyContent: 'space-between',
							}}>
							{networkDetails.map((detail) => (
								<Stack key={detail.key} className='info-container'>
									{typeof detail.label === 'string' ? (
										<Typography className='label'>{detail.label}</Typography>
									) : (
										detail.label
									)}
									{typeof detail.value === 'string' ? (
										<Typography className='value'>
											{detail.value || '-'}
										</Typography>
									) : (
										detail.value
									)}
								</Stack>
							))}
						</Stack>
						<Divider
							sx={{
								borderTop: '1px solid black',
								borderColor: theme.palette.grey[200],
							}}
						/>
						<HorizontalScrollStack
							sites={getBatchListQuery?.data?.projectDetail?.sites}
						/>
					</Box>
					<Grid container spacing={2.5}>
						{/* Biochar Production */}
						<Grid item xs={12} md={4}>
							<Typography
								variant='body2'
								pb={theme.spacing(1)}
								pl={theme.spacing(1)}>
								Biochar Production
							</Typography>
							<Stack gap={theme.spacing(3)}>
								{getProductionQuery?.data?.kilns?.map((kiln) => (
									<Box className='box-container' key={kiln?.id}>
										<Stack
											p={theme.spacing(1)}
											width={'100%'}
											gap={theme.spacing(0.5)}>
											<Typography
												variant='h6'
												alignSelf={'flex-start'}
												sx={{ textDecoration: 'none', fontStyle: 'normal' }}>
												{kiln?.name}
											</Typography>
											<Typography>
												<Button
													component='a'
													href={getGoogleMapLink(
														String(kiln?.coordinate?.x ?? '0'),
														String(kiln?.coordinate?.y ?? '0')
													)}
													target='_blank'
													variant='text'
													sx={{
														p: 0,
														width: 'fit-content',
														color: 'black',
													}}>
													<Typography
														variant='subtitle1'
														sx={{
															textDecoration: 'underline',
														}}>
														{kiln?.address}
													</Typography>
												</Button>
											</Typography>
										</Stack>
										<Stack className='box-container-bottom' width={'100%'}>
											<CustomTagComponent
												label='Quantity (Litres)'
												value={`${kiln?.biocharQuantity ?? 0} liters`}
											/>
											<CustomTagComponent
												label='Quantity (ton)'
												value={kiln?.biocharQuantityInTonnes}
											/>
										</Stack>
									</Box>
								))}
							</Stack>
						</Grid>

						{/* Mixing */}
						<Grid item xs={12} md={4}>
							<Typography
								variant='body2'
								pb={theme.spacing(1)}
								pl={theme.spacing(1)}>
								Mixing/Packaging :
							</Typography>
							<Stack gap={theme.spacing(3)}>
								{getMixingQuery?.data?.map((mixing) => (
									<Box className='box-container-2' key={mixing?.id}>
										<Stack direction='column' width='100%'>
											<CustomTagComponent
												label='Matrix ID'
												value={`${mixing?.matrixId} (${mixing?.name})`}
											/>
											<CustomTagComponent
												label='Quantity (Litres)'
												value={`${mixing?.biocharQuantity ?? 0} liters`}
											/>
										</Stack>

										<CustomTagComponent
											label='Locations:'
											value={
												<Stack direction='column'>
													{mixing?.locations?.map((x, index) => (
														<Typography
															variant='body1'
															key={index}>{`${x.x}, ${x.y}`}</Typography>
													))}
												</Stack>
											}
										/>
									</Box>
								))}
							</Stack>
						</Grid>

						{/* Distribution */}
						<Grid item xs={12} md={4}>
							<Typography
								variant='body2'
								pb={theme.spacing(1)}
								pl={theme.spacing(1)}>
								Application:
							</Typography>
							<Stack gap={theme.spacing(3)}>
								{getDistributionQuery?.data?.map((dist) => (
									<Box className='box-container-2' key={dist?.id}>
										<Stack direction='column' width='100%'>
											<CustomTagComponent label='Name: ' value={dist?.name} />
											<CustomTagComponent
												label='Quantity (Litres)'
												value={`${dist?.biocharQuantity ?? 0} liters`}
											/>
										</Stack>

										<CustomTagComponent
											label='Locations:'
											value={`${dist?.location?.x}, ${dist?.location?.y}`}
										/>
									</Box>
								))}
							</Stack>
						</Grid>
					</Grid>
				</Stack>

				<Stack className='container'>
					<CustomDataGrid
						onRowClick={handleRowClick}
						showPagination
						columns={columns}
						headerComponent={<Typography variant='body2'>Batches:</Typography>}
						rows={getBatchListQuery?.data?.processes ?? []}
						rowCount={getBatchListQuery?.data?.count ?? 0}
						loading={getBatchListQuery?.isLoading}
					/>
				</Stack>
			</StyledContainer>
			{getBatchListQuery?.data?.projectDetail?.labReportDocuments?.length ===
			1 ? (
				<PdfPreviewDialog
					open={showDocument}
					close={() => setShowDocument(false)}
					pdfUrl={
						getBatchListQuery?.data?.projectDetail?.labReportDocuments[0]?.url
					}
					showDownloadButton={false}
				/>
			) : (
				<DocumentViewer
					open={showDocument}
					close={() => setShowDocument(false)}
					documents={(
						getBatchListQuery?.data?.projectDetail?.labReportDocuments ?? []
					)?.map((i) => ({
						...i,
						fileName: '.pdf',
					}))}
					showDownloadButton={false}
				/>
			)}

			<Dialog open={!!showDialog} onClose={() => setShowDialog('')} fullScreen>
				<DialogTitle
					sx={{
						py: theme.spacing(5),
						display: 'flex',
						justifyContent: 'space-between',
					}}>
					<Typography variant='h6'>Batch details</Typography>
					<IconButton aria-label='close' onClick={() => setShowDialog('')}>
						<CloseIcon />
					</IconButton>
				</DialogTitle>

				<DialogContent>
					<BatchDetails id={showDialog} showHeader={false} />
				</DialogContent>
			</Dialog>
		</>
	)
}

const StyledContainer = styled(Stack)(({ theme }) => ({
	gap: theme.spacing(2),
	'.header-navigation': {
		padding: theme.spacing(4, 1, 2),
		borderBottom: `${theme.spacing(0.125)} solid ${
			theme.palette.neutral['100']
		}`,
		alignItems: 'center',
		flexDirection: 'row',
		justifyContent: 'space-between',
		'.batch-button': {
			color: theme.palette.neutral['500'],
			...theme.typography.body1,
			'.arrow-icon': {
				color: theme.palette.neutral['500'],
			},
		},
		'.header-navigation-start': {
			alignItems: 'center',
			flexDirection: 'row',
		},
		'.header-navigation-end': {
			alignItems: 'center',
			flexDirection: 'row',
		},
	},
	'.container': {
		padding: theme.spacing(1, 2.5),
		gap: theme.spacing(3),

		'.box-container': {
			display: 'flex',
			flexDirection: 'column',
			alignItems: 'center',
			borderRadius: theme.spacing(2),
			boxShadow: `0 0 ${theme.spacing(0.5)} 0 ${alpha(
				theme.palette.common.black,
				0.4
			)}`,
			padding: theme.spacing(1, 2),
			'.box-container-bottom': {
				Padding: '0',
				display: 'flex',
				justifyContent: 'space-between',
				flexDirection: 'row',
			},
		},
		'.box-container-2': {
			display: 'flex',
			flexDirection: 'row',
			borderRadius: theme.spacing(2),
			boxShadow: `0 0 ${theme.spacing(0.5)} 0 ${alpha(
				theme.palette.common.black,
				0.4
			)}`,
			padding: theme.spacing(1, 2),
		},
		'.project-details': {
			display: 'grid',
			gridTemplateColumns: 'repeat(auto-fill, minmax(320px, 1fr))',
			rowGap: theme.spacing(2),
			paddingBottom: theme.spacing(4.375),
			'.info-container': {
				gap: theme.spacing(1),
				'.label': {
					fontSize: theme.typography.caption.fontSize,
					color: theme.palette.neutral[300],
				},
				'.value': {
					...theme.typography.body2,
					fontWeight: theme.typography.body1.fontWeight,
				},
				'.btn': {
					width: 'fit-content',
					...theme.typography.body2,
				},
			},
		},
		'.project-network-details': {
			display: 'flex',
			flexDirection: 'column',
			borderRadius: theme.spacing(2),
			border: `${theme.spacing(0.05)} solid ${theme.palette.primary.main}`,
			backgroundColor: '#FBF7F6',
			gap: theme.spacing(2),
			padding: theme.spacing(1, 2),
			'.network-details-header': {
				display: 'grid',
				padding: theme.spacing(0.5),
				gridTemplateColumns: 'repeat(auto-fill, minmax(320px, 1fr))',

				'.info-container': {
					gap: theme.spacing(1),
					'.label': {
						fontSize: theme.typography.caption.fontSize,
						color: theme.palette.neutral[300],
					},
					'.value': {
						...theme.typography.body2,
						fontWeight: theme.typography.body1.fontWeight,
					},
					'.btn': {
						width: 'fit-content',
						...theme.typography.body2,
					},
				},
			},
		},
		'.viewDocumentButton': {
			color: theme.palette.grey[600],
			cursor: 'pointer',
		},
	},
}))

const StyledMenuItem = styled(MenuItem)(({ theme }) => ({
	display: 'flex',
	alignItems: 'center',
	justifyContent: 'center',
	width: theme.spacing(20),
}))
interface ITagComponent {
	label: string
	value: string | ReactNode
	headingStyle?: TypographyProps
}

function CustomTagComponent(props: ITagComponent) {
	const { label, value, headingStyle } = props

	return (
		<StyledTag>
			<Typography className='title' {...headingStyle}>
				{label}
			</Typography>
			{typeof value === 'string' ? (
				<Typography variant='body1' sx={{ font: 'bold' }}>
					{value}
				</Typography>
			) : (
				value
			)}
		</StyledTag>
	)
}
const StyledTag = styled(Stack)(({ theme }) => ({
	gap: theme.spacing(0.5),
	width: '100%',
	flexDirection: 'column',
	padding: theme.spacing(1),
	display: 'flex',
	'.title': {
		fontSize: theme.spacing(1.75),
		fontStyle: 'normal',
		color: theme.palette.neutral[300],
	},
}))

function HorizontalScrollStack({ sites }: { sites: Site[] | undefined }) {
	return (
		<Box sx={{ overflowX: 'auto' }}>
			<Typography
				variant='body2'
				sx={{
					paddingBottom: theme.spacing(1.5),
					color: theme.palette.grey[600],
				}}>
				Site Information
			</Typography>
			<Stack
				direction='row'
				spacing={2}
				sx={{ paddingBottom: theme.spacing(2) }}>
				{sites?.map((item) => (
					<Paper
						key={item.id}
						sx={{
							borderRadius: theme.spacing(1),
							padding: theme.spacing(1.5),
							minWidth: 250,
						}}>
						<Typography variant='body2'>{item.name}</Typography>
						<Typography
							variant='subtitle1'
							sx={{
								color: theme.palette.neutral[300],
							}}>
							{item?.address}
						</Typography>
					</Paper>
				))}
			</Stack>
		</Box>
	)
}
