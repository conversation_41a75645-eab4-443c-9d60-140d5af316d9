import { CustomCard } from "@/components"
import { IMixing, IStockDistribution } from "@/interfaces"
import { Kiln, KilnsResponse } from "@/types"
import { Factory, Grass, Science } from "@mui/icons-material"
import { Box, Divider, Stack, Typography, useTheme } from "@mui/material"
import { UseQueryResult } from "@tanstack/react-query"
import { useMemo } from "react"
import { useSearchParams } from "react-router-dom"
import { DetailedCard } from "./index"

enum ProcessStepEnum {
    Production = 'Production Details',
    Mixing = 'Mixing Details',
    Application = 'Application Details'
}

interface IProcessJourneyProps {
    getProductionQuery: UseQueryResult<KilnsResponse | null, Error>
    getMixingQuery: UseQueryResult<IMixing[] | null, Error>
    getDistributionQuery: UseQueryResult<IStockDistribution[] | null, Error>
}

export const ProcessJourney = ({
    getProductionQuery,
    getMixingQuery,
    getDistributionQuery
}: IProcessJourneyProps) => {
    const theme = useTheme()
    const [searchParams, setSearchParams] = useSearchParams()

    const selectedProcessStep = useMemo(() => {
        const processStep = searchParams.get('processStep') || '1'
        switch (processStep) {
            case '1':
                return ProcessStepEnum.Production
            case '2':
                return ProcessStepEnum.Mixing
            case '3':
                return ProcessStepEnum.Application
            default:
                return 'Production'
        }
    }, [searchParams])

    const processJourneySteps = useMemo(() => [
        {
            id: '1',
            icon: <Factory fontSize='small' />,
            name: 'Production',
            caption: `${getProductionQuery?.data?.kilns?.length ?? 0} sites`,
            className: 'production',
            show: (getProductionQuery?.data?.kilns?.length ?? 0) > 0,
        },
        {
            id: '2',
            icon: <Science fontSize='small' />,
            name: 'Mixing & Packaging',
            caption: `${getMixingQuery?.data?.length ?? 0} sites`,
            className: 'mixing',
            show: (getMixingQuery?.data?.length ?? 0) > 0,
        },
        {
            id: '3',
            icon: <Grass fontSize='small' />,
            name: 'Application',
            caption: `${getDistributionQuery?.data?.length ?? 0} recipients`,
            className: 'application',
            show: (getDistributionQuery?.data?.length ?? 0) > 0,
        },
    ], [
        getProductionQuery?.data?.kilns?.length,
        getMixingQuery?.data?.length,
        getDistributionQuery?.data?.length
    ])

    const isProduction = useMemo(() =>
        selectedProcessStep === ProcessStepEnum.Production,
        [selectedProcessStep])

    const isMixing = useMemo(() =>
        selectedProcessStep === ProcessStepEnum.Mixing,
        [selectedProcessStep])

    const isApplication = useMemo(() =>
        selectedProcessStep === ProcessStepEnum.Application,
        [selectedProcessStep])

    return (
        <Stack className='process-Journey'>
            <Typography variant='h5'>Process Journey</Typography>
            <Stack direction='row' gap={2} flexWrap='wrap'>
                <Stack flex={1} gap={2} mt={2}>
                    {processJourneySteps.filter(i => i.show).map((item) => {
                        const isSelected = searchParams.get('processStep') === item.id
                        return (
                            <CustomCard
                                key={item.id}
                                onClick={() => {
                                    setSearchParams((prev) => {
                                        const updated = new URLSearchParams(prev)
                                        updated.set('processStep', item.id)
                                        return updated
                                    })
                                }}
                                className={`process-journey-card ${item.className} ${isSelected ? 'selected' : ''}`}
                                headerComponent={
                                    <Stack
                                        direction='row'
                                        alignItems='center'
                                        gap={theme.spacing(1)}
                                    >
                                        <Box className='process-journey-icon'>
                                            {item.icon}
                                        </Box>
                                        <Stack direction='column' gap={theme.spacing(0.5)}>
                                            <Typography
                                                variant='subtitle2'
                                            >
                                                {item.name}
                                            </Typography>
                                            <Typography variant='caption' color='text.secondary'>
                                                {item.caption}
                                            </Typography>
                                        </Stack>
                                    </Stack>
                                }
                            />
                        )
                    })}
                </Stack>

                <Divider orientation='vertical' flexItem />
                <Stack flex={1}>
                    <Typography mb={2}>{selectedProcessStep}</Typography>
                    <Stack
                        direction='row'
                        flexWrap='wrap'
                        gap={theme.spacing(2)}
                        sx={{
                            overflowY: 'auto',
                            maxHeight: '425px',
                            pr: 2,
                        }}
                    >
                        {isProduction && getProductionQuery?.data?.kilns?.map((item: Kiln) =>
                            <DetailedCard
                                key={item.id}
                                item={item}
                                type={ProcessStepEnum.Production}
                            />
                        )}
                        {isApplication && getDistributionQuery?.data?.map((item: IStockDistribution) =>
                            <DetailedCard
                                key={item.id}
                                item={item}
                                type={ProcessStepEnum.Application}
                            />
                        )}
                        {isMixing && getMixingQuery?.data?.map((item: IMixing) =>
                            <DetailedCard
                                key={item.id}
                                item={item}
                                type={ProcessStepEnum.Mixing}
                            />
                        )}
                    </Stack>
                </Stack>
            </Stack>
        </Stack>
    )
}
