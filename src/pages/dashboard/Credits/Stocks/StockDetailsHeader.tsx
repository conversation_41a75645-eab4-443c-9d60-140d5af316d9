import { ILabReportDocument, IProcessData } from '@/interfaces'
import { FileOpen } from '@mui/icons-material'
import { But<PERSON>, Stack, Tooltip, Typography } from '@mui/material'
import { UseQueryResult } from '@tanstack/react-query'
import { useMemo } from 'react'

enum NetworkType {
	ArtisanPro = 'Artisan Pro',
	CSinkNetwork = 'Csink Network',
}

interface IStockDetailsHeaderProps {
	getBatchListQuery: UseQueryResult<IProcessData | null, Error>
	setShowPdfPreview: React.Dispatch<
		React.SetStateAction<ILabReportDocument[] | null>
	>
}

function getCommaSeparatedNames(data: { name: string }[]): string {
	return data.map(item => item.name).join(', ');
}

export const StockDetailsHeader = ({
	getBatchListQuery,
	setShowPdfPreview,
}: IStockDetailsHeaderProps) => {
	const networkName = useMemo(() => (
		getCommaSeparatedNames(getBatchListQuery?.data?.projectDetail?.networks || [])
	), [getBatchListQuery?.data?.projectDetail?.networks])

	const stocksDetails = useMemo(
		() => [
			{
				label: 'Csink Manager',
				value: getBatchListQuery?.data?.projectDetail?.csinkManagerName || '-',
			},
			{
				label: 'Project ID',
				value: getBatchListQuery?.data?.projectDetail?.ProjectId || '-',
			},
			{
				label: getBatchListQuery?.data?.projectDetail?.networks[0]?.isArtisan
					? NetworkType.ArtisanPro
					: NetworkType.CSinkNetwork,
				value: networkName,
			},
			{
				label: 'Feedstock ',
				value: (
					<Stack direction='row' gap={1} alignItems='center'>
						{getBatchListQuery?.data?.projectDetail?.biomassName || '-'}
						<Button
							size='small'
							variant='text'
							startIcon={<FileOpen />}
							onClick={() => {
								setShowPdfPreview(
									getBatchListQuery?.data?.projectDetail?.labReportDocuments ||
									null
								)
							}}
							sx={{ padding: 0 }}>
							View
						</Button>
					</Stack>
				),
			},
			{
				label: 'Biochar Qty | Csink Qty',
				value:
					getBatchListQuery?.data?.projectDetail?.biocharQuantity &&
						getBatchListQuery?.data?.projectDetail?.totalCarbonCreditsInTonnes
						? `${getBatchListQuery?.data?.projectDetail?.biocharQuantity} tCO2 | ${getBatchListQuery?.data?.projectDetail?.totalCarbonCreditsInTonnes} tCO2e`
						: '-',
			},
		],
		[
			getBatchListQuery?.data?.projectDetail?.ProjectId, 
			getBatchListQuery?.data?.projectDetail?.biocharQuantity, 
			getBatchListQuery?.data?.projectDetail?.biomassName, 
			getBatchListQuery?.data?.projectDetail?.csinkManagerName, 
			getBatchListQuery?.data?.projectDetail?.labReportDocuments, 
			getBatchListQuery?.data?.projectDetail?.networks, 
			getBatchListQuery?.data?.projectDetail?.totalCarbonCreditsInTonnes, 
			networkName, 
			setShowPdfPreview
		]
	)

	return (
		<Stack className='project-details'>
			{stocksDetails.map((detail) => (
				<Stack key={detail.label} className='info-container'>
					<Typography className='label'>{detail.label} </Typography>
					<Tooltip
						title={
							typeof detail.value === 'string'
								? detail.value
								: getBatchListQuery?.data?.projectDetail?.biomassName ?? '-'
						}
						arrow>
						{typeof detail.value === 'string' ? (
							<Typography className='value'>
								{detail?.value?.substring(0, 30) || '-'}
								{detail.value.length > 30 ? '...' : ''}
							</Typography>
						) : (
							detail.value
						)}
					</Tooltip>
				</Stack>
			))}
		</Stack>
	)
}
