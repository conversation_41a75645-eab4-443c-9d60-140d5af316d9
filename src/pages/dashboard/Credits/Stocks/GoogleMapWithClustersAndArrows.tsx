import { useEffect, useMemo, useRef } from 'react'
import { IStockLocation, Location } from '@/interfaces'
import { ILocationDetails } from '@/interfaces/stocks'

import { Box, CircularProgress, styled, useTheme } from '@mui/material'
import {
	GoogleMap,
	useLoadScript,
	LoadScriptProps,
	MarkerF,
	CircleF,
	PolylineF,
} from '@react-google-maps/api'

const libraries: LoadScriptProps['libraries'] = ['drawing', 'maps']
const { VITE_GOOGLE_MAPS_API_KEY } = import.meta.env

enum GroupName {
	Production = 'production',
	Mixing = 'mixing',
	Application = 'application',
}

type GroupedLocation = {
	id: string
	name: string
	biocharQuantity: number
	location: Location
	group: GroupName
}

type GroupedData = {
	production: GroupedLocation[]
	mixing: GroupedLocation[]
	application: GroupedLocation[]
}

const convertGroupedLocation = (data: IStockLocation): GroupedData => {
	const result: GroupedData = {
		production: [],
		mixing: [],
		application: [],
	}

	data?.production?.forEach((item: ILocationDetails) => {
		if (item.siteLocation) {
			result.production.push({
				id: item.id,
				name: item.name,
				biocharQuantity: item.biocharQuantity,
				location: item.siteLocation,
				group: GroupName.Production,
			})
		}
	})

	data?.mixingPackaging?.forEach((item: ILocationDetails) => {
		if (item.siteLocation) {
			result.mixing.push({
				id: item.id,
				name: item.name,
				biocharQuantity: item.biocharQuantity,
				location: item.siteLocation,
				group: GroupName.Mixing,
			})
		}
	})

	data?.application?.forEach((item: ILocationDetails) => {
		if (item.location) {
			result.application.push({
				id: item.id,
				name: item.name,
				biocharQuantity: item.biocharQuantity,
				location: item.location,
				group: GroupName.Application,
			})
		}
	})

	return result
}

const haversineDistance = (coord1: Location, coord2: Location): number => {
	const toRadians = (degree: number) => (degree * Math.PI) / 180
	const earthRadiusInMeters = 6371e3

	const lat1 = toRadians(coord1.x)
	const lat2 = toRadians(coord2.x)
	const deltaLat = toRadians(coord2.x - coord1.x)
	const deltaLng = toRadians(coord2.y - coord1.y)

	const a =
		Math.sin(deltaLat / 2) ** 2 +
		Math.cos(lat1) * Math.cos(lat2) * Math.sin(deltaLng / 2) ** 2

	const angularDistance = 2 * Math.atan2(Math.sqrt(a), Math.sqrt(1 - a))

	return earthRadiusInMeters * angularDistance
}

const getGroupCenter = (
	group: GroupedLocation[]
): { lat: number; lng: number } => {
	const total = group.length
	if (total === 0) {
		return { lat: 0, lng: 0 }
	}
	const sum = group.reduce(
		(acc, item) => ({
			x: acc.x + item.location.x,
			y: acc.y + item.location.y,
		}),
		{ x: 0, y: 0 }
	)
	return {
		lat: sum.x / total,
		lng: sum.y / total,
	}
}

const getGroupRadius = (group: GroupedLocation[]): number => {
	if (group.length === 0) return 0
	const center = getGroupCenter(group)
	const maxDistance = Math.max(
		...group.map((item) =>
			haversineDistance(item.location, { x: center.lat, y: center.lng })
		)
	)
	return maxDistance + 1000
}

const getMarkerIconUrl = (group: GroupName) => {
	const colorMap = {
		production: 'yellow',
		mixing: 'blue',
		application: 'green',
	}

	// Google marker image with custom color
	return `https://maps.google.com/mapfiles/ms/icons/${colorMap[group]}-dot.png`
}

const areCentersSame = (
	c1: { lat: number; lng: number },
	c2: { lat: number; lng: number }
): boolean => {
	return (
		Math.abs(c1.lat - c2.lat) < 0.0001 && Math.abs(c1.lng - c2.lng) < 0.0001
	)
}

const areRadiiSame = (r1: number, r2: number): boolean => {
	return Math.abs(r1 - r2) < 100 // within 100 meters difference
}

export const GoogleMapWithClustersAndArrows = ({
	data,
}: {
	data: IStockLocation
}) => {
	const theme = useTheme()
	const mapRef = useRef<google.maps.Map | null>(null)

	const { production, mixing, application } = convertGroupedLocation(data)
	const allLocations = useMemo(() => {
		return [...production, ...mixing, ...application]
	}, [production, mixing, application])

	const { isLoaded } = useLoadScript({
		id: 'google-map-script',
		googleMapsApiKey: VITE_GOOGLE_MAPS_API_KEY,
		libraries,
	})

	const onMapLoad = (map: google.maps.Map) => {
		mapRef.current = map
	}

	useEffect(() => {
		if (!mapRef.current || !isLoaded || allLocations.length === 0) return

		const validLocations = allLocations.filter(
			(loc) => loc?.location?.x && loc?.location?.y
		)

		if (validLocations.length === 0) return

		const bounds = new window.google.maps.LatLngBounds()
		validLocations.forEach((location) => {
			if (location.location?.x && location.location?.y) {
				bounds.extend({
					lat: location.location.x,
					lng: location.location.y,
				})
			}
		})

		mapRef.current.fitBounds(bounds, 100)
	}, [allLocations, isLoaded])

	const productionCenter = getGroupCenter(production)
	const mixingCenter = getGroupCenter(mixing)
	const applicationCenter = getGroupCenter(application)

	const radiusMap = useMemo(() => {
		const baseMap: Record<GroupName, number> = {
			production: getGroupRadius(production),
			mixing: getGroupRadius(mixing),
			application: getGroupRadius(application),
		}

		const adjustedMap = { ...baseMap }

		// Case 3: All three overlap
		if (
			areCentersSame(productionCenter, mixingCenter) &&
			areCentersSame(mixingCenter, applicationCenter) &&
			areRadiiSame(baseMap.production, baseMap.mixing) &&
			areRadiiSame(baseMap.mixing, baseMap.application)
		) {
			adjustedMap.production *= 0.6
			adjustedMap.mixing *= 0.8
			// application remains same
			return adjustedMap
		}

		// Case 1: Production and Mixing overlap
		if (
			areCentersSame(productionCenter, mixingCenter) &&
			areRadiiSame(baseMap.production, baseMap.mixing)
		) {
			adjustedMap.production *= 0.7
		}

		// Case 2: Mixing and Application overlap
		if (
			areCentersSame(mixingCenter, applicationCenter) &&
			areRadiiSame(baseMap.mixing, baseMap.application)
		) {
			adjustedMap.mixing *= 0.7
		}

		return adjustedMap
	}, [
		production,
		mixing,
		application,
		productionCenter,
		mixingCenter,
		applicationCenter,
	])

	const clusters: {
		name: GroupName
		show: boolean
		data: GroupedLocation[]
		center: { lat: number; lng: number }
		color: { stroke: string; fill: string }
	}[] = useMemo(
		() => [
			{
				name: GroupName.Production,
				show: production.length > 0,
				data: production,
				center: productionCenter,
				color: {
					stroke: theme.palette.custom.yellow[600],
					fill: theme.palette.custom.yellow[400],
				},
			},
			{
				name: GroupName.Mixing,
				show: mixing.length > 0,
				data: mixing,
				center: mixingCenter,
				color: {
					stroke: theme.palette.custom.blue[600],
					fill: theme.palette.custom.blue[400],
				},
			},
			{
				name: GroupName.Application,
				show: application.length > 0,
				data: application,
				center: applicationCenter,
				color: {
					stroke: theme.palette.custom.green[800],
					fill: theme.palette.custom.green[100],
				},
			},
		],
		[
			production,
			productionCenter,
			theme.palette.custom.yellow,
			theme.palette.custom.blue,
			theme.palette.custom.green,
			mixing,
			mixingCenter,
			application,
			applicationCenter,
		]
	)

	const curvedLines = useMemo(
		() => [
			{
				from: productionCenter,
				to: mixingCenter,
				show: production.length > 0 && mixing.length > 0,
			},
			{
				from: mixingCenter,
				to: applicationCenter,
				show: mixing.length > 0 && application.length > 0,
			},
		],
		[
			application.length,
			applicationCenter,
			mixing.length,
			mixingCenter,
			production.length,
			productionCenter,
		]
	)

	if (!isLoaded) return <CircularProgress />

	return (
		<StyledWrapper>
			<GoogleMap
				mapContainerStyle={{ width: '100%', height: '500px' }}
				onLoad={onMapLoad}
				options={{
					mapTypeId: google.maps.MapTypeId.ROADMAP,
					restriction: {
						latLngBounds: {
							north: 85,
							south: -85,
							west: -179.999,
							east: 179.999,
						},
						strictBounds: true,
					},
				}}>
				{allLocations.map((location) => (
					<MarkerF
						key={location.id}
						position={{
							lat: location.location.x,
							lng: location.location.y,
						}}
						icon={{ url: getMarkerIconUrl(location.group) }}
					/>
				))}

				{clusters
					.filter((cluster) => cluster.show)
					.map((cluster) => (
						<CircleF
							key={cluster.name + 'circle'}
							center={cluster.center}
							radius={radiusMap[cluster.name]}
							options={{
								strokeColor: cluster.color.stroke,
								strokeOpacity: 1,
								strokeWeight: 3,
								fillColor: cluster.color.fill,
								fillOpacity: 0.25,
							}}
						/>
					))}

				{curvedLines
					.filter((line) => line.show)
					.map((line, index) => (
						<PolylineF
							key={index}
							path={[line.from, line.to]}
							options={{
								strokeColor: theme.palette.primary.main,
								strokeOpacity: 1,
								strokeWeight: 2,
							}}
						/>
					))}
			</GoogleMap>
		</StyledWrapper>
	)
}

const StyledWrapper = styled(Box)(() => ({
	position: 'relative',
	width: '100%',
	height: '100%',
}))
