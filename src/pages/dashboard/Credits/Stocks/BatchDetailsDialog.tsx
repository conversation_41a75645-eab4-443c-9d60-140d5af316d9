import { Dialog, DialogContent, DialogTitle, IconButton, Typography, useTheme } from "@mui/material"
import CloseIcon from '@mui/icons-material/Close'
import { BatchDetails } from "../../Production"


interface BatchDetailsDialogProps {
    showDialog: string
    setShowDialog: React.Dispatch<React.SetStateAction<string>>
}

export const BatchDetailsDialog = ({
    showDialog,
    setShowDialog,
}: BatchDetailsDialogProps) => {
    const theme = useTheme()
    return (
        <Dialog open={!!showDialog} onClose={() => setShowDialog('')} fullScreen>
            <DialogTitle
                sx={{
                    py: theme.spacing(5),
                    display: 'flex',
                    justifyContent: 'space-between',
                }}>
                <Typography variant='h6'>Batch details</Typography>
                <IconButton aria-label='close' onClick={() => setShowDialog('')}>
                    <CloseIcon />
                </IconButton>
            </DialogTitle>

            <DialogContent>
                <BatchDetails id={showDialog} showHeader={false} />
            </DialogContent>
        </Dialog>
    )
}
