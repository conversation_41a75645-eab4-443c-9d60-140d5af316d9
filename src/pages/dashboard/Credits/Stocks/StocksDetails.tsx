import { CustomDataGrid } from '@/components'
import { dateFormats } from '@/utils/constant'
import { getFormattedDate, getSerialNumber } from '@/utils/helper'

import { alpha, Box, Stack, styled, Typography } from '@mui/material'
import { GridColDef, GridValidRowModel } from '@mui/x-data-grid'
import { useMemo, useState } from 'react'
import { ILabReportDocument, IStockLocation } from '@/interfaces'
import { LocationOn } from '@mui/icons-material'
import { DocumentViewer } from '@/components/TrainingProofRenderer/DocumentViewer'
import { GoogleMapWithClustersAndArrows } from './GoogleMapWithClustersAndArrows'
import { useStockDetails } from './useStockDetails'
import { PdfPreviewDialog } from '@/components/TrainingProofRenderer/PdfPreviewDialog'
import {
	BatchDetailsDialog,
	StockNavigationHeader,
	StockDetailsHeader,
	ProcessJourney,
} from './index'

function getFileNameFromPath(path: string): string {
	return path.split('/').pop() || ''
}

export const StocksDetails = () => {
	const [showDialog, setShowDialog] = useState<string>('')

	const [showPdfOrDocumentPreview, setShowPdfOrDocumentPreview] = useState<
		ILabReportDocument[] | null
	>(null)
	const {
		getProductionQuery,
		getDistributionQuery,
		getMixingQuery,
		getBatchListQuery,
		getStockLocationsQuery,
		handleBack,
		stockId,
		limit,
	} = useStockDetails()

	const columns: GridColDef<GridValidRowModel>[] = useMemo(
		() => [
			{
				field: 'id',
				headerName: 'S.No',
				flex: 0.5,
				minWidth: 80,
				renderCell: (params) => (
					<Typography variant='subtitle1'>
						{getSerialNumber(params, Number(limit))}
					</Typography>
				),
			},
			{
				field: 'startDate',
				headerName: 'Date',
				flex: 1,
				minWidth: 150,
				renderCell: (params) => (
					<Typography variant='subtitle1'>
						{getFormattedDate(params?.value, dateFormats.yyyy_MM_dd)}
					</Typography>
				),
			},

			{
				field: 'biocharQuantity',
				headerName: 'Biochar Qty',
				minWidth: 100,
				flex: 1,
				renderCell: (params) => (
					<Typography variant='subtitle1'>
						{params?.value ?? 0} litres
					</Typography>
				),
			},
			{
				field: 'siteName',
				headerName: 'Site Name',
				minWidth: 100,
				flex: 1,
				renderCell: (params) => (
					<Typography variant='subtitle1'>
						{params?.row?.isSiteProcess
							? params?.row?.siteName
							: params?.row?.kilnName}
					</Typography>
				),
			},

			{
				field: 'cropName',
				headerName: 'Biomass Type',
				minWidth: 100,
				flex: 1,
				renderCell: (params) => (
					<Typography variant='subtitle1'>{params?.value}</Typography>
				),
			},

			{
				field: 'carbonCredits',
				headerName: 'Carbon credits',
				minWidth: 150,
				flex: 1,
				renderCell: (params) => (
					<Typography variant='subtitle1'>
						{params?.value ? `${params?.value} tCO2` : '-'}
					</Typography>
				),
			},
		],
		[limit]
	)

	return (
		<>
			<BatchDetailsDialog
				showDialog={showDialog}
				setShowDialog={setShowDialog}
			/>

			{showPdfOrDocumentPreview ? (
				showPdfOrDocumentPreview.length === 1 ? (
					<PdfPreviewDialog
						open={!!showPdfOrDocumentPreview}
						close={() => setShowPdfOrDocumentPreview(null)}
						pdfUrl={showPdfOrDocumentPreview?.[0]?.url}
					/>
				) : (
					showPdfOrDocumentPreview.length >= 1 && (
						<DocumentViewer
							open={!!showPdfOrDocumentPreview}
							close={() => setShowPdfOrDocumentPreview(null)}
							documents={(showPdfOrDocumentPreview ?? [])?.map((i) => ({
								...i,
								fileName: getFileNameFromPath(i.path),
							}))}
							showDownloadButton
						/>
					)
				)
			) : null}

			<StyledContainer>
				<Stack className='container' sx={{ margin: '0px' }}>
					<StockNavigationHeader
						getBatchListQuery={getBatchListQuery}
						stockId={stockId}
						handleBack={handleBack}
						setShowPdfPreview={setShowPdfOrDocumentPreview}
					/>

					<StockDetailsHeader
						setShowPdfPreview={setShowPdfOrDocumentPreview}
						getBatchListQuery={getBatchListQuery}
					/>

					<Box className='map-full'>
						<Box className='project-summary-header'>
							<LocationOn className='project-summary-header-icon' />
							<Typography variant='h5'>Process Summary</Typography>
						</Box>
						<GoogleMapWithClustersAndArrows
							data={getStockLocationsQuery?.data || ({} as IStockLocation)}
						/>
					</Box>

					<ProcessJourney
						getProductionQuery={getProductionQuery}
						getMixingQuery={getMixingQuery}
						getDistributionQuery={getDistributionQuery}
					/>
				</Stack>

				<Stack className='container'>
					<CustomDataGrid
						onRowClick={(params) => {
							setShowDialog(params?.row?.id)
						}}
						showPagination
						columns={columns}
						headerComponent={<Typography variant='body2'>Batches:</Typography>}
						rows={getBatchListQuery?.data?.processes ?? []}
						rowCount={getBatchListQuery?.data?.count ?? 0}
						loading={getBatchListQuery?.isLoading}
					/>
				</Stack>
			</StyledContainer>
		</>
	)
}

const StyledContainer = styled(Stack)(({ theme }) => ({
	gap: theme.spacing(2),
	'.header-navigation': {
		padding: theme.spacing(4, 1, 2),
		borderBottom: `${theme.spacing(0.125)} solid ${
			theme.palette.neutral['100']
		}`,
		alignItems: 'center',
		flexDirection: 'row',
		justifyContent: 'space-between',
		'.batch-button': {
			color: theme.palette.neutral['500'],
			...theme.typography.body1,
			'.arrow-icon': {
				color: theme.palette.neutral['500'],
			},
		},
		'.header-navigation-start': {
			display: 'flex',
			alignItems: 'center',
			flexDirection: 'row',
		},
		'.header-navigation-end': {
			alignItems: 'center',
			flexDirection: 'row',
		},
	},
	'.project-summary-header': {
		display: 'flex',
		alignItems: 'center',
		gap: theme.spacing(1),
		'.project-summary-header-icon': {
			color: theme.palette.custom.green[800],
			fontSize: theme.typography.h5.fontSize,
		},
	},
	'.container': {
		padding: theme.spacing(1, 2.5),
		gap: theme.spacing(3),

		'.box-container': {
			display: 'flex',
			flexDirection: 'column',
			alignItems: 'center',
			borderRadius: theme.spacing(2),
			boxShadow: `0 0 ${theme.spacing(0.5)} 0 ${alpha(
				theme.palette.common.black,
				0.4
			)}`,
			padding: theme.spacing(1, 2),
			'.box-container-bottom': {
				Padding: '0',
				display: 'flex',
				justifyContent: 'space-between',
				flexDirection: 'row',
			},
		},
		'.box-container-2': {
			display: 'flex',
			flexDirection: 'row',
			borderRadius: theme.spacing(2),
			boxShadow: `0 0 ${theme.spacing(0.5)} 0 ${alpha(
				theme.palette.common.black,
				0.4
			)}`,
			padding: theme.spacing(1, 2),
		},
		'.project-details': {
			width: '100%',
			display: 'flex',
			flexDirection: 'row',
			justifyContent: 'space-evenly',
			flexWrap: 'wrap',
			gap: theme.spacing(3),
			paddingBottom: theme.spacing(1),

			'.info-container': {
				display: 'flex',
				alignItems: 'center',
				gap: theme.spacing(1),
				'.label': {
					fontSize: theme.typography.caption.fontSize,
					color: theme.palette.neutral[300],
					whiteSpace: 'nowrap',
				},
				'.value': {
					fontSize: theme.typography.body2,
					fontWeight: theme.typography.body1.fontWeight,
					whiteSpace: 'nowrap',
				},
			},
		},

		'.map-full': {
			display: 'flex',
			flexDirection: 'column',
			borderRadius: theme.spacing(2),
			border: `${theme.spacing(0.05)} solid ${theme.palette.neutral['100']}`,
			gap: theme.spacing(2),
			padding: theme.spacing(4),
		},
		'.process-journey-card': {
			cursor: 'pointer',
			border: `1px solid transparent`,
			'&.production': {
				backgroundColor: theme.palette.custom.yellow[100],
				borderColor: theme.palette.custom.yellow[400],
				'.process-journey-icon': {
					display: 'flex',
					alignItems: 'center',
					justifyContent: 'center',
					borderRadius: '50%',
					padding: theme.spacing(1.5),
					backgroundColor: theme.palette.custom.yellow[600],
				},
				'&.selected': {
					boxShadow: `0px 6px 8px -1px ${theme.palette.grey[400]}`,
				},
			},
			'&.mixing': {
				backgroundColor: theme.palette.custom.blue[100],
				borderColor: theme.palette.custom.blue[400],
				'.process-journey-icon': {
					display: 'flex',
					alignItems: 'center',
					justifyContent: 'center',
					borderRadius: '50%',
					padding: theme.spacing(1.5),
					backgroundColor: theme.palette.custom.blue[600],
				},
				'&.selected': {
					boxShadow: `0px 6px 8px -1px ${theme.palette.grey[400]}`,
				},
			},
			'&.application': {
				backgroundColor: theme.palette.custom.green[100],
				borderColor: theme.palette.custom.green[400],
				'.process-journey-icon': {
					display: 'flex',
					alignItems: 'center',
					justifyContent: 'center',
					borderRadius: '50%',
					padding: theme.spacing(1.5),
					backgroundColor: theme.palette.custom.green[800],
				},
				'&.selected': {
					boxShadow: `0px 6px 8px -1px ${theme.palette.grey[400]}`,
				},
			},
		},
		'.viewDocumentButton': {
			color: theme.palette.grey[600],
			cursor: 'pointer',
		},
	},
}))
