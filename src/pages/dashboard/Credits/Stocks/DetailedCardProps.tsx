import { CustomCard } from "@/components"
import { IMixing, IStockDistribution } from "@/interfaces"
import { Kiln } from "@/types"
import { Stack, Typography, useTheme } from "@mui/material"

enum ProcessStepEnum {
	Production = 'Production Details',
	Mixing = 'Mixing Details',
	Application = 'Application Details'
}

interface DetailedCardProps {
    item: Kiln | IStockDistribution | IMixing,
    type: ProcessStepEnum
}

export const DetailedCard = ({
    item,
    type
}: DetailedCardProps) => {
    const theme = useTheme()

    const renderLocations = () => {
        if (type === ProcessStepEnum.Mixing && 'locations' in item) {
            return item.locations?.map((loc, index) => (
                <Typography key={index} variant='body1'>
                    {`${loc.x}, ${loc.y}`}
                </Typography>
            ))
        } else if (type === ProcessStepEnum.Production && 'coordinate' in item) {
            return (
                <Typography variant='body1'>
                    {`${item.coordinate.x}, ${item.coordinate.y}`}
                </Typography>
            )
        } else if (type === ProcessStepEnum.Application && 'location' in item) {
            return (
                <Typography variant='body1'>
                    {`${item.location?.x}, ${item.location?.y}`}
                </Typography>
            )
        }
    }

    return (
        <CustomCard
            id={item.id}
            headerComponent={
                <Stack direction='row' gap={2}>
                    <Stack flex={1} direction='column' gap={theme.spacing(1)}>
                        <Stack direction='column' gap={theme.spacing(0.5)}>
                            <Typography
                                variant='caption'
                                color={theme.palette.custom.grey[800]}>
                                {
                                    type === ProcessStepEnum.Mixing
                                        ? 'Matrix ID'
                                        : 'Name'
                                }
                            </Typography>
                            <Typography variant='body1'>{item.name}</Typography>
                        </Stack>

                        <Stack direction='column' gap={theme.spacing(0.5)}>
                            <Typography
                                variant='caption'
                                color={theme.palette.custom.grey[800]}>
                                Qty (liters)
                            </Typography>
                            <Typography variant='body1'>
                                {item.biocharQuantity ?? '-'}
                            </Typography>
                        </Stack>
                    </Stack>

                    <Stack flex={1} direction='column' gap={theme.spacing(1)}>
                        <Typography
                            variant='caption'
                            color={theme.palette.custom.grey[800]}>
                            {type === ProcessStepEnum.Mixing ? 'Locations' : 'Location'}
                        </Typography>
                        {renderLocations()}
                    </Stack>
                </Stack>
            }
            sx={{ cursor: 'pointer', gap: theme.spacing(2) }}
        />
    )
}
