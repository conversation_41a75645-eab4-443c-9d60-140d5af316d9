import {
	<PERSON><PERSON>,
	<PERSON><PERSON><PERSON>,
	<PERSON><PERSON>,
	<PERSON><PERSON><PERSON>,
	Typography,
	useTheme,
} from '@mui/material'
import ChevronRightIcon from '@mui/icons-material/ChevronRight'
import { ILabReportDocument, IProcessData } from '@/interfaces'
import { UseQueryResult } from '@tanstack/react-query'
import { FileOpen } from '@mui/icons-material'
import { useMemo } from 'react'

interface StockNavigationHeaderProps {
	getBatchListQuery: UseQueryResult<IProcessData | null, Error>
	stockId: string | undefined
	handleBack: () => void
	setShowPdfPreview: React.Dispatch<
		React.SetStateAction<ILabReportDocument[] | null>
	>
}

export const StockNavigationHeader = ({
	getBatchListQuery,
	stockId,
	handleBack,
	setShowPdfPreview,
}: StockNavigationHeaderProps) => {
	const theme = useTheme()

	const DownloadableReportsList = useMemo(() => {
		return [
			{
				label: 'Annex Report',
				show: getBatchListQuery?.data?.annexReport?.length,
				onClick: () =>
					setShowPdfPreview(getBatchListQuery?.data?.annexReport || null),
			},
			{
				label: 'Monitoring Report',
				show: getBatchListQuery?.data?.monitoringReport
					? [getBatchListQuery?.data?.monitoringReport]
					: null,
				onClick: () =>
					setShowPdfPreview(
						getBatchListQuery?.data?.monitoringReport
							? [getBatchListQuery?.data?.monitoringReport]
							: null
					),
			},
			{
				label: 'Methane Documents',
				show: getBatchListQuery?.data?.methaneCompensateStrategyDocuments
					?.length,
				onClick: () =>
					setShowPdfPreview(
						getBatchListQuery?.data?.methaneCompensateStrategyDocuments || null
					),
			},
		]
	}, [
		getBatchListQuery?.data?.annexReport,
		getBatchListQuery?.data?.methaneCompensateStrategyDocuments,
		getBatchListQuery?.data?.monitoringReport,
		setShowPdfPreview,
	])

	return (
		<Stack className='header-navigation'>
			<Stack className='header-navigation-start'>
				<Button onClick={handleBack} className='batch-button' variant='text'>
					Stock
				</Button>
				<ChevronRightIcon />
				<Typography
					variant='body1'
					paddingTop={theme.spacing(0.2)}>{`Stock ID: ${stockId}`}</Typography>
			</Stack>
			<Stack direction={'row'} spacing={2} paddingX={4}>
				{DownloadableReportsList.filter((item) => item.show).map(
					(detail, index) => (
						<Stack key={detail.label} className='info-container'>
							<Tooltip title={detail.label} arrow>
								<Stack direction='row' gap={1} alignItems='center'>
									{index > 0 && (
										<Divider
											orientation='vertical'
											style={{
												border: `${theme.spacing(0.2)} solid ${
													theme.palette.common.black
												}`,
											}}
										/>
									)}
									<Button
										size='small'
										variant='text'
										startIcon={<FileOpen />}
										onClick={detail.onClick}
										sx={{ padding: 0 }}>
										{detail.label}
									</Button>
								</Stack>
							</Tooltip>
						</Stack>
					)
				)}
			</Stack>
		</Stack>
	)
}
