import { authAxios } from "@/contexts"
import { IMixing, IProcessData, IStockDistribution, IStockLocation } from "@/interfaces"
import { KilnsResponse } from "@/types"
import { defaultLimit, defaultPage } from "@/utils/constant"
import { showAxiosErrorToast } from "@/utils/helper"
import { useQuery } from "@tanstack/react-query"
import { useNavigate, useParams, useSearchParams } from "react-router-dom"

export const useStockDetails = () => {
    const navigate = useNavigate()
    const { stockId } = useParams()
    const [searchParams, setSearchParams] = useSearchParams()
    const page = searchParams.get('page') || defaultPage
    const limit = searchParams.get('limit') || defaultLimit

    const handleBack = () => {
		navigate(-1)
	}

    const getStockLocationsQuery = useQuery({
        queryKey: ['getStockLocationsQuery', stockId],
        queryFn: async () => {
            try {
                const endPoint = `stock/${stockId}/locations`
                const { data } = await authAxios.get<IStockLocation>(endPoint)
                return data
            } catch (error: any) {
                showAxiosErrorToast(error)
                return null
            }
        }
    })

    const getBatchListQuery = useQuery({
        queryKey: ['BatchesQuery', page, limit, stockId],
        queryFn: async () => {
            try {
                const endPoint = `new/stocks/${stockId}/processes?limit=${limit}&page=${page}`
                const { data } = await authAxios.get<IProcessData>(endPoint)
                return data
            } catch (error: any) {
                showAxiosErrorToast(error)
                return null
            }
        },
    })

    const getMixingQuery = useQuery({
        queryKey: ['getMixingQuery', stockId],
        queryFn: async () => {
            try {
                const endPoint = `new/stocks/${stockId}/mixing`
                const { data } = await authAxios.get<IMixing[]>(endPoint)
                return data
            } catch (error: any) {
                showAxiosErrorToast(error)
                return null
            }
        },
    })

    const getDistributionQuery = useQuery({
        queryKey: ['getDistributionQuery', stockId],
        queryFn: async () => {
            try {
                const endPoint = `new/stocks/${stockId}/distribution`
                const { data } = await authAxios.get<IStockDistribution[]>(endPoint)
                return data
            } catch (error: any) {
                showAxiosErrorToast(error)
                return null
            }
        },
    })

    const getProductionQuery = useQuery({
        queryKey: ['getProductionQuery', stockId],
        queryFn: async () => {
            try {
                const endPoint = `new/stocks/${stockId}/production`

                const { data } = await authAxios.get<KilnsResponse>(endPoint)
                return data
            } catch (error: any) {
                showAxiosErrorToast(error)
                return null
            }
        },
    })

    return {
        handleBack,
        stockId,
        searchParams,
        setSearchParams,
        page,
        limit,
        getStockLocationsQuery,
        getBatchListQuery,
        getMixingQuery,
        getDistributionQuery,
        getProductionQuery,
    }
}