import { Confirmation } from '@/components/Confirmation'
import { authAxios } from '@/contexts'
import { Delete, Download, UploadFileOutlined } from '@mui/icons-material'
import {
	CircularProgress,
	IconButton,
	Menu,
	MenuItem,
	Stack,
	styled,
	Tooltip,
	Typography,
} from '@mui/material'
import { useMutation } from '@tanstack/react-query'
import React, { memo, useCallback, useMemo, useState } from 'react'
import { toast } from 'react-toastify'
import { UploadSinkFileDialog } from './UploadSinkFileDialog'
import { AxiosError } from 'axios'
import { theme } from '@/lib/theme/theme'

const getDeleteSinkConfirmationText = (status: SinkStatusType) => {
	switch (status) {
		case SinkStatusType.Approved:
		case SinkStatusType.Rejected:
			return 'Are you sure you want to delete the sink? If yes then this sink will only be deleted from the Circonomy only.'
		default:
			return 'Are you sure you want to delete this sink?'
	}
}

type ConfirmationModal = {
	open: boolean
	title: string
	onYesClick: () => void
	onNoClick: () => void
	onCancelClick: () => void
	isDoubleConfirmationModal: boolean
}

enum SinkStatusType {
	Approved = 'Approved',
	Rejected = 'Rejected',
	Pending = 'Pending',
}

const initialConfirmationModalValues: ConfirmationModal = {
	open: false,
	title: '',
	onYesClick: () => {},
	onNoClick: () => {},
	onCancelClick: () => {},
	isDoubleConfirmationModal: false,
}

export const SinkActionButtons: React.FC<{
	sinkId: string
	refetchApi: () => void
	sinkStatus: SinkStatusType
	isDeleted?: boolean
	isAdmin?: boolean
}> = ({ sinkId, refetchApi, sinkStatus, isDeleted, isAdmin = false }) => {
	const [anchorEl, setAnchorEl] = React.useState<null | HTMLElement>(null)
	const open = Boolean(anchorEl)

	const handleClick = (event: React.MouseEvent<HTMLElement>) => {
		setAnchorEl(event.currentTarget)
	}
	const handleClose = () => {
		setAnchorEl(null)
	}

	const [confirmationModal, setConfirmationModal] = useState<ConfirmationModal>(
		initialConfirmationModalValues
	)

	const [showUploadFileDialog, setshowUploadFileDialog] =
		useState<boolean>(false)
	const deletedOrRejected = useMemo(() => {
		return isDeleted || sinkStatus === SinkStatusType.Rejected
	}, [isDeleted, sinkStatus])

	const deleteSinkMutation = useMutation({
		mutationKey: ['deleteSink'],
		mutationFn: async () => {
			const { data } = await authAxios.delete(`global-csink/sink/${sinkId}`)
			return data
		},
		onError: (error: AxiosError) => {
			toast((error?.response?.data as { messageToUser: string })?.messageToUser)
		},
		onSuccess: (data) => {
			toast(data?.message)
			refetchApi()
		},
	})

	const handleDeleteSink = useCallback(() => {
		deleteSinkMutation.mutate()
	}, [deleteSinkMutation])

	const downloadExcelFileMuatation = useMutation({
		mutationKey: ['downloadExcelFile'],
		mutationFn: async (id: string) => {
			const response = await authAxios.get(`/global-csink/sink/${id}/excel`, {
				responseType: 'blob',
			})

			const url = window.URL.createObjectURL(response.data)
			const link = document.createElement('a')
			link.href = url
			link.setAttribute('download', `Sink.xlsx`)
			document.body.appendChild(link)
			link.click()
		},
		onSuccess: () => {
			toast('Excel File Downloaded')
			handleClose()
		},
		onError: async (err: AxiosError) => {
			if (err.response?.data instanceof Blob) {
				try {
					const text = await err.response.data.text()
					const json = JSON.parse(text)
					toast(json.messageToUser || 'File Download Failed')
				} catch (e) {
					toast('Unexpected error format')
				}
			} else {
				const message = (err.response?.data as { messageToUser: string })
					?.messageToUser
				toast(message || 'File Download Failed')
			}
		},
	})

	const downloadMonitoringReportFileMutation = useMutation({
		mutationKey: ['downloadMonitoringReportFile'],
		mutationFn: async (id: string) => {
			const response = await authAxios.post(
				`/internal-project/monitoring-report/${id}`,
				null,
				{ responseType: 'blob' }
			)
			const url = window.URL.createObjectURL(response.data)

			const link = document.createElement('a')
			link.href = url
			link.setAttribute('download', `Monitoring_Report.pdf`)
			document.body.appendChild(link)
			link.click()
		},
		onSuccess: () => {
			toast('Excel File Downloaded')
			handleClose()
		},
		onError: async (err: AxiosError) => {
			if (err.response?.data instanceof Blob) {
				try {
					const text = await err.response.data.text()
					const json = JSON.parse(text)
					toast(json.messageToUser || 'File Download Failed')
				} catch (e) {
					toast('Unexpected error format')
				}
			} else {
				const message = (err.response?.data as { messageToUser: string })
					?.messageToUser
				toast(message || 'File Download Failed')
			}
		},
	})

	const handleDownloadExcelFile = useCallback(
		(id: string) => {
			downloadExcelFileMuatation.mutate(id)
		},
		[downloadExcelFileMuatation]
	)

	const handleDownloadMonitoringReportFile = useCallback(
		(id: string) => {
			downloadMonitoringReportFileMutation.mutate(id)
		},
		[downloadMonitoringReportFileMutation]
	)

	return (
		<>
			<UploadSinkFileDialog
				sinkId={sinkId}
				onClose={() => setshowUploadFileDialog(false)}
				open={showUploadFileDialog}
			/>
			<Stack
				style={{
					display: 'flex',
					flexDirection: 'row',
				}}>
				{!deletedOrRejected && (
					<Tooltip title='Download Files'>
						<IconButton
							onClick={(e) => {
								e.stopPropagation()
								handleClick(e)
							}}>
							<Download />
						</IconButton>
					</Tooltip>
				)}
				<Menu anchorEl={anchorEl} open={open} onClose={handleClose}>
					<StyledMenuItem onClick={() => handleDownloadExcelFile(sinkId)}>
						{downloadExcelFileMuatation?.isPending ? (
							<CircularProgress size={20} />
						) : (
							'Annex Report'
						)}
					</StyledMenuItem>
					<StyledMenuItem
						onClick={() => handleDownloadMonitoringReportFile(sinkId)}>
						{downloadMonitoringReportFileMutation?.isPending ? (
							<CircularProgress size={20} />
						) : (
							'Monitoring Report'
						)}
					</StyledMenuItem>
				</Menu>
				{!deletedOrRejected && isAdmin && (
					<Tooltip title='Upload Sink Files'>
						<IconButton
							onClick={(e) => {
								e.stopPropagation()
								setshowUploadFileDialog(true)
							}}>
							<UploadFileOutlined />
						</IconButton>
					</Tooltip>
				)}
				<DeleteButtonContainer
					isLoading={deleteSinkMutation?.isPending}
					isDeleted={isDeleted}
					handleOnClick={() => {
						setConfirmationModal((prev) => ({
							...prev,
							open: true,
							onCancelClick: () =>
								setConfirmationModal(initialConfirmationModalValues),
							onNoClick: () =>
								setConfirmationModal(initialConfirmationModalValues),
							onYesClick: () => {
								handleDeleteSink()
								setConfirmationModal(initialConfirmationModalValues)
							},
							title: getDeleteSinkConfirmationText(sinkStatus),
						}))
					}}
					isAdmin={isAdmin}
				/>
			</Stack>
			{confirmationModal.open ? (
				<Confirmation
					open={confirmationModal.open}
					handleYesClick={confirmationModal.onYesClick}
					handleClose={confirmationModal.onCancelClick}
					handleNoClick={confirmationModal.onNoClick}
					confirmationText={confirmationModal.title}
				/>
			) : null}
		</>
	)
}

interface IDeleteContainerProps {
	isLoading: boolean
	handleOnClick: () => void
	isDeleted?: boolean
	isAdmin?: boolean
}

const DeleteButtonContainer = memo(
	({
		isLoading,
		handleOnClick,
		isDeleted,
		isAdmin = false,
	}: IDeleteContainerProps) => {
		if (isDeleted)
			return (
				<Typography
					sx={{
						fontSize: 14,
						color: theme.palette.primary.main,
						fontWeight: 500,
					}}>
					Deleted
				</Typography>
			)
		return (
			isAdmin && (
				<IconButton
					disabled={isLoading}
					onClick={(e) => {
						e.stopPropagation()
						handleOnClick()
					}}>
					{isLoading ? <CircularProgress size={20} /> : <Delete />}
				</IconButton>
			)
		)
	}
)
const StyledMenuItem = styled(MenuItem)(({ theme }) => ({
	display: 'flex',
	alignItems: 'center',
	justifyContent: 'center',
	width: theme.spacing(20),
}))
