import { CustomDataGrid } from '@/components'
import { Confirmation } from '@/components/Confirmation'
import { authAxios } from '@/contexts'
import { IProcessForInternalProject } from '@/interfaces'
import { TModal } from '@/types'
import { Add, Cancel } from '@mui/icons-material'
import { LoadingButton } from '@mui/lab'
import {
	Checkbox,
	Dialog,
	DialogContent,
	DialogTitle,
	FormControl,
	IconButton,
	InputLabel,
	MenuItem,
	Paper,
	Select,
	Stack,
	styled,
	Typography,
} from '@mui/material'
import { GridColDef, GridValidRowModel } from '@mui/x-data-grid'
import { AdapterMoment } from '@mui/x-date-pickers/AdapterMoment'
import { DatePicker } from '@mui/x-date-pickers/DatePicker'
import { LocalizationProvider } from '@mui/x-date-pickers/LocalizationProvider'
import { useMutation, useQuery, useQueryClient } from '@tanstack/react-query'
import { format } from 'date-fns'
import moment from 'moment'
import { FC, useCallback, useMemo, useState } from 'react'
import { useParams, useSearchParams } from 'react-router-dom'
import { toast } from 'react-toastify'
type TIdAndName = {
	id: string
	name: string
}

type TProp = TModal & {
	siteList: TIdAndName[]
	cropList: TIdAndName[]
}

export const CreateStocksForAvailableProcessesDialog: FC<TProp> = ({
	open,
	onClose,
	siteList,
	cropList,
}) => {
	const [selectedProcesses, setSelectedProcesses] = useState<string[]>([])
	const [selectedTotalBioChar, setSelectedTotalBioChar] = useState(0)
	const [showConfirmationDialog, setShowConfirmationDialog] =
		useState<boolean>(false)
	const queryClient = useQueryClient()
	const { projectId } = useParams()
	const [searchParams, setSearchParams] = useSearchParams()
	const paramsSite = searchParams.get('site') || ''
	const paramsStartDate =
		searchParams.get('startDate') === null
			? ''
			: moment(searchParams.get('startDate'), 'DD-MM-YYYY').format('YYYY-MM-DD')

	const paramsEndDate =
		searchParams.get('endDate') === null
			? null
			: moment(searchParams.get('endDate'), 'DD-MM-YYYY').format('YYYY-MM-DD')
	const paramsCrop = searchParams.get('crop') || ''

	const handleSelectProcess = useCallback(
		(id: string, bioCharQuantity: number) => {
			if (selectedProcesses?.includes(id)) {
				setSelectedProcesses((prev) => prev.filter((x) => x !== id))
				setSelectedTotalBioChar((prev) => prev - bioCharQuantity)
				return
			}
			setSelectedProcesses((prev) => [...prev, id])
			setSelectedTotalBioChar((prev) => prev + bioCharQuantity)
		},
		[selectedProcesses]
	)

	const processColumn: GridColDef<GridValidRowModel>[] = useMemo(
		() => [
			{
				field: 'checkbox',
				headerName: ' ',
				minWidth: 80,
				maxWidth: 80,
				flex: 1,
				renderCell: (params) => (
					<Checkbox
						disabled={params?.row?.isStockCreated}
						checked={selectedProcesses?.includes(params?.row?.id)}
						onClick={(e) => e.stopPropagation()}
						onChange={() =>
							handleSelectProcess(params?.row?.id, params?.row?.bioCharQuantity)
						}
					/>
				),
			},
			{
				field: 'id',
				headerName: 'S. No',
				minWidth: 60,
				flex: 1,
				renderCell: (params) => (
					<Typography>
						{params.api.getRowIndexRelativeToVisibleRows(params.row.id) + 1}
					</Typography>
				),
			},
			{
				field: 'kilnProcessCreatedAt',
				headerName: 'Date',
				minWidth: 150,
				flex: 1,
				renderCell: (params) => (
					<Typography>
						{format(new Date(params?.value), 'yyyy-MM-dd')}
					</Typography>
				),
			},
			{
				field: 'bioCharQuantity',
				headerName: 'Biochar Qty',
				minWidth: 100,
				flex: 1,
				renderCell: (params) => <Typography>{params?.value}</Typography>,
			},
			{
				field: 'totalSinkQuantity',
				headerName: 'Sinked Biochar Qty',
				minWidth: 150,
				flex: 1,
				renderCell: (params) => (
					<Typography>{params?.value?.toFixed(2)}</Typography>
				),
			},
			{
				field: 'siteName',
				headerName: 'Site Name',
				minWidth: 150,
				flex: 1,
				renderCell: (params) => <Typography>{params?.value || '-'}</Typography>,
			},
			{
				field: 'cropName',
				headerName: 'Biomass Type',
				minWidth: 150,
				flex: 1,
				renderCell: (params) => <Typography>{params?.value}</Typography>,
			},
			{
				field: 'carbonCredits',
				headerName: 'Carbon Credits',
				minWidth: 100,
				flex: 1,
				renderCell: (params) =>
					params?.value ? (
						<Typography>{params?.value || 0} tCO2</Typography>
					) : (
						<Typography>-</Typography>
					),
			},
			{
				field: 'stockId',
				headerName: 'Stock ID',
				minWidth: 150,
				flex: 1,
				renderCell: (params) => <Typography>{params?.value || '-'}</Typography>,
			},
		],
		[handleSelectProcess, selectedProcesses]
	)

	const getProcessList = useQuery({
		queryKey: [
			'processListForDialog',
			projectId,
			paramsSite,
			paramsStartDate,
			paramsEndDate,
			paramsCrop,
		],
		queryFn: async () => {
			const { data } = await authAxios.get<IProcessForInternalProject>(
				`/internal-project/${projectId}/kiln-process?limit=100000&page=0&siteId=${paramsSite}&startDate=${paramsStartDate}&endDate=${paramsEndDate}&cropId=${paramsCrop}&isStockNotCreated=true`
			)
			return data?.process ?? []
		},
	})

	const isAllProcessSelected = useMemo(
		() =>
			getProcessList?.isLoading ||
			(getProcessList?.data ?? [])?.filter((i) => !i.isStockCreated)?.length ===
				0
				? false
				: selectedProcesses?.length ===
				  (getProcessList?.data ?? [])?.filter((i) => !i.isStockCreated)
						?.length,
		[getProcessList?.data, getProcessList?.isLoading, selectedProcesses?.length]
	)

	const isAllTrueOnlyProcessSelected = useMemo(
		() =>
			getProcessList?.isLoading ||
			(getProcessList?.data ?? [])?.filter((i) => {
				let canSink = true
				if (
					(i?.bioCharQuantity ?? 0) * 0.99 > (i?.totalSinkQuantity ?? 0) ||
					(i?.bioCharQuantity ?? 0) + (i?.bioCharQuantity ?? 0) * 0.01 <
						i?.totalSinkQuantity
				) {
					canSink = false
				}

				return canSink && !i?.isStockCreated
			})?.length === 0
				? false
				: selectedProcesses?.length ===
				  (getProcessList?.data ?? [])?.filter((i) => {
						let canSink = true
						if (
							(i?.bioCharQuantity ?? 0) * 0.99 > (i?.totalSinkQuantity ?? 0) ||
							(i?.bioCharQuantity ?? 0) + (i?.bioCharQuantity ?? 0) * 0.01 <
								i?.totalSinkQuantity
						) {
							canSink = false
						}

						return canSink && !i?.isStockCreated
				  })?.length,
		[getProcessList?.data, getProcessList?.isLoading, selectedProcesses?.length]
	)

	const handleSelectAllProcesses = useCallback(() => {
		if (
			selectedProcesses?.length !== 0 &&
			(getProcessList?.data ?? [])?.length !== 0
		) {
			setSelectedProcesses([])
			setSelectedTotalBioChar(0)
			return
		}

		setSelectedProcesses(
			(getProcessList?.data ?? [])
				.filter((i) => !i?.isStockCreated)
				?.map((i) => i.id)
		)
		let totalCount = 0
		getProcessList?.data?.map((i) => {
			if (!i?.isStockCreated) totalCount = totalCount + i?.bioCharQuantity
		})
		setSelectedTotalBioChar(totalCount)
	}, [getProcessList?.data, selectedProcesses?.length])

	const handleSelectAllTrueOnlyProcesses = useCallback(() => {
		if (selectedProcesses?.length !== 0 && getProcessList?.data?.length !== 0) {
			setSelectedProcesses([])
			setSelectedTotalBioChar(0)

			return
		}
		const canSinkProcesses = getProcessList?.data?.filter((i) => {
			let canSink = true
			if (
				(i?.bioCharQuantity ?? 0) * 0.99 > (i?.totalSinkQuantity ?? 0) ||
				(i?.bioCharQuantity ?? 0) + (i?.bioCharQuantity ?? 0) * 0.01 <
					i?.totalSinkQuantity
			) {
				canSink = false
			}

			return canSink && !i?.isStockCreated
		})
		let count = 0
		canSinkProcesses?.forEach((element) => {
			count = count + element?.bioCharQuantity
		})

		setSelectedTotalBioChar(count)
		setSelectedProcesses((canSinkProcesses ?? [])?.map((i) => i.id))
	}, [getProcessList?.data, selectedProcesses?.length])

	const handleChangeParamsValue = useCallback(
		(key: string, value: string) => {
			setSelectedTotalBioChar(0)
			setSelectedProcesses([])
			setSearchParams(
				(prev) => {
					if (value === 'true' || value === 'all') {
						prev.delete(key)
					} else {
						prev.set(key, value)
					}
					prev.delete('limit')
					prev.delete('page')
					return prev
				},
				{ replace: true }
			)
		},
		[setSearchParams]
	)

	const createStockMutation = useMutation({
		mutationKey: ['createSelectedStockInDialog', selectedProcesses, projectId],
		mutationFn: async () => {
			const { data } = await authAxios.post(`/global-csink/process/stock`, {
				processIds: selectedProcesses,
				internalProjectId: projectId,
			})
			return data
		},
		onSuccess: (data) => {
			toast(data?.message)
			setSelectedProcesses([])
			queryClient?.refetchQueries({
				queryKey: ['processListForInternalProject'],
			})
			onClose()
		},
		onError: (error: any) => {
			toast(error?.response?.data?.messageToUser)
		},
	})

	const handleCreateMutation = useCallback(() => {
		createStockMutation.mutate()
	}, [createStockMutation])

	const handleClearDateFilter = useCallback(
		(key: 'startDate' | 'endDate') => {
			setSearchParams(
				(prev) => {
					prev.delete(key)
					prev.delete('limit')
					prev.delete('page')
					return prev
				},
				{ replace: true }
			)
		},
		[setSearchParams]
	)

	return (
		<>
			<StyledDialog
				open={open}
				onClose={onClose}
				fullWidth
				maxWidth='lg'
				sx={{
					'.MuiDialog-paper': {
						borderRadius: 4,
					},
				}}>
				<IconButton
					sx={{ position: 'absolute', right: 5, top: 5 }}
					onClick={onClose}>
					<Cancel />
				</IconButton>
				<DialogTitle textAlign='center'>All Processes</DialogTitle>
				<DialogContent>
					<Paper sx={{ pt: 2 }}>
						<CustomDataGrid
							columns={processColumn}
							rows={getProcessList?.data ?? []}
							rowCount={0}
							showPagination={false}
							loading={getProcessList?.isLoading}
							showRowsPerPage={false}
							headerStyle={{ alignItems: 'flex-start', px: 2 }}
							headerEndComponent={
								<HeaderEndComponent
									isLoading={createStockMutation?.isPending}
									onClick={() => setShowConfirmationDialog(true)}
									selectedProcessLength={selectedProcesses?.length || 0}
								/>
							}
							headerComponent={
								<Stack rowGap={2}>
									<Stack direction='row' alignItems='center' columnGap={1}>
										{(getProcessList?.data ?? [])?.length > 0 ? (
											<Stack direction='row' alignItems='center'>
												<Checkbox
													indeterminate={
														selectedProcesses?.length !== 0 &&
														(getProcessList?.data ?? [])?.filter(
															(i) => !i.isStockCreated
														)?.length !== 0 &&
														selectedProcesses?.length !==
															(getProcessList?.data ?? [])?.filter(
																(i) => !i.isStockCreated
															)?.length
													}
													checked={isAllProcessSelected}
													onChange={handleSelectAllProcesses}
												/>
												<Typography sx={{ mt: 0.5 }}>Select All</Typography>
											</Stack>
										) : null}
										{(getProcessList?.data ?? [])?.length > 0 ? (
											<Stack direction='row' alignItems='center'>
												<Checkbox
													checked={isAllTrueOnlyProcessSelected}
													onChange={handleSelectAllTrueOnlyProcesses}
												/>
												<Typography sx={{ mt: 0.5 }}>
													Select true only
												</Typography>
											</Stack>
										) : null}
										<FormControl
											className='form-controller'
											sx={{
												margin: 1,
												minWidth: 112,
											}}>
											<InputLabel>Site</InputLabel>
											<Select
												label='Site'
												value={paramsSite}
												onChange={(event) =>
													handleChangeParamsValue(
														'site',
														event.target.value as string
													)
												}>
												<MenuItem value='all'>All</MenuItem>
												{siteList?.map((site) => (
													<MenuItem key={site?.id} value={site?.id}>
														{site?.name}
													</MenuItem>
												))}
											</Select>
										</FormControl>
										<FormControl
											className='form-controller'
											sx={{
												margin: 1,
												minWidth: 112,
											}}>
											<InputLabel id='statusLabel'>Crop</InputLabel>
											<Select
												labelId='statusLabel'
												label='Crop'
												value={paramsCrop}
												onChange={(e) =>
													handleChangeParamsValue(
														'crop',
														e.target.value as string
													)
												}>
												<MenuItem value='all'>All</MenuItem>
												{cropList?.map((crop) => (
													<MenuItem key={crop?.id} value={crop?.id}>
														{crop?.name}
													</MenuItem>
												))}
											</Select>
										</FormControl>
										<FormControl className='form-controller'>
											<LocalizationProvider dateAdapter={AdapterMoment}>
												<DatePicker
													label='Start Date'
													value={
														paramsStartDate ? moment(paramsStartDate) : null
													}
													onChange={(newValue: any) => {
														if (newValue === null || newValue === undefined) {
															handleClearDateFilter('startDate')
															return
														}
														handleChangeParamsValue(
															'startDate',
															moment(new Date(newValue))
																.format('DD-MM-YYYY')
																.toString()
														)
													}}
													format='DD/MM/YYYY'
													className='date-picker'
													slotProps={{
														field: {
															clearable: true,
														},
													}}
												/>
											</LocalizationProvider>
										</FormControl>
										<FormControl className='form-controller'>
											<LocalizationProvider dateAdapter={AdapterMoment}>
												<DatePicker
													label='End Date'
													value={paramsEndDate ? moment(paramsEndDate) : null}
													onChange={(newValue: any) => {
														if (newValue === null || newValue === undefined) {
															handleClearDateFilter('endDate')
															return
														}
														handleChangeParamsValue(
															'endDate',
															moment(new Date(newValue))
																.format('DD-MM-YYYY')
																.toString()
														)
													}}
													format='DD/MM/YYYY'
													className='date-picker'
													slotProps={{
														field: {
															clearable: true,
														},
													}}
												/>
											</LocalizationProvider>
										</FormControl>
									</Stack>
									<Typography
										variant='subtitle2'
										sx={{ color: 'primary.main' }}>
										Total Biochar Qty: {selectedTotalBioChar || 0} ltr
									</Typography>
								</Stack>
							}
						/>
					</Paper>
				</DialogContent>
			</StyledDialog>

			{showConfirmationDialog ? (
				<Confirmation
					open={showConfirmationDialog}
					handleClose={() => setShowConfirmationDialog(false)}
					handleNoClick={() => setShowConfirmationDialog(false)}
					handleYesClick={() => {
						handleCreateMutation()
						setShowConfirmationDialog(false)
					}}
					confirmationText={`Are you sure you want to create stock for the selected ${
						selectedProcesses?.length > 1 ? selectedProcesses?.length : ''
					} ${selectedProcesses?.length > 1 ? 'processes' : 'process'} ?`}
				/>
			) : null}
		</>
	)
}

const HeaderEndComponent: FC<{
	onClick: () => void
	isLoading: boolean
	selectedProcessLength: number
}> = ({ onClick, isLoading, selectedProcessLength }) => {
	return (
		<Stack direction='row' columnGap={2} alignItems='center'>
			<LoadingButton
				onClick={onClick}
				startIcon={<Add fontSize='large' />}
				size='small'
				sx={{ fontSize: 14 }}
				loading={isLoading}
				disabled={isLoading || selectedProcessLength === 0}
				variant='contained'>
				Create Stock
			</LoadingButton>
		</Stack>
	)
}

const StyledDialog = styled(Dialog)(({ theme }) => ({
	'.form-controller': {
		margin: theme.spacing(0.125),
		minWidth: theme.spacing(14),
		'.MuiOutlinedInput-notchedOutline': {
			borderRadius: theme.spacing(1.25),
		},
		'.date-picker': {
			height: theme.spacing(4.5),
			input: {
				width: theme.spacing(10),
				padding: theme.spacing(1.15, 1, 1.15, 2),
				fontSize: theme.typography.caption.fontSize,
			},
			'& .MuiInputAdornment-root': {
				'& .MuiIconButton-root': {
					svg: {
						width: theme.spacing(2.5),
						height: theme.spacing(2.5),
					},
				},
			},
		},
	},
}))
