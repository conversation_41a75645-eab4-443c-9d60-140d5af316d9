import { CustomFileUploader } from '@/components'
import { Confirmation } from '@/components/Confirmation'
import { authAxios } from '@/contexts'
import { theme } from '@/lib/theme/theme'
import { TModal } from '@/types'
import { CancelOutlined } from '@mui/icons-material'
import { LoadingButton } from '@mui/lab'
import {
	Button,
	Dialog,
	DialogActions,
	DialogContent,
	DialogTitle,
	FormControl,
	FormHelperText,
	IconButton,
	Stack,
	Typography,
} from '@mui/material'
import { useMutation, useQueryClient } from '@tanstack/react-query'
import { AxiosError } from 'axios'
import { useState } from 'react'
import { useForm } from 'react-hook-form'
import { toast } from 'react-toastify'
import { TUploadSinkFile, uploadSinkFileScehma } from '../schema'
import { yupResolver } from '@hookform/resolvers/yup'

interface IProps extends TModal {
	sinkId: string
}

export const UploadSinkFileDialog = ({ open, onClose, sinkId }: IProps) => {
	const [confirmUpload, SetConfirmUpload] = useState(false)
	const queryClient = useQueryClient()

	const {
		reset,
		setValue,
		getValues,
		formState: { errors },
		trigger,
	} = useForm<TUploadSinkFile>({
		mode: 'all',
		defaultValues: {
			uploadId: '',
		},
		resolver: yupResolver<TUploadSinkFile>(uploadSinkFileScehma),
	})

	const handleUploadSinkFile = useMutation({
		mutationKey: ['uploadSinkFile'],
		mutationFn: async () => {
			const response = await authAxios.post(
				`/global-csink/sink/${sinkId}/file`,
				getValues()
			)
			return response
		},
		onSuccess: (response) => {
			toast(response?.data?.message)
			queryClient.refetchQueries({
				queryKey: ['sinkList'],
			})
			SetConfirmUpload(false)
			onClose()
			reset
		},
		onError: (err: AxiosError) => {
			toast(
				(err?.response?.data as { messageToUser: string })?.messageToUser ??
				'File Upload Failed'
			)
		},
	})

	const handleUploadFile = async () => {
		const isValid = await trigger()
		if (isValid) SetConfirmUpload(true)
	}
	return (
		<Dialog
			open={open}
			onClose={onClose}
			fullWidth
			maxWidth='sm'
			sx={{
				'& .MuiPaper-root': {
					p: 3,
				},
			}}>
			<IconButton
				sx={{
					position: 'absolute',
					right: 10,
					top: 10,
				}}
				onClick={onClose}>
				<CancelOutlined />
			</IconButton>
			<DialogTitle textAlign='center' variant='h6'>
				Upload Sink File
			</DialogTitle>
			<DialogContent>
				<Stack padding={theme.spacing(1.5)} gap={theme.spacing(2.5)}>
					<FormControl fullWidth>
						<CustomFileUploader
							type='sink_file'
							setUploadData={(data) => {
								setValue('uploadId', data?.id)
							}}
							heading='Upload Sink File'
							required
						/>
						<FormHelperText error={Boolean(errors.uploadId)}>
							{errors?.uploadId?.message}
						</FormHelperText>
					</FormControl>
				</Stack>
			</DialogContent>
			<Confirmation
				confirmationText={
					<Stack
						textAlign='center'
						padding={theme.spacing(2)}
						gap={theme.spacing(2)}>
						<Typography>
							Are you sure you want to upload this sink file?
						</Typography>
					</Stack>
				}
				handleClose={() => SetConfirmUpload(false)}
				handleNoClick={() => SetConfirmUpload(false)}
				handleYesClick={() => handleUploadSinkFile.mutate()}
				open={confirmUpload}
			/>
			<DialogActions sx={{ display: 'flex', justifyContent: 'center' }}>
				<LoadingButton onClick={() => handleUploadFile()}>Upload</LoadingButton>
				<Button onClick={onClose}>Cancel</Button>
			</DialogActions>
		</Dialog>
	)
}
