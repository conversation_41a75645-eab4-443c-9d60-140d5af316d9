import { FormControl, FormHelperText, MenuItem, Stack } from '@mui/material'
import {
	FieldErrors,
	useForm,
	UseFormClearErrors,
	UseFormGetValues,
	UseFormRegister,
	UseFormSetValue,
	UseFormWatch,
} from 'react-hook-form'
import { addCertification, TAddCertification } from '../schema'
import { yupResolver } from '@hookform/resolvers/yup'
import React, { useCallback } from 'react'
import { LocalizationProvider } from '@mui/x-date-pickers/LocalizationProvider'
import { AdapterMoment } from '@mui/x-date-pickers/AdapterMoment'
import { DatePicker } from '@mui/x-date-pickers/DatePicker'
import moment from 'moment'
import { CustomFileUploader } from '@/components'
import { useParams } from 'react-router-dom'
import { LoadingButton } from '@mui/lab'
import { useMutation, useQuery, useQueryClient } from '@tanstack/react-query'
import { authAxios } from '@/contexts'
import { toast } from 'react-toastify'
import { IAssignedCertificate } from '@/interfaces'
import { ILabelWithValue } from '@/types'
import { certificateTypeList } from '@/utils/constant'
import { CustomTextField } from '@/utils/components'
import { AnyObjectSchema } from 'yup'

const initialValues = {
	certificationBodyName: '',
	certificateId: '',
	certificateFile: '',
}

type TProps = {
	onClose: () => void
	submitFor?: 'project' | 'stock'
	stockId?: string
}

export const AddCertificates: React.FC<TProps> = ({
	onClose,
	submitFor = 'project',
	stockId,
}) => {
	const { projectId } = useParams()
	const queryClient = useQueryClient()
	const {
		register,
		setValue,
		watch,
		formState: { errors },
		clearErrors,
		getValues,
		handleSubmit,
	} = useForm<TAddCertification>({
		defaultValues: initialValues,
		mode: 'all',
		resolver: yupResolver<TAddCertification>(addCertification),
	})

	const fieldArray = [
		{
			id: 'internalProjectCertificateId',
			label: 'Certificate Type',
			hidden: false,
			type: 'select',
		},
		{
			id: 'certificationBodyName',
			label: 'Certification Body Name',
			type: 'textField',
		},
		{
			id: 'certificationDate',
			label: 'Certification Date',
			type: 'datePicker',
		},
		{
			id: 'certificateId',
			label: 'Certificate Id',
			type: 'textField',
		},
		{
			id: 'certificateFile',
			label: 'Certificate File',
			type: 'fileUploader',
		},
	]

	const addCertificationToProject = useMutation({
		mutationKey: ['addCertificationToProject', projectId],
		mutationFn: async (values: TAddCertification) => {
			const { certificationDate, ...rest } = values
			const payload = {
				certificationDate: certificationDate
					? new Date(certificationDate)
					: undefined,
				...rest,
			}
			const { data } = await authAxios.put(
				`/internal-project/${projectId}`,
				payload
			)
			return data
		},
		onSuccess: ({ data }) => {
			toast(data?.message)
			queryClient.refetchQueries({ queryKey: ['internalProjectDetails'] })
			onClose()
		},
		onError: (error: any) => {
			toast(error?.response?.data?.messageToUser)
		},
	})

	const getStocksAttachedListQuery = useQuery({
		queryKey: ['getStocksAttachedListQuery', stockId],
		queryFn: () => {
			const data = authAxios.get<{
				certificates: IAssignedCertificate[]
			}>(`/global-csink/stock/${stockId}/certificate`)
			return data
		},
		enabled: !!stockId,
		select: ({ data }) => ({
			certificates: data?.certificates,
			labelValue: data?.certificates?.map((item) => ({
				label:
					certificateTypeList?.find((i) => i.value === item?.type)?.label || '',
				value: item?.id,
			})),
		}),
	})

	const addCertificationToStock = useMutation({
		mutationKey: ['addCertificationToStock', stockId],
		mutationFn: async (values: TAddCertification) => {
			const { certificationDate,certificateUrl, ...rest } = values
			console.log(certificateUrl);
			
			const payload = {
				certificationDate: certificationDate
					? new Date(certificationDate).toISOString()?.split('T')?.[0]
					: undefined,
				...rest,
			}
			const { data } = await authAxios.post(
				`global-csink/stock/${stockId}/attach-certificate`,
				payload
			)
			return data
		},
		onSuccess: ({ data }) => {
			toast(data?.message)
			queryClient.refetchQueries({ queryKey: ['stockList'] })
			onClose()
		},
		onError: (error: any) => {
			toast(error?.response?.data?.messageToUser)
		},
	})

	const handleOnTypeSelect = useCallback(
		(id: string) => {
			setValue('internalProjectCertificateId', id)
			const selectType = getStocksAttachedListQuery?.data?.certificates?.find(
				(item) => item?.id === id
			)
			setValue('certificateFile', selectType?.file?.id || '')
			setValue('certificateId', selectType?.certificateId || '')
			setValue('certificationBodyName', selectType?.bodyName || '')
			setValue('certificationDate', new Date(selectType?.issueDate || ''))
			setValue('certificateUrl', selectType?.file?.url)
			clearErrors()
		},
		[clearErrors, getStocksAttachedListQuery?.data?.certificates, setValue]
	)

	const handleFormSubmit = useCallback(
		(values: TAddCertification) => {
			if (submitFor === 'project') {
				addCertificationToProject.mutate(values)
			} else {
				addCertificationToStock.mutate(values)
			}
		},
		[addCertificationToProject, addCertificationToStock, submitFor]
	)

	return (
		<Stack gap={2}>
			{fieldArray.map((field) => (
				<RenderComponent
					{...field}
					register={register}
					options={getStocksAttachedListQuery?.data?.labelValue}
					setValue={setValue}
					clearErrors={clearErrors}
					errors={errors}
					getValues={getValues}
					watch={watch}
					schema={addCertification}
					handleChaneCertificateType={handleOnTypeSelect}
					key={field.id}
				/>
			))}
			<LoadingButton
				variant='contained'
				fullWidth
				onClick={handleSubmit(handleFormSubmit)}
				loading={addCertificationToProject.isPending}
				disabled={addCertificationToProject.isPending}>
				Add
			</LoadingButton>
		</Stack>
	)
}

type TRenderComponent = {
	type: string
	label: string
	id: string
	register: UseFormRegister<TAddCertification>
	setValue: UseFormSetValue<TAddCertification>
	errors: FieldErrors<TAddCertification>
	clearErrors: UseFormClearErrors<TAddCertification>
	getValues: UseFormGetValues<TAddCertification>
	options?: ILabelWithValue[]
	handleChaneCertificateType?: (id: string) => void
	schema: AnyObjectSchema
	watch: UseFormWatch<TAddCertification>
}

const RenderComponent: React.FC<TRenderComponent> = ({
	type,
	label,
	register,
	id,
	setValue,
	errors,
	clearErrors,
	getValues,
	options,
	handleChaneCertificateType,
	schema,
	watch,
}) => {
	const key = id as keyof TAddCertification
	switch (type) {
		case 'textField':
			return (
				<CustomTextField
					watch={watch}
					schema={schema}
					label={label}
					id={id}
					{...register(key)}
					error={!!errors?.[key]?.message}
					helperText={errors?.[key]?.message}
				/>
			)
		case 'select':
			return (
				<CustomTextField
					watch={watch}
					schema={schema}
					label={label}
					id={id}
					onChange={(e) => handleChaneCertificateType?.(e.target.value)}
					select
					error={!!errors?.[key]?.message}
					helperText={errors?.[key]?.message}>
					{options?.map((i, index) => (
						<MenuItem key={index + i?.value} value={i?.value}>
							{i?.label}
						</MenuItem>
					))}
				</CustomTextField>
			)
		case 'datePicker':
			return (
				<FormControl error={!errors?.[key]?.message}>
					<LocalizationProvider dateAdapter={AdapterMoment}>
						<DatePicker
							label={label}
							value={getValues(key) ? moment(getValues(key)) : null}
							onChange={(newValue) => {
								setValue(key, moment(newValue).set('hour', 11).toDate())

								clearErrors(key)
							}}
							format='DD/MM/YYYY'
						/>
					</LocalizationProvider>
					<FormHelperText error={!!errors?.[key]?.message}>
						{errors?.[key]?.message}
					</FormHelperText>
				</FormControl>
			)
		case 'fileUploader':
			return (
				<FormControl>
					<CustomFileUploader
						imageUrl={watch('certificateUrl')} // hardcoded to show selected files
						heading={label}
						sx={{
							height: { xs: 100, md: 166 },
							width: '100%',
						}}
						imageHeight={100}
						setUploadData={(data) => {
							setValue(key, data?.id as string)
							clearErrors(key)
						}}
					/>
					<FormHelperText error={!!errors?.[key]?.message}>
						{errors?.[key]?.message}
					</FormHelperText>
				</FormControl>
			)
		default:
			return null
	}
}
