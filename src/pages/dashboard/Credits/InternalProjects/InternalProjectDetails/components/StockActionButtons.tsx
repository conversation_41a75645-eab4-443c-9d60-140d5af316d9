import { Confirmation } from '@/components/Confirmation'
import { authAxios } from '@/contexts'
import { AttachFile } from '@mui/icons-material'
import { LoadingButton } from '@mui/lab'
import { Button, Stack, Typography, useTheme } from '@mui/material'
import { useMutation, useQueryClient } from '@tanstack/react-query'
import { useCallback, useState } from 'react'
import { useParams } from 'react-router-dom'
import { toast } from 'react-toastify'

type ConfirmationModal = {
	open: boolean
	title: string
	onYesClick: () => void
	onNoClick: () => void
	onCancelClick: () => void
	isDoubleConfirmationModal: boolean
}

const initialConfirmationModalValues: ConfirmationModal = {
	open: false,
	title: '',
	onYesClick: () => {},
	onNoClick: () => {},
	onCancelClick: () => {},
	isDoubleConfirmationModal: false,
}

export const StockActionButtons: React.FC<{
	stockId: string
	refetch: () => void
	showCreateSinkBtn: boolean
	handleClickCertificate: () => void
	params?: any
	getRowParams: (params: any) => void
	isDeleted?: boolean
	isAdmin?: boolean
}> = ({
	params,
	stockId,
	refetch,
	showCreateSinkBtn,
	handleClickCertificate,
	getRowParams,
	isDeleted,
	isAdmin = false,
}) => {
	const theme = useTheme()
	const { projectId } = useParams()
	const queryClient = useQueryClient()

	const [confirmationModal, setConfirmationModal] = useState<ConfirmationModal>(
		initialConfirmationModalValues
	)

	const createSinkMutation = useMutation({
		mutationKey: ['createSink', projectId, stockId],
		mutationFn: async () => {
			const { data } = await authAxios.post(
				`/global-csink/stock/${stockId}/sink`
			)
			return data
		},
		onSuccess: ({ data }) => {
			if (!data?.length) toast(`Successfully created Sink for Id: ${projectId}`)
			// check with abhishek if we need these conditions
			const successIds = data
				?.filter((i: any) => i?.type === 'success')
				?.map((i: any) => i?.stockId)
			const errorIds = data
				?.filter((i: any) => i?.type === 'error')
				?.map((i: any) => i?.stockId)
			if (successIds?.length > 0) {
				toast(`Successfully created Sink for ids : [${successIds?.toString()}]`)
			}
			if (errorIds?.length > 0) {
				toast(`Failed to created sink for ids : [${errorIds?.toString()}]`)
			}
			refetch()
		},
		onError: (error: any) => {
			toast(error?.response?.data?.messageToUser)
		},
	})

	const handleCreateSinkMutation = useCallback(
		(e: React.MouseEvent<HTMLButtonElement>) => {
			e.stopPropagation()
			createSinkMutation.mutate()
		},
		[createSinkMutation]
	)

	const deleteStockMutation = useMutation({
		mutationKey: ['deleteStock'],
		mutationFn: async () => {
			const { data } = await authAxios.delete(`/global-csink/stock/${stockId}`)
			return data
		},
		onError: (error: any) => {
			toast(error?.response?.data?.messageToUser)
		},
		onSuccess: (data) => {
			toast(data?.message)
			queryClient.refetchQueries({ queryKey: ['stockList'] })
		},
	})

	const deleteStock = useCallback(() => {
		deleteStockMutation.mutate()
	}, [deleteStockMutation])
	
	return (
		<>
			<Stack justifyContent='flex-start' direction='row' columnGap={2}>
				{isDeleted ? (
					<Typography
						sx={{
							fontSize: 14,
							color: theme.palette.primary.main,
							fontWeight: 500,
						}}>
						Deleted
					</Typography>
				) : showCreateSinkBtn ? (
					isAdmin && (
						<LoadingButton
							onClick={handleCreateSinkMutation}
							loading={createSinkMutation?.isPending}
							disabled={createSinkMutation?.isPending}
							sx={{ fontSize: 14 }}>
							Create Sink
						</LoadingButton>
					)
				) : null}

				{params?.row?.sinks?.length === 0 && params?.open ? (
					<Stack
						justifyContent='flex-start'
						alignItems='center'
						direction='row'>
						{params?.editStockBool ? (
							<>
								<LoadingButton
									onClick={(e) => {
										e.stopPropagation()
										setConfirmationModal((prev) => ({
											...prev,
											open: true,
											title: 'Are you sure you want to delete this stock ?',
											onYesClick: () => {
												setConfirmationModal(initialConfirmationModalValues)
												deleteStock()
											},
											onNoClick: () =>
												setConfirmationModal(initialConfirmationModalValues),
											onCancelClick: () =>
												setConfirmationModal(initialConfirmationModalValues),
										}))
									}}
									loading={deleteStockMutation.isPending}
									disabled={deleteStockMutation.isPending}
									sx={{ fontSize: 14 }}>
									Delete
								</LoadingButton>
								<Button
									sx={{ fontSize: 14 }}
									onClick={(e) => {
										e.stopPropagation()
										params?.setEditStockBool(false)
										params?.setSelectedProcess([])
									}}>
									cancel
								</Button>
							</>
						) : (
							<Button
								sx={{ fontSize: 14 }}
								variant='text'
								onClick={(e) => {
									e.stopPropagation()
									params?.setOpen(true)
									params?.setEditStockBool(true)
									getRowParams(params)
								}}>
								Edit
							</Button>
						)}
					</Stack>
				) : null}
				<Button
					onClick={(event) => {
						event.stopPropagation()
						handleClickCertificate()
					}}
					startIcon={
						<AttachFile
							sx={{
								color: theme.palette.neutral[500],
							}}
						/>
					}
				/>
			</Stack>
			{confirmationModal.open &&
			!confirmationModal.isDoubleConfirmationModal ? (
				<Confirmation
					open={confirmationModal.open}
					handleYesClick={confirmationModal.onYesClick}
					handleClose={confirmationModal.onCancelClick}
					handleNoClick={confirmationModal.onNoClick}
					confirmationText={confirmationModal.title}
				/>
			) : null}
			{confirmationModal.open && confirmationModal.isDoubleConfirmationModal ? (
				<Confirmation
					open={confirmationModal.open}
					handleYesClick={confirmationModal.onYesClick}
					handleClose={confirmationModal.onCancelClick}
					handleNoClick={confirmationModal.onNoClick}
					confirmationText={confirmationModal.title}
				/>
			) : null}
		</>
	)
}
