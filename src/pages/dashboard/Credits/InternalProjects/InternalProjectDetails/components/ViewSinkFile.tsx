import { <PERSON><PERSON><PERSON>, TrainingProofRenderer } from '@/components'
import { IMedia, ISinkFile } from '@/interfaces'
import { theme } from '@/lib/theme/theme'
import { dateFormats } from '@/utils/constant'
import { Close, Delete } from '@mui/icons-material'
import { IconButton, Stack, styled, Typography } from '@mui/material'
import { format } from 'date-fns'
import { FC } from 'react'
import { Confirmation } from '@/components/Confirmation'
import { UseMutationResult } from '@tanstack/react-query'
import { AxiosError } from 'axios'

type TProps = {
	onClose: () => void
	sinkFiles?: ISinkFile[]
	showDeleteStockSinkFileConfirmationDialog: string | null
	setShowDeleteStockSinkFileConfirmationDialog: React.Dispatch<
		React.SetStateAction<string | null>
	>
	handleDeleteSinkFileMutation: UseMutationResult<any, AxiosError, string>
}

type TSubProps = {
	sinkFiles?: ISinkFile[]
	showDeleteStockSinkFileConfirmationDialog: string | null
	setShowDeleteStockSinkFileConfirmationDialog: React.Dispatch<
		React.SetStateAction<string | null>
	>
	handleDeleteSinkFileMutation: UseMutationResult<any, AxiosError, string>
}

export const ViewSinkFile: FC<TProps> = ({
	onClose,
	sinkFiles,
	showDeleteStockSinkFileConfirmationDialog,
	setShowDeleteStockSinkFileConfirmationDialog,
	handleDeleteSinkFileMutation,
}) => {
	return (
		<StyledStack>
			<Stack className='header'>
				<Stack
					direction='row'
					spacing={1}
					alignItems='center'
					width='100%'
					justifyContent='space-between'>
					<Typography variant='h4'>Sink File Details</Typography>
					<Stack direction='row' columnGap={2} alignItems='center'>
						<IconButton onClick={onClose}>
							<Close />
						</IconButton>
					</Stack>
				</Stack>
			</Stack>
			<Stack className='container'>
				<ViewsinkFiles
					sinkFiles={sinkFiles ?? []}
					showDeleteStockSinkFileConfirmationDialog={
						showDeleteStockSinkFileConfirmationDialog
					}
					setShowDeleteStockSinkFileConfirmationDialog={
						setShowDeleteStockSinkFileConfirmationDialog
					}
					handleDeleteSinkFileMutation={handleDeleteSinkFileMutation}
				/>
			</Stack>
		</StyledStack>
	)
}

const ViewsinkFiles: FC<TSubProps> = ({
	sinkFiles,
	showDeleteStockSinkFileConfirmationDialog,
	setShowDeleteStockSinkFileConfirmationDialog,
	handleDeleteSinkFileMutation,
}) => {
	if (sinkFiles?.length === 0) return <NoData />

	return (
		<Stack rowGap={6}>
			{sinkFiles?.map((certificate) => (
				<Stack key={certificate?.id} className='file-container'>
					<Stack
						flexDirection='row'
						justifyContent='space-between'
						gap={theme.spacing(3)}>
						<TagComponent
							label='Identification Number'
							value={
								certificate?.identificationCode +
									' ' +
									(certificate?.createdAt
										? '(' +
										  format(certificate?.createdAt, dateFormats.dd_MM_yyyy) +
										  ')'
										: 'Not Mentioned') || ''
							}
						/>
						<IconButton
							onClick={(e) => {
								e.stopPropagation()
								setShowDeleteStockSinkFileConfirmationDialog(certificate?.id)
							}}>
							<Delete />
						</IconButton>
					</Stack>
					<TagComponent
						label='Description'
						className='tag_component-column'
						value={certificate?.description || ''}
					/>

					<Stack rowGap={3}>
						<Typography className='font_size_14 font_weight_500'>
							File:
						</Typography>
						<TrainingProofRenderer
							viewMode='table'
							hideTitle
							ShowDeleteOption={false}
							showInRow
							componentSize={40}
							media={[
								{
									...certificate?.file,
									fileName: certificate?.file?.path,
								} as IMedia,
							]}
						/>
					</Stack>
				</Stack>
			))}
			<Confirmation
				confirmationText='Are you sure you want to delete this sink?'
				open={!!showDeleteStockSinkFileConfirmationDialog}
				handleClose={() => setShowDeleteStockSinkFileConfirmationDialog(null)}
				handleNoClick={() => setShowDeleteStockSinkFileConfirmationDialog(null)}
				handleYesClick={() => {
					handleDeleteSinkFileMutation.mutate(
						showDeleteStockSinkFileConfirmationDialog || ''
					)
				}}
			/>
		</Stack>
	)
}

const TagComponent: FC<{
	label: string
	value: string | number
	className?: string
}> = ({ label, value, className }) => {
	return (
		<Stack className={`tag_component ${className}`}>
			<Typography className='font_size_14 font_weight_500'>{label}:</Typography>
			<Typography className='font_size_14 first_letter_capitalize'>
				{value}
			</Typography>
		</Stack>
	)
}

const StyledStack = styled(Stack)(({ theme }) => ({
	gap: theme.spacing(2),
	'.header': {
		flexDirection: 'row',
		justifyContent: 'space-between',
		alignItems: 'center',
		padding: theme.spacing(4.5, 2),
		borderBottom: `${theme.spacing(0.125)} solid ${
			theme.palette.neutral['100']
		}`,
	},
	'.container': {
		padding: theme.spacing(2, 3),
		gap: theme.spacing(4),
		'.formcontrol': {
			gap: theme.spacing(0.6),

			'.label': {
				color: theme.palette.neutral[500],
			},
		},
		'.buttonContainer button': {
			width: theme.spacing(30),
			height: theme.spacing(4.5),
			padding: theme.spacing(1, 2.5),
		},
		'.file-container': {
			paddingBottom: theme.spacing(1),
			gap: theme.spacing(3),
			borderBottom: `${theme.spacing(0.125)} solid ${
				theme.palette.neutral['100']
			}`,
		},
	},
	'.tag_component': {
		flexDirection: 'row',
		columnGap: 0,
		alignItems: 'center',
	},
	'.tag_component-column': {
		flexDirection: 'column',
		rowGap: theme.spacing(1),
		alignItems: 'flex-start',
	},
	'.font_size_14': {
		fontSize: theme.typography.subtitle2.fontSize,
	},
	'.font_weight_500': {
		fontWeight: 500,
	},
}))
