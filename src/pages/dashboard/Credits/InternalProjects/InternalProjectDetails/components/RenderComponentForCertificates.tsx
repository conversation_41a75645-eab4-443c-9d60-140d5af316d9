import {
	FieldErrors,
	FieldValues,
	UseFormClearErrors,
	UseFormRegister,
	UseFormSetValue,
} from 'react-hook-form'
import {
	TAddMultipleCertificateBody,
	TAddMultipleCertification,
} from '../schema'

import { LocalizationProvider } from '@mui/x-date-pickers/LocalizationProvider'
import { AdapterMoment } from '@mui/x-date-pickers/AdapterMoment'
import { DatePicker } from '@mui/x-date-pickers/DatePicker'
import moment, { MomentInput } from 'moment'
import { CustomFileUploader } from '@/components'
import { ILabelWithValue } from '@/types'
import { TextField, MenuItem, FormControl, FormHelperText } from '@mui/material'

type TRenderComponent = {
	type: string
	label: string
	id: string
	register: UseFormRegister<FieldValues>
	setValue: UseFormSetValue<FieldValues>
	errors: FieldErrors<TAddMultipleCertification>
	clearErrors: UseFormClearErrors<TAddMultipleCertification>
	getValues: UseFormRegister<FieldValues>
	options?: ILabelWithValue[]
	fieldIndex: number
}

export const RenderComponentForCertificates: React.FC<TRenderComponent> = ({
	type,
	label,
	register,
	id,
	setValue,
	errors,
	clearErrors,
	options,
	getValues,
	fieldIndex,
}) => {
	const keyOfCertificate: keyof TAddMultipleCertificateBody =
		id as keyof TAddMultipleCertificateBody
	switch (type) {
		case 'textField':
			return (
				<TextField
					label={label}
					id={id}
					{...register(`certificates.${fieldIndex}.${keyOfCertificate}`)}
					error={
						!!errors?.certificates?.[fieldIndex]?.[keyOfCertificate]?.message
					}
					helperText={
						errors?.certificates?.[fieldIndex]?.[keyOfCertificate]?.message
					}
				/>
			)
		case 'select':
			return (
				<TextField
					label={label}
					id={id}
					select
					{...register(`certificates.${fieldIndex}.${keyOfCertificate}`)}
					error={
						!!errors?.certificates?.[fieldIndex]?.[keyOfCertificate]?.message
					}
					helperText={
						errors?.certificates?.[fieldIndex]?.[keyOfCertificate]?.message
					}>
					{options?.map((i, index) => (
						<MenuItem key={index + i?.value} value={i?.value}>
							{i?.label}
						</MenuItem>
					))}
				</TextField>
			)
		case 'datePicker':
			return (
				<FormControl
					error={
						!!errors?.certificates?.[fieldIndex]?.[keyOfCertificate]?.message
					}>
					<LocalizationProvider dateAdapter={AdapterMoment}>
						<DatePicker
							label={label}
							value={
								getValues(`certificates.${fieldIndex}.${keyOfCertificate}`)
									? moment(
											getValues(
												`certificates.${fieldIndex}.${keyOfCertificate}`
											) as MomentInput
									  )
									: null
							}
							onChange={(newValue) => {
								setValue(
									`certificates.${fieldIndex}.${keyOfCertificate}`,
									moment(newValue).set('hour', 11).toDate()
								)

								clearErrors(`certificates.${fieldIndex}.${keyOfCertificate}`)
							}}
							format='DD/MM/YYYY'
						/>
					</LocalizationProvider>
					<FormHelperText
						error={
							!!errors?.certificates?.[fieldIndex]?.[keyOfCertificate]?.message
						}>
						{errors?.certificates?.[fieldIndex]?.[keyOfCertificate]?.message}
					</FormHelperText>
				</FormControl>
			)
		case 'fileUploader':
			return (
				<FormControl>
					<CustomFileUploader
						heading={label}
						sx={{
							height: { xs: 100, md: 166 },
							width: '100%',
						}}
						imageHeight={100}
						setUploadData={(data) => {
							setValue(
								`certificates.${fieldIndex}.${keyOfCertificate}`,
								data?.id
							)
							clearErrors(`certificates.${fieldIndex}.${keyOfCertificate}`)
						}}
					/>
					<FormHelperText
						error={
							!!errors?.certificates?.[fieldIndex]?.[keyOfCertificate]?.message
						}>
						{errors?.certificates?.[fieldIndex]?.[keyOfCertificate]?.message}
					</FormHelperText>
				</FormControl>
			)
		default:
			return null
	}
}
