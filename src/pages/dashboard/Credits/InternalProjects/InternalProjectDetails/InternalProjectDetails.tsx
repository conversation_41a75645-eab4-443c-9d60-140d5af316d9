import {
	Add,
	ArrowLeftRounded,
	AttachFile,
	Delete,
	Edit,
	Refresh,
} from '@mui/icons-material'
import {
	Box,
	Button,
	FormControl,
	IconButton,
	InputLabel,
	MenuItem,
	Select,
	Stack,
	styled,
	Tooltip,
	Typography,
	useTheme,
} from '@mui/material'
import { FC, useCallback, useMemo, useState } from 'react'
import { useInternalProjectDetails } from './useInternalProjectDetails'
import { format } from 'date-fns'
import Process from '../../../../../assets/icons/process.svg'
import Stocks from '../../../../../assets/icons/firepot.svg'
import Sinks from '../../../../../assets/icons/hexgon.svg'
import {
	GridColDef,
	GridEventListener,
	GridValidRowModel,
} from '@mui/x-data-grid'
import {
	ActionInformationDrawer,
	AddInternalProject,
	CustomChip,
	CustomDataGrid,
} from '@/components'
import {
	IProcess,
	ISink,
	IStock,
	KilnProcessList,
	TCertificateDetailForStock,
	TCertificateDetails,
} from '@/interfaces'
import { LocalizationProvider } from '@mui/x-date-pickers/LocalizationProvider'
import { AdapterMoment } from '@mui/x-date-pickers/AdapterMoment'
import { DatePicker } from '@mui/x-date-pickers/DatePicker'
import { NavigateFunction, useSearchParams } from 'react-router-dom'
import moment from 'moment'
import { CustomTable } from '@/components/CustomTable'
import { LoadingButton } from '@mui/lab'
import { CreateStocksForAvailableProcessesDialog } from './components/CreateStocksForAvailableProcessesDialog'
import { ViewOrAddCertificates } from './components/ViewOrAddCertificates'
import {
	SinkActionButtons,
	StockActionButtons,
	ViewSinkFile,
} from './components'
import { Confirmation } from '@/components/Confirmation'
import { getSerialNumber } from '@/utils/helper'
import { UseMutationResult } from '@tanstack/react-query'
import { AxiosError } from 'axios'
import { defaultLimit } from '@/utils/constant'
import { useAuthContext } from '@/contexts'

const chipStatusClass: { [key: string]: string } = {
	Approved: 'approved',
	Rejected: 'rejected',
	Pending: 'pending',
	Deleted: 'deleted',
}

export enum StockStatusType {
	Approved = 'Approved',
	Rejected = 'Rejected',
	Pending = 'Pending',
}

type TCertificationDrawerProps = {
	mode: 'view' | 'upload'
	open: boolean
	certificates: TCertificateDetails[]
	for: 'project' | 'stock'
	stockId?: string
}

const initialValue: TCertificationDrawerProps = {
	mode: 'view',
	open: false,
	certificates: [],
	for: 'project',
}

const getDeleteStockConfirmationText = (
	status: StockStatusType | undefined
) => {
	switch (status) {
		case StockStatusType.Approved:
		case StockStatusType.Rejected:
			return 'Are you sure you want to delete the stock? If yes then this stock will only be deleted from the Circonomy only.'
		default:
			return 'Are you sure you want to delete this stock?'
	}
}

export const InternalProjectDetails = () => {
	const theme = useTheme()
	const { userDetails } = useAuthContext()
	const isAdmin = useMemo(() => {
		return userDetails?.accountType === 'admin'
	}, [userDetails?.accountType])

	const {
		internalProjectDetails,
		handleBack,
		paramsTab,
		handleTabChange,
		getSinkList,
		rowCount,
		paramsLimit,
		paramsPage,
		getSiteList,
		getProcessList,
		getStocksList,
		getCropList,
		selectedIdsForProcessTab,
		handleCreateMutation,
		createStockMutation,
		navigate,
		getRowParams,
		showSinkFileDetails,
		setShowSinkFileDetails,
		handleSinkRowClick,
		openEditDrawer,
		setOpenEditDrawer,
		getInternalProjectDetails,
		showDeleteStockConfirmationDialog,
		handleDeleteStockMutation,
		setShowDeleteStockConfirmationDialog,
		showSinkFileDetailsMemo,
		showDeleteStockSinkFileConfirmationDialog,
		setShowDeleteStockSinkFileConfirmationDialog,
		handleDeleteSinkFileMutation,
		useGetKilnProcessList,
		useStockOrSinkStatusMutation,
	} = useInternalProjectDetails()
	const handleStockOrSinkMutation = useStockOrSinkStatusMutation()
	const [showAvailableProcessesDialog, setShowAvailableProcessesDialog] =
		useState<boolean>(false)
	const [certificationDrawerDetail, setCertificationDrawerDetail] =
		useState<TCertificationDrawerProps>(initialValue)
	const [searchParams] = useSearchParams()

	const limit = searchParams.get('limit') ?? defaultLimit

	const projectDetails = useMemo(
		() => [
			{
				label: 'Project Name',
				value: internalProjectDetails?.name,
			},
			{
				label: 'Project ID',
				value: internalProjectDetails?.projectID,
			},
			{
				label: 'Comment',
				value: internalProjectDetails?.comments,
			},
		],
		[
			internalProjectDetails?.comments,
			internalProjectDetails?.name,
			internalProjectDetails?.projectID,
		]
	)

	const tabs = [
		{
			label: 'Process',
			value: 'process',
			imageSrc: Process,
		},
		{
			label: 'Stocks',
			value: 'stocks',
			imageSrc: Stocks,
		},
		{
			label: 'Sinks',
			value: 'sinks',
			imageSrc: Sinks,
		},
	]

	const processColumn: GridColDef<GridValidRowModel>[] = useMemo(
		() => [
			{
				field: 'id',
				headerName: 'S. No',
				minWidth: 60,
				flex: 1,
				renderCell: (params) => {
					return (
						<Typography variant='subtitle1'>
							{getSerialNumber(params, Number(limit))}
						</Typography>
					)
				},
			},
			{
				field: 'kilnProcessCreatedAt',
				headerName: 'Date',
				minWidth: 150,
				flex: 1,
				renderCell: (params) => (
					<Typography variant='subtitle1'>
						{format(new Date(params?.value), 'yyyy-MM-dd')}
					</Typography>
				),
			},
			{
				field: 'bioCharQuantity',
				headerName: 'Biochar Qty',
				minWidth: 100,
				flex: 1,
				renderCell: (params) => (
					<Typography variant='subtitle1'>{params?.value}</Typography>
				),
			},
			{
				field: 'totalSinkQuantity',
				headerName: 'Sinked Biochar Qty',
				minWidth: 150,
				flex: 1,
				renderCell: (params) => (
					<Typography variant='subtitle1'>
						{params?.value?.toFixed(2)}
					</Typography>
				),
			},
			{
				field: 'siteName',
				headerName: 'Site Name',
				minWidth: 150,
				flex: 1,
				renderCell: (params) => (
					<Typography variant='subtitle1'>{params?.value || '-'}</Typography>
				),
			},
			{
				field: 'cropName',
				headerName: 'Biomass Type',
				minWidth: 150,
				flex: 1,
				renderCell: (params) => (
					<Typography variant='subtitle1'>{params?.value}</Typography>
				),
			},
			{
				field: 'canSink',
				headerName: 'Can Sink',
				minWidth: 150,
				flex: 1,
				renderCell: (params) => {
					let canSink = true
					if (
						params.row.bioCharQuantity * 0.99 >
							params?.row?.totalSinkQuantity ||
						params.row.bioCharQuantity + params.row.bioCharQuantity * 0.01 <
							params?.row?.totalSinkQuantity
					) {
						canSink = false
					}

					return <Typography>{canSink ? 'True' : 'False'}</Typography>
				},
			},
			{
				field: 'kilnProcessStatus',
				headerName: 'Batch Status',
				minWidth: 150,
				flex: 1,
				renderCell: (params) => (
					<Typography variant='subtitle1' textTransform='capitalize'>
						{params?.value?.replace(/[-_]/g, ' ')}
					</Typography>
				),
			},
			{
				field: 'stockId',
				headerName: 'Stock ID',
				minWidth: 150,
				flex: 1,
				renderCell: (params) => (
					<Typography variant='subtitle1'>{params?.value || '-'}</Typography>
				),
			},
		],
		[paramsLimit, paramsPage, limit]
	)

	const sinkColumns: GridColDef<GridValidRowModel>[] = useMemo(
		() => [
			{
				field: 'id',
				headerName: 'Sink ID',
				minWidth: 100,
				flex: 1,
				renderCell: (params) => (
					<Typography variant='subtitle1'>{params?.value}</Typography>
				),
			},
			{
				field: 'stockId',
				headerName: 'Stock ID',
				minWidth: 100,
				flex: 1,
				renderCell: (params) => (
					<Typography variant='subtitle1'>{params?.value}</Typography>
				),
			},
			{
				field: 'biocharQuantity',
				headerName: 'Quantity (tonnes)',
				minWidth: 150,
				flex: 1,
				renderCell: (params) => (
					<Typography variant='subtitle1'>{params?.value}</Typography>
				),
			},

			{
				field: 'blendingMatrixId',
				headerName: 'Matrix ID',
				minWidth: 150,
				flex: 1,
				renderCell: (params) =>
					(params?.value ?? [])?.length > 0 ? (
						<Stack direction='column'>
							{params?.value?.map((i: string) => (
								<Typography variant='subtitle1' key={i}>
									{i}
								</Typography>
							))}
						</Stack>
					) : (
						<Typography variant='subtitle1'>-</Typography>
					),
			},
			{
				field: 'createdAt',
				headerName: 'Created At',
				minWidth: 150,
				flex: 1,
				renderCell: (params) => (
					<Typography variant='subtitle1'>
						{format(new Date(params?.value), 'yyyy-MM-dd')}
					</Typography>
				),
			},
			{
				field: 'sinkStatus',
				headerName: 'Status',
				minWidth: 100,
				flex: 1,
				renderCell: (params) => (
					<CustomChip
						appliedClass={chipStatusClass[params?.value]}
						label={params?.value}
					/>
				),
			},
			{
				field: 'action',
				headerName: 'Action',
				minWidth: 150,
				flex: 1,
				renderCell: (params) => (
					<SinkActionButtons
						sinkId={params?.row?.id}
						refetchApi={getSinkList.refetch}
						sinkStatus={params?.row?.sinkStatus}
						isDeleted={params?.row?.isDeleted}
						isAdmin={isAdmin}
					/>
				),
			},
		],
		[getSinkList.refetch]
	)

	const handleViewStockCertificates = useCallback((params: any) => {
		setCertificationDrawerDetail({
			open: true,
			certificates: (
				(params?.row?.certificates as TCertificateDetailForStock[]) ?? []
			)?.map(({ certificateFile, ...restCertificateDetails }) => ({
				...restCertificateDetails,
				certificate: certificateFile,
			})),
			mode: 'view',
			for: 'stock',
			stockId: params?.row?.stockId,
		})
	}, [])

	const networkDetails = useMemo(() => {
		let network = []
		internalProjectDetails?.artisanProDetails?.length &&
			network.push(...internalProjectDetails?.artisanProDetails)
		internalProjectDetails?.csinkNetworkDetails?.length &&
			network.push(...internalProjectDetails?.csinkNetworkDetails)
		return network
	}, [
		internalProjectDetails?.csinkNetworkDetails,
		internalProjectDetails?.artisanProDetails,
	])

	const stockColumns: GridColDef<GridValidRowModel>[] = useMemo(
		() => [
			{
				field: 'code',
				headerName: 'Stock Code',
				minWidth: 100,
				flex: 1,
				renderCell: (params) => (
					<Typography variant='subtitle1'>{params?.value}</Typography>
				),
			},
			{
				field: 'stockId',
				headerName: 'Stock ID',
				minWidth: 100,
				flex: 1,
				renderCell: (params) => {
					const status = params?.row?.status
					const chipClass = chipStatusClass[status]
					return (
						<CustomChip
							appliedClass={`${chipClass}  fontWeight`}
							label={params?.value}
						/>
					)
				},
			},

			{
				field: 'bioCharQuantity',
				headerName: 'Quantity (tonnes)',
				minWidth: 100,
				flex: 1,
				renderCell: (params) => (
					<Typography variant='subtitle1'>{params?.value}</Typography>
				),
			},

			{
				field: 'carbonCredits',
				headerName: 'C-Qty (tCO2)',
				minWidth: 150,
				flex: 1,
				renderCell: (params) => (
					<Typography variant='subtitle1'>{params?.value}</Typography>
				),
			},
			{
				field: 'cropName',
				headerName: 'Biomass Type',
				minWidth: 150,
				flex: 1,
				renderCell: (params) => (
					<Typography variant='subtitle1'>{params?.value}</Typography>
				),
			},

			{
				field: 'productionDate',
				headerName: 'Date of Production',
				minWidth: 150,
				flex: 1,
				renderCell: (params) => (
					<Typography variant='subtitle1'>
						{format(new Date(params?.value), 'yyyy-MM-dd')}
					</Typography>
				),
			},
			{
				field: 'createdAt',
				headerName: 'Created At',
				minWidth: 150,
				flex: 1,
				renderCell: (params) => (
					<Typography variant='subtitle1'>
						{format(new Date(params?.value), 'yyyy-MM-dd')}
					</Typography>
				),
			},
			{
				field: 'actions',
				headerName: 'Actions',
				minWidth: 150,
				flex: 1,
				renderCell: (params) => {
					const isStockDeleted = params?.row?.isDeleted

					return (
						<Stack direction='row'>
							{!isStockDeleted && isAdmin && (
								<IconButton
									onClick={(e) => {
										e.stopPropagation()
										setShowDeleteStockConfirmationDialog({
											id: params?.row?.stockId,
											status: params?.row?.status,
										})
									}}>
									<Delete />
								</IconButton>
							)}
							<StockActionButtons
								params={params}
								stockId={params?.row?.stockId}
								refetch={() => getStocksList?.refetch()}
								showCreateSinkBtn={params?.row?.canSinkStock}
								handleClickCertificate={() =>
									handleViewStockCertificates(params)
								}
								getRowParams={getRowParams}
								isDeleted={isStockDeleted}
								isAdmin={isAdmin}
							/>
						</Stack>
					)
				},
			},
		],
		[
			getRowParams,
			getStocksList,
			handleViewStockCertificates,
			setShowDeleteStockConfirmationDialog,
			isAdmin,
		]
	)

	const handleCreateStockClick = useCallback(() => {
		if (selectedIdsForProcessTab?.length > 0) {
			handleCreateMutation()
			return
		}
		setShowAvailableProcessesDialog(true)
	}, [handleCreateMutation, selectedIdsForProcessTab?.length])

	const handleViewOrAttachProjectCertificate = useCallback(() => {
		setCertificationDrawerDetail({
			open: true,
			certificates: internalProjectDetails?.certificates?.length
				? internalProjectDetails?.certificates?.map((item) => {
						return {
							certificate: {
								type: item?.type,
								path: item?.file?.path,
								url: item?.file?.url,
							},
							certificateId: item?.certificateId,
							certificationBodyName: item?.bodyName,
							certificationDate: item?.issueDate,
						}
				  })
				: [],
			mode: internalProjectDetails?.certificates?.length ? 'view' : 'upload',
			for: 'project',
			stockId: '',
		})
	}, [internalProjectDetails])

	return (
		<>
			<ActionInformationDrawer
				open={certificationDrawerDetail.open}
				onClose={() => setCertificationDrawerDetail(initialValue)}
				anchor='right'
				component={
					<ViewOrAddCertificates
						mode={certificationDrawerDetail.mode}
						modal={certificationDrawerDetail.for}
						onClose={() =>
							setCertificationDrawerDetail((prev) => ({
								...initialValue,
								mode: prev.mode,
								for: prev.for,
								stockId: '',
							}))
						}
						certificates={certificationDrawerDetail.certificates ?? []}
						stockId={certificationDrawerDetail.stockId}
						isAdmin={isAdmin}
					/>
				}
			/>
			<ActionInformationDrawer
				open={!!showSinkFileDetails}
				onClose={() => setShowSinkFileDetails(null)}
				anchor='right'
				component={
					<ViewSinkFile
						onClose={() => setShowSinkFileDetails(null)}
						sinkFiles={showSinkFileDetailsMemo || []}
						showDeleteStockSinkFileConfirmationDialog={
							showDeleteStockSinkFileConfirmationDialog
						}
						setShowDeleteStockSinkFileConfirmationDialog={
							setShowDeleteStockSinkFileConfirmationDialog
						}
						handleDeleteSinkFileMutation={handleDeleteSinkFileMutation}
					/>
				}
			/>
			<ActionInformationDrawer
				open={!!openEditDrawer}
				onClose={() => setOpenEditDrawer(null)}
				anchor='right'
				component={
					<AddInternalProject
						projectDetails={openEditDrawer}
						setIsActionInfoDrawer={() => setOpenEditDrawer(null)}
						cb={() => {
							setOpenEditDrawer(null)
							getInternalProjectDetails?.refetch()
						}}
						isEdit
						isProjectCodeHidden={
							internalProjectDetails?.code !== null ? true : false
						}
					/>
				}
			/>
			<Confirmation
				confirmationText={getDeleteStockConfirmationText(
					showDeleteStockConfirmationDialog?.status
				)}
				open={!!showDeleteStockConfirmationDialog}
				handleClose={() => setShowDeleteStockConfirmationDialog(null)}
				handleNoClick={() => setShowDeleteStockConfirmationDialog(null)}
				handleYesClick={() => {
					handleDeleteStockMutation.mutate(
						showDeleteStockConfirmationDialog?.id || ''
					)
				}}
				isLoading={handleDeleteStockMutation.isPending || false}
			/>

			{showAvailableProcessesDialog ? (
				<CreateStocksForAvailableProcessesDialog
					cropList={getCropList?.data?.data || []}
					siteList={getSiteList?.data?.data || []}
					open={showAvailableProcessesDialog}
					onClose={() => setShowAvailableProcessesDialog(false)}
				/>
			) : null}
			<StyledContained>
				<Stack className='header-navigation'>
					<Button
						onClick={handleBack}
						className='batch-button'
						variant='text'
						startIcon={<ArrowLeftRounded fontSize='small' />}>
						Projects
					</Button>
					<Typography variant='body1' color={theme.palette.neutral['500']}>
						&nbsp;/ {internalProjectDetails?.name}
					</Typography>
				</Stack>
				<Stack className='container'>
					{isAdmin && (
						<Box className='editContainer'>
							<IconButton
								onClick={() =>
									setOpenEditDrawer(internalProjectDetails || null)
								}>
								<Edit color='primary' />
							</IconButton>
						</Box>
					)}
					<Box className='project-details'>
						{projectDetails.map((detail) => (
							<Stack key={detail.label} className='info-container'>
								<Typography className='label'>{detail.label}</Typography>
								<Typography className='value'>{detail.value || '-'}</Typography>
							</Stack>
						))}
						<Stack className='info-container'>
							<Typography className='label'>Certificate</Typography>
							<Button
								className='btn'
								variant='text'
								onClick={handleViewOrAttachProjectCertificate}
								startIcon={
									<AttachFile sx={{ color: theme.palette.neutral[500] }} />
								}>
								{internalProjectDetails?.certificates?.length
									? internalProjectDetails?.certificates?.[0]?.type
									: 'Attach'}
							</Button>
						</Stack>
						<Stack className='info-container'>
							<Typography className='label'>Csink Manager:</Typography>
							<Typography className='value'>
								{internalProjectDetails?.csinkManagerName}
							</Typography>
						</Stack>
						<Stack className='info-container'>
							<Typography className='label'>Networks:</Typography>
							<Tooltip
								title={
									<Typography>
										{networkDetails?.map((i) => i?.name)?.join(', ')}{' '}
									</Typography>
								}
								placement='bottom'>
								<Stack flexDirection='column'>
									{networkDetails?.slice(0, 2).map((i, index) => (
										<Typography key={`networkName-${index}`}>
											{i?.name}
										</Typography>
									))}{' '}
								</Stack>
							</Tooltip>
						</Stack>
						<Stack className='info-container'>
							<Typography className='label'>Project Code</Typography>
							<Typography className='value'>
								{internalProjectDetails?.code
									? internalProjectDetails?.code
									: '-'}
							</Typography>
						</Stack>
					</Box>
					<Stack className='tab-container'>
						<Stack className='tab-list'>
							{tabs.map((tab) => (
								<Stack
									key={tab.value}
									onClick={() => handleTabChange(tab.value)}
									component={Button}
									id={tab.value}
									className={`tab ${
										paramsTab === tab.value ? 'active-tab' : ''
									}`}>
									<Box component='img' src={tab.imageSrc} className='tab-img' />
									<Typography className='tab-label'>{tab.label}</Typography>
								</Stack>
							))}
						</Stack>
						<Stack className='tab-panel'>
							<RenderTabPanel
								navigate={navigate}
								panelValue={paramsTab}
								sink={{
									rows: getSinkList?.data ?? [],
									columns: sinkColumns,
									onRowClick: handleSinkRowClick,
									loading: getSinkList?.isLoading,
								}}
								process={{
									rows: getProcessList?.data ?? [],
									columns: processColumn,
									loading: getProcessList?.isLoading,
								}}
								stock={{
									rows: getStocksList?.data ?? [],
									columns: stockColumns,
									isLoading:
										getStocksList?.isLoading || getStocksList?.isFetching,
								}}
								rowCount={rowCount}
								siteList={getSiteList?.data?.data ?? []}
								cropList={getCropList?.data?.data ?? []}
								onCreateStockClick={handleCreateStockClick}
								isCreateStockApiLoading={createStockMutation.isPending ?? false}
								useGetKilnProcessList={useGetKilnProcessList}
								onStockOrSinkRefresh={handleStockOrSinkMutation}
								isAdmin={isAdmin}
							/>
						</Stack>
					</Stack>
				</Stack>
			</StyledContained>
		</>
	)
}

type TTabCommonProps<T> = {
	rows: T[]
	loading: boolean
	columns: GridColDef<GridValidRowModel>[]
	onRowClick?: GridEventListener<'rowClick'>
}

type TRenderTabPanel = {
	panelValue: string
	sink: TTabCommonProps<ISink>
	process: TTabCommonProps<IProcess>
	stock: Omit<TTabCommonProps<IStock>, 'loading'> & { isLoading: boolean }
	rowCount: number
	onCreateStockClick: () => void
	isCreateStockApiLoading: boolean
	navigate: NavigateFunction
	useGetKilnProcessList: UseMutationResult<KilnProcessList, Error, string>
	onStockOrSinkRefresh: UseMutationResult<
		{ message: string },
		AxiosError,
		'stock' | 'sink'
	>
	isAdmin?: boolean
} & TProcessHeaderStartComponent

type IdAndNameType = { id: string; name: string }

type TProcessHeaderStartComponent = {
	siteList: IdAndNameType[]
	cropList: IdAndNameType[]
}

const RenderTabPanel: FC<TRenderTabPanel> = ({
	panelValue,
	sink,
	rowCount,
	process,
	siteList,
	stock,
	cropList,
	onCreateStockClick,
	isCreateStockApiLoading,
	navigate,
	useGetKilnProcessList,
	onStockOrSinkRefresh,
	isAdmin = false,
}) => {
	const [isRowIndexLoading, setIsRowIndexLoading] = useState<number[]>([])
	const handleRowClick = useCallback(
		(params: any) => {
			if (params.row?.isDeleted) return
			params.setOpen(!params?.open)
			setIsRowIndexLoading((prev) => [...prev, params?.row?.id])

			useGetKilnProcessList.mutate(params?.row?.stockId, {
				onSuccess: (data: KilnProcessList) => {
					const collapseDetails = {
						kilnProcess: data?.process,
						stockId: params?.row?.stockId,
					}
					params.setHookData(collapseDetails)
					setIsRowIndexLoading((prev) =>
						prev.filter((id) => id !== params?.row?.id)
					)
				},
			})
		},
		[useGetKilnProcessList]
	)
	const handleProcessRowClick: GridEventListener<'rowClick'> = useCallback(
		(params) => {
			navigate(`/dashboard/production/batches/${params?.id}/details`)
		},
		[navigate]
	)

	switch (panelValue) {
		case 'process':
			return (
				<CustomDataGrid
					{...process}
					showPagination
					rowCount={rowCount}
					onRowClick={handleProcessRowClick}
					headerEndComponent={
						isAdmin && (
							<ProcessHeaderEndComponent
								onClick={onCreateStockClick}
								isLoading={isCreateStockApiLoading}
								startIcon={<Add fontSize='small' />}
								label='Create Stock'
							/>
						)
					}
					headerComponent={
						<ProcessHeaderStartComponent
							siteList={siteList}
							cropList={cropList}
						/>
					}
				/>
			)
		case 'stocks':
			return (
				<CustomTable
					{...stock}
					isRowIndexLoading={isRowIndexLoading}
					count={rowCount}
					showPagination
					showPaginationDetails
					isComponent
					component='stock_process_details'
					handleRowClick={handleRowClick}
					headerEndComponent={
						<ProcessHeaderEndComponent
							onClick={() => onStockOrSinkRefresh.mutate('stock')}
							isLoading={onStockOrSinkRefresh.isPending}
							startIcon={<Refresh fontSize='small' />}
							label='Refresh the status'
						/>
					}
				/>
			)
		case 'sinks':
			return (
				<CustomDataGrid
					{...sink}
					showPagination
					rowCount={rowCount}
					headerEndComponent={
						<ProcessHeaderEndComponent
							onClick={() => onStockOrSinkRefresh.mutate('sink')}
							isLoading={onStockOrSinkRefresh.isPending}
							startIcon={<Refresh fontSize='small' />}
							label='Refresh the status'
						/>
					}
				/>
			)
		default:
			return null
	}
}

const ProcessHeaderEndComponent: FC<{
	onClick: () => void
	isLoading: boolean
	startIcon: React.ReactNode
	label: string
}> = ({ onClick, isLoading, startIcon, label }) => (
	<LoadingButton
		onClick={onClick}
		startIcon={startIcon}
		size='small'
		sx={{ fontSize: 14 }}
		loading={isLoading}
		disabled={isLoading}
		variant='contained'>
		{label}
	</LoadingButton>
)

const ProcessHeaderStartComponent: FC<TProcessHeaderStartComponent> = ({
	siteList,
	cropList,
}) => {
	const [searchParams, setSearchParams] = useSearchParams()
	const paramsSite = searchParams.get('site') || 'all'
	const paramsStartDate =
		searchParams.get('startDate') === null
			? ''
			: moment(searchParams.get('startDate'), 'DD-MM-YYYY').format('YYYY-MM-DD')

	const paramsEndDate =
		searchParams.get('endDate') === null
			? null
			: moment(searchParams.get('endDate'), 'DD-MM-YYYY').format('YYYY-MM-DD')
	const paramsCrop = searchParams.get('crop') || 'all'
	const paramsStockCreated = searchParams.get('stockCreated') || 'true'

	const handleChangeParamsValue = useCallback(
		(key: string, value: string) => {
			setSearchParams(
				(prev) => {
					if (value === 'true' || value === 'all') {
						prev.delete(key)
					} else {
						prev.set(key, value)
					}
					prev.delete('limit')
					prev.delete('page')
					return prev
				},
				{ replace: true }
			)
		},
		[setSearchParams]
	)

	const handleClearDateFilter = useCallback(
		(key: 'startDate' | 'endDate') => {
			setSearchParams(
				(prev) => {
					prev.delete(key)
					prev.delete('limit')
					prev.delete('page')
					return prev
				},
				{ replace: true }
			)
		},
		[setSearchParams]
	)

	return (
		<Stack className='grid-header-component' columnGap={1}>
			<FormControl className='form-controller'>
				<InputLabel>Site</InputLabel>
				<Select
					label='Site'
					value={paramsSite}
					onChange={(event) =>
						handleChangeParamsValue('site', event.target.value as string)
					}>
					<MenuItem value='all'>All</MenuItem>
					{siteList?.map((site) => (
						<MenuItem key={site?.id} value={site?.id}>
							{site?.name}
						</MenuItem>
					))}
				</Select>
			</FormControl>
			<FormControl className='form-controller'>
				<InputLabel id='statusLabel'>Crop</InputLabel>
				<Select
					labelId='statusLabel'
					label='Crop'
					value={paramsCrop}
					onChange={(e) =>
						handleChangeParamsValue('crop', e.target.value as string)
					}>
					<MenuItem value='all'>All</MenuItem>
					{cropList?.map((crop) => (
						<MenuItem key={crop?.id} value={crop?.id}>
							{crop?.name}
						</MenuItem>
					))}
				</Select>
			</FormControl>
			<FormControl className='form-controller'>
				<InputLabel id='statusLabel'>Stock Created</InputLabel>
				<Select
					labelId='statusLabel'
					label='Stock Created'
					value={paramsStockCreated}
					onChange={(e) =>
						handleChangeParamsValue('stockCreated', e.target.value as string)
					}>
					<MenuItem value='true'>All</MenuItem>
					<MenuItem value='false'>No</MenuItem>
				</Select>
			</FormControl>
			<FormControl className='form-controller'>
				<LocalizationProvider dateAdapter={AdapterMoment}>
					<DatePicker
						label='Start Date'
						value={paramsStartDate ? moment(paramsStartDate) : null}
						onChange={(newValue: any) => {
							if (newValue === null || newValue === undefined) {
								handleClearDateFilter('startDate')
								return
							}
							handleChangeParamsValue(
								'startDate',
								moment(new Date(newValue)).format('DD-MM-YYYY').toString()
							)
						}}
						format='DD/MM/YYYY'
						className='date-picker'
						slotProps={{
							field: {
								clearable: true,
							},
						}}
					/>
				</LocalizationProvider>
			</FormControl>
			<FormControl className='form-controller'>
				<LocalizationProvider dateAdapter={AdapterMoment}>
					<DatePicker
						label='End Date'
						value={paramsEndDate ? moment(paramsEndDate) : null}
						onChange={(newValue: any) => {
							if (newValue === null || newValue === undefined) {
								handleClearDateFilter('endDate')
								return
							}
							handleChangeParamsValue(
								'endDate',
								moment(new Date(newValue)).format('DD-MM-YYYY').toString()
							)
						}}
						format='DD/MM/YYYY'
						className='date-picker'
						slotProps={{
							field: {
								clearable: true,
							},
						}}
					/>
				</LocalizationProvider>
			</FormControl>
		</Stack>
	)
}

const StyledContained = styled(Stack)(({ theme }) => ({
	gap: theme.spacing(2),
	'.header-navigation': {
		padding: theme.spacing(4, 1, 2),
		borderBottom: `${theme.spacing(0.125)} solid ${
			theme.palette.neutral['100']
		}`,
		alignItems: 'center',
		flexDirection: 'row',
		'.batch-button': {
			color: theme.palette.neutral['500'],
			...theme.typography.body1,
			'.arrow-icon': {
				color: theme.palette.neutral['500'],
			},
		},
	},
	'.container': {
		padding: theme.spacing(1, 2.2),
		rowGap: theme.spacing(3.5),
		'.editContainer': {
			display: 'flex',
			justifyContent: 'flex-end',
		},
		'.project-details': {
			display: 'grid',
			gridTemplateColumns: 'repeat(auto-fill, minmax(350px, 1fr))',
			rowGap: theme.spacing(2),
			paddingBottom: theme.spacing(4.375),
			'.info-container': {
				'.label': {
					fontSize: theme.typography.caption.fontSize,
					fontWeight: theme.typography.h1.fontWeight,
					color: theme.palette.neutral[300],
				},
				'.value': {
					...theme.typography.body2,
					fontWeight: theme.typography.body1.fontWeight,
				},
				'.btn': {
					width: 'fit-content',
					...theme.typography.body2,
				},
			},
		},
		'.tab-container': {
			gap: theme.spacing(3.5),
			'.tab-list': {
				flexDirection: 'row',
				columnGap: theme.spacing(1),
				'.tab': {
					flexDirection: 'row',
					alignItems: 'center',
					justifyContent: 'flex-start',
					gap: theme.spacing(1.25),
					width: theme.spacing(25),
					border: `1px solid #74747445`,
					borderRadius: theme.spacing(1.5),
					padding: theme.spacing(1.25),
					background: '#B9B9B945',
					'.tab-img': {
						width: theme.spacing(2.5),
						height: theme.spacing(2.5),
						objectFit: 'contain',
						filter: 'grayscale(1)',
					},
					'.tab-label': {
						...theme.typography.caption,
						fontWeight: theme.typography.body2.fontWeight,
						color: theme.palette.neutral[500],
					},
					'&.active-tab': {
						border: `1px solid ${theme.palette.neutral[100]}`,
						background: theme.palette.success.light,
						'.tab-img': {
							filter: 'grayScale(0)',
						},
						'.tab-label': {
							color: theme.palette.success.main,
						},
					},
					'& .MuiTouchRipple-root': {
						color: theme.palette.success.main,
					},
				},
			},
			'.tab-panel': {
				'.grid-header-component': {
					flexDirection: 'row',
					alignItems: 'center',
					flexWrap: 'wrap',
					rowGap: theme.spacing(1),
					'.form-controller': {
						margin: theme.spacing(0.125),
						minWidth: theme.spacing(14),
						'.MuiOutlinedInput-notchedOutline': {
							borderRadius: theme.spacing(1.25),
						},
						'.date-picker': {
							height: theme.spacing(4.5),
							input: {
								width: theme.spacing(10),
								padding: theme.spacing(1.15, 1, 1.15, 2),
								fontSize: theme.typography.caption.fontSize,
							},
							'& .MuiInputAdornment-root': {
								'& .MuiIconButton-root': {
									svg: {
										width: theme.spacing(2.5),
										height: theme.spacing(2.5),
									},
								},
							},
						},
					},
				},
			},
			'.fontWeight': {
				fontWeight: '400',
			},
		},
	},
}))
