import { authAxios } from '@/contexts'
import {
	IArtisanProDetails,
	IArtisanProNetworkList,
	IOtherBuyers,
	ISiteData,
	ISiteList,
} from '@/interfaces'
import { defaultLimit, defaultPage } from '@/utils/constant'
import { QueryFunctionContext, useQuery } from '@tanstack/react-query'
import { SyntheticEvent, useCallback, useEffect, useState } from 'react'
import { useNavigate, useParams, useSearchParams } from 'react-router-dom'
import { artisanTabEnum } from './ArtisanProDetails'

const getArtisanProDetailService = async ({
	queryKey,
}: QueryFunctionContext) => {
	const artisanProId = queryKey[1]
	const { data } = await authAxios.get<IArtisanProDetails>(
		`/artisian-pro/${artisanProId}`
	)
	return data
}

const getAllSitesService = async ({ queryKey }: QueryFunctionContext) => {
	const artisanProId = queryKey[1]
	const { data } = await authAxios.get<ISiteData>(
		`/artisian-pro/${artisanProId}/site?sendAllSites=true&limit=50&page=0`
	)
	return data
}

export const useArtisanProDetails = () => {
	const { artisanProId } = useParams()
	const navigate = useNavigate()
	const [searchParams, setSearchParams] = useSearchParams()
	const [isActionInfoDrawer, setIsActionInfoDrawer] = useState(false)
	const [isAddSiteInfoDrawer, setIsAddSiteInfoDrawer] = useState(false)

	const [selectedSiteDetails, setSelectedSiteDetails] = useState<ISiteList>()
	const [isPackagingDrawer, setPackagingDrawer] = useState(false)
	const paramsLimit = searchParams.get('limit') || defaultLimit
	const paramsPage = searchParams.get('page') || defaultPage

	const paramsTab = searchParams.get('tab') ?? artisanTabEnum.sites

	const siteTab = searchParams.get('siteTab') || ''

	const handleTabChange = useCallback(
		(e: SyntheticEvent, newValue: string) => {
			e.preventDefault()
			setSearchParams(
				(prev) => {
					prev.set('tab', newValue)
					return prev
				},
				{ replace: true }
			)
		},
		[setSearchParams]
	)

	const artisanProDetailsQuery = useQuery({
		queryKey: ['artisanProDetail', artisanProId],
		queryFn: getArtisanProDetailService,
		enabled: !!artisanProId,
	})

	const allSitesQuery = useQuery({
		queryKey: ['allSites', artisanProId],
		queryFn: getAllSitesService,
		enabled: !!artisanProId,
		staleTime: 0,
	})
	const artisanProsDetails = useQuery({
		queryKey: ['artisanPros', artisanProId, paramsLimit, paramsPage],
		queryFn: () => {
			return authAxios.get<IArtisanProNetworkList>(
				`/artisian-pro/${artisanProId}`
			)
		},
		select: ({ data }) => data,
		enabled: !!artisanProId,
	})

	const getBuyersQuery = useQuery({
		queryKey: ['getArtisanProBuyers', paramsLimit, paramsPage, paramsTab],
		queryFn: async () => {
			const { data } = await authAxios<{
				count: number
				othersBuyers: IOtherBuyers[]
			}>(
				`/artisian-pro/${artisanProId}/other-buyer?limit=${paramsLimit}&page=${paramsPage}`
			)
			return data
		},
		enabled: paramsTab === 'buyers',
	})

	const handleInitialSiteDetails = useCallback(
		(siteId?: string) => {
			if (siteId) {
				setSelectedSiteDetails(
					allSitesQuery?.data?.siteList?.find((s) => s?.id === siteId)
				)
			} else setSelectedSiteDetails(allSitesQuery?.data?.siteList?.[0])
		},
		[allSitesQuery?.data?.siteList]
	)

	useEffect(() => {
		let siteId = ''
		if (allSitesQuery?.data?.siteList?.length === 0) return
		if (allSitesQuery?.data?.siteList?.find((s) => s.id === siteTab)) {
			siteId = siteTab
			handleInitialSiteDetails(siteId)
		} else {
			siteId = allSitesQuery?.data?.siteList?.[0]?.id ?? ''
			handleInitialSiteDetails()
		}

		if (!siteId) return
		setSearchParams(
			(prev) => {
				prev.set('siteTab', siteId)
				return prev
			},
			{ replace: true }
		)
	}, [
		allSitesQuery?.data?.siteList,
		handleInitialSiteDetails,
		setSearchParams,
		siteTab,
	])

	const handleSiteTabChange = useCallback(
		(e: SyntheticEvent, site: ISiteList) => {
			e.preventDefault()
			setSearchParams(
				(prev) => {
					prev.set('siteTab', site?.id)
					return prev
				},
				{ replace: true }
			)

			setSelectedSiteDetails(site)
		},
		[setSearchParams]
	)

	const refetchAllDetails = async () => {
		artisanProsDetails.refetch()
		artisanProDetailsQuery.refetch()
	}

	return {
		allSitesQuery,
		navigate,
		isActionInfoDrawer,
		setIsActionInfoDrawer,
		isPackagingDrawer,
		setPackagingDrawer,
		handleTabChange,
		paramsTab,
		handleSiteTabChange,
		siteTab,
		getAllSitesService,
		setSearchParams,
		searchParams,
		isAddSiteInfoDrawer,
		setIsAddSiteInfoDrawer,
		selectedSiteDetails,
		setSelectedSiteDetails,
		getBuyersQuery,
		buyersList: getBuyersQuery?.data?.othersBuyers,
		artisanProDetails: artisanProDetailsQuery.data,
		artisanProsData: artisanProsDetails?.data,
		isLoading: artisanProsDetails.isLoading,
		refetchAllDetails,
	}
}
