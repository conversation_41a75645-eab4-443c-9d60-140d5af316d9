import CloseIcon from '@/assets/icons/arrow-bar-right.svg'
import { Add } from '@mui/icons-material'
import {
	Box,
	Button,
	IconButton,
	Stack,
	styled,
	Typography,
} from '@mui/material'

interface IProps {
	close: () => void
}

export const PackagingDetails = ({ close }: IProps) => {
	return (
		<StyleContainer>
			<Stack className='header'>
				<Stack direction='row' spacing={1} alignItems='center'>
					<IconButton onClick={close}>
						<Box
							component='img'
							src={CloseIcon}
							alt='closeIcon'
							height={20}
							width={20}
						/>
					</IconButton>
					<Typography variant='h4'>Packaging Details</Typography>
				</Stack>
				<Button
					variant='contained'
					startIcon={<Add />}
					size='small'
					onClick={() => console.log('hi')}>
					Add Bags
				</Button>
			</Stack>
			<Stack className='container' spacing={4}>
				{new Array(4).fill(0).map((index) => (
					<Stack key={index}>
						<Typography variant='h5'>Bags ABC</Typography>
						<Stack
							direction='row'
							alignItems='center'
							justifyContent='space-between'>
							<Typography variant='body1'>+91 039928844</Typography>
							<Typography variant='body1'><EMAIL></Typography>
							<Button variant='text' size='small'>
								Generate Certificate
							</Button>
						</Stack>
					</Stack>
				))}
			</Stack>
		</StyleContainer>
	)
}

const StyleContainer = styled(Stack)(({ theme }) => ({
	gap: theme.spacing(2),
	'.header': {
		flexDirection: 'row',
		justifyContent: 'space-between',
		alignItems: 'center',
		padding: theme.spacing(4.5, 2),
		borderBottom: `${theme.spacing(0.125)} solid ${
			theme.palette.neutral['100']
		}`,
	},
	'.container': {
		padding: theme.spacing(2, 4),
		maxHeight: 'calc( 100vh - 200px)',
		overflowY: 'scroll',
	},
}))
