import {
	ActionInformationDrawer,
	CustomCard,
	TagComponentWithToolTip,
} from '@/components'
import { MultipleAvatar } from '@/components/MultipleAvatar.tsx'
import { ILocation, ImageURL, ISite } from '@/interfaces'
import { Box, Button, Stack, Typography } from '@mui/material'
import React, {
	ChangeEvent,
	useCallback,
	useMemo,
	useRef,
	useState,
} from 'react'
import EditIcon from '@/assets/icons/editIcon.svg'
import { theme } from '@/lib/theme/theme'
import { NavigateFunction, SetURLSearchParams } from 'react-router-dom'
import { AddKmlButton } from '@/components/AddKmlButton/AddKmlButton'
import { getKmlCoordinates, handleViewKMLFile } from '@/utils/helper'
import { GoogleMapsDraw } from '@/components/GoogleMap'
import { EntityTabEnum, userRoles } from '@/utils/constant'
import { IUserContext } from '@/contexts/Auth/type'
import { toast } from 'react-toastify'
import { Upload } from '@mui/icons-material'
import { AddSite } from '@/components/AddSite'

type HeaderComponentProps = {
	siteDetails?: ISite
	navigate: NavigateFunction
	setSearchParams: SetURLSearchParams
	handleSaveKml: (mapData: ILocation[]) => Promise<void>
	handleEditButton?: (entity: EntityTabEnum) => void
	handleViewBatches?: () => void
	handleDeleteDialog: () => void
	handleSuspendDialog: () => void
	userDetails?: IUserContext
	// disableEditandAdd?: boolean
}

const ToolTipComponent: React.FC<{
	dataArr: { name: string; description?: string }[]
}> = ({ dataArr }) => {
	return (
		<Stack rowGap={1}>
			{dataArr.map((data, index) => (
				<Stack
					key={`${data?.name}-${index}`}
					direction='row'
					columnGap={1}
					alignItems='center'>
					<Typography variant='caption'>{data?.name}</Typography>
					{data?.description ? (
						<Typography fontSize={10}>({data?.description})</Typography>
					) : null}
				</Stack>
			))}
		</Stack>
	)
}

const getCount = (length: number) => {
	if (length > 3) {
		return `+ ${length - 3}`
	}
	return ''
}

const MultipleAvatarWrapper: React.FC<{
	images: ImageURL[]
	length: number
}> = ({ images, length }) => {
	return (
		<Stack direction='row' alignItems='center' columnGap={1}>
			<MultipleAvatar size={30} imageList={images} MaxAvatar={3} />
			<Typography variant='subtitle1'>{getCount(length)}</Typography>
		</Stack>
	)
}

export const HeaderComponent: React.FC<HeaderComponentProps> = ({
	siteDetails,
	handleEditButton,
	handleSaveKml,
	navigate,
	setSearchParams,
	handleViewBatches,
	handleDeleteDialog,
	handleSuspendDialog,
	userDetails,
	// disableEditandAdd,
}) => {
	const [farmCoordinates, setFarmCoordinates] = useState<
		google.maps.LatLng[] | google.maps.LatLngLiteral[]
	>([])
	const [showMap, setShowMap] = useState(false)
	const [showUploadKmlFileDialog, setShowUploadKmlFileDialog] = useState(false)
	const ref = useRef<HTMLInputElement>(null)
	const [openAddSiteModal, setOpenAddSiteModal] = useState<boolean>(false)

	const isAllowSuspendSite = useMemo(() => {
		const allowedRoles: string[] = [
			userRoles.Admin,
			userRoles.Manager,
			userRoles.ArtisanPro,
			userRoles.BiomassAggregator,
			userRoles.artisanProNetworkManager,
		]
		return userDetails?.accountType
			? allowedRoles.includes(userDetails?.accountType)
			: false
	}, [userDetails])

	const networkDetails: {
		[key: string]: {
			label: string
			value: React.ReactNode
			tooltipComponent?: React.ReactNode
			showTooltip?: boolean
			clickEvents?: () => void
		}[]
	} = useMemo(
		() => ({
			upper: [
				{
					label: 'Kiln',
					value: siteDetails?.details?.kilns?.length ? (
						<MultipleAvatarWrapper
							images={
								siteDetails?.details?.kilns?.map(
									(kiln) => kiln?.imageURLs?.[0]
								) || []
							}
							length={siteDetails?.details?.kilns?.length}
						/>
					) : (
						0
					),
					tooltipComponent: (
						<ToolTipComponent
							dataArr={(siteDetails?.details?.kilns?.slice(0, 5) ?? [])?.map(
								(item) => ({
									name: item?.name,
									description:
										`${item?.volume ?? ''} ${item?.volumeUnit ?? ''}` || '',
								})
							)}
						/>
					),
					showTooltip: !!siteDetails?.details?.kilns?.length,
					clickEvents: () => handleEditButton?.(EntityTabEnum.kilns),
				},
				{
					label: 'Measuring Container',
					value: siteDetails?.details?.measuringContainers?.length ? (
						<MultipleAvatarWrapper
							images={
								siteDetails?.details?.measuringContainers?.map(
									(container) => container?.imageURLs?.[0]
								) || []
							}
							length={siteDetails?.details?.measuringContainers?.length}
						/>
					) : (
						0
					),
					tooltipComponent: (
						<ToolTipComponent
							dataArr={(siteDetails?.details?.measuringContainers ?? [])?.map(
								(item) => ({
									name: item?.name,
									description: `${item?.volume ? item?.volume + `ltrs` : ''}`,
								})
							)}
						/>
					),
					showTooltip: !!siteDetails?.details?.measuringContainers?.length,
					clickEvents: () => handleEditButton?.(EntityTabEnum.containers),
				},
				{
					label: 'Sampling Container',
					value: siteDetails?.details?.samplingContainers?.length ? (
						<MultipleAvatarWrapper
							images={
								siteDetails?.details?.samplingContainers?.map(
									(container) => container?.imageURLs?.[0]
								) || []
							}
							length={siteDetails?.details?.samplingContainers?.length}
						/>
					) : (
						0
					),
					tooltipComponent: (
						<ToolTipComponent
							dataArr={(siteDetails?.details?.samplingContainers ?? [])?.map(
								(item) => ({
									name: item?.name,
									description: `${item?.volume || 0} ltr`,
								})
							)}
						/>
					),
					showTooltip: !!siteDetails?.details?.samplingContainers?.length,
					clickEvents: () =>
						handleEditButton?.(EntityTabEnum.samplingContainer),
				},
				{
					label: 'Vehicles',
					value: siteDetails?.details?.vehicles?.length ? (
						<MultipleAvatarWrapper
							images={
								siteDetails?.details?.vehicles?.map(
									(vehicle) => vehicle?.imageURLs?.[0]
								) || []
							}
							length={siteDetails?.details?.vehicles?.length}
						/>
					) : (
						0
					),
					tooltipComponent: (
						<ToolTipComponent
							dataArr={(siteDetails?.details?.vehicles ?? [])?.map((item) => ({
								name: item?.name,
								description: item?.number || '',
							}))}
						/>
					),
					showTooltip: !!siteDetails?.details?.vehicles?.length,
					clickEvents: () => handleEditButton?.(EntityTabEnum.vehicles),
				},
				{
					label: 'Operators',
					value: siteDetails?.operators?.length ? (
						<MultipleAvatarWrapper
							images={siteDetails?.operators?.map((operator) => ({
								...operator?.profileImageUrl,
								path: operator?.profileImageUrl?.fileName ?? '',
							}))}
							length={siteDetails?.operators?.length}
						/>
					) : (
						0
					),
					tooltipComponent: (
						<ToolTipComponent
							dataArr={(siteDetails?.operators ?? [])?.map((item) => ({
								name: item?.name,
								description: `${item?.countryCode}-${item?.phoneNo}` || '',
							}))}
						/>
					),
					showTooltip: !!siteDetails?.operators?.length,
					clickEvents: () => handleEditButton?.(EntityTabEnum.operator),
				},
				{
					label: 'Biomass Source',
					value: siteDetails?.details?.fpu ? (
						<Stack maxHeight={theme.spacing(5.5)} overflow='hidden'>
							{siteDetails?.details?.fpu?.slice(0, 2)?.map((item) => (
								<Typography variant='subtitle1' key={item?.id}>
									{item?.name}
								</Typography>
							))}
						</Stack>
					) : (
						'-'
					),
					tooltipComponent: (
						<ToolTipComponent
							dataArr={(siteDetails?.details?.fpu ?? [])?.map((item) => ({
								name: `${item?.name} (${item?.address})` || '',
							}))}
						/>
					),
					showTooltip: !!siteDetails?.details?.fpu?.length,
					clickEvents: () => handleEditButton?.(EntityTabEnum.biomassSource),
				},
			],
		}),
		[
			handleEditButton,
			siteDetails?.details?.fpu,
			siteDetails?.details?.kilns,
			siteDetails?.details?.measuringContainers,
			siteDetails?.details?.samplingContainers,
			siteDetails?.details?.vehicles,
			siteDetails?.operators,
		]
	)
	const [coordinates, setCoordinates] = useState<google.maps.LatLng[]>([])

	const handleFileUpload = useCallback(
		async (event: ChangeEvent<HTMLInputElement>) => {
			const file = event?.target?.files?.[0]
			if (!file) return

			// Check if file is KML
			if (!file.name.toLowerCase().endsWith('.kml')) {
				toast('Please upload a KML file')
				setCoordinates([])
				return
			}
			if (file) {
				const coordinate = await getKmlCoordinates(file)
				const coordinateCenter = siteDetails?.coordinate
				const latLng = { lat: '', long: '' }
				if (coordinateCenter) {
					const [latitude, longitude] = coordinateCenter
						.replace(/[()]/g, '')
						.split(',')
						.map(Number)
					latLng.lat = String(latitude)
					latLng.long = String(longitude)
				}

				const newarr = coordinate?.reduce(
					(arr: any[], curr: any) => [
						...arr,
						{
							lat: curr[1],
							lng: curr[0],
						},
					],
					[]
				)

				const searchParams = new URLSearchParams(window.location.search)
				searchParams.set('networkId', siteDetails?.id || '')
				searchParams.set('lat', latLng?.lat || '')
				searchParams.set('long', latLng?.long || '')
				navigate(`?${searchParams.toString()}`, { replace: true })

				setShowUploadKmlFileDialog(true)

				setCoordinates(newarr)
			}
		},
		[navigate, siteDetails?.id]
	)

	//replace it with the boolean vale from the sitedetails

	return (
		<Box className='card_container'>
			<CustomCard
				headerComponent={
					<Stack className='network_detail_container'>
						<Stack
							justifyContent='space-between'
							flexDirection='row'
							pb={theme.spacing(2)}>
							<Stack
								flexDirection='row'
								alignItems='center'
								gap={theme.spacing(1.5)}>
								<Typography variant='h5'>Site Details</Typography>
								<Button
									variant='text'
									size='small'
									onClick={() => setOpenAddSiteModal(true)}
									// disabled={disableEditandAdd}
								>
									{' '}
									Edit site
								</Button>
								{userDetails?.accountType === userRoles.Admin ? (
									<Button
										variant='text'
										size='small'
										onClick={handleDeleteDialog}>
										Delete this Site?
									</Button>
								) : null}
								{isAllowSuspendSite && (
									<Button
										variant='text'
										size='small'
										onClick={handleSuspendDialog}>
										{siteDetails?.isSuspended ? 'Active?' : 'Suspend Site?'}
									</Button>
								)}
							</Stack>
							<Stack
								flexDirection='row'
								gap={theme.spacing(1)}
								alignItems='center'>
								<Button
									variant='text'
									onClick={() => {
										handleViewBatches?.()
									}}>
									View Batches
								</Button>
								<AddKmlButton
									className='edit_btn'
									downloadButton
									showKMLButton
									// disabled={disableEditandAdd}
									variant='outlined'
									addButtonText='Add KML File'
									data={{
										name: siteDetails?.name,
										landmark: siteDetails?.address,
										coordinate: siteDetails?.coordinate,
										farmArea: siteDetails?.kmlCoordinates ?? [],
										id: siteDetails?.id,
										siteCoordinateLat: String(
											siteDetails?.kmlCoordinates?.[0]?.x
										),
										siteCoordinateLng: String(
											siteDetails?.kmlCoordinates?.[0]?.y
										),
									}}
									handleViewKMLFile={(farmCoordinates, center, networkId) =>
										handleViewKMLFile({
											center,
											farmCoordinates,
											navigate,
											networkId,
											setFarmCoordinates,
											setShowMap: () => setShowMap(true),
										})
									}
									setSearchParams={setSearchParams}
									setShowMap={(bool: boolean) => setShowMap(bool)}
								/>
								<Button
									onClick={() => ref.current?.click()}
									variant='outlined'
									startIcon={<Upload />}
									// disabled={disableEditandAdd}
									className='edit_btn'>
									Upload Kml File
								</Button>

								<input
									ref={ref}
									style={{ display: 'none' }}
									type='file'
									accept='.kml'
									onChange={handleFileUpload}
									className='block w-full mt-1 border rounded p-2'
								/>
								<Button
									variant='outlined'
									className='edit_btn'
									startIcon={<Box component='img' src={EditIcon} />}
									onClick={() => handleEditButton?.(EntityTabEnum.kilns)}>
									Edit
								</Button>
							</Stack>
						</Stack>
						<Stack className='section_container'>
							{Object.keys(networkDetails).map((position) => (
								<Box key={position} className={`section ${position}`}>
									{networkDetails[position].map((item) => (
										<TagComponentWithToolTip
											lighterHeading
											key={item?.label}
											clickEvents={item?.clickEvents}
											label={item?.label}
											value={item?.value}
											{...(item?.tooltipComponent
												? {
														tooltipComponent: item?.tooltipComponent,
														showTooltip: item?.showTooltip,
												  }
												: {})}
										/>
									))}
								</Box>
							))}
						</Stack>
					</Stack>
				}
			/>
			{showUploadKmlFileDialog ? (
				<GoogleMapsDraw
					open={showUploadKmlFileDialog}
					handleModalClose={() => {
						setSearchParams((params) => {
							params.delete('lat')
							params.delete('long')
							params.delete('networkId')
							return params
						})
						setShowUploadKmlFileDialog(false)
						setCoordinates([])
					}}
					disableDrawingMarker
					handleSave={handleSaveKml}
					initialPolygons={coordinates}
				/>
			) : null}

			{showMap ? (
				<GoogleMapsDraw
					open={showMap}
					handleModalClose={() => {
						setSearchParams((params) => {
							params.delete('lat')
							params.delete('long')
							params.delete('networkId')

							return params
						})
						setShowMap(false)
						setFarmCoordinates([])
					}}
					handleSave={handleSaveKml}
					initialPolygons={farmCoordinates}
				/>
			) : null}
			{openAddSiteModal ? (
				<ActionInformationDrawer
					open={openAddSiteModal}
					onClose={() => setOpenAddSiteModal(false)}
					anchor='right'
					component={
						<AddSite
							siteDetails={siteDetails}
							handleClose={() => setOpenAddSiteModal(false)}
						/>
					}
				/>
			) : null}
		</Box>
	)
}
