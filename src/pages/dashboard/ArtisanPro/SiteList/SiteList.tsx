import {
	ActionInformationDrawer,
	CustomDataGrid,
	QueryInput,
} from '@/components'
import {
	Box,
	CircularProgress,
	Stack,
	ToggleButton,
	ToggleButtonGroup,
	Tooltip,
	Typography,
	styled,
} from '@mui/material'
import {
	GridColDef,
	GridMenuIcon,
	GridRenderCellParams,
	GridValidRowModel,
} from '@mui/x-data-grid'
import toggleIcon from '@/assets/icons/nav-icons.png'
import { TabContext, TabPanel } from '@mui/lab'
import { useEffect, useMemo, useState } from 'react'
import {
	BioCharDetails,
	Details,
	Farm,
	IArtisanProDetails,
	ISiteList,
} from '@/interfaces'
import { EditSite } from '@/components/EditSite'
import { PlaceOutlined, Search } from '@mui/icons-material'
import { useSiteList } from './useSiteList'
import { HeaderComponent } from './HeaderComponent'
import { theme } from '@/lib/theme/theme'
import { EditSiteEntity } from '@/components/EditSiteEntity'
import LeafMapComponent from '@/components/Leaflet-Maps/LeafletMaps'
import { Confirmation } from '@/components/Confirmation'
import { EntityTabEnum } from '@/utils/constant'

interface IProps {
	open: boolean
	siteDetails?: ISiteList
	artisanProDetails?: IArtisanProDetails | undefined
	setSiteChartDetails: (details: Details | undefined) => void
	// disableEditandAdd?: boolean
}

export const SiteList = ({
	open,
	siteDetails,
	artisanProDetails,
	setSiteChartDetails,
	// disableEditandAdd = false,
}: IProps) => {
	const [siteTab, setSiteTab] = useState<string>('map')
	const [openEditEntityModal, setopenEditEntityModal] =
		useState<EntityTabEnum | null>(null)
	const {
		sitedetailsQuery,
		farmerList,
		farmListQuery,
		farmerListQuery,
		handleSaveKml,
		setSearchParams,
		navigate,
		handleViewBatchesClick,
		refetchAllDetails,
		showConfirmationDialog,
		setShowConfirmationDialog,
		showSuspendConfirmationDialog,
		setShowSuspendConfirmationDialog,
		handleSuspendSite,
		handleDeleteSite,
		userDetails,
	} = useSiteList()
	const [isActionInfoDrawer, setIsActionInfoDrawer] = useState(false)
	useEffect(() => {
		setSiteChartDetails(sitedetailsQuery?.data?.details)
	}, [setSiteChartDetails, sitedetailsQuery?.data])
	useEffect(() => {
		if (!openEditEntityModal) {
			refetchAllDetails()
		}
	}, [openEditEntityModal])
	const farmerColumn: GridColDef<GridValidRowModel>[] = useMemo(
		() => [
			{
				field: 'name',
				headerName: 'Farmer',
				minWidth: 280,
				flex: 1,
				renderCell: (params: GridRenderCellParams) => {
					return (
						<Stack flexDirection='column'>
							<Typography
								variant='subtitle1'
								sx={{
									color: theme.palette.neutral['900'],
								}}>
								{params?.row?.name ?? ''}
							</Typography>
							<Typography variant='subtitle1'>
								({params?.row?.countryCode ?? ''} {params?.row?.phoneNo ?? ''})
							</Typography>
						</Stack>
					)
				},
			},
			{
				field: 'farmsCount',
				headerName: 'Farms',
				minWidth: 280,
				flex: 1,
				renderCell: (params) =>
					(params?.row?.farms || []).length > 0 ? (
						<Tooltip
							arrow
							title={
								<Stack rowGap={1}>
									{(params?.row?.farms || []).map((farm: Farm) => (
										<Stack key={farm.id}>
											<Stack direction='row' columnGap={1} alignItems='center'>
												<PlaceOutlined
													sx={{
														color: theme.palette.primary.main,
														width: theme.spacing(1.75),
													}}
												/>
												<Typography variant='caption'>
													{farm?.landmark}
												</Typography>
											</Stack>
											<Typography variant='subtitle1' fontSize={12} ml={3}>
												{farm?.fieldSize} {farm?.fieldSizeUnit}
											</Typography>
										</Stack>
									))}
								</Stack>
							}>
							<Typography minWidth={50} variant='subtitle1'>
								{params?.value || 0}
							</Typography>
						</Tooltip>
					) : (
						<Typography minWidth={50} variant='subtitle1'>
							{params?.value || 0}
						</Typography>
					),
			},
			{
				field: 'biochar',
				headerName: 'Total Biochar Produced',
				minWidth: 200,
				flex: 1,
				renderCell: (params) => (
					<Stack>
						{(params?.value || []).map((biochar: BioCharDetails) => (
							<Typography key={biochar.cropId} variant='subtitle1'>
								{biochar.cropName}: {biochar?.biocharProducedInTonne || 0} tonne
							</Typography>
						))}
					</Stack>
				),
			},
		],
		[]
	)

	if (open)
		return (
			<Stack width='100%' alignItems='center' pt={4}>
				<Typography>No Site Available</Typography>
			</Stack>
		)
	if (sitedetailsQuery.isFetching)
		return (
			<Stack justifyContent='center' alignItems='center' py={10} width='100%'>
				<CircularProgress size={60} />
			</Stack>
		)

	return (
		<StyledContainer>
			<Confirmation
				open={showConfirmationDialog}
				handleClose={() => setShowConfirmationDialog(false)}
				handleNoClick={() => {
					setShowConfirmationDialog(false)
				}}
				confirmationText={
					<Typography>
						Are you sure you want to delete "{siteDetails?.name}", if you do so
						then all the details will be removed.
					</Typography>
				}
				handleYesClick={() => handleDeleteSite()}
			/>
			<Confirmation
				open={showSuspendConfirmationDialog}
				handleClose={() => setShowSuspendConfirmationDialog(false)}
				handleNoClick={() => {
					setShowSuspendConfirmationDialog(false)
				}}
				confirmationText={
					<Typography>
						Are you sure you want to {(sitedetailsQuery.data?.isSuspended)? 'Active' : 'Suspend'} this site "{siteDetails?.name}".
					</Typography>
				}
				handleYesClick={() => handleSuspendSite()}
			/>

			<ActionInformationDrawer
				open={isActionInfoDrawer}
				onClose={() => setIsActionInfoDrawer(false)}
				childComponentsProps={{ height: '100%' }}
				anchor='right'
				component={
					<EditSite
						isEdit
						siteDetails={siteDetails ?? undefined}
						handleClose={() => setIsActionInfoDrawer(false)}
					/>
				}
			/>
			<Stack>
				<HeaderComponent
					handleSaveKml={handleSaveKml}
					setSearchParams={setSearchParams}
					navigate={navigate}
					handleDeleteDialog={() => setShowConfirmationDialog(true)}
					handleSuspendDialog={() => setShowSuspendConfirmationDialog(true)}
					siteDetails={sitedetailsQuery?.data}
					handleEditButton={(entity) => setopenEditEntityModal(entity)}
					// disableEditandAdd={disableEditandAdd}
					userDetails={userDetails}
					handleViewBatches={() => {
						handleViewBatchesClick(siteDetails, artisanProDetails)
					}}
				/>
			</Stack>
			<TabContext value={siteTab}>
				<Stack gap={1} pt={theme.spacing(4.5)}>
					<Stack gap={2.5}>
						<Typography variant='h5'>Farmer</Typography>
						<ToggleButtonGroup exclusive value={siteTab}>
							<ToggleButton onClick={() => setSiteTab('map')} value='map'>
								<CustomToggleButton />
							</ToggleButton>
							<ToggleButton onClick={() => setSiteTab('list')} value='list'>
								<GridMenuIcon color='disabled' />
							</ToggleButton>
						</ToggleButtonGroup>
					</Stack>

					<TabPanel value='map'>
						{/* <GoogleMapWithMultipleMarker
							markers={farmListQuery?.data || []}
							mapContainerStyle={{
								width: '100%',
								height: 500,
								position: 'relative',
							}}
							zoom={5}
							showLabel
						/> */}
						<LeafMapComponent markers={farmListQuery?.data || []} />
					</TabPanel>
					<TabPanel value='list'>
						<CustomDataGrid
							headerComponent={
								<QueryInput
									className='search-textFiled'
									queryKey='search'
									sx={{ width: theme.spacing(30) }}
									placeholder='Search'
									setPageOnSearch
									InputProps={{
										startAdornment: (
											<Search
												fontSize='small'
												color='disabled'
												sx={{ pr: theme.spacing(0.3) }}
											/>
										),
									}}
								/>
							}
							showPagination={true}
							rows={farmerList ?? []}
							columns={farmerColumn}
							rowCount={farmerListQuery.data?.count ?? 0}
						/>
					</TabPanel>
				</Stack>
			</TabContext>
			{openEditEntityModal ? (
				<ActionInformationDrawer
					open={!!openEditEntityModal}
					onClose={() => setopenEditEntityModal(null)}
					anchor='right'
					component={
						<EditSiteEntity
							openTab={openEditEntityModal}
							handleClose={() => setopenEditEntityModal(null)}
							siteDetails={sitedetailsQuery?.data}
							// disableEditandAdd={disableEditandAdd}
						/>
					}
				/>
			) : null}
		</StyledContainer>
	)
}

const StyledContainer = styled(Stack)(({ theme }) => ({
	width: '100%',

	'.card_container': {
		'.network_detail_container': {
			// justifyContent: 'space-between',
			'.section_container': {
				flexDirection: 'column',
				columnGap: theme.spacing(2),
				rowGap: theme.spacing(2),
				[theme.breakpoints.down('md')]: {
					flexWrap: 'wrap',
					rowGap: theme.spacing(1),
				},
				'.section': {
					width: '100%',
					display: 'grid',
				},
				'.upper': {
					gridTemplateColumns: 'repeat(6,minmax(100px, 1fr))',
					[theme.breakpoints.down('md')]: {
						gridTemplateColumns: 'repeat(auto-fill,minmax(100px, 200px))',
					},
				},
				'.chart_section': {
					display: 'flex',
					flexDirection: 'row',
					flexWrap: 'wrap',
					justifyContent: 'space-evenly',

					'.chart_text': {
						color: theme.palette.neutral[300],
						...theme.typography.caption,
						fontWeight: theme.typography.h5.fontWeight,
					},
				},
			},
		},
	},
	'.map-container': {
		height: 556,
		width: '100%',

		background: theme.palette.neutral['200'],
		borderRadius: theme.spacing(2),
	},
	'.edit_btn': {
		textTransform: 'none',
		borderColor: theme.palette.neutral[300],
		color: theme.palette.neutral[300],
		borderRadius: 8,
	},
}))
const CustomToggleButton = () => {
	return <Box component='img' width={27} height={27} src={toggleIcon} />
}
