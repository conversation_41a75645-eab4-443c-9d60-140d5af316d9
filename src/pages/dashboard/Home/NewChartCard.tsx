import {
	Card,
	Typography,
	Stack,
	Box,
	MenuItem,
	Select,
	FormControl,
	InputLabel,
	Tooltip,
	IconButton,
} from '@mui/material'
import ReactApexChart from 'react-apexcharts'
import FiberManualRecordRoundedIcon from '@mui/icons-material/FiberManualRecordRounded'
import { ApexOptions } from 'apexcharts'
import { MeasuringUnits } from '@/types/generic.types'
import { roundNumber } from '@/utils/helper'
import { InfoOutlined } from '@mui/icons-material'

type ChartLabelDataItem = {
	labelName: string
	labelQuantity: number
	color: string
}

type ChartSection = {
	id: string
	label: string
	unit: string
	allData: ChartLabelDataItem[]
}

export interface ChartCardProps {
	data: ChartSection
	chartOptions: ApexOptions
	chartSeries: number[]
	biocharProps?: {
		biocharUnit: string
		handleBiocharUnit: (value: MeasuringUnits) => void
		measuringUnitOptions: { value: string; label: string }[]
	}
}

export const NewChartCard: React.FC<ChartCardProps> = ({
	data,
	chartOptions,
	chartSeries,
	biocharProps,
}) => {
	const totalQuality =
		data.id == 'credit'
			? roundNumber(
					data?.allData?.[0]?.labelQuantity + data?.allData?.[1]?.labelQuantity,
					3
			  )
			: roundNumber(
					data?.allData.reduce((sum: number, item: ChartLabelDataItem) => {
						return sum + item.labelQuantity
					}, 0 as number),
					3
			  )
	return (
		<Card key={data?.id} className='border-card card'>
			<Stack direction='row' alignItems='center'>
				<Typography variant='body2' ml={2}>
					{data?.label}
				</Typography>
				<Box flexGrow={1} />
				{data.id === 'biochar' && (
					<FormControl sx={{ width: 80 }}>
						<InputLabel>Unit</InputLabel>
						<Select
							label='Unit'
							value={biocharProps?.biocharUnit}
							onChange={(e) =>
								biocharProps?.handleBiocharUnit(
									e.target.value as MeasuringUnits
								)
							}>
							{biocharProps?.measuringUnitOptions.map((option) => (
								<MenuItem key={option.value} value={option.value}>
									{option.label}
								</MenuItem>
							))}
						</Select>
					</FormControl>
				)}
				{data.id === 'credit' && (
					<Tooltip
						sx={{ padding: 0 }}
						title='Total Credits here is the sum of the Registered and Pending Credits.'>
						<IconButton>
							<InfoOutlined color='primary' />
						</IconButton>
					</Tooltip>
				)}
			</Stack>

			<Stack className='card-container' direction='row'>
				{totalQuality !== 0 ? (
					<>
						<Stack className='chart'>
							<ReactApexChart
								options={chartOptions}
								series={chartSeries}
								type='donut'
							/>
						</Stack>
						<Stack width='100%' justifyContent='center'>
							<Typography variant='h5'>
								{totalQuality} {data?.unit}
							</Typography>
							<Stack mt={2}>
								{data.allData.map((item: ChartLabelDataItem, index) => (
									<Stack
										direction='row'
										alignItems='center'
										key={`${item?.labelName}-${index}`}
										className='container'>
										<FiberManualRecordRoundedIcon
											className='circle-icon'
											sx={{ color: item.color }}
										/>
										<Stack
											key={item.labelName}
											direction='row'
											gap={0.3}
											alignItems='center'
											className='full-width-stack'>
											<Typography variant='subtitle1' fontWeight={400}>
												{`${item.labelName}: 
													${item?.labelQuantity > 0 ? item?.labelQuantity : 0}
												 ${data.unit}`}
											</Typography>
										</Stack>
									</Stack>
								))}
							</Stack>
						</Stack>
					</>
				) : (
					<>
						<Stack>
							<Box
								component='img'
								src='/images/homePageDefaultImage.svg'
								alt='Example'
								sx={{ width: 150, height: 150, borderRadius: 2 }}
							/>
						</Stack>
						<Stack justifyContent='center' ml={2}>
							<Typography>
								Currently,{' '}
								{data.id === 'pending'
									? 'no credits in pending biochar.'
									: `no biochar is produced. Please add some batches and
								create some biochar.`}
							</Typography>
						</Stack>
					</>
				)}
			</Stack>
		</Card>
	)
}
