import { PeriodEnum } from '@/interfaces'
import { <PERSON><PERSON>, <PERSON><PERSON><PERSON>, <PERSON><PERSON>, <PERSON>ack, styled, Tooltip } from '@mui/material'
import { AdapterMoment } from '@mui/x-date-pickers/AdapterMoment'
import { DateCalendar } from '@mui/x-date-pickers/DateCalendar'
import { LocalizationProvider } from '@mui/x-date-pickers/LocalizationProvider'
import moment from 'moment'
import React, { useCallback, useEffect, useState } from 'react'
import { useSearchParams } from 'react-router-dom'
import { toast } from 'react-toastify'
const MIN_DATA_YEAR = 2023
const DEFAULT_CUSTOM_RANGE_DAYS = 8
const Buttons = [
	{
		label: 'All',
		value: PeriodEnum.all,
	},
	{
		label: 'MTD',
		value: PeriodEnum.mtd,
	},
	{
		label: 'YTD',
		value: PeriodEnum.ytd,
	},
	{
		label: 'Custom',
		value: PeriodEnum.custom,
	},
]

interface CalendarPickerProps {
	activePicker: PeriodEnum | null
	startCalDate: moment.Moment | null
	endCalDate?: moment.Moment | null
	setStartCalDate: (date: moment.Moment) => void
	setEndCalDate?: (date: moment.Moment) => void
	handleDateChange: (
		date: moment.Moment | null,
		type: 'startDate' | 'endDate',
		setDate: (date: moment.Moment) => void,
		period?: PeriodEnum
	) => void
	MIN_DATA_YEAR: number
	monthYearDate: moment.Moment | null
	setMonthYearDate: (date: moment.Moment) => void
	yearDate: moment.Moment | null
	setYearDate: (date: moment.Moment) => void
}

export const TimefilterHomeScreen = () => {
	const [searchParams, setSearchParams] = useSearchParams()
	const selectedPeriod = searchParams.get('period') || PeriodEnum.all
	const [anchorEl, setAnchorEl] = React.useState<HTMLButtonElement | null>(null)
	const open = Boolean(anchorEl)
	const [startCalDate, setStartCalDate] = useState<moment.Moment | null>(null)
	const [endCalDate, setEndCalDate] = useState<moment.Moment | null>(null)
	const [monthYearDate, setMonthYearDate] = useState<moment.Moment | null>(null)
	const [activePicker, setActivePicker] = useState<PeriodEnum | null>(null)
	const [yearDate, setYearDate] = useState<moment.Moment | null>(null)

	const handleReset = () => {
		// Clear all URL parameters
		setSearchParams(
			(prev) => {
				prev.delete('startDate')
				prev.delete('endDate')
				prev.delete('period')
				return prev
			},
			{ replace: true }
		)

		// Reset all state
		setStartCalDate(null)
		setEndCalDate(null)
		setMonthYearDate(null)
		setYearDate(null)
		setActivePicker(null)
		setAnchorEl(null)
	}
	const handleSelectButton = useCallback(
		(event: React.MouseEvent<HTMLButtonElement>, value: PeriodEnum) => {
			setAnchorEl(event.currentTarget)
			setActivePicker(value)

			setAnchorEl(event.currentTarget)
			setActivePicker(value)

			if (value === PeriodEnum.all) {
				handleReset()
				return
			}

			const now = moment()
			setSearchParams(
				(prev) => {
					prev.set('period', value)
					return prev
				},
				{ replace: true }
			)
			if (value === PeriodEnum.mtd) {
				const start =
					monthYearDate?.clone().startOf('month') ??
					now.clone().startOf('month')
				const end = start.clone().endOf('month')

				setMonthYearDate(start)
				setSearchParams(
					(prev) => {
						prev.set('startDate', start.format('DD-MM-YYYY'))
						prev.set('endDate', end.format('DD-MM-YYYY'))
						return prev
					},
					{ replace: true }
				)
			}

			if (value === PeriodEnum.ytd) {
				const start =
					yearDate?.clone().startOf('year') ?? now.clone().startOf('year')
				const end = start.clone().endOf('year')

				setYearDate(start)
				setSearchParams(
					(prev) => {
						prev.set('startDate', start.format('DD-MM-YYYY'))
						prev.set('endDate', end.format('DD-MM-YYYY'))
						return prev
					},
					{ replace: true }
				)
			}

			if (value === PeriodEnum.custom && (!startCalDate || !endCalDate)) {
				const start = now
					.clone()
					.subtract(DEFAULT_CUSTOM_RANGE_DAYS - 1, 'days')
				const end = now

				setStartCalDate(start)
				setEndCalDate(end)

				setSearchParams(
					(prev) => {
						prev.set('startDate', start.format('DD-MM-YYYY'))
						prev.set('endDate', end.format('DD-MM-YYYY'))
						return prev
					},
					{ replace: true }
				)
			}
		},
		[monthYearDate, yearDate, startCalDate, endCalDate, setSearchParams]
	)

	const getTooltipLabel = (value: PeriodEnum) => {
		if (value === PeriodEnum.mtd) {
			return monthYearDate?.format('MMMM YYYY') || moment().format('MMMM YYYY')
		}
		if (value === PeriodEnum.ytd) {
			return yearDate?.format('YYYY') || moment().format('YYYY')
		}
		if (value === PeriodEnum.custom) {
			const start =
				startCalDate?.format('D MMM YYYY') ||
				moment()
					.subtract(DEFAULT_CUSTOM_RANGE_DAYS - 1, 'days')
					.format('D MMM YYYY')

			const end =
				endCalDate?.format('D MMM YYYY') || moment().format('D MMM YYYY')

			if (start && end) {
				return `${start} - ${end}`
			}
		}

		return ''
	}

	const handleDateChange = (
		newValue: moment.Moment | null,
		type: 'startDate' | 'endDate',
		setDate: (date: moment.Moment) => void,
		period?: PeriodEnum
	) => {
		if (!newValue?.isValid()) {
			toast('Invalid date selected')
			return
		}

		if (period === PeriodEnum.mtd || period === PeriodEnum.ytd) {
			const unit = period === PeriodEnum.mtd ? 'month' : 'year'
			const start = newValue.clone().startOf(unit)
			const end = newValue.clone().endOf(unit)
			if (period === PeriodEnum.mtd) {
				setMonthYearDate(start)
			} else {
				setYearDate(start)
			}

			setSearchParams(
				(prev) => {
					prev.set('startDate', start.format('DD-MM-YYYY'))
					prev.set('endDate', end.format('DD-MM-YYYY'))
					prev.set('period', period)
					return prev
				},
				{ replace: true }
			)
			return
		}
		setDate(newValue)

		setSearchParams(
			(prev) => {
				prev.set(type, newValue.format('DD-MM-YYYY'))
				prev.set('period', PeriodEnum.custom)
				return prev
			},
			{ replace: true }
		)
	}

	useEffect(() => {
		const now = moment()

		// Check if dates are already set in URL params
		const existingStartDate = searchParams.get('startDate')
		const existingEndDate = searchParams.get('endDate')

		if (existingStartDate && existingEndDate) {
			// If dates exist in URL, parse and set them
			const startDate = moment(existingStartDate, 'DD-MM-YYYY', true)
			const endDate = moment(existingEndDate, 'DD-MM-YYYY', true)
			if (!startDate.isValid() || !endDate.isValid()) {
				console.warn('Ignoring invalid date params in URL')
				return
			}

			if (selectedPeriod === PeriodEnum.mtd) {
				setMonthYearDate(startDate)
			} else if (selectedPeriod === PeriodEnum.ytd) {
				setYearDate(startDate)
			} else if (selectedPeriod === PeriodEnum.custom) {
				setStartCalDate(startDate)
				setEndCalDate(endDate)
			}
		} else {
			// Initialize default dates based on selected period
			if (selectedPeriod === PeriodEnum.mtd) {
				const start = now.clone().startOf('month')
				const end = start.clone().endOf('month')
				setMonthYearDate(start)
				setSearchParams(
					(prev) => {
						prev.set('startDate', start.format('DD-MM-YYYY'))
						prev.set('endDate', end.format('DD-MM-YYYY'))
						prev.set('period', selectedPeriod)
						return prev
					},
					{ replace: true }
				)
			} else if (selectedPeriod === PeriodEnum.ytd) {
				const start = now.clone().startOf('year')
				const end = start.clone().endOf('year')
				setYearDate(start)
				setSearchParams(
					(prev) => {
						prev.set('startDate', start.format('DD-MM-YYYY'))
						prev.set('endDate', end.format('DD-MM-YYYY'))
						prev.set('period', selectedPeriod)
						return prev
					},
					{ replace: true }
				)
			} else if (selectedPeriod === PeriodEnum.custom) {
				const start = now
					.clone()
					.subtract(DEFAULT_CUSTOM_RANGE_DAYS - 1, 'days')
				const end = now.clone()
				setStartCalDate(start)
				setEndCalDate(end)
				setSearchParams(
					(prev) => {
						prev.set('startDate', start.format('DD-MM-YYYY'))
						prev.set('endDate', end.format('DD-MM-YYYY'))
						prev.set('period', selectedPeriod)
						return prev
					},
					{ replace: true }
				)
			}
		}
	}, []) // Empty dependency array to run only on mount

	return (
		<StyledStack>
			<Stack className='tab-container'>
				{Buttons.map(({ label, value }) => {
					return (
						<Tooltip key={value} title={getTooltipLabel(value)} arrow>
							<Button
								className={`tab-button ${
									selectedPeriod === value ? 'selected-button' : ''
								}`}
								size='small'
								variant='text'
								onClick={(event) => handleSelectButton(event, value)}>
								{label}
							</Button>
						</Tooltip>
					)
				})}

				<Popover
					open={open}
					anchorEl={anchorEl}
					onClose={() => {
						setAnchorEl(null)
						setActivePicker(null)
					}}
					anchorOrigin={{ vertical: 'bottom', horizontal: 'left' }}>
					<Stack direction='row' columnGap={1} alignItems='center'>
						<LocalizationProvider dateAdapter={AdapterMoment}>
							<CalendarPicker
								activePicker={activePicker}
								startCalDate={startCalDate}
								endCalDate={endCalDate}
								setStartCalDate={setStartCalDate}
								setEndCalDate={setEndCalDate}
								handleDateChange={handleDateChange}
								MIN_DATA_YEAR={MIN_DATA_YEAR}
								monthYearDate={monthYearDate}
								setMonthYearDate={setMonthYearDate}
								yearDate={yearDate}
								setYearDate={setYearDate}
							/>
						</LocalizationProvider>
					</Stack>
				</Popover>
			</Stack>
		</StyledStack>
	)
}

const CalendarPicker = ({
	activePicker,
	startCalDate,
	endCalDate,
	setStartCalDate,
	setEndCalDate,
	handleDateChange,
	MIN_DATA_YEAR,
	monthYearDate,
	setMonthYearDate,
	yearDate,
	setYearDate,
}: CalendarPickerProps) => {
	if (activePicker === PeriodEnum.mtd) {
		return (
			<DateCalendar
				views={['year', 'month']}
				disableFuture
				shouldDisableYear={(year) => year.year() < MIN_DATA_YEAR}
				value={monthYearDate}
				onChange={(date) =>
					handleDateChange(date, 'startDate', setMonthYearDate, PeriodEnum.mtd)
				}
			/>
		)
	}

	if (activePicker === PeriodEnum.ytd) {
		return (
			<DateCalendar
				views={['year']}
				disableFuture
				shouldDisableYear={(year) => year.year() < MIN_DATA_YEAR}
				value={yearDate}
				onChange={(date) =>
					handleDateChange(date, 'startDate', setYearDate, PeriodEnum.ytd)
				}
			/>
		)
	}

	if (activePicker === PeriodEnum.custom && endCalDate && setEndCalDate) {
		return (
			<>
				<DateCalendar
					views={['year', 'month', 'day']}
					disableFuture
					value={startCalDate}
					shouldDisableYear={(year) => year.year() < MIN_DATA_YEAR}
					onChange={(date) =>
						handleDateChange(date, 'startDate', setStartCalDate)
					}
				/>
				<Divider orientation='vertical' flexItem />
				<DateCalendar
					views={['year', 'month', 'day']}
					disableFuture
					value={endCalDate}
					shouldDisableYear={(year) => year.year() < MIN_DATA_YEAR}
					shouldDisableDate={(date) =>
						startCalDate ? date.isBefore(startCalDate, 'day') : false
					}
					onChange={(date) => handleDateChange(date, 'endDate', setEndCalDate)}
				/>
			</>
		)
	}

	return null
}

const StyledStack = styled(Stack)(({ theme }) => ({
	'.reset-button': {
		color: theme.palette.error.main,
		justifyContent: 'flex-end',
		alignSelf: 'flex-end',
		width: 'fit-content',
		fontSize: 10,
	},
	'.tab-container': {
		flexDirection: 'row',
		alignItems: 'center',
		background: theme.palette.neutral['100'],
		padding: theme.spacing(0.25),
		borderRadius: theme.spacing(0.75),
		'.tab-button': {
			background: 'transparent',
			color: theme.palette.common.black,
			textTransform: 'none',
			height: theme.spacing(4),
			width: theme.spacing(11),
			fontWeight: theme.typography.fontWeightRegular,
		},
		'.selected-button': {
			background: theme.palette.common.white,
		},
		'.button-border': {
			height: '60%',
			borderRight: `2px solid ${theme.palette.neutral['100']}`,
			borderRadius: theme.spacing(0.25),
		},
	},
}))
