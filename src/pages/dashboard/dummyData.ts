import { IImage } from '@/interfaces'

export const CSinkNetworkData = [
	{
		id: '1',
		networkName: 'Network A',
		baName: 'Network A',
		location: 'Kurseong, West Bengal',
		supplyStatus: {
			label: 'In stock',
			status: 'stock',
		},
		farmerCounts: 100,
		networkAdmin: {
			image: '',
			name: '<PERSON><PERSON><PERSON>',
		},
	},
	{
		id: '2',
		networkName: 'Network B',
		baName: 'Network B',
		location: 'Kurseong, West Bengal',
		supplyStatus: {
			label: 'In transit',
			status: 'transit',
		},
		farmerCounts: 200,
		networkAdmin: {
			image: '',
			name: '<PERSON><PERSON><PERSON>',
		},
	},
	{
		id: '3',
		networkName: 'Network A',
		baName: 'Network A',
		location: 'Kurseong, West Bengal',
		supplyStatus: {
			label: 'In transit',
			status: 'transit',
		},
		farmerCounts: 100,
		networkAdmin: {
			image: '',
			name: '<PERSON><PERSON><PERSON>',
		},
	},
	{
		id: '4',
		networkName: 'Network A',
		baName: 'Network A',
		location: 'Kurseong, West Bengal',
		supplyStatus: {
			label: 'Request received',
			status: 'requested',
		},
		farmerCounts: 100,
		networkAdmin: {
			image: '',
			name: '<PERSON><PERSON><PERSON>',
		},
	},
	{
		id: '5',
		networkName: 'Network A',
		baName: 'Network A',
		location: 'Kurseong, West Bengal',
		supplyStatus: {
			label: 'In stock',
			status: 'stock',
		},
		farmerCounts: 100,
		networkAdmin: {
			image: '',
			name: 'Aarushi Pawar',
		},
	},
	{
		id: '6',
		networkName: 'Network A',
		baName: 'Network A',
		location: 'Kurseong, West Bengal',
		supplyStatus: {
			label: 'Request received',
			status: 'requested',
		},
		farmerCounts: 100,
		networkAdmin: {
			image: '',
			name: 'Aarushi Pawar',
		},
	},
	{
		id: '7',
		networkName: 'Network A',
		baName: 'Network A',
		location: 'Kurseong, West Bengal',
		supplyStatus: {
			label: 'In stock',
			status: 'stock',
		},
		farmerCounts: 100,
		networkAdmin: {
			image: '',
			name: 'Aarushi Pawar',
		},
	},
	{
		id: '8',
		networkName: 'Network A',
		baName: 'Network A',
		location: 'Kurseong, West Bengal',
		supplyStatus: {
			label: 'In stock',
			status: 'stock',
		},
		farmerCounts: 100,
		networkAdmin: {
			image: '',
			name: 'Aarushi Pawar',
		},
	},
]

export const biomassGridData = [
	{
		id: '1',
		cropName: 'Crop/Biomass Name',
		seasonName: 'All seasons',
		type: 'Crop',
		biomassDensity: 670,
		description: 'Agricultural residue after harvesting of rapeseed.',
		images: [
			{
				id: 'a1',
				url: 'abc',
			},
		],
	},
	{
		id: '2',
		cropName: 'Crop/Biomass Name',
		seasonName: 'Season Name',
		type: 'Biomass',
		biomassDensity: 34,
		description: 'Agricultural residue after harvesting of rapeseed.',
		images: [
			{
				id: 'a1',
				url: 'abc',
			},
		],
	},
	{
		id: '3',
		cropName: 'Crop/Biomass Name',
		seasonName: 'Biomass Type',
		type: 'Biomass',
		biomassDensity: 542,
		description: 'Agricultural residue after harvesting of rapeseed.',
		images: [
			{
				id: 'a1',
				url: 'abc',
			},
		],
	},
	{
		id: '4',
		cropName: 'Crop/Biomass Name',
		seasonName: 'All seasons',
		type: 'Crop',
		biomassDensity: 45,
		description: 'Agricultural residue after harvesting of rapeseed.',
		images: [
			{
				id: 'a1',
				url: 'abc',
			},
		],
	},
	{
		id: '5',
		cropName: 'Crop/Biomass Name',
		seasonName: 'Biomass Type',
		type: 'Biomass',
		biomassDensity: 47,
		description: 'Agricultural residue after harvesting of rapeseed.',
		images: [
			{
				id: 'a1',
				url: 'abc',
			},
		],
	},
	{
		id: '6',
		cropName: 'Crop/Biomass Name',
		seasonName: 'Biomass Type',
		type: 'Crop',
		biomassDensity: 145,
		description: 'Agricultural residue after harvesting of rapeseed.',
		images: [
			{
				id: 'a1',
				url: 'abc',
			},
		],
	},
]

export const certificateGridData = [
	{
		id: '1',
		network: 'Crop/Biomass Name',
		name: 'All seasons',
		certificationType: 'Artisan Biochar Producer',
		generateDate: '15.Mar.2024 12:34:21',
		images: [
			{
				id: 'a1',
				url: 'abc',
			},
		],
	},
	{
		id: '2',
		network: 'Crop/Biomass Name',
		name: 'Season Name',
		certificationType: 'Operator',
		generateDate: '15.Mar.2024 12:34:21',
		images: [
			{
				id: 'a1',
				url: 'abc',
			},
		],
	},
	{
		id: '3',
		network: 'Crop/Biomass Name',
		name: 'Biomass Type',
		certificationType: 'Operator',
		generateDate: '15.Mar.2024 12:34:21',
		images: [
			{
				id: 'a1',
				url: 'abc',
			},
		],
	},
	{
		id: '4',
		network: 'Crop/Biomass Name',
		name: 'All seasons',
		certificationType: 'Artisan Biochar Producer',
		generateDate: '15.Mar.2024 12:34:21',
		images: [
			{
				id: 'a1',
				url: 'abc',
			},
		],
	},
	{
		id: '5',
		network: 'Crop/Biomass Name',
		name: 'Biomass Type',
		certificationType: 'C-Sink Manager',
		generateDate: '15.Mar.2024 12:34:21',
		images: [
			{
				id: 'a1',
				url: 'abc',
			},
		],
	},
	{
		id: '6',
		network: 'Crop/Biomass Name',
		name: 'Biomass Type',
		certificationType: 'Crop',
		generateDate: '15.Mar.2024 12:34:21',
		images: [
			{
				id: 'a1',
				url: 'abc',
			},
		],
	},
]

export const projectGridData = [
	{
		id: '1',
		name: 'Biochar Producti...',
		credits: '1299 tCO2',
		time: '100+ Years',
		location: 'Kahna, Hyderab..',
		price_per_credit: '200 USD',
		status: 'Active',
		image: [],
	},
	{
		id: '2',
		name: 'Biochar Producti...',
		credits: '1299 tCO2',
		time: '100+ Years',
		location: 'Kahna, Hyderab..',
		price_per_credit: '200 USD',
		status: 'Active',
		image: [],
	},
	{
		id: '3',
		name: 'Biochar Producti...',
		credits: '1299 tCO2',
		time: '100+ Years',
		location: 'Kahna, Hyderab..',
		price_per_credit: '200 USD',
		status: 'Active',
		image: [],
	},
	{
		id: '4',
		name: 'Biochar Producti...',
		credits: '1299 tCO2',
		time: '100+ Years',
		location: 'Kahna, Hyderab..',
		price_per_credit: '200 USD',
		status: 'Archived',
		image: [],
	},
	{
		id: '5',
		name: 'Biochar Producti...',
		credits: '1299 tCO2',
		time: '100+ Years',
		location: 'Kahna, Hyderab..',
		price_per_credit: '200 USD',
		status: 'Active',
		image: [],
	},
	{
		id: '6',
		name: 'Biochar Producti...',
		credits: '1299 tCO2',
		time: '100+ Years',
		location: 'Kahna, Hyderab..',
		price_per_credit: '200 USD',
		status: 'Archived',
		image: [],
	},
]

export const queriesGridData = [
    {
        id: '1',
        sender: 'Dummy',
        email: '<EMAIL>',
        phoneNumber: '+**********',
        queryTopic: 'tc02e',
        date: '15.Mar.2024 12:34:21',
        description: 'Hello, we would like to request...'
    },
    {
        id: '2',
        sender: 'Jane Smith',
        email: '<EMAIL>',
        phoneNumber: '+**********',
        queryTopic: 'tc01a',
        date: '16.Mar.2024 10:15:30',
        description: 'I have an issue with my account...'
    },
    {
        id: '3',
        sender: 'John Doe',
        email: '<EMAIL>',
        phoneNumber: '+**********',
        queryTopic: 'tc03b',
        date: '17.Mar.2024 14:50:00',
        description: 'Can you provide more details on...'
    },
    {
        id: '4',
        sender: 'Alice Johnson',
        email: '<EMAIL>',
        phoneNumber: '+**********',
        queryTopic: 'tc04c',
        date: '18.Mar.2024 09:45:15',
        description: 'I would like to know about...'
    },
    {
        id: '5',
        sender: 'Bob Brown',
        email: '<EMAIL>',
        phoneNumber: '+**********',
        queryTopic: 'tc05d',
        date: '19.Mar.2024 11:30:45',
        description: 'Please send me the invoice for...'
    },
    {
        id: '6',
        sender: 'Charlie Green',
        email: '<EMAIL>',
        phoneNumber: '+**********',
        queryTopic: 'tc06f',
        date: '20.Mar.2024 08:20:10',
        description: 'How can I reset my password?...'
    },
    {
        id: '7',
        sender: 'Diana Prince',
        email: '<EMAIL>',
        phoneNumber: '+9134567890',
        queryTopic: 'tc07g',
        date: '21.Mar.2024 14:05:35',
        description: 'I need assistance with...'
    },
    {
        id: '8',
        sender: 'Eve Adams',
        email: '<EMAIL>',
        phoneNumber: '+9123892345',
        queryTopic: 'tc08h',
        date: '22.Mar.2024 16:45:22',
        description: 'Could you provide an update on...'
    },
    {
        id: '9',
        sender: 'Frank Wright',
        email: '<EMAIL>',
        phoneNumber: '+9112345678',
        queryTopic: 'tc09i',
        date: '23.Mar.2024 11:10:00',
        description: 'Where can I find the...'
    },
    {
        id: '10',
        sender: 'Grace Lee',
        email: '<EMAIL>',
        phoneNumber: '+9198765432',
        queryTopic: 'tc10j',
        date: '24.Mar.2024 13:25:50',
        description: 'I would like to schedule a...'
    },
    {
        id: '11',
        sender: 'Hank Moore',
        email: '<EMAIL>',
        phoneNumber: '+9187654323',
        queryTopic: 'tc11k',
        date: '25.Mar.2024 09:15:20',
        description: 'Can I change my subscription...'
    },
    
]

export const userManagementGridData = [
	{
		id: '1',
		userName: 'Crop/Biomass Name',
		role: 'All seasons',
		email: '<EMAIL>',
		createdAt: '15.Mar.2024 12:34:21',
		certificate: [
			{
				id: 'a1',
				url: 'abc',
			},
		],
	},
	{
		id: '2',
		userName: 'Crop/Biomass Name',
		role: 'Season Name',
		email: '<EMAIL>',
		createdAt: '15.Mar.2024 12:34:21',
		certificate: [
			{
				id: 'a1',
				url: 'abc',
			},
		],
	},
	{
		id: '3',
		userName: 'Crop/Biomass Name',
		role: 'Biomass Type',
		email: '<EMAIL>',
		createdAt: '15.Mar.2024 12:34:21',
		certificate: [
			{
				id: 'a1',
				url: 'abc',
			},
		],
	},
	{
		id: '4',
		userName: 'Crop/Biomass Name',
		role: 'All seasons',
		email: '<EMAIL>',
		createdAt: '15.Mar.2024 12:34:21',
		certificate: [
			{
				id: 'a1',
				url: 'abc',
			},
		],
	},
	{
		id: '5',
		userName: 'Crop/Biomass Name',
		role: 'Biomass Type',
		email: '<EMAIL>',
		createdAt: '15.Mar.2024 12:34:21',
		certificate: [
			{
				id: 'a1',
				url: 'abc',
			},
		],
	},
	{
		id: '6',
		userName: 'Crop/Biomass Name',
		role: 'Biomass Type',
		email: '<EMAIL>',
		createdAt: '15.Mar.2024 12:34:21',
		certificate: [
			{
				id: 'a1',
				url: 'abc',
			},
		],
	},
]

export const ArtisianProRowData = [
	{
		id: '1',
		networkName: 'Network A',
		baName: 'Network A',
		location: 'Kurseong, West Bengal',
		supplyStatus: {
			label: 'In stock',
			status: 'stock',
		},
		siteCount: 100,
		networkAdmin: {
			image: '',
			name: 'Aarushi Pawar',
		},
	},
	{
		id: '2',
		networkName: 'Network B',
		baName: 'Network B',
		location: 'Kurseong, West Bengal',
		supplyStatus: {
			label: 'In transit',
			status: 'transit',
		},
		siteCount: 200,
		networkAdmin: {
			image: '',
			name: 'Aarushi Pawar',
		},
	},
	{
		id: '3',
		networkName: 'Network A',
		baName: 'Network A',
		location: 'Kurseong, West Bengal',
		supplyStatus: {
			label: 'In transit',
			status: 'transit',
		},
		siteCount: 100,
		networkAdmin: {
			image: '',
			name: 'Aarushi Pawar',
		},
	},
	{
		id: '4',
		networkName: 'Network A',
		baName: 'Network A',
		location: 'Kurseong, West Bengal',
		supplyStatus: {
			label: 'Request received',
			status: 'requested',
		},
		siteCount: 100,
		networkAdmin: {
			image: '',
			name: 'Aarushi Pawar',
		},
	},
	{
		id: '5',
		networkName: 'Network A',
		baName: 'Network A',
		location: 'Kurseong, West Bengal',
		supplyStatus: {
			label: 'In stock',
			status: 'stock',
		},
		siteCount: 100,
		networkAdmin: {
			image: '',
			name: 'Aarushi Pawar',
		},
	},
	{
		id: '6',
		networkName: 'Network A',
		baName: 'Network A',
		location: 'Kurseong, West Bengal',
		supplyStatus: {
			label: 'Request received',
			status: 'requested',
		},
		siteCount: 100,
		networkAdmin: {
			image: '',
			name: 'Aarushi Pawar',
		},
	},
	{
		id: '7',
		networkName: 'Network A',
		baName: 'Network A',
		location: 'Kurseong, West Bengal',
		supplyStatus: {
			label: 'In stock',
			status: 'stock',
		},
		siteCount: 100,
		networkAdmin: {
			image: '',
			name: 'Aarushi Pawar',
		},
	},
	{
		id: '8',
		networkName: 'Network A',
		baName: 'Network A',
		location: 'Kurseong, West Bengal',
		supplyStatus: {
			label: 'In stock',
			status: 'stock',
		},
		siteCount: 100,
		networkAdmin: {
			image: '',
			name: 'Aarushi Pawar',
		},
	},
]

export const trainingContentData = [
	{
		id: '1',
		title: 'Biochar for Climate-resilient Cocoa...',
		tag: 'Farmer',
		uploadDate: 'today',
		fileSize: '4',
		status: 'Archived',
		desc: 'Short description about the file...',
	},
	{
		id: '2',
		title: 'Project name',
		tag: 'Farmer',
		uploadDate: '03 Mar 2024',
		fileSize: '4',
		status: 'Active',
		desc: 'Short description about the file...',
	},
	{
		id: '3',
		title: 'Project name',
		tag: 'Farmer',
		uploadDate: '01 Mar 2024',
		fileSize: '4',
		status: 'Archived',
		desc: 'Short description about the file...',
	},
	{
		id: '4',
		title: 'Project name',
		tag: 'Farmer',
		uploadDate: '24 Feb 2024',
		fileSize: '4',
		status: 'Active',
		desc: 'Short description about the file...',
	},
	{
		id: '5',
		title: 'Project name',
		tag: 'Farmer',
		uploadDate: '24 Feb 2024',
		fileSize: '4',
		status: 'Active',
		desc: 'Short description about the file...',
	},
	{
		id: '6',
		title: 'Project name',
		tag: 'Farmer',
		uploadDate: '20 Feb 2024',
		fileSize: '4',
		status: 'Active',
		desc: 'Short description about the file...',
	},
]

export const batchesData = [
	{
		id: '1',
		networkName: 'Name A',
		batchId: 'BA11A11111',
		biomassQuantity: 10,
		biomassType: 'Biomass A',
		createdDate: '11/03/2024 ',
		biocharQty: '2 kg',
		site: 'Site',
		status: 'approved',
	},
	{
		id: '2',
		networkName: 'Name B',
		batchId: 'BA11A11112',
		biomassQuantity: 10,
		biomassType: 'Biomass B',
		createdDate: '11/03/2024 ',
		biocharQty: '2 kg',
		site: 'Site',
		status: 'approved',
	},
	{
		id: '3',
		networkName: 'Name C',
		batchId: 'BA11A11113',
		biomassQuantity: 10,
		biomassType: 'Biomass C',
		createdDate: '11/03/2024 ',
		biocharQty: '2 kg',
		site: 'Site',
		status: 'pending',
	},
	{
		id: '4',
		networkName: 'Name D',
		batchId: 'BA11A11114',
		biomassQuantity: 10,
		biomassType: 'Biomass D',
		createdDate: '11/03/2024 ',
		biocharQty: '2 kg',
		site: 'Site',
		status: 'approved',
	},
	{
		id: '5',
		networkName: 'Name E',
		batchId: 'BA11A11115',
		biomassQuantity: 10,
		biomassType: 'Biomass E',
		createdDate: '11/03/2024 ',
		biocharQty: '2 kg',
		site: 'Site',
		status: 'rejected',
	},
]

export const productionBatchDetails = [
	{ label: 'Biomass Aggregator', value: 'BA 1202 (1202)' },
	{ label: 'Crop Name', value: 'Sugarcane' },
	{ label: 'Carbon Percentage', value: '23 %' },
	{ label: 'Carbon Credit', value: '0 tCO2' },
	{ label: 'CO2 Emission', value: '0 tCO2' },
	{ label: 'Methane Emission', value: '0 kg' },
	{ label: 'C-Sink Network', value: 'C SINK 1202 (1202F001)' },
	{ label: 'Biomass Qty', value: '2000 Kg' },
	{ label: 'Density', value: '0.05 t/m3' },
	{ label: 'Short Term Carbon Sink', value: '0.05 tCO2' },
	{ label: 'Carbon Sink Potential', value: '0 tCO2' },
	{
		label: 'Production Time',
		value: '22/01/2024 (04:13 PM)- 22/01/2024 (04:34 PM)',
	},
]

export const productionApplicationDetails = [
	{ label: 'Biomass Aggregator', value: 'BA 1202 (1202)' },
	{ label: 'Farmer Name', value: 'Farmer Name AB' },
	{ label: 'Farmer Phone Number', value: '+91 545455454' },
	{ label: 'Distribution Quantity', value: '200 kg' },
	{ label: 'C-Sink Network', value: 'C SINK 1202 (1202F001)' },
	{ label: 'Farm Location', value: 'Location' },
	{ label: 'Bags Detail', value: '3 bags - 87 litre' },
	{ label: 'Distribution Date and Time', value: '22/01/2024 (04:13 PM)' },
]

export const networkDetails = {
	id: 'newNetwork123',
	baName: 'New BA ABC',
	baShortName: 'BA329C',
	networkName: 'ArtisanPro Network Name',
	networkShortname: 'APS2198',
	location: 'Noida',
	admin: {
		id: 'admin123',
		name: 'Admin new',
		email: '<EMAIL>',
	},
	networkCount: 6,
}

export const biomassCollectionRow = [
	{
		id: '1',
		biomassCollection: 'Lemon Myrtle',
		vehicle: 'Tractor / Gj04ee4267',
		biomassQty: 10,
		network: 'Network Name',
		site: 'Site Name',
		siteDistance: '1 Km',
		dropTime: '15.Mar.24 15:03',
	},
	{
		id: '2',
		biomassCollection: 'Lemon Myrtle',
		vehicle: 'Tractor / Gj04ee4267',
		biomassQty: 80,
		network: 'Network Name',
		site: 'Site Name',
		siteDistance: '1 Km',
		dropTime: '15.Mar.24 15:03',
	},
	{
		id: '3',
		biomassCollection: 'Lemon Myrtle',
		vehicle: 'Tractor / Gj04ee4267',
		biomassQty: 40,
		network: 'Network Name',
		site: 'Site Name',
		siteDistance: '1 Km',
		dropTime: '15.Mar.24 15:03',
	},
]

export const biomassAvailableRow = [
	{
		id: '1',
		biomassName: 'Lemon Myrtle',
		biomassQty: 10,
		network: 'Network Name',
		site: 'Site Name',
		farmLocation: 'Locationou',
		siteDistance: '2 Km',
	},
	{
		id: '2',
		biomassName: 'Lemon Myrtle',
		biomassQty: 10,
		network: 'Network Name',
		site: 'Site Name',
		farmLocation: 'Locationou',
		siteDistance: '2 Km',
	},
	{
		id: '3',
		biomassName: 'Lemon Myrtle',
		biomassQty: 20,
		network: 'Network Name',
		site: 'Site Name',
		farmLocation: 'Locationou',
		siteDistance: '2 Km',
	},
	{
		id: '4',
		biomassName: 'Lemon Myrtle',
		biomassQty: 40,
		network: 'Network Name',
		site: 'Site Name',
		farmLocation: 'Locationou',
		siteDistance: '2 Km',
	},
]
export const artisanProDetails = {
	id: 'Ap123',
	baName: 'New BA ABC',
	baShortName: 'BA329C',
	name: 'artisanPro',
	apShortName: 'BBAS2',
	methaneStrategy: 'Methane Compensation Startegy',
	isTrained: true,
	packingBagType: 5,
	measuringContainer: 32,
	operatorCount: 3,
	adminCount: 12,
	networkName: 'ArtisanPro Network Name',
	networkShortname: 'APS2198',
	location: 'Noida',
	admin: {
		id: 'admin123',
		name: 'Admin new',
		email: '<EMAIL>',
	},
}
export const dummyImageList: IImage[] = [
	{
		id: 'ksajdkashkd',
		url: 'https://picsum.photos/seed/picsum/200/300',
		fileName: '',
		path: '',
	},
	{
		id: 'dummy2',
		url: 'https://picsum.photos/200/300?grayscale',
		fileName: '',
		path: '',
	},
	{
		id: 'dummy1',
		url: 'https://picsum.photos/200/300/?blur',
		fileName: '',
		path: '',
	},
	{
		id: 'dummy2',
		url: 'https://picsum.photos/200/300?grayscale',
		fileName: '',
		path: '',
	},
]

export const dummyCSinkNetwork = [
	{
		id: '1',
		name: 'Klin name A',
		kilnShortName: 'KL123',
		locationName: 'Noida',
		address: 'Noida',
		kilnOperator: '--',
	},
	{
		id: '2',
		name: 'Klin name A',
		kilnShortName: 'KL123',
		locationName: 'Noida',
		address: 'Noida',
		kilnOperator: '--',
	},
	{
		id: '3',
		name: 'Klin name A',
		kilnShortName: 'KL123',
		locationName: 'Noida',
		address: 'Noida',
		kilnOperator: '--',
	},
	{
		id: '4',
		name: 'Klin name A',
		kilnShortName: 'KL123',
		locationName: 'Noida',
		address: 'Noida',
		kilnOperator: '--',
	},
	{
		id: '5',
		name: 'Klin name A',
		kilnShortName: 'KL123',
		locationName: 'Noida',
		address: 'Noida',
		kilnOperator: '--',
	},
	{
		id: '6',
		name: 'Klin name A',
		kilnShortName: 'KL123',
		locationName: 'Noida',
		address: 'Noida',
		kilnOperator: '--',
	},
]

export const FarmerData = [
	{
		id: '1',
		farmerName: 'Farmer A',
		aggregator: 'BA A',
		cSinkNetwork: 'Network A',
		farmsCount: 2,
		totalSize: '5 hactares',
		totalBiomassQuantity: '34 kg',
	},
	{
		id: '2',
		farmerName: 'Farmer B',
		aggregator: 'BA B',
		cSinkNetwork: 'Network A',
		farmsCount: 2,
		totalSize: '5 hactares',
		totalBiomassQuantity: '34 kg',
	},
	{
		id: '3',
		farmerName: 'Farmer C',
		aggregator: 'BA C',
		cSinkNetwork: 'Network B',
		farmsCount: 4,
		totalSize: '5 hactares',
		totalBiomassQuantity: '14 kg',
	},
	{
		id: '4',
		farmerName: 'Farmer D',
		aggregator: 'BA D',
		cSinkNetwork: 'Network D',
		farmsCount: 5,
		totalSize: '5 hactares',
		totalBiomassQuantity: '7 kg',
	},
	{
		id: '5',
		farmerName: 'Farmer E',
		aggregator: 'BA E',
		cSinkNetwork: 'Network A',
		farmsCount: 1,
		totalSize: '5 hactares',
		totalBiomassQuantity: '34 kg',
	},
	{
		id: '6',
		farmerName: 'Farmer F',
		aggregator: 'BA F',
		cSinkNetwork: 'Network B',
		farmsCount: 7,
		totalSize: '5 hactares',
		totalBiomassQuantity: '34 kg',
	},
]

export const FarmData = [
	{
		id: '1',
		farm: 'Farm A',
		farmerName: 'Farmer A',
		crop: 'Lemon Myrtle',
		stage: 'cropping',
		status: 'accepted',
		location: 'Noida',
		biomassQuantity: 34,
		size: 100,
		aggregator: 'BA A',
		cSinkNetwork: 'Network A',
		totalBiomassQuantity: '34 kg',
	},
	{
		id: '2',
		farm: 'Farm B',
		farmerName: 'Farmer B',
		crop: 'Lemon Myrtle',
		stage: 'cropping',
		status: 'accepted',
		location: 'Noida',
		biomassQuantity: 35,
		size: 101,
		aggregator: 'BA B',
		cSinkNetwork: 'Network A',
	},
	{
		id: '3',
		farm: 'Farm C',
		farmerName: 'Farmer C',
		crop: 'Lemon Myrtle',
		stage: 'cropping',
		status: 'pending',
		location: 'Noida',
		biomassQuantity: 37,
		size: 102,
		aggregator: 'BA C',
		cSinkNetwork: 'Network C',
	},
	{
		id: '4',
		farm: 'Farm D',
		farmerName: 'Farmer D',
		crop: 'Lemon Myrtle',
		stage: 'cropping',
		status: 'accepted',
		location: 'Jaipur',
		biomassQuantity: 31,
		size: 109,
		aggregator: 'BA D',
		cSinkNetwork: 'Network D',
	},
	{
		id: '5',
		farm: 'Farm E',
		farmerName: 'Farmer E',
		crop: 'Lemon Myrtle',
		stage: 'cropping',
		status: 'rejected',
		location: 'Noida',
		biomassQuantity: 30,
		size: 80,
		aggregator: 'BA E',
		cSinkNetwork: 'Network A',
	},
	{
		id: '6',
		farm: 'Farm F',
		farmerName: 'Farmer F',
		crop: 'Lemon Myrtle',
		stage: 'cropping',
		status: 'accepted',
		location: 'Noida',
		biomassQuantity: 34,
		size: 100,
		aggregator: 'BA F',
		cSinkNetwork: 'Network B',
	},
	{
		id: '7',
		farm: 'Farm G',
		farmerName: 'Farmer G',
		crop: 'Lemon Myrtle',
		stage: 'cropping',
		status: 'accepted',
		location: 'Noida',
		biomassQuantity: 31,
		size: 100,
		aggregator: 'BA G',
		cSinkNetwork: 'Network G',
	},
	{
		id: '8',
		farm: 'Farm H',
		farmerName: 'Farmer H',
		crop: 'Lemon Myrtle',
		stage: 'cropping',
		status: 'accepted',
		location: 'Gurgaon',
		biomassQuantity: 34,
		size: 100,
		aggregator: 'BA F',
		cSinkNetwork: 'Network B',
	},
]

export const SingleFarmerData = [
	{
		id: '1',
		farmerName: 'Farmer A',
		farm: 'Farm A',
		crop: 'Lemon Myrtle',
		stage: 'Cropping',
		status: 'Accepted',
		cSinkNetwork: 'Network A',
		aggregator: 'BA A',
		location: 'Noida',
		size: '100 acre',
		biomass: '19kg',
	},
	{
		id: '2',
		farmerName: 'Farmer A',
		farm: 'Farm A',
		crop: 'Lemon Myrtle',
		stage: 'Cropping',
		status: 'Accepted',
		cSinkNetwork: 'Network A',
		aggregator: 'BA A',
		location: 'Noida',
		size: '100 acre',
		biomass: '19kg',
	},
]

export const FarmRowDetails = [
	{
		id: '1',
		date: 'yyyy-mm-dd',
		cropStage: 'cropping',
		crop: 'Lemon Myrtle',
		cSinkNetwork: 'Network A',
		aggregator: 'BA A',
		farmer: 'Farmer A',
		size: '100 acre',
		biomassQuantity: '19 kg',
		address: 'Noida',
	},
	{
		id: '2',
		date: 'yyyy-mm-dd',
		cropStage: 'cropping',
		crop: 'Lemon Myrtle',
		cSinkNetwork: 'Network A',
		aggregator: 'BA A',
		farmer: 'Farmer A',
		size: '100 acre',
		biomassQuantity: '19 kg',
		address: 'Noida',
	},
	{
		id: '3',
		date: 'yyyy-mm-dd',
		cropStage: 'completed',
		crop: 'Lemon Myrtle',
		cSinkNetwork: 'Network A',
		aggregator: 'BA A',
		farmer: 'Farmer A',
		size: '100 acre',
		biomassQuantity: '19 kg',
		address: 'Noida',
	},
	{
		id: '4',
		date: 'yyyy-mm-dd',
		cropStage: 'cropping',
		crop: 'Lemon Myrtle 4',
		cSinkNetwork: 'Network A 4',
		aggregator: 'BA A 4',
		farmer: 'Farmer A 4',
		size: '100 acre 4',
		biomassQuantity: '19 kg',
		address: 'Noida',
	},
]

export const artisanProDetailsCarbonCredit = [
	{
		id: '1',
		creditId: 'test1',
		batchId: 'batch1',
		network: 'network1',
		aggregator: 'aggregator',
		emission: 'emission',
		status: 'Issue',
		artisanProCount: 5,
		crop: 'Lemon',
	},
	{
		id: '2',
		creditId: 'test2',
		batchId: 'batch2',
		network: 'network2',
		aggregator: 'aggregator2',
		emission: 'emission2',
		status: 'Issue2',
		artisanProCount: 5,
		crop: 'Lemon',
	},
]
