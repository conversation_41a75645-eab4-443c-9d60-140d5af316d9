import { authAxios } from '@/contexts'
import { IInventories } from '@/interfaces'
import { defaultLimit, defaultPage } from '@/utils/constant'
import { useQuery } from '@tanstack/react-query'
import { useSearchParams } from 'react-router-dom'

export const useInventory = () => {
	const [searchParams, setSearchParams] = useSearchParams()
	const paramsLimit = searchParams.get('limit') || defaultLimit.toString()
	const paramsPage = searchParams.get('page') || defaultPage.toString()
	const paramsBAId = searchParams.get('baId') || ''
	const paramsNetworkId = searchParams.get('networkId') || ''
	const paramsSiteId = searchParams.get('siteId') || ''
	const paramsKilnId = searchParams.get('kilnId') || ''
	const paramsSearchName = searchParams.get('searchName') || ''
	const graphTab = searchParams.get('graphTab') || 'biomass'
	const productionTab = searchParams.get('productionTab') || 'biomassCollection'
	const paramsSubnetwork = searchParams.get('subNetwork') || ''

	const allInventory = useQuery({
		queryKey: [
			'allInventory',
			paramsSearchName,
			paramsBAId,
			paramsNetworkId,
			paramsSiteId,
			paramsKilnId,
			paramsLimit,
			paramsPage,
		],
		queryFn: () => {
			const queryParams = new URLSearchParams({
				limit: paramsLimit,
				page: paramsPage,
				search: paramsSearchName,
				biomassAggregatorId: paramsBAId,
				networkId: paramsNetworkId,
				siteId: paramsSiteId,
				kilnId: paramsKilnId,
			})
			return authAxios.get<{ count: number; inventories: IInventories[] }>(
				`/new/inventories?${queryParams.toString()}`
			)
		},
		select: ({ data }) => data,
	})

	return {
		inventory: allInventory?.data,
		loadingInventory: allInventory?.isLoading,
		setSearchParams,
		searchParams,
		paramsSubnetwork,
		graphTab,
		productionTab,
	}
}
