import { QueryInput } from '@/components/QueryInputs'
import {  ArrowLeftRounded,  Search } from '@mui/icons-material'
import { TabContext, TabList, TabPanel } from '@mui/lab'
import {
	<PERSON>readcrumbs,
	IconButton,
	Stack,
	styled,
	Tab,
	Typography,
	useTheme,
} from '@mui/material'
import {
	GridColDef,
	GridMoreVertIcon,
	GridRenderCellParams,
	GridValidRowModel,
} from '@mui/x-data-grid'
import { format } from 'date-fns'
import { useCallback, useMemo } from 'react'
import { Link } from 'react-router-dom'
import { useInventory } from '.'
import {
	CustomChip,
	CustomDataGrid,
	CustomHeader,
} from '../../../../components'

export const Inventory = () => {
	const theme = useTheme()

	const { inventory, loadingInventory } = useInventory()
	const getStatusChipClass = useCallback((type: string) => {
		switch (type) {
			case 'Approved':
				return 'approved'
			case 'Rejected':
				return 'rejected'
			default:
				return 'pending'
		}
	}, [])
	const inventoryTableColumn: GridColDef<GridValidRowModel>[] = useMemo(
		() => [
			{
				field: 'displayId',
				headerName: 'Inventory ID',
				minWidth: 175,
				flex: 1,
			},
			{
				field: 'biomassAggregatorName',
				headerName: 'BA Name',
				minWidth: 175,
				flex: 1,
			},
			{
				field: 'artisanProName',
				headerName: 'Network/Artisan Pro',
				minWidth: 175,
				flex: 1,
				valueGetter: (params) =>
					params.row.artisanProName || params.row.csinkNetworkName || '-',
			},
			{
				field: 'siteName',
				headerName: 'Site/Kiln ID',
				minWidth: 120,
				flex: 1,
				valueGetter: (params) =>
					params.row.siteShortName || params.row.kilnShortName,
			},
			{
				field: 'kilnProcessDisplayIds',
				headerName: 'Batch ID',
				minWidth: 160,
				flex: 1,
				valueGetter: (params) => params?.row?.kilnProcessDisplayIds.join(','),
			},
			{
				field: 'packagingType',
				headerName: 'Packaging Type',
				minWidth: 120,
				flex: 1,
			},
			{
				field: 'bagQuantity',
				headerName: 'Bag Details',
				minWidth: 80,
				flex: 1,
				valueGetter: (params) =>
					`${params?.row?.bagQuantity} ${params?.row?.bagQuantityUnit}`,
			},
			{
				field: 'packedDate',
				headerName: 'Packed Date',
				headerAlign: 'center',
				align: 'center',
				minWidth: 120,
				flex: 1,
				valueGetter: (params) =>
					params.row.packedDate
						? format(params?.row?.packedDate, 'yyyy-MM-dd')
						: '-',
			},
			{
				field: 'status',
				headerName: 'Status',
				headerAlign: 'center',
				align: 'center',
				minWidth: 120,
				flex: 1,
				renderCell: (params: GridRenderCellParams) => {
					return (
						<CustomChip
							appliedClass={getStatusChipClass(params?.row?.status)}
							label={params?.row?.status}
						/>
					)
				},
			},
			{
				field: 'action',
				headerName: '',
				maxWidth: 100,
				flex: 1,
				renderCell: () => (
					<IconButton size='small' onClick={() => {}}>
						<GridMoreVertIcon />
					</IconButton>
				),
			},
		],
		[getStatusChipClass]
	)

	// const StatusChart = useMemo(
	// 	() => ({
	// 		series: [45, 32, 25, 11, 60],
	// 		options: {
	// 			labels: [
	// 				'Category 1: 45',
	// 				'Category 2: 32',
	// 				'Category 3: 25 ',
	// 				'Category 4: 11',
	// 				'Others: 60',
	// 			],
	// 			legend: {
	// 				show: true,
	// 				markers: {
	// 					fillColors: [
	// 						theme.palette.error.main,
	// 						theme.palette.success.main,
	// 						theme.palette.warning.main,
	// 						theme.palette.info.main,
	// 						theme.palette.error.light,
	// 					],
	// 				},
	// 			},
	// 			dataLabels: {
	// 				enabled: false,
	// 				textAnchor: 'middle',
	// 				style: {
	// 					fontSize: '12px',
	// 					fontWeight: '700',
	// 					colors: [
	// 						theme.palette.neutral['900'],
	// 						theme.palette.neutral['900'],
	// 						theme.palette.neutral['900'],
	// 					],
	// 				},
	// 			},
	// 			tooltip: { enabled: false },
	// 			responsive: [
	// 				{
	// 					breakpoint: theme.breakpoints.values.sm,
	// 					options: {
	// 						chart: {
	// 							width: theme.spacing(35),
	// 						},
	// 					},
	// 				},
	// 				{
	// 					breakpoint: theme.breakpoints.values.xl,
	// 					options: {
	// 						chart: {
	// 							width: theme.spacing(38),
	// 						},
	// 					},
	// 				},
	// 			],
	// 			chart: {
	// 				type: 'donut',
	// 				width: 100,
	// 			},
	// 			plotOptions: {
	// 				pie: {
	// 					donut: {
	// 						size: '70%',
	// 					},
	// 				},
	// 			},
	// 			fill: {
	// 				colors: [
	// 					theme.palette.error.main,
	// 					theme.palette.success.main,
	// 					theme.palette.warning.main,
	// 					theme.palette.info.main,
	// 					theme.palette.error.light,
	// 				],
	// 			},
	// 			states: {
	// 				hover: { filter: { type: 'darken', value: 0.7 } },
	// 				active: { filter: { type: 'none', value: 0 } },
	// 			},
	// 			stroke: { width: 0 },
	// 		},
	// 	}),
	// 	[theme]
	// )

	// const LocationImage = () => {
	// 	return <Box padding={0} component='img' src={mapCard} />
	// }

	// const CreditComponent = ({ title }: { title: string }) => {
	// 	return (
	// 		<Stack className='credit-details-card'>
	// 			<Typography variant='body2' ml={4}>
	// 				{title}
	// 			</Typography>
	// 			<Stack className='chart'>
	// 				<ReactApexChart
	// 					options={StatusChart.options as ApexOptions}
	// 					series={StatusChart.series}
	// 					type='donut'
	// 				/>
	// 			</Stack>
	// 		</Stack>
	// 	)
	// }

	// const GridHeaderComponents = () => {
	// 	return (
	// 		<Stack className='grid-header-component' columnGap={2.5}>
	// 			<CustomCard
	// 				sx={{
	// 					height: theme.spacing(27.8),
	// 					width: theme.spacing(43.75),
	// 					padding: 0,
	// 				}}
	// 				headerComponent={<LocationImage />}
	// 			/>
	// 			<CustomCard
	// 				sx={{ height: theme.spacing(27.8), width: theme.spacing(43.75) }}
	// 				headerComponent={<CreditComponent title='Credit' />}
	// 			/>
	// 			<CustomCard
	// 				sx={{ height: theme.spacing(27.8), width: theme.spacing(43.75) }}
	// 				headerComponent={<CreditComponent title='Biomass Type' />}
	// 			/>
	// 		</Stack>
	// 	)
	// }

	return (
		<StyledContainer>
			<Stack className='header'>
				<CustomHeader
					showBottomBorder={true}
					headingComponent={
						<Breadcrumbs>
							<Link
								to='/dashboard/production'
								style={{
									textDecoration: 'none',
									color: theme.palette.neutral[300],
									display: 'flex',
									alignItems: 'center',
								}}>
								<Typography sx={{ display: 'flex', alignItems: 'center' }}>
									<ArrowLeftRounded fontSize='small' /> Production
								</Typography>
							</Link>
							<Typography variant='h6'>Inventory</Typography>
						</Breadcrumbs>
					}
					showButton={false}
				/>
			</Stack>

			<Stack className='container'>
				{/* <GridHeaderComponents /> */}
				<TabContext value={'all'}>
					<TabList className='tab-list'>
						<Tab label='All Inventory' value='all' />
					</TabList>
					<TabPanel value='all'>
						<CustomDataGrid
							showPagination={true}
							rows={inventory?.inventories ?? []}
							loading={loadingInventory}
							columns={inventoryTableColumn}
							rowCount={inventory?.count}
							headerComponent={
								<Stack className='header-filter-search' gap={2}>
									<QueryInput
										className='search-textFiled'
										queryKey='searchName'
										placeholder='Search'
										setPageOnSearch
										InputProps={{
											startAdornment: <Search fontSize='small' />,
										}}
									/>
									{/* {userDetails?.accountType === userRoles.Admin && (
										<CommonFilter />
									)} */}
								</Stack>
							}
						/>
					</TabPanel>
				</TabContext>
			</Stack>
		</StyledContainer>
	)
}

const StyledContainer = styled(Stack)(({ theme }) => ({
	'.header': {
		padding: theme.spacing(2, 0),
		borderBottom: `${theme.spacing(0.125)} solid ${
			theme.palette.neutral['100']
		}`,
		'.active-chip': {
			backgroundColor: theme.palette.custom.blue[200],
			color: theme.palette.custom.blue[500],
			borderRadius: theme.spacing(0.8),
			Height: theme.spacing(4),
			minWidth: theme.spacing(15),
		},
		'.disabled-chip': {
			backgroundColor: theme.palette.common.white,
			border: '1px dotted',
			borderColor: theme.palette.neutral[300],
			color: theme.palette.neutral[300],
			borderRadius: theme.spacing(0.8),
			Height: theme.spacing(4),
			minWidth: theme.spacing(15),
		},
	},
	'.container': {
		padding: theme.spacing(0),
		'.MuiDataGrid-columnHeader': {
			backgroundColor: theme.palette.neutral[20],
		},
		'.tab-list': {
			background: theme.palette.neutral['50'],
			padding: theme.spacing(0, 4),
		},
		'.grid-header-component': {
			flexWrap: 'wrap',
			alignItem: 'center',
			flexDirection: 'row',
			gap: theme.spacing(2),
			padding: theme.spacing(2, 4),
			background: theme.palette.neutral['50'],
		},
		'.header-filter-search': {
			flexDirection: 'row',
			alignItems: 'center',
			'.search-textFiled': {
				minWidth: 260,
				maxWidth: 260,
				width: '100%',
				'.MuiInputBase-root': {
					height: theme.spacing(4.5),
					borderRadius: theme.spacing(1.25),
				},
			},
			'.form-controller': {
				margin: theme.spacing(0.125),
				minWidth: theme.spacing(12.5),
				width: '100%',
			},
		},
	},
}))
