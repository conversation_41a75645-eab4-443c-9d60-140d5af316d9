import { TabContext, TabList, TabPanel } from '@mui/lab'
import {
	Box,
	IconButton,
	Stack,
	styled,
	Tab,
	Typography,
	useTheme,
} from '@mui/material'
import { useCallback, useMemo, useState } from 'react'
import { useBiomassCollection } from './useBiomassCollection'
import {
	GridColDef,
	GridRenderCellParams,
	GridValidRowModel,
} from '@mui/x-data-grid'
import { Biomass_Default_Unit, IImage } from '@/interfaces'
import { format } from 'date-fns'
import { proxyImage } from '@/utils/helper'
import { CustomDataGrid, CustomHeader, ImageCarouselDialog } from '@/components'
import { userRoles } from '@/utils/constant'
import { useAuthContext } from '@/contexts'

enum tabEnum {
	all = 'all',
	network = 'network',
	artisanPro = 'artisanPro',
}

export const BiomassCollection = () => {
	const theme = useTheme()
	const {
		biomassCollection,
		biomasssAvailable,
		isBiomassAvailableLoading,
		setSearchParams,
		tab,
		isBioCollectionLoading,
	} = useBiomassCollection()
	const [tabValue, setTabValue] = useState<tabEnum | string>(tab || tabEnum.all)
	const [imageList, setImageList] = useState<IImage[]>([])
	const [imageIndexShow, setImageIndexShow] = useState<number>(0)
	const [isImageDialogOpen, setIsImageDialogOpen] = useState<boolean>(false)
	const { userDetails } = useAuthContext()
	const handleChange = useCallback(
		(_: unknown, newValue: tabEnum) => {
			setTabValue(newValue)
			setSearchParams((prev) => ({
				...prev,
				bioCollectionLimit: 10,
				bioCollectionPage: 0,
				tab: newValue,
			}))
		},
		[setSearchParams]
	)
	const tabs = [
		{
			label: 'All',
			value: tabEnum.all,
			hide: false,
		},
		{
			label: 'C Sink Network',
			value: tabEnum.network,
			hide: [userRoles.ArtisanPro, userRoles.artisanProNetworkManager].includes(
				userDetails?.accountType as userRoles
			),
		},
		{
			label: 'Artisan Pro',
			value: tabEnum.artisanPro,
			hide: userDetails?.accountType === userRoles.cSinkNetwork,
		},
	]

	const networkHeaderName = useMemo(() => {
		switch (tab) {
			case 'network':
				return 'Network'

			case 'artisanPro':
				return 'Artisian Pro'
			default:
				return 'Network/Artisian Pro'
		}
	}, [tab])
	const siteHeaderName = useMemo(() => {
		switch (tab) {
			case 'network':
				return 'Kiln'

			case 'artisanPro':
				return 'Site'
			default:
				return 'Kiln/Site'
		}
	}, [tab])
	const biomassCollectionColumn: GridColDef<GridValidRowModel>[] = useMemo(
		() => [
			{
				field: 'fpuName',
				headerName: tab === 'network' ? 'Farmer' : 'Biomass Source/Farmer',
				minWidth: 120,
				flex: 1,
				renderCell: (params: GridRenderCellParams) => {
					return (
						<Typography
							variant='subtitle1'
							sx={{
								color: theme.palette.neutral['900'],
							}}>
							{tab === 'network'
								? params?.row?.farmerName
								: params?.row?.fpuName || params?.row?.farmerName}
						</Typography>
					)
				},
			},
			{
				field: 'vehicleType',
				headerName: 'Vehicle',
				minWidth: 120,
				flex: 1,
				renderCell: (params: GridRenderCellParams) => {
					const { vehicleType, vehicleNumber } = params.row
					const value = vehicleType
						? `${vehicleType}${vehicleNumber ? ` / ${vehicleNumber}` : ''}`
						: '-'
					return (
						<Typography
							variant='subtitle1'
							sx={{
								color: theme.palette.neutral['900'],
							}}>
							{value}
						</Typography>
					)
				},
			},
			{
				field: 'biomassQuantity',
				headerName: `Biomass Qty (${Biomass_Default_Unit})`,
				minWidth: 120,
				align: 'center',
				headerAlign: 'center',
				flex: 1,

				renderCell: (params: GridRenderCellParams) => {
					return (
						<Typography
							variant='subtitle1'
							sx={{
								color: theme.palette.neutral['900'],
							}}>
							{params?.row?.biomassQuantity || '-'}
						</Typography>
					)
				},
			},
			{
				field: 'networkName',
				headerName:
					tab === 'artisanPro'
						? 'Artisian Pro'
						: tab === 'network'
						? 'Network'
						: 'Network/Artisian Pro',
				minWidth: 120,
				flex: 1,
				renderCell: (params: GridRenderCellParams) => {
					return (
						<Typography
							variant='subtitle1'
							sx={{
								color: theme.palette.neutral['900'],
							}}>
							{tab === 'network'
								? params?.row?.networkName
								: params?.row?.networkName || params?.row?.artisanProName}
						</Typography>
					)
				},
			},
			{
				field: 'siteName',
				headerName:
					tab === 'network'
						? 'Kiln'
						: tab === 'artisanPro'
						? 'Site'
						: 'Site/Kiln',
				minWidth: 120,
				flex: 1,
				renderCell: (params: GridRenderCellParams) => {
					return (
						<Typography
							variant='subtitle1'
							sx={{
								color: theme.palette.neutral['900'],
							}}>
							{tab === 'network'
								? params?.row?.kilnName
								: params?.row?.siteName || params?.row?.kilnName}
						</Typography>
					)
				},
			},
			{
				field: 'distance',
				headerName:
					tab === 'network' ? 'Biomass-Kiln Distance' : 'Biomass-Site Distance',
				minWidth: 120,
				align: 'center',
				headerAlign: 'center',
				flex: 1,
				renderCell: (params: GridRenderCellParams) => {
					return (
						<Typography
							variant='subtitle1'
							sx={{
								color: theme.palette.neutral['900'],
							}}>
							{params?.row?.distance ?? '-'}
						</Typography>
					)
				},
			},
			{
				field: 'images',
				headerName: 'Images',
				minWidth: 200,
				align: 'center',
				headerAlign: 'center',
				flex: 1,
				renderCell: (params: GridRenderCellParams) => {
					const imageLength = params?.row?.images.length
					return (
						<Stack direction='row' spacing={0.5} alignItems='center'>
							{params?.row?.images
								.slice(0, 2)
								.map((image: IImage, index: number) => (
									<IconButton
										key={index}
										onClick={() => {
											setImageIndexShow(index)
											setIsImageDialogOpen(true)
											setImageList(params.row.images)
										}}>
										<Box
											component='img'
											src={proxyImage(image?.fileName ?? '')}
											alt='connection-image'
											sx={{
												height: theme.spacing(4.5),
												width: theme.spacing(4.5),
												borderRadius: theme.spacing(0.5),
											}}
										/>
									</IconButton>
								))}
							{imageLength > 2 && (
								<IconButton
									size='small'
									onClick={() => {
										setImageIndexShow(0)
										setIsImageDialogOpen(true)
										setImageList(params.row.images)
									}}
									sx={{
										...theme.typography.body1,
										color: theme.palette.primary.main,
									}}>
									+{imageLength - 2}
								</IconButton>
							)}
						</Stack>
					)
				},
			},
			{
				field: 'dropTime',
				headerName: 'Drop Time',
				minWidth: 120,
				align: 'center',
				headerAlign: 'center',
				flex: 1,
				renderCell: (params: GridRenderCellParams) => {
					return (
						<Typography
							variant='subtitle1'
							sx={{
								color: theme.palette.neutral['900'],
							}}>
							{params.row.dropTime
								? format(params?.row?.dropTime, 'dd.MMM.yy HH:mm')
								: '-'}
						</Typography>
					)
				},
			},
		],
		[tab, theme]
	)

	const biomassAvailableColumn: GridColDef<GridValidRowModel>[] = [
		// { field: 'biomassTypeId', headerName: '', width: 0 },
		{
			field: 'biomassTypeName',
			headerName: 'Biomass Name',
			minWidth: 120,
			flex: 1,
			renderCell: (params: GridRenderCellParams) => {
				return (
					<Typography
						variant='subtitle1'
						sx={{
							color: theme.palette.neutral['900'],
						}}>
						{params?.row?.biomassTypeName ? params?.row?.biomassTypeName : '-'}
					</Typography>
				)
			},
		},
		{
			field: 'biomassQty',
			headerName: `Biomass Qty (${Biomass_Default_Unit})`,
			align: 'center',
			headerAlign: 'center',
			minWidth: 120,
			flex: 1,

			renderCell: (params: GridRenderCellParams) => {
				return (
					<Typography
						variant='subtitle1'
						sx={{
							color: theme.palette.neutral['900'],
						}}>
						{params?.row?.currentBiomassQuantity
							? params?.row?.currentBiomassQuantity
							: '-'}
					</Typography>
				)
			},
		},
		{
			field: 'networkName',
			headerName: networkHeaderName,
			minWidth: 120,
			flex: 1,

			renderCell: (params: GridRenderCellParams) => {
				return (
					<Typography
						variant='subtitle1'
						sx={{
							color: theme.palette.neutral['900'],
						}}>
						{params?.row?.networkName ?? params?.row?.artisanProName ?? '-'}
					</Typography>
				)
			},
		},
		{
			field: 'site',
			headerName: siteHeaderName,
			minWidth: 120,
			flex: 1,

			renderCell: (params: GridRenderCellParams) => {
				return (
					<Typography
						variant='subtitle1'
						sx={{
							color: theme.palette.neutral['900'],
						}}>
						{params?.row?.siteName ?? params?.row?.kilnName ?? '-'}
					</Typography>
				)
			},
		},
	]

	return (
		<StyleContainer>
			<ImageCarouselDialog
				open={isImageDialogOpen}
				close={() => {
					setIsImageDialogOpen(false)
				}}
				ImagesList={imageList}
				imageIndex={imageIndexShow}
			/>
			<Box className='header'>
				<CustomHeader
					showBottomBorder={true}
					heading='Biomass Collection'
					showButton={true}
				/>
			</Box>
			<TabContext value={tabValue}>
				<TabList className='tabList' onChange={handleChange}>
					{tabs.map(({ label, value, hide }, index) =>
						!hide ? <Tab key={index} label={label} value={value} /> : null
					)}
				</TabList>

				<TabPanel value={tabEnum.all} className='tab-panel'>
					<Stack className='container'>
						<Typography variant='h5'>Biomass Collection</Typography>
						<CustomDataGrid
							showPagination={true}
							rows={biomassCollection?.siteBiomassList ?? []}
							columns={biomassCollectionColumn}
							rowCount={biomassCollection?.count ?? 0}
							loading={isBioCollectionLoading}
							pageName={'bioCollectionPage'}
							limitName={'bioCollectionLimit'}
							sx={{
								maxHeight: 400,
								minHeight: 400,
								border: `1px solid ${theme.palette.neutral['100']}`,
							}}
						/>
					</Stack>
					<Stack className='container'>
						<Typography variant='h5'>Biomass Available</Typography>
						<CustomDataGrid
							showPagination={true}
							rows={biomasssAvailable?.biomassAvailable ?? []}
							loading={isBiomassAvailableLoading}
							columns={biomassAvailableColumn}
							rowCount={biomasssAvailable?.count ?? 0}
							pageName='bioAvailablePage'
							limitName='bioAvailableLimit'
							sx={{
								maxHeight: 400,
								minHeight: 400,
								border: `1px solid ${theme.palette.neutral['100']}`,
							}}
						/>
					</Stack>
				</TabPanel>
				<TabPanel value={tabEnum.network} className='tab-panel'>
					<Stack className='container'>
						<Typography variant='h5'>Biomass Collection</Typography>
						<CustomDataGrid
							showPagination={true}
							rows={biomassCollection?.siteBiomassList ?? []}
							columns={biomassCollectionColumn}
							rowCount={biomassCollection?.count ?? 0}
							loading={isBioCollectionLoading}
							pageName={'bioCollectionPage'}
							limitName={'bioCollectionLimit'}
							sx={{
								maxHeight: 400,
								minHeight: 400,
								border: `1px solid ${theme.palette.neutral['100']}`,
							}}
						/>
					</Stack>
					<Stack className='container'>
						<Typography variant='h5'>Biomass Available</Typography>
						<CustomDataGrid
							showPagination={true}
							rows={biomasssAvailable?.biomassAvailable ?? []}
							loading={isBiomassAvailableLoading}
							columns={biomassAvailableColumn}
							rowCount={biomasssAvailable?.count ?? 0}
							sx={{
								maxHeight: 400,
								minHeight: 400,
							}}
						/>
					</Stack>
				</TabPanel>
				<TabPanel value={tabEnum.artisanPro} className='tab-panel'>
					<Stack className='container'>
						<Typography variant='h5'>Biomass Collection</Typography>
						<CustomDataGrid
							showPagination={true}
							rows={biomassCollection?.siteBiomassList ?? []}
							columns={biomassCollectionColumn}
							rowCount={biomassCollection?.count ?? 0}
							loading={isBioCollectionLoading}
							pageName={'bioCollectionPage'}
							limitName={'bioCollectionLimit'}
							sx={{
								maxHeight: 400,
								minHeight: 400,
							}}
						/>
					</Stack>
					<Stack className='container'>
						<Typography variant='h5'>Biomass Available</Typography>
						<CustomDataGrid
							showPagination={true}
							rows={biomasssAvailable?.biomassAvailable ?? []}
							loading={isBiomassAvailableLoading}
							columns={biomassAvailableColumn}
							rowCount={biomasssAvailable?.count ?? 0}
							sx={{
								maxHeight: 400,
								minHeight: 400,
							}}
						/>
					</Stack>
				</TabPanel>
			</TabContext>
		</StyleContainer>
	)
}

const StyleContainer = styled(Stack)(({ theme }) => ({
	gap: theme.spacing(2),
	'.header': {
		padding: theme.spacing(4, 0, 2),
		borderBottom: `${theme.spacing(0.125)} solid ${
			theme.palette.neutral['100']
		}`,
	},
	'.tabList': {
		padding: theme.spacing(0, 3),
	},
	'.tab-panel': {
		padding: theme.spacing(0, 3),
		display: 'flex',
		flexDirection: 'column',
		gap: theme.spacing(4),
		'.container': {
			'.grid-header-component': {
				flexDirection: 'row',
				alignItems: 'center',
				'.search-textFiled': {
					minWidth: 334,
					width: '100%',
					'.MuiInputBase-root': {
						height: theme.spacing(4.5),
						borderRadius: theme.spacing(1.25),
					},
				},
				'.form-controller': {
					margin: theme.spacing(0.125),
					minWidth: theme.spacing(18),
					width: '100%',
					'.MuiOutlinedInput-notchedOutline': {
						borderRadius: theme.spacing(1.25),
					},
				},
			},
		},
	},
}))
