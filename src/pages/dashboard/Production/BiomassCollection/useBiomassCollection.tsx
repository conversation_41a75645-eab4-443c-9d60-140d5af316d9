import { authAxios, useAuthContext } from '@/contexts'
import { defaultLimit, defaultPage } from '@/utils/constant'
import { useQuery } from '@tanstack/react-query'
import { useSearchParams } from 'react-router-dom'

export const useBiomassCollection = () => {
	const [searchParams, setSearchParams] = useSearchParams()
	const { userDetails } = useAuthContext()
	const paramsBioCollectionLimit =
		searchParams.get('bioCollectionLimit') || defaultLimit
	const paramsBioCollectionPage =
		searchParams.get('bioCollectionPage') || defaultPage
	const paramsBioAvailableLimit =
		searchParams.get('bioAvailableLimit') || defaultLimit
	const paramsBioAvailablePage =
		searchParams.get('bioAvailablePage') || defaultPage
	const tab = searchParams.get('tab') || ''

	const getBiomassCollection = async () => {
		const { data } = await authAxios.get(
			`/biomass-aggregator/${
				userDetails?.biomassAggregatorId
			}/biomass-drop?subNetwork=${
				tab === 'all' ? '' : tab
			}&limit=${paramsBioCollectionLimit}&page=${paramsBioCollectionPage}`
		)
		return data
	}

	const getBiomassAvailable = async () => {
		const { data } = await authAxios.get(
			`/biomass-aggregator/${
				userDetails?.biomassAggregatorId
			}/biomass-available?subNetwork=${
				tab === 'all' ? '' : tab
			}&limit=${paramsBioAvailableLimit}&page=${paramsBioAvailablePage}`
		)
		return data
	}
	const biomassCollectionQuery = useQuery({
		queryKey: [
			'biomassCollection',
			userDetails?.biomassAggregatorId,
			tab,
			paramsBioCollectionLimit,
			paramsBioCollectionPage,
		],
		queryFn: getBiomassCollection,
		enabled: !!userDetails?.biomassAggregatorId,
	})
	const biomassAvailableQuery = useQuery({
		queryKey: [
			'biomassAvailable',
			userDetails?.biomassAggregatorId,
			tab,
			paramsBioAvailableLimit,
			paramsBioAvailablePage,
		],
		queryFn: getBiomassAvailable,
		enabled: !!userDetails?.biomassAggregatorId,
	})

	return {
		biomassCollection: biomassCollectionQuery.data,
		biomasssAvailable: biomassAvailableQuery.data,
		isBiomassAvailableLoading: biomassAvailableQuery.isLoading,
		isBioCollectionLoading: biomassCollectionQuery.isLoading,
		setSearchParams,
		tab,
	}
}
