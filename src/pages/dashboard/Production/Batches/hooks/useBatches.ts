import { authAxios } from '@/contexts'
import { IBatch } from '@/interfaces'
import { defaultLimit, defaultPage } from '@/utils/constant'
import { useMutation, useQuery } from '@tanstack/react-query'
import { AxiosError } from 'axios'
import { useState } from 'react'
import { useSearchParams } from 'react-router-dom'
import { toast } from 'react-toastify'

export const useBatches = () => {
	const [searchParams, setSearchParams] = useSearchParams()
	const paramsLimit = searchParams.get('limit') || defaultLimit.toString()
	const paramsPage = searchParams.get('page') || defaultPage.toString()
	const paramsBAId = searchParams.get('baId') || ''
	const paramsNetworkId = searchParams.get('networkIds') || ''
	const paramsSiteId = searchParams.get('siteId') || ''
	const paramsFarmerId = searchParams.get('farmerId') || ''
	const paramsKilnId = searchParams.get('kilnId') || ''
	const paramsCropIds = searchParams.getAll('cropIds')

	const paramsSearchName = searchParams.get('searchName') || ''
	const paramsSubnetwork = searchParams.get('subNetwork') || ''
	const paramsStatus = searchParams.getAll('statuses') || ''

	const [showDownloadCheckbox, setShowDownloadCheckbox] =
		useState<boolean>(false)
	const [selectedProcess, setselectedProcess] = useState<string[] | null>(null)

	const getAllBatches = useQuery({
		queryKey: [
			'allBatchesDetails',
			paramsLimit,
			paramsPage,
			paramsBAId,
			paramsNetworkId,
			paramsSiteId,
			paramsKilnId,
			paramsCropIds,
			paramsSearchName,
			paramsFarmerId,
			paramsSubnetwork,
			paramsStatus,
		],
		queryFn: () => {
			const params = {
				limit: paramsLimit,
				page: paramsPage,
				search: paramsSearchName,
				// biomassAggregatorId: paramsBAId,
				networkId: paramsNetworkId,
				cropIds: paramsCropIds.join(','),
				siteId: paramsFarmerId || paramsSiteId,
				kilnIds: paramsKilnId,
				subNetwork: paramsSubnetwork,
				statuses: paramsStatus.join(','),
			}

			const queryParams = new URLSearchParams()

			for (const [key, value] of Object.entries(params)) {
				if (value !== '') {
					queryParams.append(key, value)
				}
			}

			return authAxios.get<IBatch>(`/new/process?${queryParams}`)
		},
		select: ({ data }) => data,
	})

	const downloadExcelFileMuatation = useMutation({
		mutationKey: ['downloadFile'],
		mutationFn: async () => {
			const payload = {
				processIds: selectedProcess,
			}
			const response = await authAxios.post('/process/sink', payload, {
				responseType: 'blob',
			})

			return response
		},
		onSuccess: (response) => {
			const url = window.URL.createObjectURL(new Blob([response.data]))
			const link = document.createElement('a')
			link.href = url
			link.setAttribute('download', `Batches.xlsx`)
			document.body.appendChild(link)
			link.click()
			toast('File Downloaded')

			setselectedProcess(null)
			setShowDownloadCheckbox(false)
		},
		onError: async (err: AxiosError) => {
			if (err.response?.data instanceof Blob) {
				try {
					const text = await err.response.data.text()
					const json = JSON.parse(text)
					toast(json.messageToUser || 'Something went wrong')
				} catch (e) {
					toast('Unexpected error format')
				}
			} else {
				const message = (err.response?.data as { messageToUser: string })
					?.messageToUser
				toast(message || 'Something went wrong')
			}
		},
	})
	const handleDownloadExcelFile = () => {
		downloadExcelFileMuatation.mutate()
	}

	return {
		allBatches: getAllBatches.data,
		setSearchParams,
		showDownloadCheckbox,
		setShowDownloadCheckbox,
		selectedProcess,
		setselectedProcess,
		handleDownloadExcelFile,
		isLoading: getAllBatches.isLoading,
	}
}
