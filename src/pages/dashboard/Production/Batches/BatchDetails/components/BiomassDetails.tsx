import { authAxios, useAuthContext } from '@/contexts'
import * as Yup from 'yup'

import { TRenderDrawerComponent } from '@/interfaces'
import { TModal } from '@/types'

import { Close } from '@mui/icons-material'
import { LoadingButton } from '@mui/lab'
import {
	Button,
	IconButton,
	InputAdornment,
	Stack,
	styled,
	TextField,
	Typography,
	useTheme,
} from '@mui/material'
import { useMutation, useQueryClient } from '@tanstack/react-query'
import { useForm } from 'react-hook-form'
import { useParams } from 'react-router-dom'
import { toast } from 'react-toastify'
import { yupResolver } from '@hookform/resolvers/yup'
import { userRoles } from '@/utils/constant'
import { useCallback, useMemo } from 'react'

type TProps = Pick<TModal, 'onClose'> &
	Pick<TRenderDrawerComponent['data'], 'networkDetails' | 'biomass'>

const biomassDetailSchema = Yup.object({
	biomass: Yup.string().required('Please enter biomass'),
})

type BiomassDetailSchema = Yup.InferType<typeof biomassDetailSchema>

export const BiomassDetails: React.FC<TProps> = ({
	biomass,
	onClose,
	networkDetails,
}) => {
	const theme = useTheme()
	const params = useParams()
	const queryClient = useQueryClient()
	const { userDetails } = useAuthContext()
	const disableUpdate = useMemo(
		() => [userRoles.compliance_manager]
			.includes(userDetails?.accountType as userRoles),
		[userDetails?.accountType])

	const {
		register,
		handleSubmit,
		formState: { errors },
	} = useForm<BiomassDetailSchema>({
		resolver: yupResolver<BiomassDetailSchema>(biomassDetailSchema),
		defaultValues: {
			biomass: String(biomass || ''),
		},
		mode: 'all',
	})

	const editBiomass = useMutation({
		mutationKey: ['editBiomass'],
		mutationFn: async (values: { biomassQuantity: number }) => {
			const apiRoute = networkDetails?.artisanProId
				? `/artisian-pro/${networkDetails?.artisanProId}/site/${
						networkDetails?.siteId
				  }/kiln/${networkDetails.kilnId}/process/${
						params?.id || ''
				  }/edit-biomass`
				: `/cs-network/${networkDetails?.networkId}/kilns/${
						networkDetails.kilnId
				  }/process/${params?.id || ''}/edit-biomass`

			const { data } = await authAxios.put(apiRoute, values)
			return data
		},
		onSuccess: (data) => {
			toast(data?.message)
			queryClient?.refetchQueries({ queryKey: ['batchDetails', params?.id] })
			onClose()
		},
		onError: (error: any) => {
			toast(error?.response?.data?.messageToUser)
		},
	})

	const handleEditBiomass = useCallback(
		(data: BiomassDetailSchema) => {
			editBiomass.mutate({ biomassQuantity: Number(data.biomass) })
		},
		[editBiomass]
	)

	return (
		<StyledStack
			sx={{
				display: 'flex',
				flexDirection: 'column',
				height: '100vh',
			}}>
			<Stack className='header'>
				<Typography variant='body2'>Biomass Details</Typography>
				<IconButton onClick={onClose}>
					<Close />
				</IconButton>
			</Stack>

			<Stack spacing={1} sx={{ flex: 1 }}>
				<Typography sx={{ color: 'grey.400', pl: 3 }} variant='caption'>
					Biomass Qty:
				</Typography>
				<Typography
					variant='subtitle1'
					sx={{ pl: 4 }}>{`${biomass} kg`}</Typography>
				<Stack>
					<Typography
						sx={{ color: 'grey.400', pl: 3, mt: 2 }}
						variant='subtitle1'>
						Biomass Qty:
					</Typography>

					<TextField
						variant='outlined'
						{...register('biomass')}
						disabled={disableUpdate}
						sx={{ mx: 2, color: 'grey.300' }}
						InputProps={{
							endAdornment: <InputAdornment position='end'>kg</InputAdornment>,
						}}
						error={!!errors?.biomass?.message}
						helperText={errors?.biomass?.message}
					/>
				</Stack>
			</Stack>

			<Stack
				direction='row'
				justifyContent='space-between'
				mb={10}
				padding={2}
				gap={2}
				className='buttonContainer'>
				<Button
					onClick={onClose}
					sx={{
						border: `1px solid ${theme.palette.primary.main}`,
					}}>
					Cancel
				</Button>
				<LoadingButton
					loading={editBiomass?.isPending}
					disabled={editBiomass?.isPending || disableUpdate}
					onClick={handleSubmit(handleEditBiomass)}
					variant='contained'>
					Save
				</LoadingButton>
			</Stack>
		</StyledStack>
	)
}

const StyledStack = styled(Stack)(({ theme }) => ({
	gap: theme.spacing(2),
	height: '100vh',
	'.header': {
		flexDirection: 'row',
		justifyContent: 'space-between',
		alignItems: 'center',
		padding: theme.spacing(4.5, 2),
		borderBottom: `${theme.spacing(0.125)} solid ${
			theme.palette.neutral['100']
		}`,
	},
	'.container': {
		padding: theme.spacing(0, 4),
		gap: theme.spacing(5),
		flex: 1,
		'.view_container': {
			'.container_item': {
				display: 'grid',
				gridTemplateColumns: '1fr 1fr',
				'.item': {
					':nth-of-type(even)': {
						justifyContent: 'end',
					},
				},
			},
		},
		'.edit_container': {
			gap: theme.spacing(2),
			marginTop: theme.spacing(2),
			'.editor_item': {
				flexDirection: 'row',
				alignItems: 'start',
				columnGap: theme.spacing(2),
				'.form_control': {
					height: '100%',
					'.select_input': {
						height: theme.spacing(7),
					},
				},
				'.delete_icon': {
					width: theme.spacing(3),
					height: theme.spacing(3),
				},
			},
		},
	},
	'.buttonContainer button': {
		width: theme.spacing(30),
		height: theme.spacing(4.5),
		padding: theme.spacing(1, 2.5),
	},
}))
