import React from 'react'
import { capitalize, Divider, Grid, styled, Typography } from '@mui/material'
import { format } from 'date-fns'
import { HistoryItem } from '../useBatchDetails'

type TProps = {
	statusLabelName: string
	statusLabelDate: string
	approvalName: string
	approvalDate?: Date
	reason: string
	isRejected: boolean
	batchHistory?: HistoryItem[]
	isInfoPopup?: boolean
}

export const ApproveRejectedInformation: React.FC<TProps> = ({
	statusLabelName,
	statusLabelDate,
	approvalName,
	approvalDate,
	reason,
	isRejected,
	batchHistory,
	isInfoPopup,
}) => {
	const RenderRows = () => {
		if (!batchHistory || batchHistory.length === 0) {
			return <Typography>No history available</Typography>
		}

		return (
			<>
				{batchHistory.map((item, index) => {
					const isLastRow = index === batchHistory.length - 1
					const processStatusLabelType =
						item.type && capitalize(`${item.type.replace(/[_-]/g, ' ')}`)
					const processStatusLabel =
						item.processStatus &&
						item.processStatus !== 'started' &&
						`(${item.processStatus.replace(/[_-]/g, ' ')})`

					return (
						<React.Fragment key={item.id}>
							<Grid
								rowSpacing={2}
								gap={2}
								display={'flex'}
								justifyContent={'space-between'}>
								<Grid item xs={6}>
									<Typography className='heading-text' variant='subtitle2'>
										{processStatusLabelType}
										<span>{processStatusLabel}:</span>
									</Typography>
									<Typography className='details-text' variant='subtitle1'>
										{item.actioned_by.name}
									</Typography>
								</Grid>
								<Grid item xs={6}>
									<Typography className='heading-text' variant='subtitle2'>
										Date:
									</Typography>
									<Typography className='details-text' variant='subtitle1'>
										{format(new Date(item.actionedAt), 'dd/MM/yyyy')}
									</Typography>
								</Grid>
							</Grid>
							{!isLastRow && <Divider sx={{ backgroundColor: 'gray' }} />}
						</React.Fragment>
					)
				})}
			</>
		)
	}

	return (
		<>
			{isInfoPopup ? (
				<StyledStack>
					<RenderRows />
				</StyledStack>
			) : (
				<StyledStack>
					<Grid container rowSpacing={2} columnSpacing={10}>
						<Grid item xs={6}>
							<Typography className='heading-text'>
								{statusLabelName}
							</Typography>
							<Typography className='details-text'>{approvalName}</Typography>
						</Grid>
						<Grid item xs={6}>
							<Typography className='heading-text'>
								{statusLabelDate} Date:
							</Typography>
							<Typography className='details-text'>
								{format(approvalDate ?? new Date(), 'dd/MM/yyyy')}
							</Typography>
						</Grid>
						{isRejected ? (
							<Grid item xs={6}>
								<Typography className='heading-text'>Reason:</Typography>
								<Typography className='details-text'>{reason}</Typography>
							</Grid>
						) : null}
					</Grid>
				</StyledStack>
			)}
		</>
	)
}

const StyledStack = styled('div')(({ theme }) => ({
	padding: theme.spacing(1),
	'.details-text': {
		color: theme.palette.neutral[900],
	},
	'.heading-text': {
		color: theme.palette.neutral[300],
	},
	'.MuiDivider-root': {
		margin: theme.spacing(2, 0),
	},
}))
