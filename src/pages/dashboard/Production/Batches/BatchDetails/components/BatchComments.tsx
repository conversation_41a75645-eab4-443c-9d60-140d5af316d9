import { LoadingWrapper } from '@/components'
import { authAxios } from '@/contexts'
import { dateFormats } from '@/utils/constant'
import { getFormattedDate } from '@/utils/helper'
import { yupResolver } from '@hookform/resolvers/yup'
import { Close } from '@mui/icons-material'
import { LoadingButton } from '@mui/lab'
import {
	Button,
	IconButton,
	Stack,
	styled,
	TextField,
	Typography,
	useTheme,
} from '@mui/material'
import { useMutation, useQuery } from '@tanstack/react-query'
import React, { useCallback } from 'react'
import { useForm } from 'react-hook-form'
import { useParams } from 'react-router-dom'
import { toast } from 'react-toastify'
import * as Yup from 'yup'

type TProps = {
	onClose: () => void
}

type ICommentListResponse = {
	comments: IComment[]
	count: number
}

type IComment = {
	name: string
	userId: string
	email: string
	comment: string
	phoneNumber?: string
	createdAt: string
	id: string
}

const addCommentSchema = Yup.object({
	comment: Yup.string().required('Comment is required'),
})

type TAddCommentSchema = Yup.InferType<typeof addCommentSchema>

export const BatchComments: React.FC<TProps> = ({ onClose }) => {
	const { id } = useParams()
	const theme = useTheme()
	const {
		register,
		handleSubmit,
		formState: { errors },
		reset,
	} = useForm<TAddCommentSchema>({
		defaultValues: { comment: '' },
		mode: 'all',
		resolver: yupResolver<TAddCommentSchema>(addCommentSchema),
	})

	const commentList = useQuery({
		queryKey: ['comments'],
		queryFn: async () => {
			const { data } = await authAxios.get<ICommentListResponse>(
				`/new/process/comments?processId=${id}`
			)
			return data
		},
	})

	const addComment = useMutation({
		mutationKey: ['addComment'],
		mutationFn: async (values: TAddCommentSchema) => {
			return await authAxios.post(`/new/process/comment?processId=${id}`, {
				...values,
			})
		},
		onSuccess: ({ data }) => {
			toast(data?.message)
			commentList.refetch()
			reset()
		},
		onError: (error: any) => {
			toast(error?.response?.data?.messageToUser)
		},
	})

	const handleAddComment = useCallback(
		(values: TAddCommentSchema) => addComment.mutate(values),
		[addComment]
	)

	return (
		<StyledStack>
			<Stack className='header'>
				<Stack
					direction='row'
					spacing={1}
					alignItems='center'
					width='100%'
					justifyContent='space-between'>
					<Typography variant='h4'>Comments</Typography>
					<IconButton onClick={onClose}>
						<Close />
					</IconButton>
				</Stack>
			</Stack>
			<Stack className='container'>
				{!commentList?.data?.comments?.length && (
					<Stack className='comment_textfield'>
						<TextField
							placeholder='Add Comment here'
							multiline
							rows={5}
							fullWidth
							{...register('comment')}
							error={!!errors?.comment?.message}
							helperText={errors?.comment?.message}
						/>
					</Stack>
				)}
	
				<Stack className='comment_container'>
					<LoadingWrapper loading={commentList?.isLoading}>
						{commentList?.data?.comments?.length ? (
							commentList.data.comments.map((comment) => (
								<Stack key={comment.id} className='comment_item'>
									<Stack className='comment_header'>
										<Typography className='fontSize_14'>
											{comment.name}
										</Typography>
										<Typography className='fontSize_14'>
											{getFormattedDate(
												comment.createdAt,
												dateFormats.dd_MM_yyyy_with_time_in_bracket_AM_PM
											)}
										</Typography>
									</Stack>
									<Typography className='comment_text fontSize_14'>
										{comment.comment}
									</Typography>
								</Stack>
							))
						) : (
							null
						)}
					</LoadingWrapper>
				</Stack>
	
				{commentList?.data?.comments?.length !==0 && (
					<Stack className='comment_textfield'>
						<TextField
							placeholder='Add Comment here'
							multiline
							rows={5}
							fullWidth
							{...register('comment')}
							error={!!errors?.comment?.message}
							helperText={errors?.comment?.message}
						/>
					</Stack>
				)}
	
				<Stack
					direction='row'
					justifyContent='space-between'
					mt={5}
					className='buttonContainer'>
					<Button
						onClick={onClose}
						sx={{
							border: `1px solid ${theme.palette.primary.main}`,
							flexGrow: 1,
							mx: 1,
						}}>
						Cancel
					</Button>
					<LoadingButton
						loading={addComment.isPending}
						disabled={addComment.isPending}
						onClick={handleSubmit(handleAddComment)}
						variant='contained'
						sx={{
							flexGrow: 1,
							mx: 1,
						}}>
						Save
					</LoadingButton>
				</Stack>
			</Stack>
		</StyledStack>
	);
	
}

const StyledStack = styled(Stack)(({ theme }) => ({
	gap: theme.spacing(2),
	height: '100vh',

	'.header': {
		flexDirection: 'row',
		justifyContent: 'space-between',
		alignItems: 'center',
		padding: theme.spacing(4.5, 2),
		borderBottom: `${theme.spacing(0.125)} solid ${
			theme.palette.neutral['100']
		}`,
	},
	'.container': {
		padding: theme.spacing(0, 4),
		position: 'relative',
		height: 'calc(100% - 138px)',

		'.comment_container': {
			height: 'calc(100% - 50px)',
			gap: theme.spacing(3),
			padding: theme.spacing(2, 1),
			overflow: 'auto',
			'.comment_item': {
				flexDirection: 'column',
				padding: theme.spacing(1, 2),
				boxShadow: '0px 0px 4px 0px rgba(0, 0, 0, 0.25)',
				gap: theme.spacing(3),
				alignItems: 'flex-start',
				borderRadius: theme.spacing(1),
				'.comment_header': {
					flexDirection: 'row',
					justifyContent: 'space-between',
					width: '100%',
				},
				'.comment_text': {
					wordBreak: 'break-all',
				},
			},
		},
		'.comment_textfield': {
			'.button': {
				fontSize: theme.typography.subtitle1.fontSize,
			},
		},
		'.fontSize_14': {
			fontSize: theme.typography.subtitle1.fontSize,
		},
	},
}))
