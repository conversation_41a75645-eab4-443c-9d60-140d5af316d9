import { Paper, Popper, styled } from '@mui/material'
import { ApproveRejectedInformation } from './ApproveRejectedInformation'
import { HistoryItem } from '../useBatchDetails'
type TProps = {
	showInfoPopup: boolean
	setShowInfoPopup: (value: boolean) => void
	statusLabelName: string
	statusLabelDate: string
	approvalName: string
	approvalDate: Date
	reason: string
	anchorEl: HTMLElement | null
	batchHistory: HistoryItem[] | undefined
	isInfoPopup?: boolean
}
export const ApproveRejectedDialog: React.FC<TProps> = ({
	showInfoPopup,
	statusLabelName,
	statusLabelDate,
	approvalName,
	approvalDate,
	reason,
	anchorEl,
	batchHistory,
	isInfoPopup = false,
}) => {
	return (
		<Popper
			open={showInfoPopup}
			sx={{
				zIndex: 999,
			}}
			anchorEl={anchorEl}
			placement='bottom'
			disablePortal>
			<StyledPaper>
				<ApproveRejectedInformation
					isInfoPopup={isInfoPopup}
					batchHistory={batchHistory}
					statusLabelName={statusLabelName}
					approvalName={approvalName}
					statusLabelDate={statusLabelDate}
					approvalDate={approvalDate}
					reason={reason}
					isRejected={true}
				/>
			</StyledPaper>
		</Popper>
	)
}

const StyledPaper = styled(Paper)(({ theme }) => ({
	backgroundColor: theme.palette.grey[300],
	borderRadius: theme.spacing(2),
	minWidth: 350,
	maxHeight: 550,
	overflow: 'auto',
	padding: theme.spacing(2),
	boxShadow: theme.shadows[5],
}))
