import { CustomChip, CustomDataGrid } from '@/components'
import { Checkbox, Chip, Stack, styled, Typography } from '@mui/material'
import { FC, useCallback, useMemo } from 'react'
import {
	GridColDef,
	GridEventListener,
	GridRenderCellParams,
	GridValidRowModel,
} from '@mui/x-data-grid'
import {
	Biochar_Default_Unit,
	Biomass_Default_Unit,
	IBatch,
} from '@/interfaces'
import { useNavigate, useSearchParams } from 'react-router-dom'
import { useAuthContext } from '@/contexts'
import { getFormattedDate, getSerialNumber } from '@/utils/helper'
import {
	BatchStatusTypeEnum,
	dateFormats,
	defaultLimit,
	userRoles,
} from '@/utils/constant'
import { CustomFilter } from '@/components/CustomFilter'
import { ILabelWithValue } from '@/types'
import { BiomassFilter } from '../../Biomass/BiomassFilters'

interface IProps {
	allBatches?: IBatch
	isLoading: boolean
	showDownloadCheckbox: boolean
	selectedProcess: string[] | null
	setselectedProcess: React.Dispatch<React.SetStateAction<string[] | null>>
}

const networkStatus = {
	['admin-approved']: 'approved',
	approved: 'approved',
	pending: 'pending',
	rejected: 'rejected',
	['admin-rejected']: 'rejected',
	started: 'pending',
	['not_assessed']: 'pending',
	Compensated: 'compensated',
}

const filterToResetOnTabChange = [
	'page',
	'limit',
	'siteId',
	'kilnId',
	'searchName',
]

export const AllBatchesPanel: FC<IProps> = ({
	allBatches,
	isLoading,
	showDownloadCheckbox,
	selectedProcess,
	setselectedProcess,
}) => {
	const navigate = useNavigate()
	const [searchParams, setSearchParams] = useSearchParams()
	const limit = searchParams.get('limit') ?? defaultLimit
	const paramsSubNetwork = searchParams.get('subNetwork') || 'all'
	const { userDetails } = useAuthContext()
	const handleClickCheckbox = useCallback(
		(id: string) => {
			setselectedProcess((prev) => {
				if (!prev) return [id]
				if (prev.includes(id)) {
					return prev.filter((item) => item !== id)
				} else {
					return [...prev, id]
				}
			})
		},
		[setselectedProcess]
	)

	const statusList = useMemo(
		() => [
			{ label: 'Approved', value: BatchStatusTypeEnum.Approved },
			{ label: 'Rejected', value: BatchStatusTypeEnum.Rejected },
			{ label: 'Not Assessed', value: BatchStatusTypeEnum.NotAssessed },
			{ label: 'Started', value: BatchStatusTypeEnum.Started },
			{ label: 'Admin Rejected', value: BatchStatusTypeEnum.AdminRejected },
			{ label: 'Admin Approved', value: BatchStatusTypeEnum.AdminApproved },
			...([userRoles.Admin, userRoles.CsinkManager].includes(
				userDetails?.accountType as userRoles
			)
				? [{ label: 'Compensated', value: BatchStatusTypeEnum.Compensated }]
				: []),
		],
		[userDetails?.accountType]
	)

	const AllBatchesColumn: GridColDef<GridValidRowModel>[] = useMemo(
		() => [
			...(showDownloadCheckbox
				? [
						{
							field: 'action',
							headerName: '',
							flex: 1,
							minWidth: 80,
							sortable: false,
							renderHeader: () => (
								<Checkbox
									onChange={(e) => {
										const checked = e.target.checked
										setselectedProcess(() => {
											return checked
												? allBatches?.kilnProcesses?.map((item) => item.id) ||
														[]
												: []
										})
									}}
									checked={allBatches?.kilnProcesses?.every(
										(item) => selectedProcess?.includes(item.id) ?? false
									)}
								/>
							),
							renderCell: (params: GridRenderCellParams) => (
								<Checkbox
									value={selectedProcess?.includes(params?.row?.id)}
									onClick={(e) => {
										e.stopPropagation()
										handleClickCheckbox(params?.row?.id)
									}}
									checked={selectedProcess?.includes(params?.row?.id) ?? false}
								/>
							),
						},
				  ]
				: []),

			{
				field: 'serialNo',
				headerName: `Serial No`,
				minWidth: 150,

				renderCell: (params) => {
					return (
						<Typography variant='subtitle1'>
							{getSerialNumber(params, Number(limit))}
						</Typography>
					)
				},
			},

			{
				field: 'date',
				headerName: `Date`,
				minWidth: 150,
				renderCell: (params) => {
					const endTime = params?.row?.endTime
						? ` - ${getFormattedDate(
								params?.row?.endTime,
								dateFormats.time24HrFormat
						  )}`
						: ''
					return (
						<Typography variant='subtitle1'>
							{getFormattedDate(
								params?.row?.startTime,
								dateFormats.dd_MMM_yyyy
							)}
							<br />
							{getFormattedDate(
								params?.row?.startTime,
								dateFormats.time24HrFormat
							)}
							{endTime}
						</Typography>
					)
				},
			},
			{
				field: 'shortCode',
				headerName: 'Batch ID',
				minWidth: 180,
				flex: 1,
				sortable: false,
				renderCell: (params) => {
					const shortcode = params?.row?.shortCode?.split('K')[1]
					return (
						<Typography variant='subtitle1'>
							{shortcode ? `K${shortcode}` : ''}
						</Typography>
					)
				},
			},
			{
				field: 'cropName',
				headerName: 'Biomass Type',
				minWidth: 180,
				flex: 1,
				sortable: false,
			},
			{
				field: 'networkName',
				headerName: 'Network Name',
				minWidth: 160,
				maxWidth: 160,
				sortable: false,
				valueGetter: (params) =>
					params.row.networkName || params.row.artisanProName,
			},

			{
				field: 'siteName',
				headerName: 'Site/Farmer',
				minWidth: 180,
				flex: 1,
				sortable: false,
				valueGetter: (params) =>
					params.row.networkName
						? params.row.farmerName
						: params.row.siteName ?? '',
			},

			{
				field: 'kilnName',
				headerName: `Kiln`,
				minWidth: 150,
			},
			{
				field: 'biomassQuantity',
				headerName: `Biomass Quantity (${Biomass_Default_Unit})`,
				minWidth: 100,
				maxWidth: 100,
			},
			{
				field: 'biocharQuantity',
				headerName: `Biochar Qty  (${Biochar_Default_Unit})`,
				minWidth: 80,
				maxWidth: 80,
				sortable: false,
			},
			{
				field: 'status',
				headerName: 'Status',
				minWidth: 180,
				flex: 1,
				sortable: false,
				renderCell: (params) => {
					const status: keyof typeof networkStatus =
						params.row.status === 'compensate'
							? 'Compensated'
							: params.row.status
					return (
						<CustomChip
							label={status?.replace(/[-_]/g, ' ') ?? ''}
							appliedClass={networkStatus[`${status}`]}
						/>
					)
				},
			},
		],
		[
			showDownloadCheckbox,
			allBatches?.kilnProcesses,
			setselectedProcess,
			selectedProcess,
			handleClickCheckbox,
			limit,
		]
	)
	const handleRowClick: GridEventListener<'rowClick'> = useCallback(
		(params) => {
			navigate(`${params?.row?.id}/details?${searchParams}`)
		},
		[navigate, searchParams]
	)
	const filterResetArray = useMemo(() => {
		switch (userDetails?.accountType) {
			case 'admin':
				return [...filterToResetOnTabChange, 'baId', 'networkIds']
			case 'biomass_aggregator':
			case 'c_sink_manager':
				return [...filterToResetOnTabChange, 'networkIds']
			default:
				return filterToResetOnTabChange
		}
	}, [userDetails?.accountType])

	const handleSubNetworkClick = useCallback(
		(value: string) => {
			const nsp = new URLSearchParams(searchParams)
			if (value === 'all') {
				nsp.delete('subNetwork')
				setSearchParams(nsp)
				return
			}
			filterResetArray.forEach((key) => {
				nsp.delete(key)
			})
			nsp.set('subNetwork', value)
			setSearchParams(nsp)
		},
		[filterResetArray, searchParams, setSearchParams]
	)

	const chipList = useMemo(() => {
		const show = ![
			userRoles.cSinkNetwork,
			userRoles.artisanProNetworkManager,
			userRoles.ArtisanPro,
		].includes(userDetails?.accountType as userRoles)
		const list = [
			{
				label: 'All',
				value: 'all',
				show: show,
			},

			{
				label: 'C Sink Network',
				value: 'network',
				show: show,
			},
			{
				label: 'Artisan Pro',
				value: 'artisanPro',
				show: show,
			},
		]
		return list
	}, [userDetails?.accountType])

	return (
		<Stack className='container'>
			<CustomDataGrid
				loading={isLoading}
				showPagination={true}
				onRowClick={handleRowClick}
				rows={allBatches?.kilnProcesses ?? []}
				columns={AllBatchesColumn}
				rowCount={allBatches?.count ?? 0}
				headerEndComponent={
					<>
						<CustomFilter
							queryKey='statuses'
							filtersToReset={['page', 'limit']}
							label='Status'
							options={statusList}
						/>
						<CustomFilter
							filtersToReset={['page', 'limit']}
							options={allBatches?.crops?.reduce(
								(acc: ILabelWithValue[], curr) => [
									...acc,
									{ label: curr?.cropName, value: curr?.id },
								],
								[]
							)}
						/>
					</>
				}
				headerComponent={
					<Stack className='header-filter-search' gap={2} flexWrap='wrap'>
						<StyledContained>
							<Stack className='filter-Chips'>
								{chipList.map(({ label, value, show }) => {
									return (
										show && (
											<Chip
												key={value}
												onClick={() => handleSubNetworkClick(value)}
												color={
													paramsSubNetwork === value ? 'primary' : 'default'
												}
												label={label}
											/>
										)
									)
								})}
							</Stack>
						</StyledContained>
						<BiomassFilter />
					</Stack>
				}
			/>
		</Stack>
	)
}

const StyledContained = styled(Stack)(({ theme }) => ({
	'.filter-Chips': {
		flexDirection: 'row',
		gap: theme.spacing(2),
		padding: theme.spacing(2, 0, 3, 0),
	},
}))
