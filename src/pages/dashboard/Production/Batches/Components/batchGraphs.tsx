import { CustomCard } from '@/components'
import { Stack, Typography, alpha, styled, useTheme } from '@mui/material'
import { ApexOptions } from 'apexcharts'
import { FC, useMemo } from 'react'
import ReactApex<PERSON>hart from 'react-apexcharts'

export const BatchGraphs: FC = () => {
	const theme = useTheme()

	const StatusChart = useMemo(
		() => ({
			series: [45, 32, 25, 11, 98],
			options: {
				labels: [
					'Category 1 : 45',
					'Category 2 : 32',
					'Category 3 : 25',
					'Category 4 : 11',
					'Other : 98',
				],
				legend: {
					show: true,
					markers: {
						fillColors: [
							theme.palette.primary.main,
							theme.palette.success.main,
							theme.palette.warning.main,
							theme.palette.info.main,
							alpha(theme.palette.primary.main, 0.1),
						],
					},
				},
				dataLabels: {
					enabled: false,
				},
				tooltip: { enabled: false },
				responsive: [
					{
						breakpoint: 680,
						options: {
							chart: {
								width: 280,
							},
						},
					},
					{
						breakpoint: 1440,
						options: {
							chart: {
								width: 300,
							},
						},
					},
				],
				chart: {
					type: 'donut',
					width: 100,
				},
				plotOptions: {
					pie: {
						donut: {
							size: '70%',
						},
					},
				},
				fill: {
					colors: [
						theme.palette.primary.main,
						theme.palette.success.main,
						theme.palette.warning.main,
						theme.palette.info.main,
						alpha(theme.palette.primary.main, 0.1),
					],
				},
				states: {
					hover: { filter: { type: 'darken', value: 0.7 } },
					active: { filter: { type: 'none', value: 0 } },
				},
				stroke: { width: 0 },
			},
		}),
		[theme.palette]
	)
	const StatusChartBiomass = useMemo(
		() => ({
			series: [45, 32, 25, 11, 98],
			options: {
				labels: [
					'Category 1 : 45',
					'Category 2 : 32',
					'Category 3 : 25',
					'Category 4 : 11',
					'Other : 98',
				],
				legend: {
					show: true,
					markers: {
						fillColors: [
							theme.palette.primary.main,
							theme.palette.success.main,
							theme.palette.warning.main,
							theme.palette.info.main,
							alpha(theme.palette.primary.main, 0.1),
						],
					},
				},
				dataLabels: {
					enabled: false,
				},
				tooltip: { enabled: false },
				responsive: [
					{
						breakpoint: 680,
						options: {
							chart: {
								width: 280,
							},
						},
					},
					{
						breakpoint: 1440,
						options: {
							chart: {
								width: 300,
							},
						},
					},
				],
				chart: {
					type: 'donut',
					width: 100,
				},
				plotOptions: {
					pie: {
						donut: {
							size: '70%',
						},
					},
				},
				fill: {
					colors: [
						theme.palette.primary.main,
						theme.palette.success.main,
						theme.palette.warning.main,
						theme.palette.info.main,
						alpha(theme.palette.primary.main, 0.1),
					],
				},
				states: {
					hover: { filter: { type: 'darken', value: 0.7 } },
					active: { filter: { type: 'none', value: 0 } },
				},
				stroke: { width: 0 },
			},
		}),
		[theme.palette]
	)

	return (
		<StyledMapContainer>
			<CustomCard
				className='crop-details-card'
				headerComponent={
					<Stack gap={3}>
						<Typography variant='body2'>Credits</Typography>
						<Stack className='chart'>
							<ReactApexChart
								options={StatusChart.options as ApexOptions}
								series={StatusChart.series}
								type='donut'
							/>
						</Stack>
					</Stack>
				}
			/>
			<CustomCard
				className='crop-details-card'
				headerComponent={
					<Stack gap={3}>
						<Typography variant='body2'>Biomass Types</Typography>
						<Stack className='chart'>
							<ReactApexChart
								options={StatusChartBiomass.options as ApexOptions}
								series={StatusChartBiomass.series}
								type='donut'
							/>
						</Stack>
					</Stack>
				}
			/>
		</StyledMapContainer>
	)
}

const StyledMapContainer = styled(Stack)(({ theme }) => ({
	padding: theme.spacing(2),
	flexDirection: 'row',
	gap: theme.spacing(2),
	flexWrap: 'wrap',
	background: theme.palette.neutral['50'],
	'.map-container': {
		height: 260,
		width: 364,
		background: theme.palette.neutral['200'],
		borderRadius: theme.spacing(2),
	},
	'.crop-details-card': {
		display: 'flex',
		width: 364,
		height: 260,
		borderRadius: theme.spacing(2),
		gap: theme.spacing(2),
		padding: theme.spacing(2),
		'.chart': {
			width: 320,
			height: '100%',
			// margin: theme.spacing(-1.5),
		},
	},
}))
