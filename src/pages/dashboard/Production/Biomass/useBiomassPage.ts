import { authAxios, useAuthContext } from '@/contexts'
import {
	GetBiomassAvailableResponse,
	GetBiomassOrCropResponse,
	GetProductionGraphDataResponse,
	IBiomassCollectionList,
	PeriodEnum,
} from '@/interfaces'
import { NetworkTabs, SiteKilnResponse, UpdateBiomassPayload } from '@/types'
import { defaultLimit, defaultPage, userRoles } from '@/utils/constant'
import { showAxiosErrorToast } from '@/utils/helper'
import { useMutation, useQuery } from '@tanstack/react-query'
import { AxiosError } from 'axios'
import moment from 'moment'
import { useCallback, useMemo } from 'react'
import { useSearchParams } from 'react-router-dom'
import { toast } from 'react-toastify'

const DATE_FORMAT = 'DD-MM-YYYY'
const INTERNAL_DATE_FORMAT = 'YYYY-MM-DD'

export const useBiomassPage = () => {
	const [searchParams, setSearchParams] = useSearchParams()
	const paramsLimit = searchParams.get('limit') || defaultLimit.toString()
	const paramsPage = searchParams.get('page') || defaultPage.toString()
	const paramsBAId = searchParams.get('biomassAggregatorIds') || ''
	const paramsNetworkId = searchParams.get('networkIds') || ''
	const paramsSiteId = searchParams.get('siteId') || ''
	const paramsKilnId = searchParams.get('kilnId') || ''
	const paramsSearchName = searchParams.get('searchName') || ''
	const graphTab = searchParams.get('graphTab') || 'biomass'
	const productionTab = searchParams.get('productionTab') || 'biomassCollection'
	const paramsSubnetwork = searchParams.get('subNetwork') || 'all'
	const editBiomassId = searchParams.get('editBiomassId') || ''
	const paramsCsinkManagerId = searchParams.get('csinkManagerId') || ''
	const { userDetails } = useAuthContext()
	const startDate = searchParams.get('startDate')
		? moment(searchParams.get('startDate'), DATE_FORMAT).format(
				INTERNAL_DATE_FORMAT
		  )
		: ''
	const endDate = searchParams.get('endDate')
		? moment(searchParams.get('endDate'), DATE_FORMAT).format(
				INTERNAL_DATE_FORMAT
		  )
		: ''
	const paramsPeriod = searchParams.get('period') ?? PeriodEnum.mtd
	const biomassCollectionQuery = useQuery({
		queryKey: [
			'biomassCollection',
			paramsBAId,
			paramsNetworkId,
			paramsSearchName,
			paramsLimit,
			paramsPage,
			paramsKilnId,
			paramsSiteId,
			paramsSubnetwork,
			paramsCsinkManagerId,
		],
		queryFn: () => {
			const queryParams = new URLSearchParams({
				limit: paramsLimit,
				page: paramsPage,
				search: paramsSearchName,
				biomassAggregatorIds: paramsBAId,
				networkIds: paramsNetworkId,
				siteIds: paramsSiteId,
				kilnIds: paramsKilnId,
				subNetwork: paramsSubnetwork === 'all' ? '' : paramsSubnetwork,
				csinkManagerId: paramsCsinkManagerId,
			})
			return authAxios.get<{
				count: number
				siteBiomassList: IBiomassCollectionList[]
			}>(`/new/biomass-collection?${queryParams}`)
		},
		select: ({ data }) => data,
		enabled: productionTab === 'biomassCollection',
	})

	const biomassAvailableQuery = useQuery({
		queryKey: [
			'biomassAvailable',
			paramsSearchName,
			paramsBAId,
			paramsNetworkId,
			paramsSiteId,
			paramsKilnId,
			paramsLimit,
			paramsPage,
			paramsSubnetwork,
			paramsCsinkManagerId,
		],
		queryFn: () => {
			const queryParams = new URLSearchParams()

			queryParams.set('limit', paramsLimit)
			queryParams.set('page', paramsPage)
			if (paramsSearchName) queryParams.set('search', paramsSearchName)
			if (paramsNetworkId) queryParams.set('networkIds', paramsNetworkId)
			if (paramsSiteId) queryParams.set('siteId', paramsSiteId)
			if (paramsKilnId) queryParams.set('kilnId', paramsKilnId)
			if (paramsSubnetwork !== 'all')
				queryParams.set('subNetwork', paramsSubnetwork)
			if (paramsBAId) queryParams.set('biomassAggregatorIds', paramsBAId) // Only set if it's defined
			if (paramsCsinkManagerId)
				queryParams.set('csinkManagerId', paramsCsinkManagerId)
			return authAxios.get<GetBiomassAvailableResponse>(
				`/new/biomass-available/v2?${queryParams.toString()}`
			)
		},
		select: ({ data }) => data,
		enabled: productionTab === 'biomassAvailable',
	})

	const getBiomassCrops = useQuery({
		queryKey: [
			'biomassCrops',
			paramsBAId,
			paramsSiteId,
			paramsKilnId,
			paramsSubnetwork,
			paramsCsinkManagerId,
			paramsNetworkId,
			startDate,
			endDate,
		],
		queryFn: async () => {
			const queryParams = new URLSearchParams({
				siteIds: paramsSiteId,
				kilnIds: paramsKilnId,
				subNetwork: paramsSubnetwork === 'all' ? '' : paramsSubnetwork,
				biomassAggregatorIds: paramsBAId,
				csinkManagerId: paramsCsinkManagerId,
				networkIds: paramsNetworkId,
				startDate: startDate,
				endDate: endDate,
			})
			const { data } = await authAxios.get<GetBiomassOrCropResponse>(
				`new/biomass-crops?${queryParams}`
			)
			return data
		},
	})

	const paramsValues: { [key: string]: { value: string; key: string } } =
		useMemo(
			() => ({
				[NetworkTabs.all]: {
					value: paramsBAId,
					key: 'biomassAggregatorIds',
				},
				[NetworkTabs.artisanPro]: {
					value: paramsSiteId,
					key: 'siteId',
				},
				[NetworkTabs.network]: {
					value: paramsKilnId,
					key: 'kilnId',
				},
			}),
			[paramsBAId, paramsKilnId, paramsSiteId]
		)

	const getGraphData = useQuery({
		queryKey: [
			'productionGraphData',
			paramsBAId,
			paramsSiteId,
			paramsKilnId,
			paramsNetworkId,
			paramsSubnetwork,
			paramsCsinkManagerId,
			startDate,
			endDate,
			paramsPeriod,
		],
		queryFn: async () => {
			const queryParams = new URLSearchParams({
				subNetwork: paramsSubnetwork === 'all' ? '' : paramsSubnetwork,
				period: [PeriodEnum.custom, PeriodEnum.wtd].includes(
					paramsPeriod as PeriodEnum
				)
					? 'day'
					: paramsPeriod,
				csinkManagerId: paramsCsinkManagerId,
				siteIds: paramsSiteId,
				kilnIds: paramsKilnId,
				networkIds: paramsNetworkId,
				startDate: startDate,
				endDate: endDate,
			})
			const { data } = await authAxios.get<GetProductionGraphDataResponse>(
				`/new/production-graph?${queryParams.toString()}`
			)
			return data
		},
	})

	const fetchSites = useQuery({
		queryKey: ['fetchSites', paramsSubnetwork],
		queryFn: async () => {
			const queryParams = new URLSearchParams({
				subNetwork: paramsSubnetwork,
			})
			const { data } = await authAxios.get<SiteKilnResponse>(
				`/new/sites-kilns?${queryParams.toString()}`
			)
			return data
		},
		select: (data) => {
			return (data?.siteKilns ?? []).map((item) => ({
				value: (item.isArtisan ? item?.siteId : item?.kilnId) || '',
				label: (item.isArtisan ? item.siteName : item.kilnName) || '',
			}))
		},
		enabled: paramsSubnetwork !== 'all',
	})

	const fetchBA = useQuery({
		queryKey: ['allBAForAddUser'],
		queryFn: () => {
			const queryParams = new URLSearchParams({
				limit: '1000',
				page: '0',
			})
			return authAxios.get<{
				biomassAggregators: { id: string; name: string; shortName: string }[]
			}>(`/biomass-aggregator?${queryParams.toString()}`)
		},
		select: ({ data }) =>
			data?.biomassAggregators?.map((item) => ({
				label: `${item.name} (${item.shortName})`,
				value: item.id,
			})),
		enabled: [
			userRoles.Admin,
			userRoles.CsinkManager,
			userRoles.BiomassAggregator,
		].includes(
			userDetails?.accountType as userRoles // need to add more account permissions
		),
	})

	const approvedBiomass = useMutation({
		mutationKey: ['approvedBiomass'],
		mutationFn: async (payload: UpdateBiomassPayload) => {
			await authAxios.put(`new/update-biomass`, payload)
		},
		onError: (error: AxiosError) => {
			toast((error?.response?.data as { messageToUser: string })?.messageToUser)
		},
		onSuccess: () => {
			toast(`successfully update the biomass`)
			biomassCollectionQuery.refetch()
		},
	})

	const handleApprovedBiomass = useCallback(
		async (selectedBiomassCollection: UpdateBiomassPayload) => {
			approvedBiomass.mutate(selectedBiomassCollection)
			setSearchParams((params) => {
				params.delete('editBiomassId')
				return params
			})
		},
		[approvedBiomass, setSearchParams]
	)

	const handleEntityFilterValueChange = useCallback(
		(key: string, value: string) => {
			setSearchParams((prev) => {
				if (value) {
					const arr = Object.values(paramsValues).reduce((acc, curr) => {
						if (curr.key === key) {
							return acc
						}
						return [...acc, curr.key]
					}, [] as string[])
					arr.forEach((item) => {
						prev.delete(item)
					})
					prev.set(key, value)
				} else {
					prev.delete(key)
				}
				return prev
			})
		},

		[paramsValues, setSearchParams]
	)

	const deleteBiomassImages = async ({
		siteId,
		siteBiomassId,
		imageIds,
	}: {
		siteId: string
		siteBiomassId: string
		imageIds: string[]
	}) => {
		try {
			const response = await authAxios.delete(
				`/site/${siteId}/biomass-collection/${siteBiomassId}/images`,
				{ data: { imageIds } }
			)
			return response.data
		} catch (error: any) {
			showAxiosErrorToast(error)
			throw error
		}
	}

	const { mutate: deleteImageHandler } = useMutation({
		mutationFn: deleteBiomassImages,
		onSuccess: () => {
			toast.success('Image deleted successfully')
			biomassCollectionQuery.refetch()
		},
		onError: (error: AxiosError) => {
			showAxiosErrorToast(error)
		},
	})

	return {
		biomassCollection: biomassCollectionQuery?.data,
		biomasssAvailable: biomassAvailableQuery?.data,
		loadingBiomassAvailable: biomassAvailableQuery.isLoading,
		loadingBiomassCollection: biomassCollectionQuery.isLoading,
		setSearchParams,
		handleApprovedBiomass,
		editBiomassId,
		searchParams,
		paramsSubnetwork,
		graphTab,
		productionTab,
		getBiomassCrops,
		getGraphData,
		fetchBA,
		fetchSites,
		paramsValues,
		handleEntityFilterValueChange,
		deleteImageHandler,
		paramsPeriod
	}
}
