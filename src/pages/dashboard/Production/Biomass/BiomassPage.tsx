import { CustomCard, CustomHeader } from '@/components'
import { <PERSON>b<PERSON>onte<PERSON><PERSON>, <PERSON>b<PERSON>ist, TabPanel } from '@mui/lab'
import {
	Box,
	Chip,
	Stack,
	styled,
	Tab,
	Table,
	TableBody,
	TableCell,
	TableContainer,
	TableHead,
	TableRow,
	Typography,
	useTheme,
} from '@mui/material'
import { ApexOptions } from 'apexcharts'
import { FC, PropsWithChildren, useCallback, useMemo } from 'react'
import ReactApexChart from 'react-apexcharts'
import { useAuthContext } from '@/contexts'
import { useBiomassPage } from './useBiomassPage'
import {
	BiomassAvailablePanel,
	BiomassCollectionPanel,
	EditBiomassCollection,
} from '../Components'
import { NetworkTabs } from '@/types'
import { convertKgToTon, roundNumber } from '@/utils/helper'
import { Biomass_Secondary_Unit } from '@/interfaces/enum'

import { BiomassFilter } from './BiomassFilters'
import { userRoles } from '@/utils/constant'
import { PeriodEnum } from '@/interfaces'

interface Props {}

const filterToResetOnTabChange = [
	'page',
	'limit',
	'siteId',
	'kilnId',
	'searchName',
]

const bannerTextForGraph = (period: PeriodEnum) => {
	switch (period) {
		case PeriodEnum.wtd:
			return 'Week-wise'
		case PeriodEnum.mtd:
			return 'Month-wise'
		case PeriodEnum.ytd:
			return 'Year-wise'
		default:
			return 'Date-wise'
	}
}

export const BiomassPage: FC<PropsWithChildren<Props>> = () => {
	const theme = useTheme()
	const { userDetails } = useAuthContext()

	const {
		productionTab,
		setSearchParams,
		handleApprovedBiomass,
		editBiomassId,
		searchParams,
		biomassCollection,
		loadingBiomassCollection,
		biomasssAvailable,
		loadingBiomassAvailable,
		paramsSubnetwork,
		getBiomassCrops,
		getGraphData,
		deleteImageHandler,
		paramsValues,
		paramsPeriod,
	} = useBiomassPage()

	const filterResetArray = useMemo(() => {
		switch (userDetails?.accountType) {
			case 'admin':
				return [...filterToResetOnTabChange, 'baId', 'networkId']
			case 'biomass_aggregator':
			case 'c_sink_manager':
				return [...filterToResetOnTabChange, 'networkId']

			default:
				return filterToResetOnTabChange
		}
	}, [userDetails?.accountType])

	const handleProductionTabChange = useCallback(
		(_: unknown, newValue: string) => {
			const nsp = new URLSearchParams(searchParams)

			filterResetArray.forEach((key) => {
				nsp.delete(key)
			})
			nsp.delete('subNetwork')
			nsp.set('productionTab', newValue)
			setSearchParams(nsp)
		},
		[filterResetArray, searchParams, setSearchParams]
	)

	const handleSubnetworkClick = useCallback(
		(value: string) => {
			const nsp = new URLSearchParams(searchParams)
			Object.values(paramsValues).forEach((item) => {
				nsp.delete(item.key)
			})
			if (value === 'all') {
				nsp.delete('subNetwork')
				setSearchParams(nsp)
				return
			}
			filterResetArray.forEach((key) => {
				nsp.delete(key)
			})
			nsp.set('subNetwork', value)
			setSearchParams(nsp)
		},
		[filterResetArray, paramsValues, searchParams, setSearchParams]
	)

	const options = useMemo(
		() => ({
			series: [
				{
					data: getGraphData?.data?.totalBiomass?.map((i) => i.total) || [],
					color: theme.palette.primary.light,
				},
			],
		}),
		[getGraphData?.data?.totalBiomass, theme.palette.primary.light]
	)

	const opt = useMemo(
		() => ({
			chart: {
				type: 'bar',
				height: 350,
				stacked: true,
				toolbar: {
					show: false,
				},
				zoom: {
					enabled: true,
				},
			},
			responsive: [
				{
					breakpoint: 480,
					options: {
						legend: {
							position: 'bottom',
							offsetX: -10,
							offsetY: 0,
						},
					},
				},
			],
			plotOptions: {
				bar: {
					horizontal: false,
					barHeight: '100%',
					dataLabels: {
						total: {
							enabled: false,
						},
					},
				},
			},
			xaxis: {
				type: paramsPeriod === PeriodEnum.ytd ? 'category' : 'datetime',
				categories: getGraphData?.data?.totalBiomass?.map((i) => i?.period),
				axisBorder: {
					show: false,
				},
				axisTicks: {
					show: false,
				},
				labels: {
					show: true,
				},
			},
			legend: {
				position: 'bottom',
				horizontalAlign: 'left',
				offsetY: 10,
				markers: {
					radius: 12,
				},
			},
			fill: {
				opacity: 1,
			},
			grid: { show: false },
			dataLabels: {
				enabled: false,
			},
		}),
		[getGraphData?.data?.totalBiomass, paramsPeriod]
	)

	const CustomCardHeader = () => {
		return (
			<Stack className='activity-graph' height='100%'>
				<ReactApexChart
					options={opt as ApexOptions}
					series={options.series}
					type='bar'
					height='100%'
				/>
			</Stack>
		)
	}

	const tabs = useMemo(() => {
		return [
			{
				label: 'All',
				value: NetworkTabs.all,
				hidden: false,
			},
			{
				label: 'C Sink Network',
				value: NetworkTabs.network,
				hidden: [
					userRoles.artisanProNetworkManager,
					userRoles.ArtisanPro,
				].includes(userDetails?.accountType as userRoles),
			},
			{
				label: 'Artisan Pro',
				value: NetworkTabs.artisanPro,
				hidden: [userRoles.cSinkNetwork].includes(
					userDetails?.accountType as userRoles
				),
			},
		]
	}, [userDetails?.accountType])

	const biomassAvailabilityData = useMemo(
		() =>
			getBiomassCrops?.data?.crops?.map((item) => ({
				label: item?.cropName,
				total: item?.totalBiomassQuantity,
				available: item?.availableBiomassQuantity,
			})),
		[getBiomassCrops?.data?.crops]
	)

	const openEditBiomassQuantity = useCallback(
		(id: string) => {
			setSearchParams((params) => {
				params.set('editBiomassId', id)
				return params
			})
		},
		[setSearchParams]
	)

	const BiomassAvailability = () => {
		return (
			<TableContainer sx={{ maxHeight: 240 }}>
				<StyledTable stickyHeader>
					<TableHead>
						<TableRow>
							<TableCell></TableCell>
							<TableCell>Total</TableCell>
							<TableCell>Available</TableCell>
						</TableRow>
					</TableHead>
					<TableBody>
						{biomassAvailabilityData?.map((data) => (
							<TableRow key={data.label}>
								<TableCell>{data.label}</TableCell>
								<TableCell>
									{(data?.total / 1000)?.toFixed(2)} {Biomass_Secondary_Unit}
								</TableCell>
								<TableCell>
									{(data?.available / 1000)?.toFixed(2)}{' '}
									{Biomass_Secondary_Unit}
								</TableCell>
							</TableRow>
						))}
					</TableBody>
				</StyledTable>
			</TableContainer>
		)
	}
	const showCsinkManagerFilter = [userRoles.Admin].includes(
		userDetails?.accountType as userRoles
	)

	return (
		<StyledContainer>
			<Box className='header'>
				<CustomHeader
					showBottomBorder={true}
					heading='Biomass'
					showButton
					showCsinkManagerFilter={showCsinkManagerFilter}
				/>

				<Stack className='filter-Chips'>
					{tabs.map(
						(tab) =>
							!tab?.hidden && (
								<Chip
									key={tab.value}
									variant='filled'
									onClick={() => handleSubnetworkClick(tab.value)}
									color={paramsSubnetwork === tab.value ? 'primary' : 'default'}
									label={tab.label}
								/>
							)
					)}
					<BiomassFilter isMultipleSelect />
				</Stack>
			</Box>

			<Stack direction='row' width='100%' px={3} height={350}>
				<Stack width='40%' className='table-container'>
					<Typography variant='body2'>Biomass Availability</Typography>
					<CustomCard
						sx={{ height: '100%' }}
						headerComponent={
							<Stack>
								<Typography variant='subtitle2'>
									Total Biomass:{' '}
									{roundNumber(
										convertKgToTon(
											getBiomassCrops?.data?.totalBiomassQuantity || 0
										),
										2
									)}{' '}
									{Biomass_Secondary_Unit}
								</Typography>
								<Stack>
									<BiomassAvailability />
								</Stack>
							</Stack>
						}
					/>
				</Stack>
				<Stack className='graph-container' width='55%'>
					<Typography variant='body2'>
						Biomass Collection {bannerTextForGraph(paramsPeriod as PeriodEnum)}
					</Typography>
					<CustomCard
						headerComponent={<CustomCardHeader />}
						sx={{ height: '100%' }}
					/>
				</Stack>
			</Stack>
			<TabContext value={productionTab}>
				<Stack className='production-tab-container'>
					<TabList onChange={handleProductionTabChange}>
						<Tab label='Biomass Collection' value='biomassCollection' />
						<Tab label='Biomass Available' value='biomassAvailable' />
					</TabList>
				</Stack>
				<TabPanel className='tab-panel-container' value='biomassCollection'>
					<BiomassCollectionPanel
						biomassCollection={biomassCollection}
						loadingBiomassCollection={loadingBiomassCollection}
						openEditBiomassQuantity={openEditBiomassQuantity}
						deleteImageHandler={deleteImageHandler}
					/>
				</TabPanel>
				<TabPanel className='tab-panel-container' value='biomassAvailable'>
					<BiomassAvailablePanel
						biomasssAvailable={biomasssAvailable}
						loadingBiomassAvailable={loadingBiomassAvailable}
					/>
				</TabPanel>
			</TabContext>
			<EditBiomassCollection
				open={editBiomassId !== ''}
				selectedBiomassCollection={
					biomassCollection?.siteBiomassList.find(
						({ id }) => id === editBiomassId
					) ?? null
				}
				onClose={() => {
					setSearchParams((params) => {
						params.delete('editBiomassId')
						return params
					})
				}}
				onSave={handleApprovedBiomass}
			/>
		</StyledContainer>
	)
}

const StyledContainer = styled(Stack)(({ theme }) => ({
	'.grey-btn': {
		color: theme.palette.neutral['500'],
		borderColor: theme.palette.neutral['500'],
		borderRadius: theme.spacing(1),
		textTransform: 'none',
		...theme.typography.subtitle2,
	},
	'.header': {
		padding: theme.spacing(2, 0),
		flexDirection: 'row',
		justifyContent: 'space-between',
		'.filter-Chips': {
			flexDirection: 'row',
			gap: theme.spacing(2),
			padding: theme.spacing(2),
		},
	},

	'.table-container': {
		gap: theme.spacing(2),
		padding: theme.spacing(3, 0, 0),
		height: '100%',
	},

	'.graph-container': {
		gap: theme.spacing(2),
		padding: theme.spacing(3, 3, 0),
		'.activity-graph': {},
		'.links-section': {
			padding: theme.spacing(2, 0),
			'.link-container': {
				flexDirection: 'row',
				justifyContent: 'space-between',
				alignItems: 'center',
				background: theme.palette.neutral['50'],
				padding: theme.spacing(2),
				borderRadius: theme.spacing(1),
			},
		},
	},
	'.production-tab-container': {
		padding: theme.spacing(3, 3, 0),
		borderBottom: `${theme.spacing(0.125)} solid ${
			theme.palette.neutral['100']
		}`,
	},
	'.tab-panel-container': {
		padding: theme.spacing(3, 3),
		'.header-filter-search': {
			flexDirection: 'row',
			alignItems: 'center',
			'.search-textFiled': {
				minWidth: 260,
				maxWidth: 260,
				width: '100%',
				'.MuiInputBase-root': {
					height: theme.spacing(4.5),
					borderRadius: theme.spacing(1.25),
				},
			},
			'.form-controller': {
				margin: theme.spacing(0.125),
				minWidth: theme.spacing(12.5),
				width: '100%',
			},
		},
	},
}))

const StyledTable = styled(Table)(({ theme }) => ({
	padding: 1,
	'.MuiTableHead-root .MuiTableCell-head': {
		fontWeight: 700,
		fontSize: 14,
		height: theme.spacing(7),
	},
	'.MuiTableRow-root': {
		borderColor: theme.palette.divider,
	},
	'.MuiTableCell-root, .MuiTablePagination-selectLabel, .MuiTablePagination-selectRoot':
		{
			fontWeight: 400,
		},
	'.MuiTableCell-root:focus, .MuiTableCell-root:focus-within': {
		outline: 0,
	},
	'.MuiTableCell-root': {
		minHeight: '52px !important',
		paddingBottom: 5,
		paddingTop: 5,
		overflowWrap: 'anywhere',
	},
	' .MuiTableRow-hover:hover > .MuiTableCell-root ': {
		cursor: 'pointer',
	},
	'&.MuiTable-root': {
		fontWeight: 500,
	},

	'.MuiTableCell-head:focus': {
		outline: 'none',
	},
	'.MuiDataGrid-virtualScroller': {
		minHeight: 40,
	},
}))
