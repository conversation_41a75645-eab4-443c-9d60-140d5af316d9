import { <PERSON>complete, Stack, TextField, styled } from '@mui/material'
import { FC, memo, useCallback, useEffect, useMemo, useRef } from 'react'
import { SetURLSearchParams, useSearchParams } from 'react-router-dom'
import { useQuery } from '@tanstack/react-query'
import { authAxios } from '@/contexts'
import { ILabelWithValue, Nullable } from '@/types'
import { INetwork } from '@/interfaces'

enum SelectedPage {
	ARTISAN_PRO = 'artisanPro',
	NETWORK = 'network',
	ALL = 'all',
}

type PropsForBiomassFilter = {
	isProductionPage?: boolean
	isMultipleSelect?: boolean
}

export const BiomassFilter: FC<PropsForBiomassFilter> = ({
	isMultipleSelect = false,
}) => {
	const [searchParams, setSearchParams] = useSearchParams()
	const subNetwork =
		searchParams.get('subNetwork') || searchParams.get('networkTab') || ''

	const selectedNetwork = searchParams.get('networkIds')

	const paramsCsinkManagerId = searchParams.get('csinkManagerId') || ''

	const selectedSite = searchParams.get('siteId')

	const selectedPage =
		subNetwork === SelectedPage.ARTISAN_PRO
			? SelectedPage.ARTISAN_PRO
			: subNetwork === SelectedPage.NETWORK
			? SelectedPage.NETWORK
			: SelectedPage.ALL

	const AllNetworksQuery = useQuery({
		queryKey: ['allNetworks', selectedPage, paramsCsinkManagerId],
		queryFn: async () => {
			return authAxios.get<{
				networks: INetwork[]
			}>(
				`/new/networks?subNetwork=${selectedPage}&csinkManagerId=${paramsCsinkManagerId}`
			)
		},
		select: ({ data }) =>
			data?.networks?.map((item) => ({
				label: item.name,
				value: item.id,
			})),
		enabled:
			selectedPage === SelectedPage.ARTISAN_PRO ||
			selectedPage === SelectedPage.NETWORK,
	})

	const GetSiteFiltersQuery = useQuery({
		queryKey: ['SiteFilters', selectedNetwork],
		queryFn: async () => {
			if (!selectedNetwork) return []

			const { data } = await authAxios.get<{
				siteDetails: { name: string; id: string }[]
			}>(`drop-down/sites?networkIds=${selectedNetwork}`)

			return (
				data?.siteDetails?.map((item) => ({
					label: item.name,
					value: item.id,
				})) || []
			)
		},
		enabled: !!selectedNetwork,
	})

	const isInitialRender = useRef(true)
	const { data: allNetworks, isLoading: isLoadingCsink } = AllNetworksQuery
	const { data: SiteFilters, isLoading: isLoadingSiteFilters } =
		GetSiteFiltersQuery

	useEffect(() => {
		if (isInitialRender.current) {
			isInitialRender.current = false
			return
		}
		if (paramsCsinkManagerId || selectedPage) {
			const nsp = new URLSearchParams(searchParams.toString())
			nsp.delete('networkIds')
			nsp.delete('siteId')
			setSearchParams(nsp, { replace: true })
		}
	}, [paramsCsinkManagerId, selectedPage])

	const showFilter = useMemo(() => {
		return (
			selectedPage === SelectedPage.ARTISAN_PRO ||
			selectedPage === SelectedPage.NETWORK
		)
	}, [selectedPage])

	return (
		<>
			{showFilter && (
				<FiltersContainer
					setSearchParams={setSearchParams}
					siteFilters={SiteFilters}
					allNetworks={allNetworks}
					isLoading={isLoadingCsink}
					searchParams={searchParams}
					selectedPage={selectedPage}
					selectedSite={selectedSite}
					selectedNetwork={selectedNetwork}
					isLoadingSiteFilters={isLoadingSiteFilters}
					siteMultiple={isMultipleSelect}
					networkMultiple={isMultipleSelect}
				/>
			)}
		</>
	)
}

interface FiltersProps {
	allNetworks?: ILabelWithValue[]
	isLoading: boolean
	selectedNetwork: Nullable<string>
	selectedSite: Nullable<string>
	selectedPage: Nullable<string>
	siteFilters?: ILabelWithValue[]
	searchParams: URLSearchParams
	setSearchParams: SetURLSearchParams
	isLoadingSiteFilters: boolean
	siteMultiple: boolean
	networkMultiple: boolean
}

const FiltersContainer: FC<FiltersProps> = memo(
	({
		allNetworks,
		isLoading,
		selectedNetwork,
		selectedSite,
		siteFilters,
		selectedPage,
		searchParams,
		setSearchParams,
		isLoadingSiteFilters,
		siteMultiple,
		networkMultiple,
	}) => {
		const getSelectedValues = useCallback(
			(paramValue: string | null, options: ILabelWithValue[]) => {
				if (!paramValue || !options) return []
				const values = paramValue.split(',')
				return options.filter((option) => values.includes(option.value))
			},
			[]
		)

		// Helper function to get single selected value
		const getSingleSelectedValue = useCallback(
			(paramValue: string | null, options: ILabelWithValue[]) => {
				if (!paramValue || !options) return null
				return options.find((option) => option.value === paramValue) || null
			},
			[]
		)
		const handleOnChange = useCallback(
			(
				selectedOption: Nullable<ILabelWithValue | ILabelWithValue[]>,
				queryKey: string
			) => {
				const nsp = new URLSearchParams(searchParams.toString())
				if (Array.isArray(selectedOption)) {
					const selectedValues = selectedOption
						.map((opt) => opt.value)
						.filter(Boolean)
						.join(',')
					if (selectedValues) {
						nsp.set(queryKey, selectedValues)
					} else {
						nsp.delete(queryKey)
					}
				} else if (selectedOption) {
					nsp.set(queryKey, selectedOption.value)
				} else {
					nsp.delete(queryKey)
				}

				if (searchParams.has('productionTab')) {
					nsp.set('productionTab', searchParams.get('productionTab')!)
				}

				if (queryKey === 'networkIds') {
					nsp.delete('siteId')
				}
				setSearchParams(nsp, { replace: true })
			},
			[searchParams, setSearchParams]
		)
		return (
			<StyledContainer>
				<Autocomplete
					multiple={networkMultiple}
					value={
						networkMultiple
							? getSelectedValues(selectedNetwork, allNetworks || [])
							: getSingleSelectedValue(selectedNetwork, allNetworks || [])
					}
					className='auto-complete'
					onChange={(_, newValue: any) => {
						handleOnChange(newValue, 'networkIds')
					}}
					{...(networkMultiple && { disableCloseOnSelect: true })}
					options={allNetworks || []}
					loading={isLoading}
					renderInput={(params) => (
						<TextField
							{...params}
							label={
								selectedPage === SelectedPage.NETWORK
									? `Select Csink Network${networkMultiple ? 's' : ''}`
									: `Select Artisan Pro${networkMultiple ? 's' : ''}`
							}
						/>
					)}
					{...(networkMultiple && {
						renderTags: (selected: ILabelWithValue[]) => {
							return renderTruncatedTags(selected)
						},
					})}
				/>
				{selectedNetwork && (
					<Autocomplete
						multiple={siteMultiple}
						value={
							siteMultiple
								? getSelectedValues(selectedSite, siteFilters || [])
								: getSingleSelectedValue(selectedSite, siteFilters || [])
						}
						className='auto-complete'
						onChange={(_, newValue: any) => {
							handleOnChange(newValue, 'siteId')
						}}
						options={siteFilters || []}
						{...(siteMultiple && { disableCloseOnSelect: true })}
						loading={isLoadingSiteFilters}
						renderInput={(params) => (
							<TextField
								{...params}
								label={
									selectedPage === SelectedPage.NETWORK
										? `Select Farmer${siteMultiple ? 's' : ''}`
										: `Select Site${siteMultiple ? 's' : ''}`
								}
							/>
						)}
						{...(siteMultiple && {
							renderTags: (selected: ILabelWithValue[]) => {
								return renderTruncatedTags(selected)
							},
						})}
					/>
				)}
			</StyledContainer>
		)
	}
)

const renderTruncatedTags = (selected: ILabelWithValue[]) => {
	const labels = selected.map((option) => option.label).join(', ')
	return labels.length > 13 ? (
		<span className='whitespace-nowrap'>{labels.substring(0, 13)}...</span>
	) : (
		labels
	)
}
const StyledContainer = styled(Stack)(({ theme }) => ({
	display: 'flex',
	flexDirection: 'row',
	gap: theme.spacing(2),
	'.auto-complete': {
		minWidth: 180,
		maxWidth: 200,
		'.MuiOutlinedInput-root': {
			height: 38,
			'.MuiInputBase-input': {
				padding: 0,
				flexWrap: 'nowrap !important',
				fontSize: theme.typography.subtitle1,
			},
		},
	},
	'.whitespace-nowrap': {
		whiteSpace: 'nowrap',
	},
}))
