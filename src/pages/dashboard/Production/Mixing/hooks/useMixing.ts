import { authAxios } from '@/contexts'
import { TMixingListResponse, TResponseMixType } from '@/interfaces'
import { defaultLimit, defaultPage } from '@/utils/constant'
import { showAxiosErrorToast } from '@/utils/helper'
import { useMutation, useQuery } from '@tanstack/react-query'
import { AxiosError } from 'axios'
import { useCallback, useState } from 'react'
import { useSearchParams } from 'react-router-dom'
import { toast } from 'react-toastify'

export const useMixing = () => {
	const [searchParams, setSearchParams] = useSearchParams()
	const [rowData, setRowData] = useState<any>('')
	const paramsLimit = searchParams.get('limit') || defaultLimit.toString()
	const paramsPage = searchParams.get('page') || defaultPage.toString()
	const paramsBAId = searchParams.get('biomassAggregatorIds') || ''
	const paramsNetworkId = searchParams.get('networkIds') || ''
	const paramsSiteId = searchParams.get('siteId') || ''
	const paramsKilnId = searchParams.get('kilnId') || ''
	const paramsMixingIds = searchParams.getAll('mixingIds')
	const paramsSearchName = searchParams.get('searchName') || ''
	const paramsSubnetwork = searchParams.get('subNetwork') || ''
	const paramsDeleteId = searchParams.get('deleteId') || ''
	const paramsDeleteSiteId = searchParams.get('deleteSiteId') || ''
	const paramsDeleteKilnId = searchParams.get('deletekilnId') || ''
	const paramsIsArtisan = searchParams.get('isArtisan') || ''
	const paramsArtisanProId = searchParams.get('artisanProId') || ''
	const paramsCsinkNetworkId = searchParams.get('csinkNetworkId') || ''

	const [showDownloadCheckbox, setShowDownloadCheckbox] =
		useState<boolean>(false)
	const [selectedProcess, setselectedProcess] = useState<string[] | null>(null)

	// paramsMixingIds do it for multiple ids
	const getAllMixing = useQuery({
		queryKey: [
			'getAllMixing',
			paramsLimit,
			paramsPage,
			paramsBAId,
			paramsNetworkId,
			paramsSiteId,
			paramsKilnId,
			paramsMixingIds,
			paramsSearchName,
			paramsSubnetwork,
		],
		queryFn: () => {
			const params = {
				limit: paramsLimit,
				page: paramsPage,
				search: paramsSearchName,
				biomassAggregatorIds: paramsBAId,
				networkIds: paramsNetworkId,
				mixTypeId: paramsMixingIds.join(','),
				siteId: paramsSiteId,
				kilnId: paramsKilnId,
				subNetwork: paramsSubnetwork,
			}

			const queryParams = new URLSearchParams()

			for (const [key, value] of Object.entries(params)) {
				if (value !== '') {
					queryParams.append(key, value)
				}
			}

			return authAxios.get<TMixingListResponse>(
				`/new/mixing-packaging?isMixing=true&${queryParams}`
			)
		},
		select: ({ data }) => data,
	})

	const fetchMixTypeListQuery = useQuery({
		queryKey: ['fetchMixTypeListQuery'],
		queryFn: async () => {
			const { data } = await authAxios.get<TResponseMixType>(
				`/packaging-types?limit=1000&page=0`
			)
			return data
		},
		enabled: true,
	})

	const downloadExcelFileMuatation = useMutation({
		mutationKey: ['downloadFile'],
		mutationFn: async () => {
			const payload = {
				processIds: selectedProcess,
			}
			const response = await authAxios.post('/process/sink', payload, {
				responseType: 'blob',
			})

			return response
		},
		onSuccess: (response) => {
			const url = window.URL.createObjectURL(new Blob([response.data]))
			const link = document.createElement('a')
			link.href = url
			link.setAttribute('download', `Mixing.xlsx`)
			document.body.appendChild(link)
			link.click()
			toast('File Downloaded')

			setselectedProcess(null)
			setShowDownloadCheckbox(false)
		},
		onError: async (err: AxiosError) => {
			if (err.response?.data instanceof Blob) {
				try {
					const text = await err.response.data.text()
					const json = JSON.parse(text)
					toast(json.messageToUser || 'Something went wrong')
				} catch (e) {
					toast('Unexpected error format')
				}
			} else {
				const message = (err.response?.data as { messageToUser: string })
					?.messageToUser
				toast(message || 'Something went wrong')
			}
		},
	})

	const deleteMixing = useMutation({
		mutationFn: async () => {
			const response =
				(await paramsIsArtisan) === 'true'
					? authAxios.delete(
							`/artisian-pro/${paramsArtisanProId}/site/${paramsDeleteSiteId}/mixing/${paramsDeleteId}`
					  )
					: authAxios.delete(
							`/cs-network/${paramsCsinkNetworkId}/kilns/${paramsDeleteKilnId}/mixing/${paramsDeleteId}`
					  )

			return response
		},
		onSuccess: () => {
			setSearchParams((params) => {
				params.delete('deleteId')
				params.delete('isArtisan')
				params.delete('artisanProId')
				params.delete('csinkNetworkId')
				params.delete('deleteKilnId')
				params.delete('deleteSiteId')
				return params
			})
			getAllMixing.refetch()
		},
		onError: (err: AxiosError) => {
			setSearchParams((params) => {
				params.delete('deleteId')
				params.delete('isArtisan')
				params.delete('artisanProId')
				params.delete('csinkNetworkId')
				params.delete('deleteKilnId')
				params.delete('deleteSiteId')
				return params
			})
			toast((err?.response?.data as { messageToUser: string })?.messageToUser)
		},
	})

	const handleDownloadExcelFile = () => {
		downloadExcelFileMuatation.mutate()
	}

	const deleteImageMutation = useMutation({
		mutationKey: ['deleteImageMutation'],
		mutationFn: async ({
			payload,
			endpoint,
		}: {
			payload: { imageIds: string[] }
			endpoint: string
		}) => {
			const response = await authAxios.delete(endpoint, { data: payload })
			return response
		},
		onSuccess: () => {
			toast('Image deleted successfully!')
			getAllMixing.refetch()
		},
		onError: (err: any) => {
			toast((err?.response?.data as { messageToUser: string })?.messageToUser)
		},
	})

	const deleteImageHandler = useCallback(
		(imgId: string) => {
			const payload = {
				imageIds: [imgId],
			}
			const endpoint =
				rowData?.isArtisan === true
					? `/artisian-pro/${rowData?.artisanProId}/site/${rowData?.siteId}/mixing/${rowData?.id}/images`
					: `/cs-network/${rowData?.csinkNetworkId}/site/${rowData?.siteId}/packaging-mixing/${rowData?.id}/images`
			deleteImageMutation.mutate({ payload, endpoint })
		},
		[deleteImageMutation, rowData]
	)

	const addImagesMutation = useMutation({
		mutationKey: ['addImagesMutation'],
		mutationFn: async ({
			payload,
			endpoint,
		}: {
			payload: { imageIds: string[] }
			endpoint: string
		}) => {
			const response = await authAxios.post(endpoint, payload)
			return response
		},
		onSuccess: () => {
			toast.success('Image added successfully!')
			getAllMixing.refetch()
		},
		onError: (err: AxiosError) => {
			showAxiosErrorToast(err)
		},
	})
 
	const addImagesHandler = useCallback(
		(
			imageIds: string[] = [],
			videos: { videoId: string; thumbnailImageId: string | null }[] = []
		  ) => {
			const payload = {
			  imageIds,
			  videos,
			}
			const endpoint =
				rowData?.isArtisan === true
					? `/artisian-pro/${rowData?.artisanProId}/site/${rowData?.siteId}/mixing/${rowData?.id}/images`
					: `/cs-network/${rowData?.csinkNetworkId}/site/${rowData?.siteId}/mixing/${rowData?.id}/images`
			addImagesMutation.mutate({ payload, endpoint })
		},
		[addImagesMutation, rowData]
	)

	const addVideosMutation = useMutation({
		mutationKey: ['addVideosMutation'],
		mutationFn: async ({
			payload,
			endpoint,
		}: {
			payload: { videos: { videoId: string; thumbnailImageId: string | null }[] }
			endpoint: string
		}) => {
			const response = await authAxios.post(endpoint, payload)
			return response
		},
		onSuccess: () => {
			toast.success('Video added successfully!')
			getAllMixing.refetch()
		},
		onError: (err: AxiosError) => {
			toast((err?.response?.data as { messageToUser: string })?.messageToUser)
		},
	})
	
	const addVideosHandler = useCallback(
		(videos: { videoId: string; thumbnailImageId: string | null }[]) => {
			const payload = { videos }
	
			const endpoint =
				rowData?.isArtisan === true
					? `/artisian-pro/${rowData?.artisanProId}/site/${rowData?.siteId}/mixing/${rowData?.id}/images`
					: `/cs-network/${rowData?.csinkNetworkId}/site/${rowData?.siteId}/mixing/${rowData?.id}/images`
	
			addVideosMutation.mutate({ payload, endpoint })
		},
		[addVideosMutation, rowData]
	)
	

	return {
		allMixing: getAllMixing.data,
		setSearchParams,
		showDownloadCheckbox,
		setShowDownloadCheckbox,
		selectedProcess,
		setselectedProcess,
		handleDownloadExcelFile,
		isLoading: getAllMixing.isLoading,
		fetchMixTypeListQuery,
		deleteMixing,
		deleteImageHandler,
		deleteImageMutation,
		setRowData,
		addImagesHandler,
		addVideosHandler
	}
}
