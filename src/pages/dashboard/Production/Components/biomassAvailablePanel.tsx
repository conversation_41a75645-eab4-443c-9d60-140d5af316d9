import { FC, useCallback, useMemo } from 'react'
import { Stack, Typography, useTheme } from '@mui/material'
import { QueryInput } from '@/components/QueryInputs'
import { Search } from '@mui/icons-material'
import {
	GridColDef,
	GridRenderCellParams,
	GridValidRowModel,
} from '@mui/x-data-grid'
import { Biomass_Secondary_Unit, BiomassAvailable } from '@/interfaces'
import { CustomTable } from '@/components/CustomTable'

interface IProps {
	biomasssAvailable?: {
		count: number
		biomassAvailable: BiomassAvailable[]
	}
	loadingBiomassAvailable: boolean
}

export const BiomassAvailablePanel: FC<IProps> = ({
	biomasssAvailable,
	loadingBiomassAvailable,
}) => {
	const theme = useTheme()

	const biomassAvailableColumn: GridColDef<GridValidRowModel>[] = useMemo(
		() => [
			{
				field: 'name',
				headerName: 'Network/Artisan Pro',
				minWidth: 120,
				flex: 1,

				renderCell: (params: GridRenderCellParams) => {
					return (
						<Typography
							variant='subtitle1'
							sx={{
								color: theme.palette.neutral['900'],
							}}>
							{params?.row?.name ?? '-'}
						</Typography>
					)
				},
			},
			{
				field: 'biomassAggregatorName',
				headerName: 'BA',
				minWidth: 120,
				flex: 1,
				renderCell: (params: GridRenderCellParams) => {
					return (
						<Typography
							variant='subtitle1'
							sx={{
								color: theme.palette.neutral['900'],
							}}>
							{params?.value ?? '-'}
						</Typography>
					)
				},
			},
			{
				field: 'biomassQuantity',
				align: 'center',
				headerName: `Biomass Qty (${Biomass_Secondary_Unit})`, //add ton enum here
				headerAlign: 'center',
				minWidth: 120,
				flex: 1,

				renderCell: (params: GridRenderCellParams) => {
					return (
						<Typography
							variant='subtitle1'
							sx={{
								color: theme.palette.neutral['900'],
							}}>
							{params?.row?.biomassQuantity
								? (params?.row?.biomassQuantity / 1000).toFixed(2)
								: '-'}
						</Typography>
					)
				},
			},
		],
		[theme.palette.neutral]
	)

	const handleRowClick = useCallback((params: any) => {
		params.setOpen(!params?.open)
		const collapseDetails = {
			biomassDetails: params?.row?.biomassDetails || [],
		}
		params.setHookData(collapseDetails)
	}, [])

	return (
		<CustomTable
			showPagination={true}
			rows={biomasssAvailable?.biomassAvailable || []}
			isLoading={loadingBiomassAvailable}
			columns={biomassAvailableColumn}
			count={biomasssAvailable?.count ?? 0}
			showPaginationDetails
			handleRowClick={handleRowClick}
			isComponent
			component='biomass_available_list'
			headerComponent={
				<Stack className='header-filter-search' gap={2}>
					<QueryInput
						className='search-textFiled'
						queryKey='searchName'
						placeholder='Search'
						setPageOnSearch
						InputProps={{
							endAdornment: <Search fontSize='small' />,
						}}
					/>
				</Stack>
			}
		/>
	)
}
