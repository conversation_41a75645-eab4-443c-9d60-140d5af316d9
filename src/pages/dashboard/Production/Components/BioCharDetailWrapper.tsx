import { CustomCard } from '@/components'
import { Stack, styled, Typography } from '@mui/material'
import React from 'react'

type Props = {
	title: string
	subTitle: string
	totalBioChar: number
	headerEndComponent?: React.ReactNode
	unit: React.ReactNode
}

export const BioCharDetailWrapper: React.FC<React.PropsWithChildren<Props>> = ({
	children,
	title,
	subTitle,
	totalBioChar,
	headerEndComponent,
	unit,
}) => {
	return (
		<StyledContainer>
			<Stack className='header'>
				<Typography className='title'>{title}</Typography>
				{headerEndComponent || null}
			</Stack>
			<CustomCard
				headerComponent={
					<Stack className='content-container'>
						<Stack
							direction='row'
							alignItems='flex-end'
							columnGap={1}
							justifyContent='center'>
							<Typography variant='subtitle1' className='bold-font'>
								{subTitle}:
							</Typography>
							<Typography variant='subtitle1'>
								{totalBioChar} {unit}
							</Typography>
						</Stack>
						{children}
					</Stack>
				}
			/>
		</StyledContainer>
	)
}

const StyledContainer = styled(Stack)(({ theme }) => ({
	width: '100%',
	rowGap: theme.spacing(1.5),
	'.header': {
		flexDirection: 'row',
		justifyContent: 'space-between',
		alignItems: 'center',
		width: '100%',
		padding: theme.spacing(0, 1),
		'.title': {
			...theme.typography.body2,
		},
	},
	'.content-container': {
		width: '100%',
		rowGap: theme.spacing(3),
		padding: theme.spacing(0.5, 0, 0, 0),
	},
	'.bold-font': {
		fontWeight: theme.typography.caption.fontWeight,
	},
}))
