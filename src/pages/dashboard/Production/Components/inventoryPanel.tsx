import { CommonFilter, CustomChip, CustomDataGrid } from '@/components'
import { QueryInput } from '@/components/QueryInputs'
import { IconButton, Stack, Typography, useTheme } from '@mui/material'
import {
	GridColDef,
	GridMoreVertIcon,
	GridRenderCellParams,
	GridValidRowModel,
} from '@mui/x-data-grid'
import { format } from 'date-fns'
import { FC, useCallback, useMemo } from 'react'
import { Search } from '@mui/icons-material'
import { IInventories } from '@/interfaces'

interface IProps {
	inventory?: {
		count: number
		inventories: IInventories[]
	}
	loadingInventory: boolean
}

export const InventoryPanel: FC<IProps> = ({ inventory, loadingInventory }) => {
	const theme = useTheme()

	const getStatusChipClass = useCallback((type: string) => {
		switch (type) {
			case 'Approved':
				return 'approved'
			case 'Rejected':
				return 'rejected'
			default:
				return 'pending'
		}
	}, [])

	const inventoryTableColumn: GridColDef<GridValidRowModel>[] = useMemo(
		() => [
			{
				field: 'displayId',
				headerName: 'Inventory ID',
				minWidth: 175,
				flex: 1,
			},
			{
				field: 'biomassAggregatorName',
				headerName: 'BA Name',
				minWidth: 175,
				flex: 1,
			},
			{
				field: 'artisanProName',
				headerName: 'Network/Artisan Pro',
				minWidth: 175,
				flex: 1,
				renderCell: (params: GridRenderCellParams) => {
					return (
						<Typography
							variant='subtitle1'
							sx={{
								color: theme.palette.neutral['900'],
							}}>
							{params.row.artisanProName || params.row.csinkNetworkName || '-'}
						</Typography>
					)
				},
			},
			{
				field: 'siteName',
				headerName: 'Site/Kiln Name',
				minWidth: 120,
				flex: 1,
				renderCell: (params: GridRenderCellParams) => {
					return (
						<Typography
							variant='subtitle1'
							sx={{
								color: theme.palette.neutral['900'],
							}}>
							{params.row.siteName || params.row.kilnName}
						</Typography>
					)
				},
			},
			{
				field: 'kilnProcessDisplayIds',
				headerName: 'Batch ID',
				minWidth: 160,
				flex: 1,
				renderCell: (params: GridRenderCellParams) => {
					return (
						<Typography
							variant='subtitle1'
							sx={{
								color: theme.palette.neutral['900'],
							}}>
							{params?.row?.kilnProcessDisplayIds.join(',')}
						</Typography>
					)
				},
			},
			{
				field: 'packagingType',
				headerName: 'Packaging Type',
				minWidth: 120,
				flex: 1,
			},
			{
				field: 'bagQuantity',
				headerName: 'Bag Details',
				minWidth: 80,
				flex: 1,
				renderCell: (params: GridRenderCellParams) => {
					return (
						<Typography
							variant='subtitle1'
							sx={{
								color: theme.palette.neutral['900'],
							}}>
							{params?.row?.bagQuantity} {params?.row?.bagQuantityUnit}
						</Typography>
					)
				},
			},
			{
				field: 'packedDate',
				headerName: 'Packed Date',
				headerAlign: 'center',
				align: 'center',
				minWidth: 120,
				flex: 1,
				renderCell: (params: GridRenderCellParams) => {
					return (
						<Typography
							variant='subtitle1'
							sx={{
								color: theme.palette.neutral['900'],
							}}>
							{params.row.packedDate
								? format(params?.row?.packedDate, 'yyyy-MM-dd')
								: '-'}
						</Typography>
					)
				},
			},
			{
				field: 'status',
				headerName: 'Status',
				headerAlign: 'center',
				align: 'center',
				minWidth: 120,
				flex: 1,
				renderCell: (params: GridRenderCellParams) => {
					return (
						<CustomChip
							appliedClass={getStatusChipClass(params?.row?.status)}
							label={params?.row?.status}
						/>
					)
				},
			},
			{
				field: 'action',
				headerName: '',
				maxWidth: 100,
				flex: 1,
				renderCell: () => (
					<IconButton size='small' onClick={() => {}}>
						<GridMoreVertIcon />
					</IconButton>
				),
			},
		],
		[getStatusChipClass, theme.palette.neutral]
	)

	return (
		<>
			<CustomDataGrid
				showPagination={true}
				rows={inventory?.inventories || []}
				columns={inventoryTableColumn}
				rowCount={inventory?.count ?? 0}
				loading={loadingInventory}
				headerComponent={
					<Stack className='header-filter-search' gap={2}>
						<QueryInput
							className='search-textFiled'
							queryKey='searchName'
							placeholder='Search'
							setPageOnSearch
							InputProps={{
								startAdornment: <Search fontSize='small' />,
							}}
						/>
						<CommonFilter />
					</Stack>
				}
			/>
		</>
	)
}
