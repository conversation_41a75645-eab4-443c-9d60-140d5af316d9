import { Divider, Stack, styled, Typography } from '@mui/material'
import React, { useMemo } from 'react'

type Open = {
	open: number
	distribution: number | null
}

type Packed = {
	packed: number
	distribution: number
}
type Data = {
	mixed: {
		value: number
		open: Open
		packed: Packed
	}
	unmixed: {
		value: number
		open: Open
		packed: Packed
	}
}

type Props = {
	unit: React.ReactNode
	data: Data
}

export const BioCharMixing: React.FC<Props> = ({ unit, data }) => {
	const contentList = useMemo(
		() => [
			{
				label: 'mixed',
				value: data.mixed.value,
			},
			{ label: 'unmixed', value: data.unmixed.value },
		],
		[data.mixed.value, data.unmixed.value]
	)

	const subArr: {
		[key: string]: { label: string; key: string; value: number | null }[]
	} = {
		mixed: [
			{ label: 'Open', key: 'open', value: data.mixed.open.open },
			{ label: 'Packed', key: 'packed', value: data.mixed.packed.packed },
			{
				label: 'Distributed',
				key: 'distributed',
				value: data.mixed.open.distribution,
			},
			{
				label: 'Distributed',
				key: 'packedDistributed',
				value: data.mixed.packed.distribution,
			},
		],
		unmixed: [
			{ label: 'Open', key: 'open', value: data.unmixed.open.open },
			{ label: 'Packed', key: 'packed', value: data.unmixed.packed.packed },
			{
				label: 'Distributed',
				key: 'distributed',
				value: data.unmixed.open.distribution,
			},
			{
				label: 'Distributed',
				key: 'packedDistributed',
				value: data.unmixed.packed.distribution,
			},
		],
	}

	return (
		<StyledContainer>
			{contentList.map((content) => (
				<Stack key={content.label} className='card' flex={1}>
					<Stack className='card-header'>
						<Typography
							variant='subtitle1'
							textTransform='capitalize'
							className='bold-font'>
							{content.label}
						</Typography>
						<Typography variant='subtitle1'>
							{content.value} {unit}
						</Typography>
					</Stack>
					<Stack className='card-body'>
						{subArr[content.label].map((item) => (
							<Stack key={item.key} className='card-item'>
								<Typography variant='subtitle1' className='bold-font'>
									{item.label}
								</Typography>
								{item?.value !== null ? (
									<Typography variant='subtitle1' textAlign='center'>
										{item?.value} {unit}
									</Typography>
								) : (
									<Typography variant='subtitle1' textAlign='center'>
										N/A
									</Typography>
								)}
								{['packed', 'open'].includes(item.key) ? <Divider /> : null}
							</Stack>
						))}
					</Stack>
				</Stack>
			))}
		</StyledContainer>
	)
}

const StyledContainer = styled(Stack)(({ theme }) => ({
	flexDirection: 'row',
	columnGap: theme.spacing(3.75),
	justifyContent: 'space-between',
	'.card': {
		border: '1px solid',
		borderColor: theme.palette.neutral['100'],
		borderRadius: theme.spacing(2),
		padding: theme.spacing(1.25),
		'.card-header': {
			alignItems: 'center',
			'.label': {
				textTransform: 'capitalize',
			},
		},
		'.card-body': {
			display: 'grid',
			gridTemplateColumns: '1fr 1fr',
			borderTop: '1px solid',
			borderColor: theme.palette.neutral['100'],
			paddingTop: theme.spacing(1.5),
			'.card-item': {
				alignItems: 'center',
				padding: theme.spacing(1),
				':nth-of-type(even)': {
					borderLeft: '1px solid',
					borderColor: theme.palette.neutral['100'],
				},
			},
		},
	},
	'.bold-font': {
		fontWeight: theme.typography.caption.fontWeight,
	},
	[theme.breakpoints.down('lg')]: {
		columnGap: theme.spacing(1),
	},
}))
