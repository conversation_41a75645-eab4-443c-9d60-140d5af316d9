import { IBiomassCollectionList } from "@/interfaces"
import { UpdateBiomassPayload } from "@/types"
import { yupResolver } from "@hookform/resolvers/yup"
import { CancelOutlined } from "@mui/icons-material"
import { Button, Dialog, DialogActions, DialogContent, DialogTitle, FormControl, FormHelperText, IconButton, InputAdornment, InputLabel, OutlinedInput, Typography} from "@mui/material"
import { FC, useEffect } from "react"
import { useForm } from "react-hook-form"
import * as Yup from 'yup'

const EditBiomassProSchema = Yup.object({
	siteBiomassID: Yup.string().required('Biomass ID is required'),
	biomassQuantity: Yup.string().required('Please enter Biomass Qty').notOneOf([''], 'Please enter Biomass Qty'),
})

export interface EditBiomass {
	siteBiomassID: string
	biomassQuantity: string
}

interface IProps {
    selectedBiomassCollection: IBiomassCollectionList | null
    open: boolean
    onClose: () => void
    onSave: (selectedBiomassCollection: UpdateBiomassPayload) => void
}

export const EditBiomassCollection: FC<IProps> = ({
	selectedBiomassCollection,
    open,
    onClose,
    onSave
}) => {

	const initialValues = {
		siteBiomassID: selectedBiomassCollection?.id ?? "",
		biomassQuantity: `${(selectedBiomassCollection?.biomassQuantity??0)<=0?"":selectedBiomassCollection?.biomassQuantity}`
	}

	const {
		register,
		handleSubmit,
		setValue,
		formState: { errors, isValid },
	} = useForm<EditBiomass>({
		defaultValues: initialValues,
		resolver: yupResolver<EditBiomass>(EditBiomassProSchema),
	})
	const onSubmit = handleSubmit((formValues: EditBiomass) => {
		const {
			siteBiomassID,
			biomassQuantity
		} = formValues
		const payload = {
			id: siteBiomassID??"",
			biomassQuantity: Number(biomassQuantity??0)
		}
		onSave(payload)
	})
	useEffect(() => {
		setValue("siteBiomassID", selectedBiomassCollection?.id ?? "")
		setValue("biomassQuantity", `${(selectedBiomassCollection?.biomassQuantity??0)<=0?"":selectedBiomassCollection?.biomassQuantity}`)
	},[selectedBiomassCollection])
    return <Dialog open={open && selectedBiomassCollection !== null} onClose={onClose} fullWidth maxWidth='sm'>
			<IconButton
				sx={{ position: 'absolute', right: 10, top: 10, cursor: 'pointer' }}
				onClick={onClose}>
				<CancelOutlined />
			</IconButton>
			<DialogContent>
				<DialogTitle textAlign='center' variant='h5'>
					Edit Biomass
				</DialogTitle>
				<FormControl fullWidth className="formControl">
					<InputLabel id='outlined-adornment-biomass-qty'>
						Enter Biomass Qty
					</InputLabel>
					<OutlinedInput
						id='outlined-adornment-biomass-qty'
						label='Enter Biomass Qty'
						{...register('biomassQuantity')}
						type='number'
						error={!!errors?.biomassQuantity}
						endAdornment={
							<InputAdornment position="end">
								kg
							</InputAdornment>
						}
					/>
					<FormHelperText id="outlined-weight-helper-text">{errors?.biomassQuantity?.message}</FormHelperText>
					{selectedBiomassCollection?.processUsedBiomassqty !== null && selectedBiomassCollection?.processUsedBiomassqty !== undefined ?
						<Typography color={"error"}>
						Used Biomass Qty: {selectedBiomassCollection?.processUsedBiomassqty} {selectedBiomassCollection?.processUsedBiomassqty > 1 ? 'kgs' : 'kg'}
						</Typography> : null
					}
				</FormControl>
            </DialogContent>
			<DialogActions>
				<Button
					variant='text'
					onClick={onClose}
					sx={{ textTransform: 'none', color: "#000000" }}
					fullWidth>
					Cancel
				</Button>
				<Button
					variant='text'
					onClick={() => {
						onSubmit()
					}}
					sx={{ textTransform: 'none' }}
					disabled={!isValid}
					fullWidth>
					Save
				</Button>
			</DialogActions>
		</Dialog>
}