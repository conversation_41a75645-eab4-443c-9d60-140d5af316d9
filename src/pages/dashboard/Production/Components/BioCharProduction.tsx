import {
	AccessTimeOutlined,
	CheckCircleOutline,
	DoDisturbOffOutlined,
} from '@mui/icons-material'
import { Stack, Typography, useTheme } from '@mui/material'
import React, { useMemo } from 'react'

type Props = {
	unit: React.ReactNode
	data: {
		approved: number
		rejected: number
		pending: number
	}
}

export const BioCharProduction: React.FC<Props> = ({ unit, data }) => {
	const theme = useTheme()
	const contentList = useMemo(
		() => [
			{
				label: 'Approved',
				icon: <CheckCircleOutline sx={{ color: theme.palette.success.dark }} />,
				value: data.approved,
			},
			{
				label: 'Rejected',
				icon: <DoDisturbOffOutlined sx={{ color: theme.palette.error.dark }} />,
				value: data.rejected,
			},
			{
				label: 'Pending',
				icon: <AccessTimeOutlined sx={{ color: theme.palette.warning.main }} />,
				value: data.pending,
			},
		],
		[
			data.approved,
			data.pending,
			data.rejected,
			theme.palette.error.dark,
			theme.palette.success.dark,
			theme.palette.warning.main,
		]
	)

	return (
		<Stack direction='row' justifyContent='space-evenly' px={3.75}>
			{contentList.map((item) => (
				<Stack key={item.label} alignItems='center' rowGap={1}>
					{item.icon}
					<Stack alignItems='center'>
						<Typography variant='subtitle1' fontWeight={600}>
							{item.label}
						</Typography>
						<Typography variant='subtitle1'>
							{item.value} {unit}
						</Typography>
					</Stack>
				</Stack>
			))}
		</Stack>
	)
}
