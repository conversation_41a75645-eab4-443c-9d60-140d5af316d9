import { PeriodEnum } from '@/interfaces'
import {
	<PERSON><PERSON>,
	<PERSON><PERSON><PERSON>,
	<PERSON>over,
	Stack,
	styled,
	useTheme,
} from '@mui/material'
import { AdapterMoment } from '@mui/x-date-pickers/AdapterMoment'
import { DateCalendar } from '@mui/x-date-pickers/DateCalendar'
import { LocalizationProvider } from '@mui/x-date-pickers/LocalizationProvider'
import moment from 'moment'
import React, { useCallback, useEffect, useState } from 'react'
import { useSearchParams } from 'react-router-dom'
const MIN_DATA_YEAR = 2023;
const Buttons = [
	{
		label: 'WTD',
		value: PeriodEnum.wtd,
	},
	{
		label: 'MTD',
		value: PeriodEnum.mtd,
	},
	{
		label: 'YTD',
		value: PeriodEnum.ytd,
	},
	{
		label: 'Custom',
		value: PeriodEnum.custom,
	},
]

export const TimeFilter = () => {
	const theme = useTheme()
	const [searchParams, setSearchParams] = useSearchParams()
	const selectedPeriod = searchParams.get('period') || PeriodEnum.mtd
	const [anchorEl, setAnchorEl] = React.useState<HTMLButtonElement | null>(null)
	const open = Boolean(anchorEl)
	const [startCalDate, setStartCalDate] = useState<moment.Moment | null>(null)
	const [endCalDate, setEndCalDate] = useState<moment.Moment | null>(null)

	const setInitialDateRange = useCallback(
		(period: PeriodEnum) => {
			const currentDate = moment()
			let startDate: string
			let endDate: string

			if (period === PeriodEnum.mtd) {
				startDate = moment(currentDate)
					.subtract(6, 'month')
					.format('DD-MM-YYYY')
				endDate = moment(currentDate).endOf('month').format('DD-MM-YYYY')
			} else if (period === PeriodEnum.ytd) {
				startDate = moment(currentDate).subtract(2, 'year').format('DD-MM-YYYY')
				endDate = moment(currentDate).endOf('year').format('DD-MM-YYYY')
			} else {
				endDate = currentDate.format('DD-MM-YYYY')
				startDate = moment(currentDate).subtract(6, 'days').format('DD-MM-YYYY')
			}

			setSearchParams(
				(prev) => {
					prev.set('startDate', startDate)
					prev.set('endDate', endDate)
					prev.set('period', period)
					return prev
				},
				{ replace: true }
			)
		},
		[setSearchParams]
	)

	const handleSelectButton = useCallback(
		(event: React.MouseEvent<HTMLButtonElement>, value: PeriodEnum) => {
			if (value === PeriodEnum.custom) {
				setAnchorEl(event.currentTarget)
			}
			if (value === selectedPeriod) return

			setInitialDateRange(value)
		},
		[selectedPeriod, setInitialDateRange]
	)

	useEffect(() => {
		const startParam = searchParams.get('startDate')
		const endParam = searchParams.get('endDate')
		if (!startParam || !endParam) {
			setInitialDateRange(selectedPeriod as PeriodEnum)
		} else {
			setStartCalDate(moment(startParam, 'DD-MM-YYYY'))
			setEndCalDate(moment(endParam, 'DD-MM-YYYY'))
		}
	}, [searchParams, selectedPeriod, setInitialDateRange])

	const handleClose = () => {
		setAnchorEl(null)
	}

	const handleDateChange = (
		newValue: moment.Moment | null,
		type: 'startDate' | 'endDate',
		setDate: (date: moment.Moment) => void
	) => {
		if (newValue?.isValid()) {
			setDate(newValue)
			setSearchParams(
				(prev) => {
					prev.set(type, newValue.format('DD-MM-YYYY'))
					prev.set('period', PeriodEnum.custom)
					return prev
				},
				{ replace: true }
			)
		} else {
			console.error('Invalid date selected')
		}
	}

	return (
		<StyledStack>
			<Stack className='tab-container'>
				{Buttons.map(({ label, value }) => (
					<Button
						key={value}
						className={`tab-button ${
							selectedPeriod === value ? 'selected-button' : ''
						}`}
						size='small'
						variant='text'
						onClick={(event) => handleSelectButton(event, value)}>
						{label}
					</Button>
				))}
				<Popover
					open={open}
					anchorEl={anchorEl}
					onClose={handleClose}
					anchorOrigin={{
						vertical: 'bottom',
						horizontal: 'left',
					}}>
					<Stack direction='row' columnGap={1} alignItems='center'>
						<LocalizationProvider dateAdapter={AdapterMoment}>
							<DateCalendar
								views={['year', 'month', 'day']}
								disableFuture
								value={startCalDate}
								shouldDisableYear={(year) => year.year() < MIN_DATA_YEAR}
								onChange={(newValue) =>
									handleDateChange(newValue, 'startDate', setStartCalDate)
								}
							/>
						</LocalizationProvider>
						<Divider
							orientation='vertical'
							flexItem
							sx={{ borderColor: theme.palette.neutral[100] }}
						/>
						<LocalizationProvider dateAdapter={AdapterMoment}>
							<DateCalendar
								views={['year', 'month', 'day']}
								disableFuture
								value={endCalDate}
								shouldDisableYear={(year) => year.year() < MIN_DATA_YEAR}
								shouldDisableDate={(date) =>
									startCalDate ? date.isBefore(startCalDate, 'day') : false
								}
								onChange={(newValue) =>
									handleDateChange(newValue, 'endDate', setEndCalDate)
								}
							/>
						</LocalizationProvider>
					</Stack>
				</Popover>
			</Stack>
		</StyledStack>
	)
}

const StyledStack = styled(Stack)(({ theme }) => ({
	'.tab-container': {
		flexDirection: 'row',
		alignItems: 'center',
		background: theme.palette.neutral['100'],
		padding: theme.spacing(0.25),
		borderRadius: theme.spacing(0.75),
		'.tab-button': {
			background: 'transparent',
			color: theme.palette.common.black,
			textTransform: 'none',
			height: 32,
			width: 88,
			fontWeight: 400,
		},
		'.selected-button': {
			background: theme.palette.common.white,
		},
		'.button-border': {
			height: '60%',
			borderRight: `2px solid ${theme.palette.neutral['100']}`,
			borderRadius: theme.spacing(0.25),
		},
	},
}))
