import { CustomHeader } from '@/components'
import { ArrowLeftRounded } from '@mui/icons-material'
import { Button, Stack, styled, Typography, useTheme } from '@mui/material'
import { useNavigate } from 'react-router-dom'
import { usePackaging } from './hooks'
import { AllPackagingPanel } from './Components'

export const Packaging = () => {
	const theme = useTheme()
	const navigate = useNavigate()

	const {
		allPackaging,
		isLoading,
		// setShowDownloadCheckbox,
		showDownloadCheckbox,
		selectedProcess,
		deletePackaging,
		setselectedProcess,
		// handleDownloadExcelFile,
		fetchAllMixTypeQuery,
	} = usePackaging()

	// function HeaderEndButtons() {
	// 	return (
	// 		<Stack direction='row' spacing={theme.spacing(2)}>
	// 			<IconButton
	// 				className='download-btn'
	// 				onClick={() =>
	// 					setShowDownloadCheckbox((prev) => {
	// 						if (!prev) return true
	// 						else if (selectedProcess) {
	// 							handleDownloadExcelFile()
	// 							return prev // return same boolean for error handling
	// 						} else {
	// 							setselectedProcess(null)
	// 							return !prev
	// 						}
	// 					})
	// 				}>
	// 				{showDownloadCheckbox ? (
	// 					<FileDownloadOutlined color='primary' fontSize='small' />
	// 				) : (
	// 					<Box component='img' src={DownloadIcon} height={20} width={20} />
	// 				)}
	// 			</IconButton>
	// 		</Stack>
	// 	)
	// }

	return (
		<StyledContainer>
			<CustomHeader
				showBottomBorder={false}
				showButton={false}
				// endComponent={<HeaderEndButtons />}
				headingComponent={
					<Stack direction='row' alignItems='center'>
						<Button
							variant='text'
							startIcon={<ArrowLeftRounded />}
							sx={{
								padding: 0,
								...theme.typography.body1,
								color: theme.palette.neutral['300'],
							}}
							onClick={() => navigate('/dashboard/production')}>
							Production
						</Button>
						<Typography variant='h6' color={theme.palette.neutral['500']}>
							&nbsp;/ Packaging
						</Typography>
					</Stack>
				}
			/>

			<AllPackagingPanel
				allPackaging={allPackaging}
				isLoading={isLoading}
				showDownloadCheckbox={showDownloadCheckbox}
				selectedProcess={selectedProcess}
				setselectedProcess={setselectedProcess}
				deletePackaging={() => {
					deletePackaging.mutate()
				}}
				fetchAllMixTypeQuery={fetchAllMixTypeQuery}
			/>
		</StyledContainer>
	)
}

const StyledContainer = styled(Stack)(({ theme }) => ({
	padding: theme.spacing(3),
	paddingTop: theme.spacing(2),
	'.filter-Chips': {
		flexDirection: 'row',
		gap: theme.spacing(1),
	},
	'.download-btn': {
		border: `${theme.spacing(0.125)} solid ${theme.palette.neutral['100']}`,
		borderRadius: theme.spacing(1.2),
	},

	'.tabList': {
		borderBottom: `${theme.spacing(0.125)} solid ${
			theme.palette.neutral['100']
		}`,
		padding: theme.spacing(0, 3),
		background: theme.palette.neutral['50'],
	},
	'.container': {
		padding: theme.spacing(2, 0),
		'.grid-header-component': {
			flexDirection: 'row',
			alignItems: 'center',
			'.search-textFiled': {
				minWidth: 334,
				width: '100%',
				'.MuiInputBase-root': {
					height: theme.spacing(4.5),
					borderRadius: theme.spacing(1.25),
				},
			},
			'.form-controller': {
				margin: theme.spacing(0.125),
				minWidth: theme.spacing(18),
				width: '100%',
				'.MuiOutlinedInput-notchedOutline': {
					borderRadius: theme.spacing(1.25),
				},
			},
		},
	},
	'.header-filter-search': {
		flexDirection: 'row',
		alignItems: 'center',
		'.search-textFiled': {
			minWidth: 260,
			maxWidth: 260,
			width: '100%',
			'.MuiInputBase-root': {
				height: theme.spacing(4.5),
				borderRadius: theme.spacing(1.25),
			},
		},
		'.form-controller': {
			margin: theme.spacing(0.125),
			minWidth: theme.spacing(12.5),
			width: '100%',
		},
	},
	'.filter-container': {
		flexWrap: 'wrap',
		rowGap: theme.spacing(2),
	},
	'.img-btn': {
		height: theme.spacing(5),
		width: theme.spacing(5),
		borderRadius: theme.spacing(0.5),
	},
}))
