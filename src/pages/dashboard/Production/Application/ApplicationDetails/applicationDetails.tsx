import farmerFarming from '@/assets/icons/farmer-farming.svg'
import fireIcon from '@/assets/icons/fire.svg'
import Picture from '@/assets/icons/picture.svg'
import ArrowLeftRoundedIcon from '@mui/icons-material/ArrowLeftRounded'
import CloseRoundedIcon from '@mui/icons-material/CloseRounded'
import DoneIcon from '@mui/icons-material/Done'
import QueryBuilderRoundedIcon from '@mui/icons-material/QueryBuilderRounded'
import {
	alpha,
	Box,
	Button,
	Grid,
	Stack,
	styled,
	Typography,
} from '@mui/material'
import { ChangeEvent, useCallback, useRef } from 'react'
import { useNavigate } from 'react-router-dom'
import { CustomChip } from '@/components'
import { filterValue } from '@/utils/constant'
import { productionApplicationDetails } from '@/pages/dashboard/dummyData'

export const ApplicationDetails = () => {
	const navigate = useNavigate()
	const inputRef = useRef<HTMLInputElement>(null)

	const handleFileChange = (event: ChangeEvent<HTMLInputElement>) => {
		const file = event.target.files?.[0]
		console.log('Selected file:', file)
	}

	const handleClick = () => {
		if (inputRef.current) {
			inputRef.current.click()
		}
	}

	const handleGoBack = useCallback(() => {
		navigate('/dashboard/production/application')
	}, [navigate])

	const networkStatus = {
		approved: {
			class: 'approved',
			icon: <DoneIcon color='success' fontSize='small' />,
		},
		pending: {
			class: 'pending',
			icon: <QueryBuilderRoundedIcon color='warning' fontSize='small' />,
		},
		rejected: {
			class: 'rejected',
			icon: <CloseRoundedIcon color='error' fontSize='small' />,
		},
	}

	return (
		<StyledContained>
			<Stack className='header-navigation'>
				<Button
					className='batch-button'
					variant='text'
					onClick={handleGoBack}
					startIcon={<ArrowLeftRoundedIcon className='arrow-icon' />}>
					Application
				</Button>
			</Stack>
			<Stack className='container' spacing={5}>
				<Stack className='header'>
					<Typography variant='h5'>Application &#62; BA11A11111</Typography>
					<Stack className='header-desc'>
						<Stack direction='row' spacing={1} alignItems={'center'}>
							<Box
								component='img'
								src={fireIcon}
								alt='fire-icon'
								height={20}
								width={20}
							/>
							<Typography variant='body1'>Kiln: Kiln Name ABCD</Typography>
						</Stack>
						<CustomChip
							label={'approved'} // need to change as per data.
							appliedClass={networkStatus.approved.class}
							icon={networkStatus.approved.icon}
						/>
					</Stack>
				</Stack>
				<Stack className='batch-detail-container'>
					<Grid container>
						{productionApplicationDetails.map(
							({ label, value }, index: number) => (
								<Grid
									key={index}
									item
									xs={6}
									sm={4}
									md={3}
									className='desc-grid-item'>
									<Stack className='desc' spacing={1}>
										<Typography className='title' variant='subtitle2'>
											{label}
										</Typography>
										<Typography variant='subtitle1'>{value}</Typography>
									</Stack>
								</Grid>
							)
						)}
					</Grid>
				</Stack>
				<Grid container>
					<Grid
						item
						xs={12}
						sm={5}
						md={5}
						className='batch-detail-image-uploader'>
						<Stack spacing={0.5}>
							<Typography
								variant='overline'
								className='grey-color-text'
								textTransform='none'>
								16/02/2024 (06:33 PM)
							</Typography>
							<Typography
								variant='overline'
								className='grey-color-text'
								textTransform='none'>
								3 Bags - 87 litre
							</Typography>
							<Typography
								variant='overline'
								className='grey-color-text'
								textTransform='none'>
								Farm location: Noida
							</Typography>
						</Stack>
						<Box>
							<input
								type='file'
								accept='image/*'
								ref={inputRef}
								style={{ display: 'none' }}
								onChange={handleFileChange}
							/>
							<Box
								onClick={handleClick}
								id={'file'}
								component='img'
								src={Picture}
								height={24}
								width={24}
								className='upload-picture'
							/>
						</Box>
					</Grid>
					<Grid item xs={12} sm={7} md={7}>
						<Stack className='image-container'>
							<Box component='img' src={farmerFarming} className='image' />
						</Stack>
					</Grid>
				</Grid>
			</Stack>
		</StyledContained>
	)
}

const StyledContained = styled(Stack)(({ theme }) => ({
	gap: theme.spacing(2),
	'.grey-color-text': {
		color: theme.palette.neutral['500'],
	},
	'.header-navigation': {
		padding: theme.spacing(4, 3, 2),
		borderBottom: `${theme.spacing(0.125)} solid ${
			theme.palette.neutral['100']
		}`,
		alignItems: 'flex-start',
		'.batch-button': {
			color: theme.palette.neutral['500'],
			...theme.typography.h6,
			'.arrow-icon': {
				color: theme.palette.neutral['500'],
			},
		},
	},
	'.container': {
		padding: theme.spacing(1, 2.2),
		'.header': {
			flexDirection: 'row',
			justifyContent: 'space-between',
			'.header-desc': {
				gap: theme.spacing(8),
				flexDirection: 'row',
				alignItems: 'center',
			},
		},
		'.batch-detail-container': {
			width: '100%',
			height: '100%',
			borderRadius: theme.spacing(2),
			boxShadow: `0 0 ${theme.spacing(0.25)} 0 ${alpha(
				theme.palette.common.black,
				0.25
			)}`,
			'.desc-grid-item': {
				padding: theme.spacing(2),
				'.desc': {
					'.title': {
						color: theme.palette.neutral['300'],
					},
				},
			},
		},
		'.batch-detail-image-uploader': {
			width: '100%',
			height: 200,
			padding: theme.spacing(2, 4),
			display: 'flex',
			justifyContent: 'space-between',
			'.upload-picture': {
				'&:hover': {
					cursor: 'pointer',
					filter: filterValue,
				},
			},
		},
		'.image-container': {
			borderRadius: theme.spacing(2),
			boxShadow: `0 0 ${theme.spacing(0.25)} 0 ${alpha(
				theme.palette.common.black,
				0.25
			)}`,
			padding: theme.spacing(2),
			'.image': {
				width: theme.spacing(60),
			},
		},
	},
}))
