import { authAxios } from '@/contexts'
import { ICsink } from '@/interfaces'
import { defaultLimit, defaultPage } from '@/utils/constant'
import { useQuery } from '@tanstack/react-query'
import { useSearchParams } from 'react-router-dom'

export const useCSinkNetwork = () => {
	const [searchParams, setSearchParams] = useSearchParams()
	const paramsLimit = searchParams.get('limit') || defaultLimit.toString()
	const paramsPage = searchParams.get('page') || defaultPage.toString()
	const paramsSearchName = searchParams.get('search') || ''
	const paramsBAId = searchParams.get('baId') || ''
	const paramsTab = searchParams.get('tab') || 'all'

	const AllBaQuery = useQuery({
		queryKey: ['allBA'],
		queryFn: () => {
			return authAxios.get<{
				baDetails: { id: string; name: string; shortCode: string }[]
			}>(`/drop-down/biomass-aggregators`)
		},
		select: ({ data }) =>
			data?.baDetails?.map((item) => ({
				label: `${item.name} (${item.shortCode})`,
				value: item.id,
			})),
	})

	const getCSinkNetwork = useQuery({
		queryKey: [
			'allNetworks',
			paramsLimit,
			paramsPage,
			paramsBAId,
			paramsSearchName,
		],
		queryFn: () => {
			const params = {
				limit: paramsLimit,
				page: paramsPage,
				search: paramsSearchName,
				biomassAggregatorId: paramsBAId,
			}

			const queryParams = new URLSearchParams()

			for (const [key, value] of Object.entries(params)) {
				if (value !== '') {
					queryParams.append(key, value)
				}
			}
			return authAxios.get<ICsink>(`/new/csink-network?${queryParams}`)
		},
		select: ({ data }) => data,
	})

	return {
		cSinkNetwork: getCSinkNetwork.data,
		isLoading: getCSinkNetwork.isLoading,
		allBaList: AllBaQuery.data,
		setSearchParams,
		paramsTab,
	}
}
