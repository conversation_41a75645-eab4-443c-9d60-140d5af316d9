import { ActionInformationDrawer, ImageCarouselDialog } from '@/components'
import { AddKilnWithDimensionImages } from '@/components/AddKilnWithDimensionImages'
import { authAxios } from '@/contexts'
import { IKiln, IKilnWithDimensionImages } from '@/interfaces'
import { showKlinShapeLabelEnum } from '@/utils/constant'
import { convertMillimeterToCentimeter, getKlinImagesList } from '@/utils/helper'
import { Close, Edit } from '@mui/icons-material'
import {
	Typography,
	styled,
	Stack,
	IconButton,
	CircularProgress,
	Container,
	Avatar,
} from '@mui/material'
import { useQuery } from '@tanstack/react-query'
import { useMemo, useState } from 'react'
import { useParams } from 'react-router-dom'

const useKilnDetails = (kilnId: string) => {
	const { cSinkNetworkId } = useParams()
	const { data, isLoading } = useQuery({
		queryKey: ['kilnDetails', kilnId],
		queryFn: async () => {
			const { data } = await authAxios.get<IKilnWithDimensionImages>(
				`/cs-network/${cSinkNetworkId}/kilns/${kilnId}`
			)
			return data
		},
		enabled: !!kilnId,
	})
	return { data, isLoading }
}
const getFormattedKilnShape = (shape: string | undefined): string => {
	if (!shape) return ''

	switch (shape) {
		case showKlinShapeLabelEnum.rectangular_frustum:
			return showKlinShapeLabelEnum.rectangular
		case showKlinShapeLabelEnum.rectangular:
			return showKlinShapeLabelEnum.pyramidal
		default:
			return shape
	}
}

export const KlinDetailsDrawer = ({
	setHideModal,
	kilnData,
}: {
	setHideModal: () => void
	kilnData?: IKiln
}) => {
	const [editKilnDrawer, setEditKilnDrawer] = useState(false)
	const [openCarousel, setOpenCarousel] = useState<boolean>(false);
	const { data, isLoading } = useKilnDetails(kilnData?.id ?? '')
	const handleClose = () => {
		setEditKilnDrawer(false)
	}

	const kilnDetails = useMemo(() => {
		switch (data?.kilnShape) {
			case 'cylindrical':
				return (
					<>
						<Stack className='container'>
							<Typography variant='subtitle1'>
								Diameter: {convertMillimeterToCentimeter(data?.diameter ?? 0)}{' '}
								cm
							</Typography>
							<Typography variant='subtitle1'>
								Height: {convertMillimeterToCentimeter(data?.depth ?? 0)} cm
							</Typography>
						</Stack>
					</>
				)
			case 'rectangular_frustum':
				return (
					<>
						<Stack className='container containerGapped'>
							<Typography variant='subtitle1'>
								Long Base: {convertMillimeterToCentimeter(data?.longBase ?? 0)}{' '}
								cm
							</Typography>
							<Typography variant='subtitle1'>
								Short Base:{' '}
								{convertMillimeterToCentimeter(data?.shortBase ?? 0)} cm
							</Typography>
						</Stack>
						<Stack className='container containerGapped'>
							<Typography variant='subtitle1'>
								Height: {convertMillimeterToCentimeter(data?.depth ?? 0)} cm
							</Typography>
							<Typography variant='subtitle1'>
								Length:{' '}
								{convertMillimeterToCentimeter(data?.frustumLength ?? 0)} cm
							</Typography>
						</Stack>
					</>
				)
			case 'rectangular':
				return (
					<>
						<Stack className='container containerGapped'>
							<Typography variant='subtitle1'>
								Long Base: {convertMillimeterToCentimeter(data?.lowerSide ?? 0)}{' '}
								cm
							</Typography>
							<Typography variant='subtitle1'>
								Short Base:{' '}
								{convertMillimeterToCentimeter(data?.upperSide ?? 0)} cm
							</Typography>
						</Stack>
						<Stack className='container'>
							<Typography variant='subtitle1'>
								Height: {convertMillimeterToCentimeter(data?.depth ?? 0)} cm
							</Typography>
						</Stack>
					</>
				)
			default:
				return (
					<>
						<Stack className='container containerGapped' spacing={0}>
							<Typography variant='subtitle1'>
								Upper Surface:{' '}
								{convertMillimeterToCentimeter(data?.upperSurfaceDiameter ?? 0)}{' '}
								cm
							</Typography>
							<Typography variant='subtitle1'>
								Lower Surface:{' '}
								{convertMillimeterToCentimeter(data?.lowerSurfaceDiameter ?? 0)}{' '}
								cm
							</Typography>
						</Stack>
						<Stack className='container'>
							<Typography variant='subtitle1'>
								Height: {convertMillimeterToCentimeter(data?.depth ?? 0)} cm
							</Typography>
						</Stack>
					</>
				)
		}
	}, [data])

	if (isLoading)
		return (
			<Container>
				<Stack flex={1} alignItems={'center'} justifyContent={'center'}>
					<CircularProgress />
				</Stack>
			</Container>
		)
	return (
		<>
			<ActionInformationDrawer
				open={editKilnDrawer}
				onClose={handleClose}
				anchor='right'
				component={
					<AddKilnWithDimensionImages
						editMode={true}
						kilnDetails={data}
						handleClose={handleClose}
						subheading={data?.ShortName}
					/>
				}
			/>
			<ImageCarouselDialog
				open={openCarousel}
				close={() => setOpenCarousel(false)}
				ImagesList={data ? getKlinImagesList(data) : []}
				ShowDeleteOption={false}
				showDownload={false}
			/>
			<StyleContainer>
				<Stack className='header'>
					<Stack className='headtitle'>
						<Stack>
							<Typography variant='h5'>{data?.name}</Typography>
						</Stack>
						<IconButton onClick={() => setHideModal()}>
							<Close />
						</IconButton>
					</Stack>
				</Stack>
				<Stack className='nameDetailed'>
					<Stack direction={'row'} className='KilnHeader'>
						<IconButton
							onClick={() => setOpenCarousel(true)}
							sx={{ padding: 0 }}
						>
							<Avatar src={data?.imageURLs?.[0]?.url || ""} />
						</IconButton>
						<Stack width={'100%'}>
							<Stack className='klin-details-header'>
								<Typography variant='body2'>
									{`${data?.name} (${data?.kilnType ? `${data.kilnType} - ` : ''
										}${getFormattedKilnShape(data?.kilnShape)} (${data?.volume} ${data?.volumeUnit
										}))`}
								</Typography>

								<IconButton onClick={() => setEditKilnDrawer(true)}>
									<Edit fontSize='small' />
								</IconButton>
							</Stack>
							{kilnDetails}
						</Stack>
					</Stack>
				</Stack>
			</StyleContainer>
		</>
	)
}

const StyleContainer = styled(Stack)(({ theme }) => ({
	gap: theme.spacing(2),
	'.header': {
		flexDirection: 'row',
		justifyContent: 'space-between',
		alignItems: 'center',
		padding: theme.spacing(4.5, 2),
		borderBottom: `${theme.spacing(0.125)} solid ${theme.palette.neutral['100']
			}`,
	},
	'.container': {
		width: '90%',
		gap: theme.spacing(0),
	},
	'.containerGapped': {
		display: 'flex',
		flexDirection: 'row',
		justifyContent: 'space-between',
	},
	'.headtitle': {
		display: 'flex',
		flexDirection: 'row',
		alignItems: 'center',
		justifyContent: 'space-between',
		width: '100%',
		gap: theme.spacing(1),
	},
	'.nameDetailed': {
		display: 'flex',
		flexDirection: 'column',
		paddingLeft: theme.spacing(2),
		paddingRight: theme.spacing(2),
		gap: theme.spacing(0.5),
		'.KilnHeader': {
			display: 'flex',
			gap: theme.spacing(3),
			alignItems: 'center',
		},
	},
	'.klin-details-header': {
		display: 'flex',
		flexDirection: 'row',
		alignItems: 'center',
		justifyContent: 'space-between',
		marginBottom: theme.spacing(1)
	}
}))
