import {
	ActionInformation<PERSON>rawer,
	CustomDataGrid,
	LoadingWrapper,
} from '@/components'
import {
	Add,
	ArrowLeft,
	Download,
	PlaceOutlined,
	Search,
	TableRows,
} from '@mui/icons-material'
import { Tab<PERSON>ontext, TabList, TabPanel } from '@mui/lab'
import {
	Avatar,
	Box,
	Button,
	IconButton,
	Link,
	Stack,
	styled,
	Tab,
	Tooltip,
	Typography,
	useTheme,
} from '@mui/material'
import {
	GridColDef,
	GridRenderCellParams,
	GridValidRowModel,
} from '@mui/x-data-grid'
import { useCallback, useMemo, useState } from 'react'
import { useNavigate } from 'react-router-dom'
import { PackagingDetails } from '../../ArtisanPro'
import { useCSinkNetworkDetails } from './useCSinkNetworkDetails'
import { QueryInput } from '@/components/QueryInputs'
import { CropforCsinkManager, IAssignOperator, INetwork } from '@/interfaces'
import { getGoogleMapLink } from '@/utils/helper'
import EditIcon from '@/assets/icons/editIcon.svg'
import { HeaderComponent } from './components'
import { Farm } from '@/interfaces/csink.type'
import { EditNetwork, StrategyContent } from '@/components/EditNetwork'
import { GoogleMapsDraw } from '@/components/GoogleMap'
import { EntityTabEnum } from '@/utils/constant'
import LeafMapComponent from '@/components/Leaflet-Maps/LeafletMaps'
import { FarmsListForSideDrawer } from '@/components/FarmsList'
import {
	AssignOperatortoFarmer,
	CsinkOperatorList,
} from '@/components/CsinkOperatorList'
import { KlinDetailsDrawer } from './components/KlinDetailsDrawer'
import { AddKilnWithDimensionImages } from '@/components/AddKilnWithDimensionImages'
import { EditOperator } from '@/components/EditNetwork/EditOperator'

const buttons = ['list', 'map']

export const CSinkNetworkDetails = () => {
	const theme = useTheme()
	const navigate = useNavigate()
	const [isPackagingDrawer, setPackagingDrawer] = useState(false)
	const [openEditNetworkModal, setOpenEditNetworkModal] =
		useState<EntityTabEnum | null>(null)
	const [showMethaneStrategy, setShowMethaneStrategy] = useState(false)
	const [showOperatorsDrawer, setShowOperatorsDrawer] = useState(false)
	const [showAssignOperatorDialog, setShowAssignOperatorDialog] = useState<
		string | null
	>(null)
	const [showModalID, setShowModalID] = useState<string | null>(null)
	const [editOperator, setEditOperator] = useState<IAssignOperator | null>()
	const [
		showBiomassPreProccessingStrategy,
		setShowBiomassPreProccessingStrategy,
	] = useState(false)

	const [openAddKilnDrawer, setOpenAddKilnDrawer] = useState(false)

	const {
		cSinkNetworkDetails,
		kilnDetails,
		isLoading,
		farmerListQuery,
		searchParams,
		setSearchParams,
		handleRowClick,
		paramsTab,
		handleChangeFarmerDataViewType,
		paramsFarmerDataViewType,
		handleFarmerRowClick,
		showMap,
		setShowMap,
		farmCoordinates,
		setFarmCoordinates,
		farmListQuery,
		handleSaveKml,
		setShowFarmDrawer,
		showFarmsDrawer,
		farmerInfoForSideDrawer,
		handleDownloadExcelFarmersMuatation,
	} = useCSinkNetworkDetails()
	const kilnColumn: GridColDef<GridValidRowModel>[] = useMemo(
		() => [
			{
				field: 'name',
				headerName: 'Kiln Name',
				minWidth: 280,
				flex: 1,
			},
			{
				field: 'farmerName',
				headerName: 'Farmer Name',
				minWidth: 180,
				flex: 1,
			},
			{
				field: 'address',
				headerName: 'Location',
				minWidth: 120,
				flex: 1,
				renderCell: (params) => {
					const extractLatLongFromCoordinateString = (coord: string) => {
						const [x, y] = coord.split(',')
						const lat = Number(x.split('(')[1] || 0)
						const lng = Number(y.split(')')[0] || 0)
						return {
							lat,
							lng,
						}
					}

					const { lat, lng } = extractLatLongFromCoordinateString(
						params?.row?.coordinate
					)
					return (
						<Stack
							direction='row'
							alignItems='center'
							component={Link}
							target='_blank'
							underline='hover'
							textTransform='none'
							color='common.black'
							href={getGoogleMapLink(`${lat}`, `${lng}`)}>
							<PlaceOutlined color='primary' sx={{ width: 20, height: 20 }} />
							<Typography variant='subtitle1'>{params?.value}</Typography>
						</Stack>
					)
				},
			},

			{
				field: 'biocharDetails',
				headerName: 'Biochar (tonnes)',
				minWidth: 120,
				flex: 1,
				renderCell: (params) => (
					<Typography variant='subtitle1'>
						{params?.row?.biocharDetails?.totalBiocharProducedInTonne || ''}
					</Typography>
				),
			},
			{
				field: 'klinDetails',
				headerName: 'Details',
				minWidth: 120,
				flex: 1,
				renderCell: (params) => (
					<Typography
						sx={{ cursor: 'pointer', color: 'primary.main' }}
						onClick={(e) => {
							setShowModalID(params.row.id)
							e.stopPropagation()
						}}
						variant='caption'>
						View
					</Typography>
				),
			},
		],
		[]
	)

	const farmerColumn: GridColDef<GridValidRowModel>[] = useMemo(
		() => [
			{
				field: 'name',
				headerName: 'Farmer',
				minWidth: 280,
				flex: 1,
				renderCell: (params: GridRenderCellParams) => {
					return (
						<Stack flexDirection='column'>
							<Typography
								variant='subtitle1'
								sx={{
									color: theme.palette.neutral['900'],
								}}>
								{params?.row?.name ?? ''}
							</Typography>
							<Typography variant='subtitle1'>
								{params?.row?.countryCode && params?.row?.phoneNo
									? `(${params?.row.countryCode} ${params?.row.phoneNo})`
									: ''}
							</Typography>
						</Stack>
					)
				},
			},
			{
				field: 'farmsCount',
				headerName: 'Farms',
				minWidth: 180,
				flex: 1,
				renderCell: (params) =>
					(params?.row?.farms || []).length > 0 ? (
						<Tooltip
							arrow
							title={
								<Stack rowGap={1}>
									{(params?.row?.farms || []).map((farm: Farm) => (
										<Stack key={farm.id}>
											<Stack direction='row' columnGap={1} alignItems='center'>
												<PlaceOutlined
													sx={{
														color: theme.palette.primary.main,
														width: theme.spacing(1.75),
													}}
												/>
												<Typography variant='caption'>
													{farm?.landmark}
												</Typography>
											</Stack>
											<Typography variant='subtitle1' fontSize={12} ml={3}>
												{farm?.fieldSize} {farm?.fieldSizeUnit}
											</Typography>
										</Stack>
									))}
								</Stack>
							}>
							<Typography minWidth={50} variant='subtitle1'>
								{params?.value || 0}
							</Typography>
						</Tooltip>
					) : (
						<Typography minWidth={50} variant='subtitle1'>
							{params?.value || 0}
						</Typography>
					),
			},
			{
				field: 'farmSize',
				headerName: 'Farm Size',
				minWidth: 180,
				flex: 1,
				renderCell: (params) => (
					<Typography variant='subtitle1'>
						{params?.row?.farmSize?.toFixed(3) || 0} hectare
					</Typography>
				),
			},
			{
				field: 'biocharQuantity',
				headerName: 'Total Biochar Produced',
				minWidth: 200,
				flex: 1,
				renderCell: (params) => (
					<Typography variant='subtitle1'>
						{params?.row?.biocharQuantity || 0} ltrs
					</Typography>
				),
			},

			{
				field: 'biomassQuantity',
				headerName: 'Biomass Quantity',
				minWidth: 180,
				flex: 1,
				renderCell: (params) => (
					<Typography variant='subtitle1'>
						{params?.row?.biomassQuantity || 0} kgs
					</Typography>
				),
			},
			{
				field: 'crop',
				headerName: 'Crops',
				minWidth: 180,
				flex: 1,
				renderCell: (params) => {
					const cropNames = params?.row?.crops
						?.map((crop: CropforCsinkManager) => crop.cropName)
						?.join(', ')
					return (
						<Tooltip title={cropNames} placement='bottom'>
							<Typography variant='subtitle1'>
								{cropNames?.length > 40
									? `${cropNames?.slice(0, 40)}...`
									: `${cropNames}`}
							</Typography>
						</Tooltip>
					)
				},
			},

			{
				field: 'action',
				headerName: 'Assign Operator',
				minWidth: 180,
				flex: 1,
				renderCell: (params) => (
					<IconButton
						onClick={(e) => {
							e.stopPropagation()
							setShowAssignOperatorDialog(params?.row?.siteId)
						}}>
						<Add />
					</IconButton>
				),
			},
		],
		[theme, setShowAssignOperatorDialog]
	)

	const disableEditAndAdd = useMemo(() => {
		return (
			cSinkNetworkDetails?.isBiomassAggregatorSuspended ||
			cSinkNetworkDetails?.isCsinkManagerSuspended ||
			cSinkNetworkDetails?.isCsinkNetworkSuspended
		)
	}, [
		cSinkNetworkDetails?.isBiomassAggregatorSuspended,
		cSinkNetworkDetails?.isCsinkManagerSuspended,
		cSinkNetworkDetails?.isCsinkNetworkSuspended,
	])

	const handleTabChange = useCallback(
		(_: unknown, newValue: string) => {
			const queryParams = new URLSearchParams(searchParams)
			queryParams.set('tab', newValue)
			queryParams.delete('search')
			queryParams.delete('limit')
			queryParams.delete('page')
			queryParams.delete('farmerDataViewType')
			setSearchParams(queryParams, { replace: true })
		},
		[searchParams, setSearchParams]
	)

	return (
		<>
			<ActionInformationDrawer
				open={showOperatorsDrawer}
				onClose={() => setShowOperatorsDrawer(false)}
				anchor='right'
				component={
					<CsinkOperatorList
						onClose={() => setShowOperatorsDrawer(false)}
						heading={'Csink Network Operator'}
					/>
				}
			/>
			{showAssignOperatorDialog ? (
				<AssignOperatortoFarmer
					id={showAssignOperatorDialog}
					onClose={() => setShowAssignOperatorDialog(null)}
					open={!!showAssignOperatorDialog}
					onEditOperator={(operator) => setEditOperator(operator)}
				/>
			) : null}
			{editOperator && (
				<ActionInformationDrawer
					open={!!editOperator}
					onClose={() => setEditOperator(null)}
					anchor='right'
					component={
						<EditOperator
							handleCloseDrawer={() => setEditOperator(null)}
							operatorDetails={editOperator}
							operatorSiteId={editOperator?.siteId ?? ''}
							cSinkManagerId={cSinkNetworkDetails?.csinkManagerId ?? ''}
							type='assign'
						/>
					}
				/>
			)}

			<ActionInformationDrawer
				open={!!showFarmsDrawer}
				onClose={() => setShowFarmDrawer(null)}
				anchor='right'
				component={
					<FarmsListForSideDrawer
						farmer={farmerInfoForSideDrawer}
						handleClose={() => setShowFarmDrawer(null)}
						setFarmCoordinates={setFarmCoordinates}
						setSearchParams={setSearchParams}
						setShowMap={setShowMap}
					/>
				}
			/>
			<ActionInformationDrawer
				open={isPackagingDrawer}
				onClose={() => setPackagingDrawer(false)}
				anchor='right'
				component={<PackagingDetails close={() => setPackagingDrawer(false)} />}
			/>
			<ActionInformationDrawer
				open={!!showModalID}
				onClose={() => setShowModalID(null)}
				anchor='right'
				component={
					<KlinDetailsDrawer
						setHideModal={() => setShowModalID(null)}
						kilnData={kilnDetails?.kilns?.find(
							(kiln) => kiln.id == showModalID
						)}
					/>
				}
			/>
			<StyledContainer>
				<Stack direction='row' className='header'>
					<Stack direction='row' alignItems='center'>
						<Button
							onClick={() => {
								navigate(-1)
							}}
							className='batch-button'
							variant='text'
							startIcon={<ArrowLeft />}>
							C-Sink Network
						</Button>
						<Typography variant='h6' className='heading' sx={{ ml: 1 }}>
							/ {cSinkNetworkDetails?.shortName}
						</Typography>
					</Stack>
					<Stack
						direction='row'
						justifyContent={'space-between'}
						alignItems={'center'}
						columnGap={2}>
						{disableEditAndAdd && (
							<Typography color='primary' variant='caption'>
								This Csink network is suspended, that is why you can not Add/
								Edit anything
							</Typography>
						)}
						<Button
							variant='outlined'
							className='edit_btn'
							startIcon={<Box component='img' src={EditIcon} />}
							onClick={() => setOpenEditNetworkModal(EntityTabEnum.bags)}>
							Edit
						</Button>
					</Stack>
				</Stack>
				<Stack className='container'>
					<HeaderComponent
						setShowBiomassPreProccessingStrategy={
							setShowBiomassPreProccessingStrategy
						}
						setShowMethaneStrategy={setShowMethaneStrategy}
						setOpenEditNetworkModal={setOpenEditNetworkModal}
						setShowOperatorsDrawer={setShowOperatorsDrawer}
						cSinkNetworkDetails={cSinkNetworkDetails as INetwork}
					/>
					<Box>
						<TabContext value={paramsTab}>
							<Stack
								direction='row'
								justifyContent='space-between'
								alignItems='center'
								className='tab-container'>
								<TabList className='tabList' onChange={handleTabChange}>
									<Tab
										label={`Kilns `}
										sx={{ position: 'relative', minWidth: theme.spacing(12) }}
										icon={
											<Avatar
												sx={{
													position: 'absolute',
													right: 2,
													top: 5,
													height: theme.spacing(2.5),
													fontSize: theme.spacing(1.5),
													width: theme.spacing(2.5),
													backgroundColor:
														paramsTab === 'kiln'
															? theme.palette.primary.main
															: '',
												}}>
												{cSinkNetworkDetails?.kilnCount ?? 0}
											</Avatar>
										}
										iconPosition='top'
										value='kiln'
									/>
									<Tab
										label='Farmers '
										sx={{ position: 'relative', minWidth: theme.spacing(12) }}
										icon={
											<Avatar
												sx={{
													position: 'absolute',
													right: 2,
													top: 5,
													height: theme.spacing(2.5),
													fontSize: theme.spacing(1.5),
													width: theme.spacing(2.5),
													backgroundColor:
														paramsTab === 'farmers'
															? theme.palette.primary.main
															: '',
												}}>
												{cSinkNetworkDetails?.farmerCount ?? 0}
											</Avatar>
										}
										iconPosition='top'
										value='farmers'
									/>
								</TabList>
								{paramsTab === 'farmers' ? (
									<Stack flexDirection={'row'}>
										<IconButton
											sx={{ marginX: '10px' }}
											disabled={
												handleDownloadExcelFarmersMuatation?.isPending || false
											}
											onClick={() =>
												handleDownloadExcelFarmersMuatation.mutate()
											}>
											<LoadingWrapper
												loading={
													handleDownloadExcelFarmersMuatation?.isPending ||
													false
												}>
												<Download />
											</LoadingWrapper>
										</IconButton>
										<Stack className='button-container'>
											{buttons.map((label) => (
												<IconButton
													key={label}
													className={`tab-button ${
														paramsFarmerDataViewType === label
															? 'selected-button'
															: ''
													}`}
													onClick={() => handleChangeFarmerDataViewType(label)}>
													{label === 'list' ? (
														<TableRows className='button-icon' />
													) : (
														<Box component='img' src='/images/nav-icons.svg' />
													)}
												</IconButton>
											))}
										</Stack>
									</Stack>
								) : null}
							</Stack>
							<Box className='panel_container'>
								<TabPanel value='kiln' className='panel'>
									<CustomDataGrid
										onRowClick={handleRowClick}
										headerComponent={
											<Stack direction='row' columnGap={2}>
												<QueryInput
													className='search-textFiled'
													queryKey='search'
													placeholder='Search'
													setPageOnSearch
													InputProps={{
														startAdornment: (
															<Search
																fontSize='small'
																color='disabled'
																sx={{ pr: theme.spacing(0.3) }}
															/>
														),
													}}
												/>
												<Button
													onClick={() => setOpenAddKilnDrawer(true)}
													variant='contained'
													startIcon={<Add />}
													size='small'>
													Add Kiln
												</Button>
											</Stack>
										}
										showPagination={true}
										rows={kilnDetails?.kilns ?? []}
										columns={kilnColumn}
										rowCount={kilnDetails?.count}
										loading={isLoading}
									/>
								</TabPanel>
								<TabPanel value='farmers' className='panel'>
									{paramsFarmerDataViewType === 'list' ? (
										<CustomDataGrid
											onRowClick={handleFarmerRowClick}
											headerComponent={
												<QueryInput
													className='search-textFiled'
													queryKey='search'
													sx={{ width: theme.spacing(30) }}
													placeholder='Search'
													setPageOnSearch
													InputProps={{
														startAdornment: (
															<Search
																fontSize='small'
																color='disabled'
																sx={{ pr: theme.spacing(0.3) }}
															/>
														),
													}}
												/>
											}
											showPagination={true}
											rows={farmerListQuery?.data?.farmers ?? []}
											columns={farmerColumn}
											rowCount={farmerListQuery?.data?.count ?? 0}
											loading={isLoading}
										/>
									) : (
										<LeafMapComponent markers={farmListQuery?.data || []} />
									)}
								</TabPanel>
							</Box>
						</TabContext>
					</Box>
				</Stack>
				{openEditNetworkModal ? (
					<ActionInformationDrawer
						open={!!openEditNetworkModal}
						onClose={() => setOpenEditNetworkModal(null)}
						anchor='right'
						component={
							<EditNetwork
								handleClose={() => setOpenEditNetworkModal(null)}
								openTab={openEditNetworkModal}
								csinkNetworkDetails={cSinkNetworkDetails}
							/>
						}
					/>
				) : null}
				{openAddKilnDrawer ? (
					<ActionInformationDrawer
						open={openAddKilnDrawer}
						onClose={() => setOpenAddKilnDrawer(false)}
						component={
							<AddKilnWithDimensionImages
								handleClose={() => setOpenAddKilnDrawer(false)}
							/>
						}
					/>
				) : null}

				{showBiomassPreProccessingStrategy ? (
					<ActionInformationDrawer
						open={showBiomassPreProccessingStrategy}
						onClose={() => setShowBiomassPreProccessingStrategy(false)}
						anchor='right'
						component={
							<StrategyContent
								handleClose={() => setShowBiomassPreProccessingStrategy(false)}
								type={EntityTabEnum.biomassProcessingStrategy}
								csinkNetworkDetails={cSinkNetworkDetails}
							/>
						}
					/>
				) : null}
				{showMethaneStrategy ? (
					<ActionInformationDrawer
						open={showMethaneStrategy}
						onClose={() => setShowMethaneStrategy(false)}
						anchor='right'
						component={
							<StrategyContent
								handleClose={() => setShowMethaneStrategy(false)}
								type={EntityTabEnum.methaneCompensationStrategy}
								csinkNetworkDetails={cSinkNetworkDetails}
							/>
						}
					/>
				) : null}
				{showMap ? (
					<GoogleMapsDraw
						open={showMap}
						handleModalClose={() => {
							setSearchParams((params) => {
								params.delete('lat')
								params.delete('long')
								params.delete('networkId')
								params.delete('farmId')
								return params
							})
							setShowMap(false)
							setFarmCoordinates([])
						}}
						handleSave={handleSaveKml}
						initialPolygons={farmCoordinates}
					/>
				) : null}
			</StyledContainer>
		</>
	)
}

const StyledContainer = styled(Stack)(({ theme }) => ({
	gap: theme.spacing(2),
	'.add-operator': {
		textTransform: 'none',
		...theme.typography.subtitle2,
	},
	'.header': {
		alignItems: 'center',
		padding: theme.spacing(2),
		borderBottom: `${theme.spacing(0.125)} solid ${
			theme.palette.neutral['100']
		}`,
		justifyContent: 'space-between',
		'.heading': {
			color: theme.palette.neutral[500],
		},
		backgroundColor: theme.palette.common.white,
	},
	'.container': {
		gap: theme.spacing(2),
		paddingBottom: theme.spacing(0),
		'.card_container': {
			padding: theme.spacing(0, 3),
			'.details-header': {
				padding: theme.spacing(2),
				border: '1px solid',
				borderColor: theme.palette.neutral[100],
				borderRadius: theme.spacing(2),
				'.managerDetails': {
					width: '100%',
					cursor: 'pointer',
				},
				'.network_detail_container': {
					'.section_container': {
						flexDirection: 'column',
						columnGap: theme.spacing(2),
						rowGap: theme.spacing(2),
						[theme.breakpoints.down('md')]: {
							flexWrap: 'wrap',
							rowGap: theme.spacing(1),
						},
						'.section': {
							width: '100%',
							display: 'grid',
							gridTemplateColumns: 'repeat(4, 1fr)',
						},
						'.upper': {
							gridTemplateColumns: 'repeat(5,minmax(100px, 1fr))',
							[theme.breakpoints.down('md')]: {
								gridTemplateColumns: 'repeat(auto-fill,minmax(100px, 200px))',
							},
						},
						'.chart_section': {
							display: 'flex',
							flexDirection: 'row',
							flexWrap: 'wrap',
							justifyContent: 'space-evenly',

							'.chart_text': {
								color: theme.palette.neutral[300],
								...theme.typography.caption,
								fontWeight: theme.typography.h5.fontWeight,
							},
						},
					},
				},
			},
		},
		'.grid-header-component': {
			width: '100%',
			flexDirection: 'row',
			alignItems: 'flex-start',
			'.search-textFiled': {
				minWidth: 334,
				width: '100%',
				'.MuiInputBase-root': {
					height: theme.spacing(4.5),
					borderRadius: theme.spacing(1.25),
				},
			},
			'.form-controller': {
				margin: theme.spacing(0.125),
				minWidth: theme.spacing(12),
				width: '100%',
				'.MuiOutlinedInput-notchedOutline': {
					borderRadius: theme.spacing(1.25),
				},
			},
		},
		'.tab-container': {
			padding: theme.spacing(0, 3),
			borderBottom: `${theme.spacing(0.125)} solid ${
				theme.palette.neutral['100']
			}`,
			'.button-container': {
				flexDirection: 'row',
				alignItems: 'center',
				background: theme.palette.neutral['100'],
				padding: theme.spacing(0.5),
				borderRadius: theme.spacing(0.75),
				'.tab-button': {
					background: 'transparent',
					color: theme.palette.common.black,
					textTransform: 'none',
					height: 32,
					width: 40,
					fontWeight: 400,
					borderRadius: theme.spacing(0.75),
				},
				'.selected-button': {
					background: theme.palette.common.white,
				},
				'.button-icon': {
					width: theme.spacing(3),
					height: theme.spacing(3),
					color: theme.palette.neutral[300],
				},
			},
		},
		'.panel_container': {
			backgroundColor: theme.palette.common.white,
			'.panel': {
				padding: theme.spacing(4, 3),
				'.search-textFiled': {
					minWidth: 260,
					maxWidth: 260,
					width: '100%',
					'.MuiInputBase-root': {
						height: theme.spacing(4.5),
						borderRadius: theme.spacing(1.25),
					},
				},
			},
		},
	},

	'.chart-container': {
		flexDirection: 'row',
		width: '100%',
		gap: theme.spacing(2),
		justifyContent: 'center',

		'.card': {
			padding: theme.spacing(2),
			width: '100%',
			maxWidth: 354,
			height: 255,
			border: '1px solid',
			borderColor: theme.palette.neutral[100],
			borderRadius: theme.spacing(2),
			'.card-container': {
				height: '100%',
				alignItems: 'center',
				'.chart': {
					position: 'relative',
					width: '70%',
					height: '100%',
					justifyContent: 'center',
					marginLeft: theme.spacing(-3),
					'.chart-data': {
						position: 'absolute',
						height: 48,
						width: 48,
					},
				},
				'.circle-icon': {
					width: 8,
					height: 8,
				},
			},
		},
	},
	'.edit_btn': {
		textTransform: 'none',
		borderColor: theme.palette.neutral[300],
		color: theme.palette.neutral[300],
		borderRadius: 8,
	},

	'.customTag': {
		padding: theme.spacing(1, 0),
		alignItems: 'start',
		'.pointer': {
			cursor: 'pointer',
		},
	},
	'.apex-tooltip': {
		padding: theme.spacing(1),
		display: 'flex',
		flexDirection: 'column',
		p: {
			margin: 0,
		},
	},
	'.batch-button': {
		color: theme.palette.neutral[300],
	},
}))
