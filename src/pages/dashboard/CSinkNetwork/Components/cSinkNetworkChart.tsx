import { CustomCard } from '@/components'
import { TabContext, TabList } from '@mui/lab'
import { Stack, Tab, Typography, alpha, styled, useTheme } from '@mui/material'
import { GridArrowUpwardIcon } from '@mui/x-data-grid'
import { ApexOptions } from 'apexcharts'
import { FC, useCallback, useMemo } from 'react'
import ReactApexChart from 'react-apexcharts'
import { useSearchParams } from 'react-router-dom'

interface IProps {}

export const CSinkNetworkCharts: FC<IProps> = () => {
	const [searchParams, setSearchParams] = useSearchParams()
	const theme = useTheme()
	const graphTab = searchParams.get('graphTab') || 'biomass'

	const ActivityGraphTabArray = useMemo(
		() => [
			{
				label: 'Biomass',
				value: 'biomass',
				incrementCount: '120',
				incrementPercentage: '12',
			},
			{
				label: 'Application',
				value: 'application',
				incrementCount: '120',
				incrementPercentage: '12',
			},
			{
				label: 'Inventory',
				value: 'inventory',
				incrementCount: '130',
				incrementPercentage: '13',
			},
		],
		[]
	)

	const StatusChart = useMemo(
		() => ({
			series: [45, 32, 25, 11, 98],
			options: {
				labels: [
					'Category 1 : 45',
					'Category 2 : 32',
					'Category 3 : 25',
					'Category 4 : 11',
					'Other : 98',
				],
				legend: {
					show: true,
					markers: {
						fillColors: [
							theme.palette.primary.main,
							theme.palette.success.main,
							theme.palette.warning.main,
							theme.palette.info.main,
							alpha(theme.palette.primary.main, 0.1),
						],
					},
				},
				dataLabels: {
					enabled: false,
				},
				tooltip: { enabled: false },
				responsive: [
					{
						breakpoint: 680,
						options: {
							chart: {
								width: 280,
							},
						},
					},
					{
						breakpoint: 1440,
						options: {
							chart: {
								width: 300,
							},
						},
					},
				],
				chart: {
					type: 'donut',
					width: 100,
				},
				plotOptions: {
					pie: {
						donut: {
							size: '70%',
						},
					},
				},
				fill: {
					colors: [
						theme.palette.primary.main,
						theme.palette.success.main,
						theme.palette.warning.main,
						theme.palette.info.main,
						alpha(theme.palette.primary.main, 0.1),
					],
				},
				states: {
					hover: { filter: { type: 'darken', value: 0.7 } },
					active: { filter: { type: 'none', value: 0 } },
				},
				stroke: { width: 0 },
			},
		}),
		[theme.palette]
	)

	const opt = useMemo(
		() => ({
			chart: {
				type: 'bar',
				height: 200,
				stacked: true,
				toolbar: {
					show: false,
				},
				zoom: {
					enabled: true,
				},
			},
			responsive: [
				{
					breakpoint: 480,
					options: {
						legend: {
							position: 'bottom',
							offsetX: -10,
							offsetY: 0,
						},
					},
				},
			],
			plotOptions: {
				bar: {
					horizontal: false,
					barHeight: '100%',
					dataLabels: {
						total: {
							enabled: false,
						},
					},
				},
			},
			xaxis: {
				type: 'datetime',
				categories: [
					'01/01/2011 GMT',
					'01/02/2011 GMT',
					'01/03/2011 GMT',
					'01/04/2011 GMT',
					'01/05/2011 GMT',
					'01/06/2011 GMT',
					'01/07/2011 GMT',
					'01/08/2011 GMT',
					'01/09/2011 GMT',
					'01/10/2011 GMT',
					'01/11/2011 GMT',
				],
				axisBorder: {
					show: false,
				},
				axisTicks: {
					show: false,
				},
				labels: {
					show: false,
				},
			},
			legend: {
				position: 'bottom',
				horizontalAlign: 'left',
				offsetY: 10,
				markers: {
					radius: 12,
				},
			},
			fill: {
				opacity: 1,
			},
			grid: { show: false },
			dataLabels: {
				enabled: false,
			},
		}),
		[]
	)

	const options = useMemo(
		() => ({
			series: [
				{
					data: [
						10000, 2000, 5000, 6000, 4400, 5000, 1100, 5263, 1236, 5222, 1000,
						8596, 8000, 1452, 1236, 4452, 1255, 1111, 5263, 8025,
					],
					color: theme.palette.primary.light,
				},
			],
		}),
		[theme.palette.primary.light]
	)

	const handleGraphTabChange = useCallback(
		(_: unknown, newValue: string) => {
			setSearchParams((searchParams) => {
				searchParams.set('graphTab', newValue)
				return searchParams
			})
		},
		[setSearchParams]
	)

	return (
		<StyledChartsContainer>
			<CustomCard
				className='crop-details-card'
				headerComponent={
					<Stack className='activity-graph'>
						<TabContext value={graphTab}>
							<TabList
								onChange={handleGraphTabChange}
								TabIndicatorProps={{
									sx: {
										top: 7,
									},
								}}>
								{ActivityGraphTabArray.map(
									(
										{ label, value, incrementCount, incrementPercentage },
										index: number
									) => (
										<Tab
											key={index}
											label={
												<Stack
													sx={{
														position: 'relative',
														padding:
															graphTab === value
																? theme.spacing(1.7, 4, 0, 0)
																: theme.spacing(0),
													}}>
													<Typography
														textTransform='none'
														variant={graphTab === value ? 'body2' : 'overline'}>
														{label}
													</Typography>
													<Typography
														variant={graphTab === value ? 'h3' : 'body1'}>
														{incrementCount}
													</Typography>
													{graphTab === value && (
														<Stack
															direction='row'
															sx={{
																position: 'absolute',
																bottom: 0,
																right: '10%',
															}}>
															<GridArrowUpwardIcon
																color='success'
																sx={{
																	fontSize: theme.typography.caption,
																}}
															/>
															<Typography
																variant='caption'
																color='success.main'>
																{incrementPercentage}%
															</Typography>
														</Stack>
													)}
												</Stack>
											}
											value={value}
										/>
									)
								)}
							</TabList>
						</TabContext>
						<Stack className='bar-chart'>
							<ReactApexChart
								options={opt as ApexOptions}
								series={options.series}
								type='bar'
							/>
						</Stack>
					</Stack>
				}
			/>

			<CustomCard
				className='crop-details-card'
				headerComponent={
					<Stack gap={3}>
						<Typography variant='body2'>Credits</Typography>
						<Stack className='chart'>
							<ReactApexChart
								options={StatusChart.options as ApexOptions}
								series={StatusChart.series}
								type='donut'
							/>
						</Stack>
					</Stack>
				}
			/>
			<CustomCard
				className='crop-details-card'
				headerComponent={
					<Stack gap={3}>
						<Typography variant='body2'>Supply</Typography>
						<Stack className='chart'>
							<ReactApexChart
								options={StatusChart.options as ApexOptions}
								series={StatusChart.series}
								type='donut'
							/>
						</Stack>
					</Stack>
				}
			/>
		</StyledChartsContainer>
	)
}

const StyledChartsContainer = styled(Stack)(({ theme }) => ({
	padding: theme.spacing(2, 3, 4),
	flexDirection: 'row',
	gap: theme.spacing(1),
	flexWrap: 'wrap',
	background: theme.palette.neutral['50'],
	'.activity-graph': {
		margin: theme.spacing(-2),
		padding: theme.spacing(0, 2),
		'.bar-chart': {
			width: 300,
			height: '100%',
			marginLeft: theme.spacing(-1.5),
		},
	},
	'.map-container': {
		height: 260,
		width: 364,
		background: theme.palette.neutral['200'],
		borderRadius: theme.spacing(2),
	},
	'.crop-details-card': {
		display: 'flex',
		width: 364,
		height: 260,
		borderRadius: theme.spacing(2),
		gap: theme.spacing(2),
		boxShadow: `0 0 ${theme.spacing(0.25)} 0 ${alpha(
			theme.palette.common.black,
			0.25
		)}`,
		padding: theme.spacing(2),
		'.chart': {
			width: 320,
			height: '100%',
		},
	},
}))
