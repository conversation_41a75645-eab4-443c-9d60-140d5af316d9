import { CustomHeader } from '@/components'
import { Box, Stack, styled, Typography } from '@mui/material'
// import { useCallback } from 'react'
import { useCSinkNetwork } from './useCSinkNetwork'
import { AllNetworkPanel } from './Components'
import { TabContext, TabPanel } from '@mui/lab'

enum tabEnum {
	all = 'all',
	credits = 'credits',
}

export const CSinkNetwork = () => {
	// const theme = useTheme()
	const { cSinkNetwork, isLoading, paramsTab, allBaList } = useCSinkNetwork()

	// const HeaderEndButtons = () => (
	// 	<Stack direction='row' spacing={2}>
	// 		<IconButton
	// 			onClick={() => console.log('hi')}
	// 			sx={{
	// 				border: `${theme.spacing(0.125)} solid ${
	// 					theme.palette.neutral['100']
	// 				}`,
	// 				borderRadius: theme.spacing(1.25),
	// 			}}>
	// 			<Box component='img' src={DownloadIcon} height={20} width={20} />
	// 		</IconButton>
	// 	</Stack>
	// )

	return (
		<StyledContained>
			<Box className='header'>
				<CustomHeader
					showBottomBorder={true}
					heading='C-Sink Networks'
					// endComponent={<HeaderEndButtons />}
				/>
			</Box>
			<TabContext value={paramsTab}>
				<TabPanel value={tabEnum.all} className='tab-panel'>
					<AllNetworkPanel
						cSinkNetwork={cSinkNetwork}
						isLoading={isLoading}
						allBaList={allBaList}
					/>
				</TabPanel>
				<TabPanel value={tabEnum.credits} className='tab-panel'>
					<Stack height={100} alignItems='center' justifyContent='center'>
						<Typography variant='h6'>Not Available</Typography>
					</Stack>
				</TabPanel>
			</TabContext>
		</StyledContained>
	)
}

const StyledContained = styled(Stack)(({ theme }) => ({
	'.header': {
		padding: theme.spacing(4, 0),
		borderBottom: `${theme.spacing(0.125)} solid ${
			theme.palette.neutral['100']
		}`,
	},
	'.tabList': {
		borderBottom: `${theme.spacing(0.125)} solid ${
			theme.palette.neutral['100']
		}`,
		padding: theme.spacing(0, 3),
		background: theme.palette.neutral['50'],
	},
	'.tab-panel': {
		padding: theme.spacing(0, 3),
		'.container': {
			padding: theme.spacing(2, 0),
			'.grid-header-component': {
				flexDirection: 'row',
				alignItems: 'center',
				'.search-textFiled': {
					minWidth: 334,
					width: '100%',
					'.MuiInputBase-root': {
						height: theme.spacing(4.5),
						borderRadius: theme.spacing(1.25),
					},
				},
				'.form-controller': {
					margin: theme.spacing(0.125),
					minWidth: theme.spacing(18),
					width: '100%',
					'.MuiOutlinedInput-notchedOutline': {
						borderRadius: theme.spacing(1.25),
					},
				},
			},
		},
		'.header-filter-search': {
			flexDirection: 'row',
			alignItems: 'center',
			'.search-textFiled': {
				minWidth: 260,
				maxWidth: 260,
				width: '100%',
				'.MuiInputBase-root': {
					height: theme.spacing(4.5),
					borderRadius: theme.spacing(1.25),
				},
			},
			'.form-controller': {
				margin: theme.spacing(0.125),
				minWidth: theme.spacing(12.5),
				width: '100%',
			},
		},
	},
}))
