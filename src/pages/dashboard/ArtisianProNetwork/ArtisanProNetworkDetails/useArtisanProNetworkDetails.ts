import { authAxios } from '@/contexts'
import {  IArtisanProNetworkList } from '@/interfaces'
import { defaultLimit, defaultPage } from '@/utils/constant'
import { useQuery } from '@tanstack/react-query'
import { useParams, useSearchParams } from 'react-router-dom'

export const useArtisanProNetworkDetails = () => {
	const [searchParams, setSearchParams] = useSearchParams()
	const paramsLimit = searchParams.get('limit') || defaultLimit
	const paramsPage = searchParams.get('page') || defaultPage
	const { artisanProNetworkId } = useParams()

	const artisanProsDetails = useQuery({
		queryKey: ['artisanPros', artisanProNetworkId, paramsLimit, paramsPage],
		queryFn: () => {
			return authAxios.get<IArtisanProNetworkList>(`/artisian-pro/${artisanProNetworkId}`)
		},
		select: ({ data }) => data,
		enabled: !!artisanProNetworkId,
	})

	return {
		artisanProsData: artisanProsDetails?.data,
		isLoading: artisanProsDetails.isLoading,
		setSearchParams,
	}
}
