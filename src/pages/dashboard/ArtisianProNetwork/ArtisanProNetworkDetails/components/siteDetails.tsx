import { Box, Button, Grid, Stack, Typography } from '@mui/material'
import { ArtisanProNetworkDetailsChart } from './artisanProNetwrokDetailsChart'
import { CustomTagComponent, TagComponentWithToolTip } from '@/components'
import { theme } from '@/lib/theme/theme'
import EditIcon from '@/assets/icons/editIcon.svg'
import { useMemo } from 'react'
import { authAxios } from '@/contexts'
import { useQuery } from '@tanstack/react-query'
import { useParams } from 'react-router-dom'
import { Details, ISiteList } from '@/interfaces'
import { MultipleAvatar } from '@/components/MultipleAvatar.tsx'

export const SiteDetails = ({
	siteId,
	setSiteChartDetails,
}: {
	siteId?: string
	setSiteChartDetails: React.Dispatch<React.SetStateAction<Details | undefined>>
}) => {
	const { artisanProNetworkId } = useParams()
	const ToolTipComponent: React.FC<{
		dataArr: { name: string; description?: string }[]
	}> = ({ dataArr }) => {
		return (
			<Stack rowGap={1}>
				{dataArr.map((data, index) => (
					<Stack key={`${data?.name}-${index}`}>
						<Typography>{data.name}</Typography>
						{data?.description ? (
							<Typography>({data?.description})</Typography>
						) : null}
					</Stack>
				))}
			</Stack>
		)
	}

	const siteDetail = useQuery({
		queryKey: ['sitedetail', siteId],
		queryFn: async () => {
			const { data } = await authAxios.get<ISiteList>(
				`/artisian-pro/${artisanProNetworkId}/site/${siteId}`
			)
			if (data?.details) {
				setSiteChartDetails(data.details)
			}
			return data
		},
		enabled: !!siteId,
	})

	const batchDetailsList = useMemo(
		() => [
			{
				label: 'Kiln',
				value: `${siteDetail?.data?.details?.kilns?.length ?? 0}`,
				tooltipComponent: (
					<ToolTipComponent
						dataArr={(siteDetail?.data?.details?.kilns ?? [])?.map((item) => ({
							name: item.name,
							description: item?.volume || '',
						}))}
					/>
				),
				showTooltip: !!siteDetail?.data?.details?.kilns?.length,
			},

			{
				label: 'Sampling Container',
				value: `${siteDetail?.data?.details?.samplingContainers.length ?? 0}`,
				tooltipComponent: (
					<ToolTipComponent
						dataArr={(siteDetail?.data?.details?.samplingContainers ?? [])?.map(
							(item) => ({
								name: item.name,
								description:
									item?.volume !== undefined ? String(item.volume) : undefined,
							})
						)}
					/>
				),
				showTooltip: !!siteDetail?.data?.details?.samplingContainers?.length,
			},
			{
				label: 'Measuring Container',
				value: `${siteDetail?.data?.details?.measuringContainers?.length ?? 0}`,
				tooltipComponent: (
					<ToolTipComponent
						dataArr={(
							siteDetail?.data?.details?.measuringContainers ?? []
						)?.map((item) => ({
							name: item.name,
							description:
								item?.volume !== undefined ? String(item.volume) : undefined,
						}))}
					/>
				),
				showTooltip: !!siteDetail?.data?.details?.measuringContainers?.length,
			},
			{
				label: 'Vehicles',
				value: `${siteDetail?.data?.details?.vehicles?.length ?? 0}`,
				tooltipComponent: (
					<ToolTipComponent
						dataArr={(siteDetail?.data?.details?.vehicles ?? [])?.map(
							(item) => ({
								name: item.name,
								description:
									item?.number !== undefined ? String(item.number) : undefined,
							})
						)}
					/>
				),
				showTooltip: !!siteDetail?.data?.details?.vehicles?.length,
			},
			{
				label: 'Biomass Source',
				value: `${siteDetail?.data?.details?.fpu.length ?? ''}`,
				tooltipComponent: (
					<ToolTipComponent
						dataArr={(siteDetail?.data?.details?.fpu ?? [])?.map((item) => ({
							name: item.name,
						}))}
					/>
				),
				showTooltip: !!siteDetail?.data?.details?.fpu?.length,
			},

			{
				label: 'Operators',
				// value: siteDetail?.data?.details?.vehicles.length || 0,
			},
		],
		[
			siteDetail?.data?.details?.fpu,
			siteDetail?.data?.details?.kilns,
			siteDetail?.data?.details?.measuringContainers,
			siteDetail?.data?.details?.samplingContainers,
			siteDetail?.data?.details?.vehicles,
		]
	)
	const siteDetailsCharts = useMemo(
		() => [
			{
				label: 'Total Biochar',
				value: (
					<ArtisanProNetworkDetailsChart
						data={{
							labels:
								siteDetail?.data?.details?.biochar.map(
									(biochar) => biochar.cropName
								) || [],
							datasets:
								siteDetail?.data?.details?.biochar.map(
									(biochar) => biochar.biocharProducedInTonne
								) || [],
						}}
					/>
				),
			},

			{
				label: 'Carbon Credits',
				value: (
					<ArtisanProNetworkDetailsChart
						data={{
							labels:
								siteDetail?.data?.details?.carbonCredits.map(
									(carbonCredits) => carbonCredits.cropName
								) || [],
							datasets:
								siteDetail?.data?.details?.carbonCredits.map(
									(carbonCredits) => carbonCredits.carbonCredits
								) || [],
						}}
					/>
				),
			},
		],
		[
			siteDetail?.data?.details?.biochar,
			siteDetail?.data?.details?.carbonCredits,
		]
	)

	return (
		<Stack display='flex' flexDirection='column' gap={2} sx={{ mt: 1 }}>
			<Stack className='icontainer'>
				<Stack className='detail-container'>
					<Stack
						display='flex'
						flexDirection='row'
						alignItems='center'
						justifyContent='space-between'
						sx={{ mt: 1, ml: 2, mr: 3 }}>
						<Typography variant='h5'>Site detail</Typography>
						<Button
							className='btn outlined'
							variant='outlined'
							startIcon={
								<Box
									component='img'
									src={EditIcon}
									alt='edit-icon'
									height={12}
									width={12}
								/>
							}>
							Edit
						</Button>
					</Stack>
					<Grid container>
						{batchDetailsList.map(
							({ label, value, tooltipComponent, showTooltip }, index) => (
								<Grid
									key={index}
									item
									xs={4}
									sm={2}
									md={2.4}
									className='desc-grid-item'>
									<Stack className='desc' spacing={1} sx={{ pr: 10 }}>
										{label !== 'Operators' ? (
											<TagComponentWithToolTip
												lighterHeading
												label={label}
												value={value}
												tooltipComponent={tooltipComponent}
												showTooltip={showTooltip}
											/>
										) : (
											<Stack alignItems='center' sx={{ pl: 5 }}>
												<CustomTagComponent
													label='Operators'
													value={
														<Box
															component={Stack}
															flexDirection='row'
															alignItems='center'
															gap={1}
															// onClick={() => setIsActionInfoDrawer(true)}
															sx={{
																cursor: 'pointer',
															}}>
															{siteDetail?.data?.operators?.length !== 0 ? (
																<Typography variant='body1'>
																	{`${siteDetail?.data?.operators?.length} ${
																		siteDetail?.data?.operators?.length === 1
																			? 'operator'
																			: 'operators'
																	}`}
																</Typography>
															) : (
																0
															)}
															<MultipleAvatar
																MaxAvatar={3}
																imageList={
																	siteDetail?.data?.operators?.map(
																		({ profileImageUrl }) => ({
																			...(profileImageUrl || {}),
																		})
																	) || []
																}
															/>
														</Box>
													}
													lighterHeading
												/>
											</Stack>
										)}
									</Stack>
								</Grid>
							)
						)}
					</Grid>
					<Stack
						display='flex'
						flexDirection='row'
						sx={{ ml: 2 }}
						gap={1}
						flexWrap='wrap'>
						{siteDetailsCharts.map(({ label, value }, index) => (
							<Stack
								key={index}
								display='flex'
								flexDirection='row'
								alignItems='center'
								sx={{ pl: 5, mb: 5 }}>
								<Typography
									variant='subtitle2'
									sx={{ color: theme.palette.neutral[300], pb: 4 }}>
									{label}
								</Typography>
								<Box>{value}</Box>
							</Stack>
						))}
					</Stack>
				</Stack>
			</Stack>
			<Typography variant='h5' sx={{ mt: 2, ml: 2 }}>
				Farmers
			</Typography>
			<Stack></Stack>
		</Stack>
	)
}
