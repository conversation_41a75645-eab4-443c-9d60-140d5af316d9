import { QueryInput } from '@/components/QueryInputs'
import { pageLimits } from '@/utils/constant'
import { FormatNumber } from '@/utils/helper'
import { MoreVert, Search } from '@mui/icons-material'
import {
	FormControl,
	IconButton,
	MenuItem,
	Select,
	SelectChangeEvent,
	Stack,
	Typography,
	styled,
	useTheme,
} from '@mui/material'
import { FC, useCallback } from 'react'
import { useNavigate, useSearchParams } from 'react-router-dom'
import { ArtisanProCarbonCreditChart } from './artisanProCarbonCreditCharts'
import { CustomDataGrid } from '@/components'
import { GridColDef, GridValidRowModel } from '@mui/x-data-grid'
import { artisanProDetailsCarbonCredit } from '@/pages/dashboard/dummyData'

const gridColumn: GridColDef<GridValidRowModel>[] = [
	{
		field: 'creditId',
		headerName: 'Credit ID',
		minWidth: 180,
		flex: 1,
	},
	{
		field: 'batchId',
		headerName: 'Batch ID',
		minWidth: 150,
		flex: 1,
	},
	{
		field: 'network',
		headerName: 'Network',
		minWidth: 150,
		flex: 1,
	},
	{
		field: 'aggregator',
		headerName: 'Aggregator',
		minWidth: 150,
		flex: 1,
	},
	{
		field: 'emission',
		headerName: 'Total emission',
		minWidth: 150,
		flex: 1,
	},
	{
		field: 'status',
		headerName: 'Status',
		minWidth: 150,
		flex: 1,
	},
	{
		field: 'artisanProCount',
		headerName: 'ArtisanPro Count',
		minWidth: 150,
		flex: 1,
	},
	{
		field: 'crop',
		headerName: 'crop',
		minWidth: 150,
		flex: 1,
	},
	{
		field: 'action',
		headerName: '',
		minWidth: 70,
		flex: 1,
		renderCell: () => {
			return (
				<IconButton>
					<MoreVert />
				</IconButton>
			)
		},
	},
]

interface IProps {}

export const CarbonCreditsPanel: FC<IProps> = () => {
	const theme = useTheme()
	const navigate = useNavigate()
	const [searchParams] = useSearchParams()
	const paramsLimit = searchParams.get('limit') ?? 10
	const paramsPage = searchParams.get('page') ?? 0

	const handleChangeRowsPerPage = useCallback(
		(event: SelectChangeEvent) => {
			const nsp = new URLSearchParams(window.location.search)
			nsp.set('limit', String(event.target.value || 10))
			nsp.set('page', String(0))

			navigate(`?${nsp.toString()}`, { replace: true })
		},
		[navigate]
	)

	return (
		<StyledContainer>
			<Stack className='header-filter-search' gap={2}>
				<QueryInput
					className='search-textFiled'
					queryKey='search'
					placeholder='Search'
					setPageOnSearch
					InputProps={{
						startAdornment: <Search fontSize='small' />,
					}}
				/>
				<Stack direction='row' spacing={2} alignItems='center'>
					<Typography variant='overline' textTransform='none'>
						Row per page:
					</Typography>
					<FormControl>
						<Select
							value={String(paramsLimit)}
							onChange={handleChangeRowsPerPage}
							sx={{
								width: theme.spacing(12.5),
							}}>
							{pageLimits.map((limit, index) => (
								<MenuItem key={index} value={limit}>
									{limit}
								</MenuItem>
							))}
						</Select>
					</FormControl>
					<Typography variant='overline' textTransform='none'>
						{Number(paramsPage) * Number(paramsLimit) + 1}-
						{(Number(paramsPage) + 1) * Number(paramsLimit)} of{' '}
						{FormatNumber(artisanProDetailsCarbonCredit.length ?? 0)}
					</Typography>
				</Stack>
			</Stack>
			<ArtisanProCarbonCreditChart />
			<CustomDataGrid
				showPaginationDetails={false}
				showPagination={true}
				rows={artisanProDetailsCarbonCredit ?? []}
				columns={gridColumn}
				rowCount={artisanProDetailsCarbonCredit.length ?? 0}
				loading={false}
			/>
		</StyledContainer>
	)
}

const StyledContainer = styled(Stack)(({ theme }) => ({
	gap: theme.spacing(2),
	'.header-filter-search': {
		flexDirection: 'row',
		alignItems: 'center',
		justifyContent: 'space-between',
		'.search-textFiled': {
			minWidth: 260,
			maxWidth: 260,
			width: '100%',
			'.MuiInputBase-root': {
				height: theme.spacing(4.5),
				borderRadius: theme.spacing(1.25),
			},
		},
		'.form-controller': {
			margin: theme.spacing(0.125),
			minWidth: theme.spacing(12.5),
			width: '100%',
		},
	},
}))
