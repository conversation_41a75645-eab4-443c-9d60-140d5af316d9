import { CustomCard } from '@/components'
import { Box, Stack, Typography, alpha, styled, useTheme } from '@mui/material'
import { ApexOptions } from 'apexcharts'
import { FC, useMemo } from 'react'
import ReactApexChart from 'react-apexcharts'

interface IProps {}

export const ArtisanProCarbonCreditChart: FC<IProps> = () => {
	const theme = useTheme()

	const StatusChart = useMemo(
		() => ({
			series: [45, 32, 25, 11, 98],
			options: {
				labels: [
					'Category 1 : 45',
					'Category 2 : 32',
					'Category 3 : 25',
					'Category 4 : 11',
					'Other : 98',
				],
				legend: {
					show: true,
					markers: {
						fillColors: [
							theme.palette.primary.main,
							theme.palette.success.main,
							theme.palette.warning.main,
							theme.palette.info.main,
							alpha(theme.palette.primary.main, 0.1),
						],
					},
				},
				dataLabels: {
					enabled: false,
				},
				tooltip: { enabled: false },
				responsive: [
					{
						breakpoint: 680,
						options: {
							chart: {
								width: 280,
							},
						},
					},
					{
						breakpoint: 1440,
						options: {
							chart: {
								width: 300,
							},
						},
					},
				],
				chart: {
					type: 'donut',
					width: 100,
				},
				plotOptions: {
					pie: {
						donut: {
							size: '70%',
						},
					},
				},
				fill: {
					colors: [
						theme.palette.primary.main,
						theme.palette.success.main,
						theme.palette.warning.main,
						theme.palette.info.main,
						alpha(theme.palette.primary.main, 0.1),
					],
				},
				states: {
					hover: { filter: { type: 'darken', value: 0.7 } },
					active: { filter: { type: 'none', value: 0 } },
				},
				stroke: { width: 0 },
			},
		}),
		[theme.palette]
	)

	return (
		<StyledChartsContainer>
			<Box className='map-container' />
			<CustomCard
				className='crop-details-card'
				headerComponent={
					<Stack gap={2}>
						<Typography variant='body2'>Credits</Typography>
						<Stack className='chart'>
							<ReactApexChart
								options={StatusChart.options as ApexOptions}
								series={StatusChart.series}
								type='donut'
							/>
						</Stack>
					</Stack>
				}
			/>
		</StyledChartsContainer>
	)
}

const StyledChartsContainer = styled(Stack)(({ theme }) => ({
	flexDirection: 'row',
	gap: theme.spacing(2),
	'.map-container': {
		height: 260,
		width: '100%',
		background: theme.palette.neutral['200'],
		borderRadius: theme.spacing(2),
	},
	'.crop-details-card': {
		display: 'flex',
		width: '100%',
		height: 260,
		borderRadius: theme.spacing(2),
		gap: theme.spacing(2),
		boxShadow: `0 0 ${theme.spacing(0.25)} 0 ${alpha(
			theme.palette.common.black,
			0.25
		)}`,
		padding: theme.spacing(2),
		'.chart': {
			width: 360,
			height: '100%',
			justifyContent: 'center',
		},
	},
}))
