import { chartColors } from '@/utils/chartColor.contants'
import { FiberManualRecord } from '@mui/icons-material'
import { Stack, styled, Tooltip, Typography } from '@mui/material'
import { ApexOptions } from 'apexcharts'
import { FC, useMemo } from 'react'
import ReactApexChart from 'react-apexcharts'

type ChartComponentProps = {
	data: {
		labels: string[]
		datasets: number[]
		extraDataSet?: number[]
		showUnit?: string
		showBiomassTooltip?: boolean
	}
	hidden?: boolean
}

export const ArtisanProNetworkDetailsChart: FC<ChartComponentProps> = ({
	data,
	hidden = false,
}) => {
	const chartData = useMemo(
		() => ({
			series: data?.datasets,
			options: {
				chart: {
					type: 'donut',
				},
				legend: {
					show: false,
					markers: {
						size: 6,
						strokeWidth: 1,
					},
				},
				labels: data.labels,
				dataLabels: { enabled: false },
				tooltip: {
					enabled: true,
					...(data?.showBiomassTooltip
						? {
								custom: (props: any) => {
									const { series, seriesIndex } = props
									return `<div class='apex-tooltip' >
									<p>${data?.labels?.[seriesIndex]}:</p>
							<p>Total Collected: ${series[seriesIndex]} ton</p>
							<p>Total Available: ${data?.extraDataSet?.[seriesIndex] || 0} ton</p>
						</div>`
								},
						  }
						: {}),
					...(data?.showUnit
						? {
								custom: (props: any) => {
									const { series, seriesIndex } = props
									return `<div class='apex-tooltip' >
										<p>${data?.labels?.[seriesIndex]}: ${series?.[seriesIndex]} ${data?.showUnit}</p>
								
							</div>`
								},
						  }
						: {}),
				},
				colors: chartColors,
				states: {
					hover: { filter: { type: 'darken', value: 0.9 } },
					active: { filter: { type: 'none', value: 0 } },
				},
				stroke: { width: 0 },
				plotOptions: {
					pie: {
						expandOnClick: false,
						donut: {
							labels: {
								show: false,
							},
							size: '75%',
						},
					},
				},
			},
		}),
		[
			data?.datasets,
			data?.extraDataSet,
			data.labels,
			data?.showBiomassTooltip,
			data?.showUnit,
		]
	)

	return (data?.datasets || [])?.length > 0 ? (
		<StyledStack direction='row' columnGap={2}>
			{!hidden && (
				<ReactApexChart
					type='donut'
					series={data.datasets}
					options={chartData.options as ApexOptions}
					height={120}
					width={100}
				/>
			)}
			<Stack mt={2} rowGap={0.2} className='label-container'>
				{data?.labels?.map((label, index) => (
					<Stack key={label} direction='row' alignItems='center' columnGap={1}>
						<FiberManualRecord
							sx={{ fontSize: 10, color: chartColors[index] }}
						/>
						{label.length > 20 ? (
							<Tooltip
								placement='right-end'
								title={<Typography variant='caption'>{label}</Typography>}
								arrow>
								<Typography variant='caption'>
									{`${label.slice(0, 20)}...`}
								</Typography>
							</Tooltip>
						) : (
							<Typography variant='caption'>{label}</Typography>
						)}
						{hidden ? (
							<Typography variant='caption'>
								({data?.datasets?.[index]}
								{data?.showBiomassTooltip ? 'ton' : data?.showUnit || ''})
							</Typography>
						) : null}
					</Stack>
				))}
			</Stack>
		</StyledStack>
	) : null
}

const StyledStack = styled(Stack)(({ theme }) => ({
	'.label-container': {
		height: theme.spacing(10),
		overflow: 'auto',
		paddingRight: theme.spacing(1.5),
		'&::-webkit-scrollbar': {
			width: theme.spacing(0.5),
		},
		'&::-webkit-scrollbar-track': {
			background: theme.palette.neutral[100],
			width: theme.spacing(4.3),
			borderRadius: theme.spacing(2.5),
		},
		'&::-webkit-scrollbar-thumb': {
			backgroundColor: theme.palette.common.black,
			borderRadius: theme.spacing(2.5),
		},
	},
}))
