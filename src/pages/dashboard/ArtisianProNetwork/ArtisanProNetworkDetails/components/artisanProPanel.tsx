import LocationOnOutlinedIcon from '@mui/icons-material/LocationOnOutlined'
import { SiteDetails } from './siteDetails'
import { alpha, Button, Stack, styled, Typography } from '@mui/material'

import { useState } from 'react'

import { useQuery } from '@tanstack/react-query'
import { useParams } from 'react-router-dom'
import { authAxios } from '@/contexts'
import { theme } from '@/lib/theme/theme'
import { Details } from '@/interfaces'

export const ArtisanProNetworkDetailsPanel = ({setSiteChartDetails}:{setSiteChartDetails:React.Dispatch<React.SetStateAction<Details | undefined>>}) => {
	const { artisanProNetworkId } = useParams()
	const [siteId, setSiteId] = useState<string>('');

	const siteLists = useQuery({
		queryKey: ['sitelists', artisanProNetworkId],
		queryFn: async () => {
			const { data } = await authAxios.get(
				`/artisian-pro/${artisanProNetworkId}/site?sendAllSites=true`
			)
			if (data?.siteList?.length > 0) {
				setSiteId(data?.siteList[0]?.id)
			}

			return data
		},
		enabled: !!artisanProNetworkId,
	})

	return (
		<>
			{siteLists?.data?.siteList?.length > 0 ? (
				<StyledStack display='row' flexDirection='row' gap={5}>
					<Stack className='container'>
						{siteLists?.data?.siteList.map(
							(
								data: { name: string; address: string; id: string },
								index: number
							) => (
								<Button
									key={index}
									className='site_container'
									variant='text'
									onClick={() => {
										setSiteId(data?.id ?? '')
									}}
									sx={{
										alignItems: 'flex-start',
										color: 'black',
										backgroundColor: siteId === data.id ? `${theme.palette.error.light}` : 'transparent',
									}}>
									<Typography>{data?.name}</Typography>
									<Stack display='flex' flexDirection='row' alignItems='center'>
										<LocationOnOutlinedIcon
											className='icon'
											sx={{ height: '76%', ml: -1 }}
											
										/>
										<Typography>{data?.address}</Typography>
									</Stack>
								</Button>
							)
						)}
					</Stack>

					<SiteDetails siteId={siteId} setSiteChartDetails={setSiteChartDetails}/>
				</StyledStack>
			) : (
				'No Sites'
			)}
		</>
	)
}

const StyledStack = styled(Stack)(({ theme }) => ({
	'.icontainer': {
		padding: theme.spacing(0, 1.2),
		'.detail-container': {
			[theme.breakpoints.down('md')]: {
				flexWrap: 'wrap',
				rowGap: theme.spacing(1),
			},
			width: '100%',
			height: '100%',

			borderRadius: theme.spacing(2),
			boxShadow: `0 0 ${theme.spacing(0.35)} 0 ${alpha(
				theme.palette.common.black,
				0.25
			)}`,
			'.desc-grid-item': {
				[theme.breakpoints.down('md')]: {
					flexWrap: 'wrap',
					rowGap: theme.spacing(1),
				},
				padding: theme.spacing(3),
				'.desc': {
					[theme.breakpoints.down('md')]: {
						flexWrap: 'wrap',
						rowGap: theme.spacing(1),
					},
					'.title': {
						color: theme.palette.neutral['300'],
					},
				},
			},
		},
	},
	'.container': {
		display: 'flex',
		flexDirection: 'column',
		padding: theme.spacing(1),
		gap: theme.spacing(1),
		' .site_container': {
			borderRadius: theme.spacing(1.5),
			width: 260,
			gap: theme.spacing(0.5),
			padding: theme.spacing(1.5, 0, 1.5, 2),
			display: 'flex',
			flexDirection: 'column',
			boxShadow: `0 0 ${theme.spacing(0.35)} 0 ${alpha(
				theme.palette.common.black,
				0.25
			)}`,
			'.icon': {
				color: theme.palette.primary.main,
			},
		},
	},
}))
