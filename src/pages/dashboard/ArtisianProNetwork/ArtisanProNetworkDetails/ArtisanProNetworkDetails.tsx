import { ArrowLeftRounded } from '@mui/icons-material'
import EditIcon from '@/assets/icons/editIcon.svg'
import {
	alpha,
	Box,
	Button,
	IconButton,
	Stack,
	styled,
	Tab,
	Typography,
	useTheme,
} from '@mui/material'
import Divider from '@mui/material/Divider'

import { useCallback, useMemo, useState } from 'react'
import { useNavigate, useSearchParams } from 'react-router-dom'
import { useArtisanProNetworkDetails } from './useArtisanProNetworkDetails'
import { TabContext, TabList, TabPanel } from '@mui/lab'
import DownloadIcon from '@/assets/icons/download.svg'
import {
	ArtisanProNetworkDetailsChart,
	ArtisanProNetworkDetailsPanel,
} from './components'
import {
	CustomHeader,
	CustomTagComponent,
	TagComponentWithToolTip,
} from '@/components'
import { MultipleAvatar } from '@/components/MultipleAvatar.tsx'
import { Details, ImageURL } from '@/interfaces'

export function ArtisanProNetworkDetails() {
	const theme = useTheme()
	const navigate = useNavigate()
	const [searchParams, setSearchParams] = useSearchParams()
	const tabValue = searchParams.get('tabValue') ?? 'artisanPro'
	const [chartState, setChartState] = useState<string>('AP')
	const [siteChartDetails, setSiteChartDetails] = useState<Details>()
	const { artisanProsData } = useArtisanProNetworkDetails()

	const handleTabChange = useCallback(
		(_: unknown, newValue: string) => {
			setSearchParams(
				(prev) => {
					prev.set('tabValue', newValue)
					return prev
				},
				{ replace: true }
			)
		},
		[setSearchParams]
	)
	const getCount = (length: number) => {
		if (length > 3) {
			return `+ ${length - 3}`
		}
		return ''
	}
	const MultipleAvatarWrapper: React.FC<{
		images: ImageURL[]
		length: number
	}> = ({ images, length }) => {
		return (
			<Stack direction='row' alignItems='center' columnGap={1}>
				<MultipleAvatar size={30} imageList={images} MaxAvatar={3} />
				<Typography variant='subtitle1'>{getCount(length)}</Typography>
			</Stack>
		)
	}

	const ToolTipComponent: React.FC<{
		dataArr: { name: string; description?: string }[]
	}> = ({ dataArr }) => {
		return (
			<Stack rowGap={2} padding={1}>
				{dataArr.map((data, index) => (
					<Stack key={`${data?.name}-${index}`}>
						<Typography>{data.name}</Typography>
						{data?.description ? (
							<Typography>({data?.description})</Typography>
						) : null}
					</Stack>
				))}
			</Stack>
		)
	}
	const batchDetailsList = useMemo(
		() => [
			{
				label: 'Packaging Bags',
				value: artisanProsData?.details?.bags?.length ? (
					<MultipleAvatarWrapper
						images={
							artisanProsData?.details?.bags?.map(
								(bags) => bags?.imageURLs?.[0]
							) || []
						}
						length={artisanProsData?.details?.bags?.length}
					/>
				) : (
					0
				),
				tooltipComponent: (
					<ToolTipComponent
						dataArr={(artisanProsData?.details?.bags ?? [])?.map((item) => ({
							name: item.name,
							description:
								item?.quantity !== undefined
									? String(item.quantity)
									: undefined,
						}))}
					/>
				),
				showTooltip: !!artisanProsData?.details?.bags?.length,
			},

			{
				label: 'Preferred Biomass',
				value: artisanProsData?.details?.preferredCrops.length ? (
					<MultipleAvatarWrapper
						images={
							artisanProsData?.details?.preferredCrops?.map(
								(biomass) => biomass?.image
							) || []
						}
						length={artisanProsData?.details?.bags?.length}
					/>
				) : (
					0
				),
				tooltipComponent: (
					<ToolTipComponent
						dataArr={(artisanProsData?.details?.preferredCrops ?? [])?.map(
							(item) => ({
								name: item.name,
							})
						)}
					/>
				),
				showTooltip: !!artisanProsData?.details?.preferredCrops?.length,
			},

			{
				label: 'Methane Compensation Strategy',
				value: `${artisanProsData?.methaneCompensationStrategy ?? ''}`,
				tooltipComponent: (
					<ToolTipComponent
						dataArr={[
							{
								name: artisanProsData?.methaneCompensationStrategy || '',
							},
						]}
					/>
				),
				showTooltip: !!artisanProsData?.methaneCompensationStrategy?.length,
			},
		],
		[
			MultipleAvatarWrapper,
			artisanProsData?.details?.bags,
			artisanProsData?.details?.preferredCrops,
			artisanProsData?.methaneCompensationStrategy,
		]
	)
	const batchDetailsListChart = useMemo(
		() => [
			{
				label: 'Biomass',
				value: (
					<ArtisanProNetworkDetailsChart
						data={
							chartState === 'AP'
								? {
										labels:
											artisanProsData?.details?.biomass?.map(
												(biomass) => biomass.cropName
											) || [],
										datasets:
											artisanProsData?.details?.biomass.map(
												(biomass) => biomass.biomassProduced
											) || [],
										extraDataSet: artisanProsData?.details?.biomass?.map(
											(item) => item.biomassAvailable
										),
										showBiomassTooltip: true,
								  }
								: {
										labels:
											siteChartDetails?.biomass?.map(
												(biomass) => biomass.cropName
											) || [],
										datasets:
											siteChartDetails?.biomass.map(
												(biomass) => biomass.biomassProduced
											) || [],
										extraDataSet: siteChartDetails?.biomass?.map(
											(item) => item.biomassAvailable
										),
										showBiomassTooltip: true,
								  }
						}
					/>
				),
			},
			{
				label: 'Biochar',
				value: (
					<ArtisanProNetworkDetailsChart
						data={
							chartState === 'AP'
								? {
										labels:
											artisanProsData?.details?.biochar.map(
												(biochar) => biochar.cropName
											) || [],
										datasets:
											artisanProsData?.details?.biochar.map(
												(biochar) => biochar.biocharProducedInTonne
											) || [],
								  }
								: {
										labels:
											siteChartDetails?.biochar.map(
												(biochar) => biochar.cropName
											) || [],
										datasets:
											siteChartDetails?.biochar.map(
												(biochar) => biochar.biocharProducedInTonne
											) || [],
								  }
						}
					/>
				),
			},
			{
				label: 'Carbon Credits',
				value: (
					<ArtisanProNetworkDetailsChart
						data={
							chartState !== 'AP'
								? {
										labels:
											artisanProsData?.details?.carbonCredits.map(
												(carbonCredits) => carbonCredits.cropName
											) || [],
										datasets:
											artisanProsData?.details?.carbonCredits.map(
												(carbonCredits) => carbonCredits.carbonCredits
											) || [],
								  }
								: {
										labels:
											siteChartDetails?.carbonCredits.map(
												(carbonCredits) => carbonCredits.cropName
											) || [],
										datasets:
											siteChartDetails?.carbonCredits.map(
												(carbonCredits) => carbonCredits.carbonCredits
											) || [],
								  }
						}
					/>
				),
			},
		],
		[
			artisanProsData?.details?.biochar,
			artisanProsData?.details?.biomass,
			artisanProsData?.details?.carbonCredits,
			chartState,
			siteChartDetails?.biochar,
			siteChartDetails?.biomass,
			siteChartDetails?.carbonCredits,
		]
	)
	return (
		<StyledContained>
			<Box className='header'>
				<CustomHeader
					headingComponent={
						<Stack direction='row' alignItems='center'>
							<Button
								variant='text'
								startIcon={<ArrowLeftRounded />}
								className='back-button'
								onClick={() =>
									navigate('/dashboard/artisan-pro-network', {
										replace: true,
									})
								}>
								Artisan Pro Networks
							</Button>
							<Typography variant='h6' color={theme.palette.neutral['500']}>
								&nbsp;/ {artisanProsData?.artisianProNetworkName} &nbsp;/
								{artisanProsData?.shortCode}
							</Typography>
						</Stack>
					}
					endComponent={
						<>
							<Button
								className='btn outlined'
								variant='outlined'
								size='small'
								startIcon={
									<Box
										component='img'
										src={EditIcon}
										alt='edit-icon'
										height={12}
										width={12}
									/>
								}>
								Edit Network
							</Button>
							<IconButton
								onClick={() => console.log('hi')}
								sx={{
									border: `${theme.spacing(0.125)} solid ${
										theme.palette.neutral['100']
									}`,
									borderRadius: theme.spacing(1.25),
								}}>
								<Box
									component='img'
									src={DownloadIcon}
									height={20}
									width={20}
								/>
							</IconButton>
						</>
					}
				/>
			</Box>

			<Stack className='hero-section'>
				<Stack className='network-details-container' gap={1}>
					<Stack
						display='flex'
						flexDirection='column'
						gap={2}
						sx={{ mt: 2, ml: 1 }}>
						<Stack sx={{ minWidth: '160px' }}>
							<Typography variant='body2' textTransform='capitalize'>
								{artisanProsData?.name ?? ''}
							</Typography>
							<Typography
								variant='overline'
								textTransform='none'>{`Artisan pro ID: ${
								artisanProsData?.shortCode ?? ''
							}`}</Typography>
						</Stack>

						<Stack>
							<CustomTagComponent
								label={'Bigha to hec'}
								lighterHeading
								value={`1 bigha = ${artisanProsData?.bighaInHectare} hec`}
							/>
						</Stack>
						<Stack>
							<CustomTagComponent
								label='Admin'
								value={
									<Box
										component={Stack}
										flexDirection='row'
										alignItems='center'
										gap={1}
										sx={{
											cursor: 'pointer',
										}}>
										<Typography variant='body1'>
											{artisanProsData?.managerDetails?.length === 1 ||
											artisanProsData?.managerDetails?.length === 0
												? `${artisanProsData?.managerDetails?.length} admin`
												: `${artisanProsData?.managerDetails?.length} admins`}
										</Typography>
										<MultipleAvatar
											MaxAvatar={3}
											imageList={artisanProsData?.managerDetails.map(
												({ profileImageUrl }) => ({
													...(profileImageUrl || {}),
												})
											)}
										/>
									</Box>
								}
								lighterHeading
							/>
						</Stack>
					</Stack>
					<Stack className='container' flex={1}>
						<Stack className='detail-container'>
							<Typography variant='h5' sx={{ mt: 2 }}>
								Artisan pro detail
							</Typography>
							<Stack
								display='flex'
								flexDirection='row'
								justifyContent='space-between'>
								{batchDetailsList.map(
									({ label, value, tooltipComponent, showTooltip }, index) => (
										<Stack key={index} className='desc-grid-item'>
											<Stack className='desc'>
												<TagComponentWithToolTip
													lighterHeading
													label={label}
													value={value}
													tooltipComponent={tooltipComponent}
													showTooltip={showTooltip}
												/>
											</Stack>
										</Stack>
									)
								)}
								<Stack sx={{ padding: theme.spacing(1, 0.3) }}>
									<Box className='selectButton'>
										<Button
											sx={{
												color: `${theme.palette.neutral['500']}`,
												backgroundColor:
													chartState === 'AP'
														? theme.palette.action.selected
														: 'transparent',
											}}
											onClick={() => setChartState('AP')}>
											<Typography variant='subtitle1'>Artisan Pro</Typography>
										</Button>
										<Divider
											orientation='vertical'
											variant='middle'
											flexItem
											sx={{ borderColor: 'black', m: 1 }}
										/>
										<Button
											sx={{
												color: `${theme.palette.neutral['500']}`,
												backgroundColor:
													chartState === 'Sites'
														? theme.palette.action.selected
														: 'transparent',
											}}
											onClick={() => setChartState('Sites')}>
											<Typography variant='subtitle1'>Sites</Typography>
										</Button>
									</Box>
								</Stack>
							</Stack>
							<Stack
								display='flex'
								flexDirection='row'
								sx={{ ml: 1 }}
								gap={0}
								justifyContent='space-evenly'
								flexWrap='wrap'>
								{batchDetailsListChart.map(({ label, value }, index) => (
									<Stack
										key={index}
										display='flex'
										flexDirection='column'
										className=''
										alignItems='center'>
										<Typography
											variant='subtitle2'
											sx={{ color: theme.palette.neutral[300], p: 1 }}>
											{label}
										</Typography>
										<Box>{value}</Box>
									</Stack>
								))}
							</Stack>
						</Stack>
					</Stack>
				</Stack>
			</Stack>
			<Stack className='tab-container'>
				<TabContext value={tabValue}>
					<Stack className='tab-list-container'>
						<TabList onChange={handleTabChange}>
							<Tab label='All Sites' value='artisanPro' />
						</TabList>
					</Stack>
					<TabPanel className='tab-Panel-container' value='artisanPro'>
						<ArtisanProNetworkDetailsPanel
							setSiteChartDetails={setSiteChartDetails}
						/>
						
					</TabPanel>
				</TabContext>
			</Stack>
		</StyledContained>
	)
}

const StyledContained = styled(Stack)(({ theme }) => ({
	'.container': {
		padding: theme.spacing(0, 1.2),
		'.detail-container': {
			paddingLeft: theme.spacing(2.5),
			[theme.breakpoints.down('md')]: {
				flexWrap: 'wrap',
				rowGap: theme.spacing(1),
			},
			width: '100%',
			height: '100%',
			borderRadius: theme.spacing(2),
			boxShadow: `0 0 ${theme.spacing(0.35)} 0 ${alpha(
				theme.palette.common.black,
				0.25
			)}`,
			'.selectButton': {
				display: 'flex',
				margin: theme.spacing(1),
				borderRadius: theme.spacing(1),
				flexDirection: 'row',
				padding: theme.spacing(0.5, 1.5),
				background: theme.palette.grey['100'],
				boxShadow: `0 0 ${theme.spacing(0.55)} 0 ${alpha(
					theme.palette.common.black,
					0.25
				)}`,
			},
			'.apex-tooltip': {
				padding: theme.spacing(1),
				display: 'flex',
				flexDirection: 'column',
				p: {
					margin: 0,
				},
			},
			'.desc-grid-item': {
				'.desc': {
					'.title': {
						color: theme.palette.neutral['300'],
					},
				},
			},
		},
	},

	'.circle-icon': {
		fontSize: '8px',
		color: theme.palette.neutral['100'],
	},
	'.btn': {
		height: theme.spacing(4.5),
		textTransform: 'none',
		borderRadius: theme.spacing(0.75),
	},
	'.outlined': {
		borderColor: theme.palette.neutral['300'],
		color: theme.palette.neutral['300'],
	},
	'.header': {
		display: 'flex',
		alignItems: 'center',
		padding: theme.spacing(2.5, 0),
		borderBottom: `${theme.spacing(0.125)} solid ${
			theme.palette.neutral['100']
		}`,
		'.heading': {
			color: theme.palette.neutral[500],
		},
	},
	'.back-button': {
		padding: 0,
		...theme.typography.body1,
		color: theme.palette.neutral['300'],
	},
	'.hero-section': {
		padding: theme.spacing(2, 2, 0),
		background: theme.palette.neutral['50'],
		'.network-details-container': {
			flexDirection: 'row',
			alignItems: 'flex-start',
			gap: theme.spacing(15),
			border: `${theme.spacing(0.125)} solid ${theme.palette.neutral['100']}`,
			borderRadius: theme.spacing(2),
			background: theme.palette.common.white,
			padding: theme.spacing(2, 4),
		},
	},
	'.tab-container': {
		'.tab-list-container': {
			padding: theme.spacing(3, 3, 0),
			background: theme.palette.neutral['50'],
			borderBottom: `${theme.spacing(0.125)} solid ${
				theme.palette.neutral['100']
			}`,
		},
		'.tab-Panel-container': {
			padding: theme.spacing(2, 3),
			'.header-filter-search': {
				flexDirection: 'row',
				alignItems: 'center',
				'.search-textFiled': {
					minWidth: 260,
					maxWidth: 260,
					width: '100%',
					'.MuiInputBase-root': {
						height: theme.spacing(4.5),
						borderRadius: theme.spacing(1.25),
					},
				},
				'.form-controller': {
					margin: theme.spacing(0.125),
					minWidth: theme.spacing(12.5),
					width: '100%',
				},
			},
		},
	},
}))
