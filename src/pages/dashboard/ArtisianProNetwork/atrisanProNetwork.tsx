import { alpha, Box, Stack, styled } from '@mui/material'
import { useArtisanProNetwork } from './useArtisanProNetwork'
import { AllAPNetworkPanel } from './components'
import { TabContext, TabPanel } from '@mui/lab'
import { CustomHeader } from '@/components'

enum tabEnum {
	all = 'all',
	biomassAvailable = 'biomassAvailable',
}

export const ArtisanProNetwork = () => {
	// const theme = useTheme()
	const { artisanPro, isLoading, paramsTab, allBaList } = useArtisanProNetwork()

	// const HeaderEndButtons = () => (
	// 	<Stack direction='row' spacing={2}>
	// 		<Button variant='contained' size='small'>
	// 			Contact admin
	// 		</Button>
	// 		<IconButton
	// 			onClick={() => console.log('hi')}
	// 			sx={{
	// 				border: `${theme.spacing(0.125)} solid ${
	// 					theme.palette.neutral['100']
	// 				}`,
	// 				borderRadius: theme.spacing(1.25),
	// 			}}>
	// 			<Box component='img' src={DownloadIcon} height={20} width={20} />
	// 		</IconButton>
	// 	</Stack>
	// )

	return (
		<StyledContained>
			<Box className='header'>
				<CustomHeader
					showBottomBorder={true}
					heading='Artisan Pros'
					showButton={false}
					// endComponent={<HeaderEndButtons />}
				/>
			</Box>

			<TabContext value={paramsTab}>
				<TabPanel value={tabEnum.all} className='tab-panel'>
					<AllAPNetworkPanel
						artisanPro={artisanPro}
						isLoading={isLoading}
						allBaList={allBaList}
					/>
				</TabPanel>
				<TabPanel
					value={tabEnum.biomassAvailable}
					className='tab-panel'></TabPanel>
			</TabContext>
		</StyledContained>
	)
}

const StyledContained = styled(Stack)(({ theme }) => ({
	'.header': {
		padding: theme.spacing(4, 0),
		borderBottom: `${theme.spacing(0.125)} solid ${
			theme.palette.neutral['100']
		}`,
		'.chips-container': {
			padding: theme.spacing(3, 3, 1),
			'.chip': {
				borderRadius: theme.spacing(0.8),
				height: theme.spacing(4),
				minWidth: theme.spacing(15),
			},
			'.active-chip': {
				backgroundColor: theme.palette.custom.blue[200],
				color: theme.palette.custom.blue[500],
			},
			'.disabled-chip': {
				backgroundColor: theme.palette.common.white,
				border: `${theme.spacing(0.125)} dotted`,
				borderColor: theme.palette.neutral[300],
				color: theme.palette.neutral[300],
			},
		},
	},
	'.chip-icon': {
		fontSize: theme.spacing(2),
	},
	'.tabList': {
		borderBottom: `${theme.spacing(0.125)} solid ${
			theme.palette.neutral['100']
		}`,
		padding: theme.spacing(0, 3),
		background: theme.palette.neutral['50'],
	},
	'.tab-panel': {
		padding: theme.spacing(2, 3),
		'.container': {
			padding: theme.spacing(2, 0),
			'.grid-header-component': {
				flexDirection: 'row',
				alignItems: 'center',
				'.search-textFiled': {
					minWidth: 334,
					width: '100%',
					'.MuiInputBase-root': {
						height: theme.spacing(4.5),
						borderRadius: theme.spacing(1.25),
					},
				},
				'.form-controller': {
					margin: theme.spacing(0.125),
					minWidth: theme.spacing(18),
					width: '100%',
					'.MuiOutlinedInput-notchedOutline': {
						borderRadius: theme.spacing(1.25),
					},
				},
			},
		},
		'.header-filter-search': {
			flexDirection: 'row',
			alignItems: 'center',
			'.search-textFiled': {
				minWidth: 260,
				maxWidth: 260,
				width: '100%',
				'.MuiInputBase-root': {
					height: theme.spacing(4.5),
					borderRadius: theme.spacing(1.25),
				},
			},
			'.form-controller': {
				margin: theme.spacing(0.125),
				minWidth: theme.spacing(12.5),
				width: '100%',
			},
		},
	},
	'.border-card': {
		borderRadius: theme.spacing(2),
		boxShadow: `0 0 ${theme.spacing(0.25)} 0 ${alpha(
			theme.palette.common.black,
			0.25
		)}`,
	},
	'.approved-chip': {
		background: theme.palette.success.light,
		color: theme.palette.success.main,
	},
}))
