import { authAxios, useAuthContext } from '@/contexts'
import {
	IArtisanProNetworkDetails,
	// TArtisanProListResponse,
} from '@/interfaces'
import { defaultLimit, defaultPage, userRoles } from '@/utils/constant'
import { useQuery } from '@tanstack/react-query'
import { useSearchParams } from 'react-router-dom'

export const useArtisanProNetwork = () => {
	const [searchParams, setSearchParams] = useSearchParams()
	const paramsLimit = searchParams.get('limit') || defaultLimit.toString()
	const paramsPage = searchParams.get('page') || defaultPage.toString()
	const { userDetails } = useAuthContext()
	const paramsTab = searchParams.get('tab') || 'all'
	const paramsSearchName = searchParams.get('search') || ''
	const paramsBaId = searchParams.get('baId') || ''

	const AllBaQuery = useQuery({
		queryKey: ['allBA'],
		queryFn: () => {
			return authAxios.get<{
				baDetails: { id: string; name: string; shortCode: string }[]
			}>(`/drop-down/biomass-aggregators`)
		},
		select: ({ data }) =>
			data?.baDetails?.map((item) => ({
				label: `${item.name} (${item.shortCode})`,
				value: item.id,
			})),
		enabled:
			!!userDetails &&
			![
				userRoles.cSinkNetwork,
				userRoles.artisanProNetworkManager,
				userRoles.ArtisanPro,
			].includes(userDetails?.accountType as userRoles),
	})

	const artisanProData = useQuery({
		queryKey: [
			'allBatchesDetails',
			paramsLimit,
			paramsPage,
			paramsBaId,
			paramsSearchName,
		],
		queryFn: () => {
			const params = {
				limit: paramsLimit,
				page: paramsPage,
				search: paramsSearchName,
				biomassAggregatorId: paramsBaId,
			}

			const queryParams = new URLSearchParams()

			for (const [key, value] of Object.entries(params)) {
				if (value !== '') {
					queryParams.append(key, value)
				}
			}
			// return authAxios.get<{
			// 	count: number
			// 	artisanProNetworks: IArtisanProNetworkDetails[]
			// }>(`/new/artisanpro-networks?${queryParams}`)
			return authAxios.get<{
				count: number
				artisanPros: IArtisanProNetworkDetails[]
			}>(`/new/artisan-pros?${queryParams}`)
		},
		select: ({ data }) => data,
		enabled: true,
	})

	// const artisanProList = useQuery({
	// 	queryKey: ['artisanProList'],
	// 	queryFn: async () => {
	// 		const queryParams = new URLSearchParams({
	// 			limit: '7',
	// 			page: '0',
	// 		})
	// 		const { data } = await authAxios.get<TArtisanProListResponse>(
	// 			`/new/artisan-pro?${queryParams}`
	// 		)
	// 		return data
	// 	},
	// })

	return {
		artisanPro: artisanProData.data,
		isLoading: artisanProData.isLoading,
		allBaList: AllBaQuery.data,
		paramsTab,
		setSearchParams,
		// artisanProList,
	}
}
