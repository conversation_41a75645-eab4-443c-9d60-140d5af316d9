import { CustomDataGrid } from '@/components'
import { QueryAutoComplete, QueryInput } from '@/components/QueryInputs'
import { useAuthContext } from '@/contexts'
import { IArtisanProNetworkDetails } from '@/interfaces'
import { Done, MoreVert, Search } from '@mui/icons-material'
import { Chip, IconButton, Stack } from '@mui/material'
import { GridColDef, GridValidRowModel } from '@mui/x-data-grid'
import { FC, useMemo } from 'react'

const filterFieldForUser = ['biomass_aggregator', 'c_sink_manager']

interface IProps {
	artisanPro?: {
		count: number
		artisanPros: IArtisanProNetworkDetails[]
	}
	isLoading: boolean
	allBaList?: {
		label: string
		value: string
	}[]
}

export const ArtisanProBiomassAvailable: FC<IProps> = ({
	artisanPro,
	isLoading,
	allBaList,
}) => {
	const { userDetails } = useAuthContext()

	const filterInitialValue = useMemo(() => {
		if (filterFieldForUser.includes(userDetails?.accountType ?? '')) {
			return userDetails?.biomassAggregatorId
		}
		return ''
	}, [userDetails])

	const gridColumn: GridColDef<GridValidRowModel>[] = [
		{
			field: 'name',
			headerName: 'Network Name',
			minWidth: 100,
			flex: 1,
		},
		{
			field: 'biomassAggregatorName',
			headerName: 'BA Name',
			minWidth: 100,
			flex: 1,
		},
		{
			field: 'address',
			headerName: 'Location',
			minWidth: 120,
			flex: 1,
		},
		{
			field: 'status',
			headerName: 'Current Supply Status',
			minWidth: 180,
			flex: 1,
			renderCell: () => (
				<Chip
					className='approved-chip'
					icon={<Done className='chip-icon' color='success' />}
					label='In stock'
				/>
			),
		},
		{
			field: 'artisanProCount',
			headerName: 'ArtisanPro Count',
			minWidth: 100,
			flex: 1,
		},
		{
			field: 'totalBiomassDropped',
			headerName: 'Biomass Quantity',
			minWidth: 100,
			flex: 1,
			valueGetter: (params) =>
				`${
					params.row?.totalBiomassDropped === null
						? '-'
						: `${params.row?.totalBiomassDropped} tons`
				}`,
		},
		{
			field: 'totalBiocharProduced',
			headerName: 'Biochar Quantity',
			minWidth: 100,
			flex: 1,
			valueGetter: (params) =>
				`${
					params.row?.totalBiocharProduced == null
						? '-'
						: `${params.row?.totalBiocharProduced} tons`
				}`,
		},
		{
			field: 'carbonCredits',
			headerName: 'Carbon Credits',
			minWidth: 120,
			valueGetter: (params) => params.row.carbonCredits || '-',
		},
		{
			field: 'siteCount',
			headerName: 'Site Count',
			minWidth: 100,
		},
		{
			field: 'action',
			headerName: '',
			flex: 1,
			maxWidth: 80,
			renderCell: () => (
				<IconButton>
					<MoreVert />
				</IconButton>
			),
		},
	]

	return (
		<CustomDataGrid
			showPagination={true}
			rows={artisanPro?.artisanPros ?? []}
			columns={gridColumn}
			rowCount={artisanPro?.count ?? 0}
			headerComponent={
				<Stack className='header-filter-search' gap={2}>
					<QueryInput
						className='search-textFiled'
						queryKey='search'
						placeholder='Search'
						setPageOnSearch
						InputProps={{
							startAdornment: <Search fontSize='small' />,
						}}
					/>
					<QueryAutoComplete
						options={allBaList}
						queryKey={'baId'}
						label={'BA'}
						isDisable={filterFieldForUser.includes(
							userDetails?.accountType ?? ''
						)}
						initialValue={filterInitialValue ?? ''}
					/>
				</Stack>
			}
			loading={isLoading}
		/>
	)
}
