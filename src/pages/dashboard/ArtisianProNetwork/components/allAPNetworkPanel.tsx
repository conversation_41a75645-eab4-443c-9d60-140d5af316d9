import { CustomDataGrid } from '@/components'
import { QueryInput } from '@/components/QueryInputs'
import { IArtisanProNetworkDetails } from '@/interfaces'
import { Search } from '@mui/icons-material'
import { Stack, Tooltip, Typography, useTheme } from '@mui/material'
import {
	GridColDef,
	GridEventListener,
	GridValidRowModel,
} from '@mui/x-data-grid'
import { FC, useCallback } from 'react'
import { useNavigate } from 'react-router-dom'

interface IProps {
	artisanPro?: {
		count: number
		artisanPros: IArtisanProNetworkDetails[]
	}
	isLoading: boolean
	allBaList?: {
		label: string
		value: string
	}[]
}

export const AllAPNetworkPanel: FC<IProps> = ({ artisanPro, isLoading }) => {
	const navigate = useNavigate()
	const handleRowClick: GridEventListener<'rowClick'> = useCallback(
		(params) => {
			navigate(`${params?.row?.id}/details`)
		},
		[navigate]
	)

	// const filterInitialValue = useMemo(() => {
	// 	return userDetails?.biomassAggregatorId
	// }, [userDetails])
	const theme = useTheme()
	const gridColumn: GridColDef<GridValidRowModel>[] = [
		{
			field: 'name',
			headerName: 'Name',
			minWidth: 100,
			flex: 1,
		},
		// {
		// 	field: 'biomassAggregatorName',
		// 	headerName: 'BA Name',
		// 	minWidth: 100,
		// 	flex: 1,
		// },
		{
			field: 'address',
			headerName: 'Location',
			minWidth: 120,
			flex: 1,
		},
		{
			field: 'siteCount',
			headerName: 'Site Count',
			flex: 1,
			minWidth: 150,
			headerAlign: 'center',
			align: 'center',
		},
		// {
		// 	field: 'artisanProCount',
		// 	headerName: 'ArtisanPro Count',
		// 	minWidth: 100,
		// 	flex: 1,
		// },
		{
			field: 'cropBiomass',
			headerName: 'Biomass Quantity',
			minWidth: 200,
			flex: 1,
			renderCell: (params) => {
				const value = params?.row?.cropBiomass
					? params?.row?.cropBiomass
							.map(
								({
									cropName,
									biomassCollectedInTonnes,
								}: {
									cropName: string
									biomassCollectedInTonnes: number
								}) => {
									return `${cropName}: ${biomassCollectedInTonnes.toFixed(
										2
									)} ton`
								}
							)
							.join(',\n')
					: '-'
				return params?.row?.cropBiomass ? (
					<Tooltip
						title={
							<Typography
								variant='subtitle1'
								style={{
									whiteSpace: 'pre-wrap',
								}}>
								{value}
							</Typography>
						}
						placement='top'>
						<Typography
							variant='subtitle1'
							style={{
								display: 'inline-block',
								wordWrap: 'break-word',
								whiteSpace: 'pre-wrap',
								textOverflow:
									(params?.row?.comments?.length ?? 0) > 200
										? 'ellipsis'
										: 'clip',
							}}>
							{value}
						</Typography>
					</Tooltip>
				) : (
					<Typography>-</Typography>
				)
			},
		},
		{
			field: 'cropBiochar',
			headerName: 'Biochar',
			minWidth: 200,
			flex: 1,
			renderCell: (params) => {
				const value = params?.row?.cropBiochar
					? params?.row?.cropBiochar
							.map(
								({
									cropName,
									biocharProducedInTonne,
								}: {
									cropName: string
									biocharProducedInTonne: number
								}) => {
									return `${cropName}: ${biocharProducedInTonne} ton`
								}
							)
							.join(',\n')
					: '-'
				return params?.row?.cropBiochar ? (
					<Tooltip
						title={
							<Typography
								variant='subtitle1'
								style={{
									whiteSpace: 'pre-wrap',
								}}>
								{value}
							</Typography>
						}
						placement='top'>
						<Typography
							variant='subtitle1'
							style={{
								display: 'inline-block',
								wordWrap: 'break-word',
								whiteSpace: 'pre-wrap',
								textOverflow:
									(params?.row?.comments?.length ?? 0) > 200
										? 'ellipsis'
										: 'clip',
							}}>
							{value}
						</Typography>
					</Tooltip>
				) : (
					<Typography>-</Typography>
				)
			},
		},
		{
			field: 'cropCarbonCredits',
			headerName: 'Credits',
			minWidth: 200,
			flex: 1,
			renderCell: (params) => {
				return params?.row?.cropCarbonCredits ? (
					<Tooltip
						title={
							<Typography
								variant='subtitle1'
								style={{
									whiteSpace: 'pre-wrap',
								}}>
								{params?.row?.cropCarbonCredits.map(
									({
										cropName,
										carbonCreditsInTonnes,
									}: {
										cropName: string
										carbonCreditsInTonnes: number
									}) => (
										<>
											{cropName} : {carbonCreditsInTonnes}
											{'tCO'}
											<sub>2ss</sub>
											{'\n'}
										</>
									)
								)}
							</Typography>
						}
						placement='top'>
						<Typography
							variant='subtitle1'
							style={{
								display: 'inline-block',
								wordWrap: 'break-word',
								whiteSpace: 'pre-wrap',
								textOverflow:
									(params?.row?.comments?.length ?? 0) > 200
										? 'ellipsis'
										: 'clip',
							}}>
							{params?.row?.cropCarbonCredits.map(
								({
									cropName,
									carbonCreditsInTonnes,
								}: {
									cropName: string
									carbonCreditsInTonnes: number
								}) => (
									<>
										{cropName} : {carbonCreditsInTonnes}
										{' tCO'}
										<sub>2</sub>
										{'\n'}
									</>
								)
							)}
						</Typography>
					</Tooltip>
				) : (
					<Typography>-</Typography>
				)
			},
		},
		{
			field: 'isSuspended',
			headerName: 'Status',
			minWidth: 200,
			flex: 1,
			renderCell: (params) => {
				return (
					<Typography
						variant='subtitle1'
						style={{
							color: params.row.suspended
								? theme.palette.error.main
								: theme.palette.custom.green[700],
						}}>
						{params.row.suspended ? 'Suspended' : 'Active'}
					</Typography>
				)
			},
		},
		// {
		// 	field: 'totalBiomassDropped',
		// 	headerName: 'Biomass Quantity',
		// 	minWidth: 100,
		// 	flex: 1,
		// 	valueGetter: (params) =>
		// 		params.row?.totalBiomassDropped === null
		// 			? '-'
		// 			: `${params.row?.totalBiomassDropped / 1000} 	${
		// 					params?.row?.totalBiomassDropped > 0 ? ' tons' : ''
		// 			  }`,
		// },
		// {
		// 	field: 'totalBiocharProduced',
		// 	headerName: 'Biochar Quantity',
		// 	minWidth: 100,
		// 	flex: 1,
		// 	valueGetter: (params) =>
		// 		params.row?.totalBiocharProduced === null
		// 			? '-'
		// 			: `${params.row?.totalBiocharProduced} ${
		// 					params?.row?.totalBiocharProduced > 0 ? ' Ltrs' : ''
		// 			  }`,
		// },
		// {
		// 	field: 'action',
		// 	headerName: '',
		// 	flex: 1,
		// 	maxWidth: 80,
		// 	renderCell: () => (
		// 		<IconButton>
		// 			<MoreVert />
		// 		</IconButton>
		// 	),
		// },
	]

	return (
		<CustomDataGrid
			showPagination={true}
			onRowClick={handleRowClick}
			rows={artisanPro?.artisanPros ?? []}
			columns={gridColumn}
			rowCount={artisanPro?.count ?? 0}
			headerComponent={
				<Stack className='header-filter-search' gap={2}>
					<QueryInput
						className='search-textFiled'
						queryKey='search'
						placeholder='Search'
						setPageOnSearch
						InputProps={{
							startAdornment: <Search fontSize='small' />,
						}}
					/>
					{/* {userDetails?.accountType === userRoles.Admin && (
						<QueryAutoComplete
							options={allBaList}
							queryKey={'baId'}
							label={'BA'}
							isDisable={false}
							initialValue={filterInitialValue ?? ''}
						/>
					)} */}
				</Stack>
			}
			loading={isLoading}
		/>
	)
}
