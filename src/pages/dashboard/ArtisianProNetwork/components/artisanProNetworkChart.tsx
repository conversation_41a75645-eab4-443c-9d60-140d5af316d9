import { TArtisanProInfo } from '@/interfaces'
import { formatDivision } from '@/utils/helper'
import FiberManualRecordRoundedIcon from '@mui/icons-material/FiberManualRecordRounded'
import { Card, Stack, Typography, alpha, styled, useTheme } from '@mui/material'
import { ApexOptions } from 'apexcharts'
import { FC, ReactNode, useMemo } from 'react'
import ReactApexChart from 'react-apexcharts'

interface IProps {
	artisanProBiomassList: TArtisanProInfo[]
	artisanProBiocharList: TArtisanProInfo[]
}

export const ArtisanProNetworkChart: FC<IProps> = ({
	artisanProBiomassList,
	artisanProBiocharList,
}) => {
	// const [searchParams, setSearchParams] = useSearchParams()
	const theme = useTheme()
	// const graphTab = searchParams.get('graphTab') || 'biomass'

	// const ActivityGraphTabArray = useMemo(
	// 	() => [
	// 		{
	// 			label: 'Biomass',
	// 			value: 'biomass',
	// 			incrementCount: '120',
	// 			incrementPercentage: '12',
	// 		},
	// 		{
	// 			label: 'Application',
	// 			value: 'application',
	// 			incrementCount: '120',
	// 			incrementPercentage: '12',
	// 		},
	// 		{
	// 			label: 'Inventory',
	// 			value: 'inventory',
	// 			incrementCount: '130',
	// 			incrementPercentage: '13',
	// 		},
	// 	],
	// 	[]
	// )

	const StatusChart = useMemo(
		() => (id: string) => ({
			options: {
				legend: {
					show: false,
				},
				dataLabels: {
					enabled: false,
				},

				tooltip: {
					enabled: true,
					fillSeriesColor: false,
					marker: {
						show: false,
					},
					y: {
						formatter: function (val: number) {
							if (id === 'biocharProduction') return val + ' m³'
							else if (id === 'biomassCollection') return val + ' Tonn'
						},
						title: {
							formatter: function () {
								return ''
							},
						},
					},
				},
				responsive: [
					{
						breakpoint: 680,
						options: {
							chart: {
								width: 280,
							},
						},
					},
					{
						breakpoint: 1440,
						options: {
							chart: {
								width: 300,
							},
						},
					},
				],
				chart: {
					type: 'donut',
					width: 100,
				},
				plotOptions: {
					pie: {
						donut: {
							size: '70%',
						},
					},
				},
				fill: {
					colors: [
						theme.palette.primary.main,
						theme.palette.success.main,
						theme.palette.warning.main,
						theme.palette.info.main,
						alpha(theme.palette.primary.main, 0.1),
						theme.palette.custom.green[500],
						theme.palette.custom.red[500],
					],
				},
				states: {
					hover: { filter: { type: 'darken', value: 0.7 } },
					active: { filter: { type: 'none', value: 0 } },
				},
				stroke: { width: 0 },
			},
		}),
		[theme.palette]
	)

	// const opt = useMemo(
	// 	() => ({
	// 		chart: {
	// 			type: 'bar',
	// 			height: 200,
	// 			stacked: true,
	// 			toolbar: {
	// 				show: false,
	// 			},
	// 			zoom: {
	// 				enabled: true,
	// 			},
	// 		},
	// 		responsive: [
	// 			{
	// 				breakpoint: 480,
	// 				options: {
	// 					legend: {
	// 						position: 'bottom',
	// 						offsetX: -10,
	// 						offsetY: 0,
	// 					},
	// 				},
	// 			},
	// 		],
	// 		plotOptions: {
	// 			bar: {
	// 				horizontal: false,
	// 				barHeight: '100%',
	// 				dataLabels: {
	// 					total: {
	// 						enabled: false,
	// 					},
	// 				},
	// 			},
	// 		},
	// 		xaxis: {
	// 			type: 'datetime',
	// 			categories: [
	// 				'01/01/2011 GMT',
	// 				'01/02/2011 GMT',
	// 				'01/03/2011 GMT',
	// 				'01/04/2011 GMT',
	// 				'01/05/2011 GMT',
	// 				'01/06/2011 GMT',
	// 				'01/07/2011 GMT',
	// 				'01/08/2011 GMT',
	// 				'01/09/2011 GMT',
	// 				'01/10/2011 GMT',
	// 				'01/11/2011 GMT',
	// 			],
	// 			axisBorder: {
	// 				show: false,
	// 			},
	// 			axisTicks: {
	// 				show: false,
	// 			},
	// 			labels: {
	// 				show: false,
	// 			},
	// 		},
	// 		legend: {
	// 			position: 'bottom',
	// 			horizontalAlign: 'left',
	// 			offsetY: 10,
	// 			markers: {
	// 				radius: 12,
	// 			},
	// 		},
	// 		fill: {
	// 			opacity: 1,
	// 		},
	// 		grid: { show: false },
	// 		dataLabels: {
	// 			enabled: false,
	// 		},
	// 	}),
	// 	[]
	// )

	// const options = useMemo(
	// 	() => ({
	// 		series: [
	// 			{
	// 				data: [
	// 					10000, 2000, 5000, 6000, 4400, 5000, 1100, 5263, 1236, 5222, 1000,
	// 					8596, 8000, 1452, 1236, 4452, 1255, 1111, 5263, 8025,
	// 				],
	// 				color: theme.palette.primary.light,
	// 			},
	// 		],
	// 	}),
	// 	[theme.palette.primary.light]
	// )

	// const handleGraphTabChange = useCallback(
	// 	(_: unknown, newValue: string) => {
	// 		setSearchParams((searchParams) => {
	// 			searchParams.set('graphTab', newValue)
	// 			return searchParams
	// 		})
	// 	},
	// 	[setSearchParams]
	// )

	const customCharts = useMemo(
		() => [
			{ id: 'biomassCollection', label: 'Biomass Collection' },
			{ id: 'biocharProduction', label: 'Biochar Production' },
		],
		[]
	)
	const customChartSeriesOptionData: { [key: string]: number[] } = useMemo(
		() => ({
			biomassCollection: artisanProBiomassList?.map(
				(item) => item?.totalBiomassDroppedInTonnes ?? 0
			),
			biocharProduction: artisanProBiocharList?.map((item) =>
				item?.totalBiocharProduced
					? formatDivision(item?.totalBiocharProduced, 1000)
					: 0
			),
		}),
		[artisanProBiocharList, artisanProBiomassList]
	)
	const colorArr = useMemo(
		() => [
			theme.palette.primary.main,
			theme.palette.success.main,
			theme.palette.warning.main,
			theme.palette.info.main,
			alpha(theme.palette.primary.main, 0.1),
			theme.palette.custom.green[500],
			theme.palette.custom.red[500],
		],
		[theme]
	)

	const customChartLabelOption: {
		[key: string]: {
			label: string
			value: number
			unit: ReactNode
			color: string
		}[]
	} = useMemo(
		() => ({
			biomassCollection: artisanProBiomassList?.map((item, index) => ({
				label: item.name,
				value: item?.totalBiomassDroppedInTonnes ?? 0,
				unit: 'Ton',
				color: colorArr?.[index],
			})),
			biocharProduction: artisanProBiocharList?.map((item, index) => ({
				label: item.name,
				value: item?.totalBiocharProduced
					? formatDivision(item?.totalBiocharProduced, 1000)
					: 0,
				unit: (
					<>
						m<sup>3</sup>
					</>
				),
				color: colorArr?.[index],
			})),
		}),
		[artisanProBiocharList, artisanProBiomassList, colorArr]
	)
	return (
		<StyledChartsContainer>
			{/* <CustomCard
				className='crop-details-card'
				headerComponent={
					<Stack className='activity-graph'>
						<TabContext value={graphTab}>
							<TabList
								onChange={handleGraphTabChange}
								TabIndicatorProps={{
									sx: {
										top: 7,
									},
								}}>
								{ActivityGraphTabArray.map(
									(
										{ label, value, incrementCount, incrementPercentage },
										index: number
									) => (
										<Tab
											key={index}
											label={
												<Stack
													sx={{
														position: 'relative',
														padding:
															graphTab === value
																? theme.spacing(1.7, 4, 0, 0)
																: theme.spacing(0),
													}}>
													<Typography
														textTransform='none'
														variant={graphTab === value ? 'body2' : 'overline'}>
														{label}
													</Typography>
													<Typography
														variant={graphTab === value ? 'h3' : 'body1'}>
														{incrementCount}
													</Typography>
													{graphTab === value && (
														<Stack
															direction='row'
															sx={{
																position: 'absolute',
																bottom: 0,
																right: '10%',
															}}>
															<GridArrowUpwardIcon
																color='success'
																sx={{
																	fontSize: theme.typography.caption,
																}}
															/>
															<Typography
																variant='caption'
																color='success.main'>
																{incrementPercentage}%
															</Typography>
														</Stack>
													)}
												</Stack>
											}
											value={value}
										/>
									)
								)}
							</TabList>
						</TabContext>
						<Stack className='bar-chart'>
							<ReactApexChart
								options={opt as ApexOptions}
								series={options.series}
								type='bar'
							/>
						</Stack>
					</Stack>
				}
			/> */}

			{/* {customCharts?.map((item) => (
				<CustomCard
					key={item.id}
					className='crop-details-card'
					headerComponent={
						<Stack gap={3}>
							<Typography variant='body2'>{item.label}</Typography>
							<Stack className='chart'>
								<ReactApexChart
									options={
										{
											...StatusChart?.options,
											...{ labels: customChartOptionData?.labels?.[item?.id] },
										} as ApexOptions
									}
									series={customChartOptionData?.series?.[item?.id]}
									type='donut'
								/>
							</Stack>
						</Stack>
					}
				/>
			))} */}
			<Stack className='chart-container' direction='row'>
				{customCharts?.map((item) => (
					<Card key={item?.id} className='border-card card'>
						<Stack className='card-container' direction='row'>
							<Stack className='chart' rowGap={2}>
								<Typography variant='body2' ml={2}>
									{item?.label}
								</Typography>
								<ReactApexChart
									options={StatusChart(item.id).options as ApexOptions}
									series={customChartSeriesOptionData?.[item?.id]}
									type='donut'
								/>
							</Stack>
							<Stack width='40%' justifyContent='center' alignItems='center'>
								<Stack spacing={0.5} mt={2}>
									{customChartLabelOption?.[item.id]?.map((val) => (
										<Stack
											direction='row'
											key={val?.label}
											alignItems='center'
											spacing={1}>
											<FiberManualRecordRoundedIcon
												className='circle-icon'
												sx={{ color: val?.color }}
											/>
											<Typography variant='subtitle1'>
												{val?.label} : {val?.value} {val?.unit}
											</Typography>
										</Stack>
									))}
								</Stack>
							</Stack>
						</Stack>
					</Card>
				))}
			</Stack>
		</StyledChartsContainer>
	)
}

const StyledChartsContainer = styled(Stack)(({ theme }) => ({
	padding: theme.spacing(2, 2, 4),
	flexDirection: 'row',
	gap: theme.spacing(2),
	flexWrap: 'wrap',
	background: theme.palette.neutral['50'],
	'.activity-graph': {
		margin: theme.spacing(-2),
		padding: theme.spacing(0, 2),
		'.bar-chart': {
			width: 300,
			height: '100%',
			marginLeft: theme.spacing(-1.5),
		},
	},
	'.map-container': {
		height: 260,
		width: 364,
		background: theme.palette.neutral['200'],
		borderRadius: theme.spacing(2),
	},
	'.crop-details-card': {
		display: 'flex',
		width: 364,
		height: 260,
		borderRadius: theme.spacing(2),
		gap: theme.spacing(2),
		padding: theme.spacing(2),
		'.chart': {
			width: 320,
			height: '100%',
		},
	},
	'.chart-container': {
		flexDirection: 'row',
		width: '100%',
		gap: theme.spacing(2),
		flexWrap: 'wrap',
		'.card': {
			padding: theme.spacing(2),
			width: '100%',
			maxWidth: 500,
			height: 300,
			'.card-container': {
				height: '100%',
				alignItems: 'center',
				'.chart': {
					position: 'relative',
					width: '70%',
					height: '100%',
					justifyContent: 'center',
					// marginLeft: theme.spacing(-3),
					'.chart-data': {
						position: 'absolute',
						height: 48,
						width: 48,
					},
				},
			},
		},
	},
	'.circle-icon': {
		height: 8,
		width: 8,
	},
}))
