import { Controller, FormProvider } from 'react-hook-form'
import {
	TextField,
	Select,
	MenuItem,
	InputLabel,
	FormControl,
	Button,
	Stack,
	styled,
	Typography,
	FormHelperText,
} from '@mui/material'
import AddIcon from '@mui/icons-material/Add'
import { CustomFileUploader, PhoneInputComponent } from '@/components'
import { useGenerateCertificateForm } from './useGenerateCertificateForm'
import { useCallback } from 'react'
import { useSearchParams } from 'react-router-dom'
import { theme } from '@/lib/theme/theme'
import SigningAuthorityItem from './SigningAuthorityDetails'
import { TGenerateCertificateSchema } from './certificateSchema'

export const ImageFileTypes = ['JPG', 'PNG', 'JPEG', 'HEIC']

type FieldName = keyof TGenerateCertificateSchema

const formFields: {
	label: string
	name: FieldName
}[] = [
	{
		label: 'Name:',
		name: 'cSinkManagerCompanyName',
	},
	{
		label: 'Email:',
		name: 'email',
	},
]

const GenerateCertificate = () => {
	const [searchParams, setSearchParams] = useSearchParams()
	const {
		handleSubmit,
		onSubmit,
		fields,
		append,
		remove,
		PrefilledData,
		cSinkManagerData,
		isCsinkManager,
		isPending,
		...methods
	} = useGenerateCertificateForm()

	const {
		control,
		formState: { errors, isDirty },
		setValue,
		reset,
		watch,
		setError,
		clearErrors,
	} = methods

	const handleOnChange = useCallback(
		(value: string, dialCode: string) => {
			setValue('countryCode', `+${dialCode}`, {
				shouldDirty: true,
			})

			setValue('phoneNumber', value, {
				shouldDirty: true,
			})
			if (value.toString().length === 0) {
				setError('phoneNumber', { message: 'Please enter phone number' })
				return
			}
			clearErrors('phoneNumber')
		},
		[clearErrors, setError, setValue]
	)

	return (
		<StyledContainer>
			<FormControl fullWidth>
				<InputLabel id='csink-manager-label'>Select Csink Manager</InputLabel>
				<Select
					labelId='csink-manager-label'
					id='csink-manager-select'
					disabled={!!isCsinkManager.id}
					className='userSelect'
					value={searchParams.get('cSinkManagerId') || ''}
					onChange={(e) => {
						reset()
						const currentParams = new URLSearchParams(searchParams)
						currentParams.set('cSinkManagerId', e.target.value ?? '')
						setSearchParams(currentParams, { replace: true })
					}}
					label='Select Csink Manager'
					MenuProps={{
						PaperProps: {
							style: {
								maxHeight: 300,
							},
						},
					}}>
					{!!isCsinkManager.id && (
						<MenuItem key={isCsinkManager.id} value={isCsinkManager.id}>
							{isCsinkManager.name}
						</MenuItem>
					)}
					{cSinkManagerData?.csinkManagers?.map((item) => (
						<MenuItem key={item.id} value={item.id}>
							{item.name}
						</MenuItem>
					))}
				</Select>
			</FormControl>
			<FormProvider handleSubmit={handleSubmit} {...methods}>
				<form onSubmit={handleSubmit((data) => onSubmit(data))}>
					<Stack spacing={4} className='containerStack'>
						<Stack className='paddedStack'>
							<Typography variant='body2'>Company Details</Typography>
							<Stack display='flex' flexDirection='row'>
								<Stack className='threeRows'>
									{formFields.map((field, index) => (
										<Stack className='gapStack' spacing={0} key={index}>
											<Stack className='leftCol'>
												<Typography>{field.label}</Typography>
											</Stack>

											<Stack className='rightCol' spacing={0}>
												<>
													<Controller
														name={field.name}
														control={control}
														render={({ field: controllerField }) => (
															<TextField
																className='inputs'
																fullWidth
																{...controllerField}
															/>
														)}
													/>
													<FormHelperText error>
														{field.name && errors?.[field.name]?.message}
													</FormHelperText>
												</>
											</Stack>
										</Stack>
									))}
									<Stack className='gapStack'>
										<Stack className='leftCol'>
											<Typography>Phone No.:</Typography>
										</Stack>

										<Stack className='rightCol'>
											<PhoneInputComponent
												smallSize
												fullWidth
												dialCode={watch('countryCode')}
												value={String(watch('phoneNumber'))}
												handleOnChange={handleOnChange}
												getSelectedCountryDialCode={(dialCode) =>
													setValue('countryCode', dialCode)
												}
											/>
											<FormHelperText error>
												{errors?.phoneNumber?.message}
												{errors?.countryCode?.message}
											</FormHelperText>
										</Stack>
									</Stack>
								</Stack>
								<Stack width='30%' paddingX={3} justifyContent='center'>
									<CustomFileUploader
										directionColumnReverse
										imageUrl={watch('cSinkManagerCompanyLogoUrl') ?? ''}
										acceptFileTypes={ImageFileTypes}
										heading='Logo'
										sx={{
											height: theme.spacing(20),
											width: theme.spacing(20),
											borderRadius: 3,
										}}
										imageHeight={100}
										setUploadData={({ id }) => {
											setValue('cSinkManagerCompanyLogo', id, {
												shouldDirty: true,
												shouldTouch: true,
											})
										}}
										mediaType='image'
									/>
									<FormHelperText error={true}>
										{errors?.cSinkManagerCompanyLogo?.message}
									</FormHelperText>
								</Stack>
							</Stack>
						</Stack>

						<Stack className='authorityHeader'>
							<Typography variant='body2'>Signing Authority Details</Typography>
						</Stack>
						{fields.map((item, index) => (
							<SigningAuthorityItem
								key={item.id}
								item={item}
								index={index}
								remove={remove}
								PrefilledData={PrefilledData}
							/>
						))}
						<Stack className='authorityHeader'>
							<FormHelperText error>
								{errors?.signingAuthorityDetails?.message}
							</FormHelperText>
						</Stack>

						<Stack className='paddedStackBtn'>
							<Button
								className='addBtn'
								type='button'
								startIcon={<AddIcon />}
								onClick={() => {
									append({
										signingAuthorityName: '',
										signingAuthorityPosition: '',
										signingAuthoritySignature: '',
									})
								}}>
								Add Signing Authority
							</Button>
						</Stack>

						<Stack className='twocol paddedStack'>
							<Button
								type='submit'
								variant='contained'
								fullWidth
								disabled={
									!isDirty || isPending || !searchParams.get('cSinkManagerId')
								}>
								{PrefilledData?.cSinkManagerCompanyName ? 'Save' : 'Create'}
							</Button>
							<Button fullWidth variant='outlined' onClick={() => reset({})}>
								Reset
							</Button>
						</Stack>
					</Stack>
				</form>
			</FormProvider>
		</StyledContainer>
	)
}

export default GenerateCertificate

const StyledContainer = styled(Stack)(({ theme }) => ({
	padding: theme.spacing(4),
	fontSize: theme.typography.pxToRem(12),
	width: '48%',
	minWidth: theme.spacing(70),
	gap: theme.spacing(3),
	'.containerStack': {
		width: '100%',
		display: 'flex',
		justifyContent: 'flex-end',
	},
	'.paddedStack': {
		paddingLeft: theme.spacing(0),
		display: 'flex',
		flexDirection: 'column',
		gap: theme.spacing(3),
	},
	'.paddedStackBtn': {
		paddingLeft: theme.spacing(15),
		display: 'flex',
		flexDirection: 'row',
		justifyContent: 'flex-end',
		gap: theme.spacing(3),
		'.addBtn': {
			color: 'red',
			backgroundColor: 'transparent',
			alignItems: 'center',
			gap: theme.spacing(1),
			'&:hover': {
				backgroundColor: 'transparent',
				color: 'darkred',
			},
		},
	},
	'.userSelect': {
		width: 'full',
		height: 56,
	},
	'.threeRows': {
		width: '70% ',
		display: 'flex',
		flexDirection: 'column',
		gap: theme.spacing(2),
	},
	'.twocol': {
		width: '100% ',
		display: 'flex',
		flexDirection: 'row',
		gap: theme.spacing(2),
	},
	'.leftCol': {
		width: '25%',
		display: 'flex',
		flexDirection: 'row-reverse',
		paddingRight: theme.spacing(1),
	},
	'.rightCol': {
		display: 'flex',
		direction: 'column',
		width: '75%',
	},
	'.gapStack': {
		display: 'flex',
		flexDirection: 'row',
		alignItems: 'center',
		gap: theme.spacing(1),
	},
	'.inputs': {
		width: '100%',
		height: theme.spacing(6),
		'& .MuiInputBase-root': {
			height: '100%',
		},
		'& .MuiOutlinedInput-input': {
			padding: `${theme.spacing(1)} ${theme.spacing(2)}`,
		},
	},
	'.dashedBox': {
		border: '1px dashed grey',
		borderRadius: theme.spacing(2),
		display: 'flex',
		alignItems: 'center',
		justifyContent: 'center',
		padding: theme.spacing(4),
		flexDirection: 'row',
		gap: theme.spacing(3),
	},
	'.authorityHeader': {
		paddingLeft: theme.spacing(0),
		marginTop: theme.spacing(7),
	},
	'.removeBtn': {
		position: 'absolute',
		top: 0,
		right: 0,
		transform: 'translate(50%, -50%)',
		padding: theme.spacing(0),
		backgroundColor: theme.palette.primary.dark,
		border: '1px solid #ccc',
		boxShadow: 1,
		'&:hover': {
			backgroundColor: theme.palette.primary.light,
		},
		'.removeIcon': {
			color: theme.palette.common.white,
			width: theme.spacing(3),
			height: theme.spacing(3),
		},
	},
}))
