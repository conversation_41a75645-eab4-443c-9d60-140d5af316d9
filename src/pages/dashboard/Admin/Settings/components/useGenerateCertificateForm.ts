import { useForm, useFieldArray } from 'react-hook-form'
import { yupResolver } from '@hookform/resolvers/yup'
import {
	CertificateSchema,
	TGenerateCertificateSchema,
} from './certificateSchema'
import { authAxios, useAuthContext } from '@/contexts'
import { useMutation, useQuery, useQueryClient } from '@tanstack/react-query'
import { useCallback, useEffect, useMemo } from 'react'
import { userRoles } from '@/utils/constant'
import { ICSinkManagerResponse } from '@/interfaces'
import { toast } from 'react-toastify'
import { useSearchParams } from 'react-router-dom'

const initialValues = {
	cSinkManagerId: '',
	cSinkManagerCompanyName: '',
	cSinkManagerCompanyLogo: '',
	cSinkManagerCompanyLogoUrl: '',
	email: '',
	phoneNumber: '',
	countryCode: '',
	signingAuthorityDetails: [
		{
			signingAuthorityName: '',
			signingAuthorityPosition: '',
			signingAuthoritySignature: '',
		},
	],
}

const fetchCsinkManagers = async (): Promise<ICSinkManagerResponse> => {
	const response = await authAxios.get('/csink-manager?limit=1000&page=0')
	return response.data
}

export const useGenerateCertificateForm = () => {
	const queryClient = useQueryClient()
	const [searchParams, setSearchParams] = useSearchParams()

	const methods = useForm<TGenerateCertificateSchema>({
		mode: 'onSubmit',
		resolver: yupResolver<TGenerateCertificateSchema>(CertificateSchema),
		defaultValues: initialValues,
	})

	const { userDetails } = useAuthContext()
	const csinkManagerIdParams =
		userDetails?.accountType === userRoles.CsinkManager
			? userDetails.csinkManagerId
			: searchParams.get('cSinkManagerId')

	const isCsinkManager = useMemo(() => {
		const isCsinkManager = {
			id:
				userDetails?.accountType === userRoles.CsinkManager
					? userDetails.csinkManagerId
					: null,
			name: userDetails?.name ?? '',
		}
		return isCsinkManager
	}, [userDetails?.accountType, userDetails?.csinkManagerId, userDetails?.name])

	useEffect(() => {
		if (isCsinkManager.id && userDetails?.csinkManagerId) {
			const currentParams = new URLSearchParams(searchParams)
			currentParams.set('cSinkManagerId', userDetails?.csinkManagerId)
			setSearchParams(currentParams, { replace: true })
		}
	}, [
		isCsinkManager,
		userDetails?.csinkManagerId,
		methods,
		setSearchParams,
		searchParams,
	])

	const { control } = methods

	const { fields, append, remove } = useFieldArray({
		control,
		name: 'signingAuthorityDetails',
	})

	const submitFunction = async (data: TGenerateCertificateSchema) => {
		try {
			const isEditing = Boolean(PrefilledData?.cSinkManagerId)
			const method = isEditing ? 'PUT' : 'POST'
			const url = `/certificate/request/v2`

			await authAxios({
				method,
				url,
				data: { ...data, cSinkManagerId: csinkManagerIdParams },
			})
		} catch (err) {
			toast('Error in submitting.')
		}
	}

	const { mutate: onSubmit, isPending } = useMutation({
		mutationFn: submitFunction,
		onSuccess: () => {
			queryClient.refetchQueries({
				queryKey: ['userCertificateData', csinkManagerIdParams],
			})
		},
	})


	const fetchUserData = useCallback(async (): Promise<
		TGenerateCertificateSchema & { cSinkManagerId?: string }
	> => {
		if (!csinkManagerIdParams) return initialValues
		const currentParams = new URLSearchParams(searchParams)
		currentParams.set('cSinkManagerId', csinkManagerIdParams)
		setSearchParams(currentParams, { replace: true })
		try {
			const api = `/certificate/request/v2/${csinkManagerIdParams}`
			const response = await authAxios.get(api)
			return response.data
		} catch (err) {
			methods.reset({
				email: '',
				countryCode: '',
				cSinkManagerCompanyName: '',
				cSinkManagerCompanyLogo: '',
				cSinkManagerCompanyLogoUrl: '',
				phoneNumber: '',
				signingAuthorityDetails: [
					{
						signingAuthorityName: '',
						signingAuthorityPosition: '',
						signingAuthoritySignature: '',
						signingAuthoritySignatureUrl: '',
					},
				],
			})

			toast('Certificate data does not exist')
		}
		return initialValues
	}, [csinkManagerIdParams, methods, searchParams, setSearchParams])

	const { data: PrefilledData } = useQuery({
		queryKey: ['userCertificateData', csinkManagerIdParams],
		queryFn: () => fetchUserData(),
		enabled: !!csinkManagerIdParams,
	})

	const { data: cSinkManagerData } = useQuery({
		queryKey: ['csinkManager'],
		queryFn: fetchCsinkManagers,
	})

	useEffect(() => {
		if (!PrefilledData?.cSinkManagerId) {
			methods.reset({ ...initialValues })
		}

		methods.reset({
			...PrefilledData,
		})
	}, [PrefilledData, methods])

	return {
		...methods,
		fields,
		append,
		remove,
		onSubmit,
		isPending,
		PrefilledData,
		cSinkManagerData,
		isCsinkManager,
	}
}
