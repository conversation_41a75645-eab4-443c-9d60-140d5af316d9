import { CustomDataGrid } from '@/components'
import { Stack, styled, Typography } from '@mui/material'
import { GridColDef, GridValidRowModel } from '@mui/x-data-grid'
import { useEffect, useMemo } from 'react'
import { useSettings } from '../useSettings'
import { containerShapeLabelValue, containerShapesEnum } from '@/utils/constant'
import { IContainerDetails } from '@/interfaces'

export const MeasuringContainerTab = () => {
	const { allMeasuringContainers } = useSettings()

	useEffect(() => {
		allMeasuringContainers?.containers?.forEach(
			(element: IContainerDetails, index: number) => {
				element.sno = index + 1

				const k = containerShapeLabelValue?.filter((item) => {
					return item.value === element?.shape ? true : false
				})
				element.containerShapeName = k[0].label
			}
		)
	}, [allMeasuringContainers])

	const column: GridColDef<GridValidRowModel>[] = useMemo(
		() => [
			{
				field: 'sno',
				headerName: 'sno',
				maxWidth: 100,
				flex: 1,
			},
			{
				field: 'name',
				headerName: 'Name',
				minWidth: 100,
				flex: 1,
			},
			{
				field: 'containerShapeName',
				headerName: 'Shape',
				minWidth: 100,
				flex: 1,
			},
			{
				field: 'dimensions',
				headerName: 'Dimensions',
				minWidth: 200,
				flex: 1,
				renderCell: (params) => {
					const element = params?.row
					const unit =
						element.heightUnit ?? element.lengthUnit ?? element.breadthUnit
					if (element?.shape === containerShapesEnum.rectangular) {
						return (
							<Stack spacing={1}>
								<Typography variant='subtitle1'>
									Long Base :{' '}
									{element.longBase !== null
										? `${element.lowerBase} ${unit}`
										: 'None'}
								</Typography>
								<Typography variant='subtitle1'>
									Short Base :{' '}
									{element.shortBase !== null
										? `${element.upperBase} ${unit}`
										: 'None'}
								</Typography>
								<Typography variant='subtitle1'>
									Length :{' '}
									{element.frustumLength !== null
										? `${element.length} ${unit}`
										: 'None'}
								</Typography>
								<Typography variant='subtitle1'>
									Height :{' '}
									{element.depth !== null
										? `${element.height} ${unit}`
										: 'None'}
								</Typography>
							</Stack>
						)
					} else if (element?.shape === containerShapesEnum.cylinder) {
						return (
							<Stack spacing={1}>
								<Typography variant='subtitle1'>
									Height :{' '}
									{element.height !== null
										? `${element.height} ${unit}`
										: 'None'}
								</Typography>
								<Typography variant='subtitle1'>
									Diameter :{' '}
									{element.diameter !== null
										? `${element.diameter} ${unit}`
										: 'None'}
								</Typography>
							</Stack>
						)
					} else if (element?.shape === containerShapesEnum.conical) {
						return (
							<Stack spacing={1}>
								<Typography variant='subtitle1'>
									Height :{' '}
									{element.height !== null
										? `${element.height} ${unit}`
										: 'None'}
								</Typography>
								<Typography variant='subtitle1'>
									Upper Surface Diameter :{' '}
									{element.upperSurfaceDiameter !== null
										? `${element.upperSurfaceDiameter} ${unit}`
										: 'None'}
								</Typography>
								<Typography variant='subtitle1'>
									Lower Surface Diameter :{' '}
									{element.lowerSurfaceDiameter !== null
										? `${element.lowerSurfaceDiameter} ${unit}`
										: 'None'}
								</Typography>
							</Stack>
						)
					} else if (element?.shape === containerShapesEnum.cuboid) {
						return (
							<Stack spacing={1}>
								<Typography variant='subtitle1'>
									Breadth :{' '}
									{element.breadth !== null
										? `${element.breadth} ${unit}`
										: 'None'}
								</Typography>
								<Typography variant='subtitle1'>
									Height :{' '}
									{element.height !== null
										? `${element.height} ${unit}`
										: 'None'}
								</Typography>
								<Typography variant='subtitle1'>
									Length :{' '}
									{element.length !== null
										? `${element.length} ${unit}`
										: 'None'}
								</Typography>
							</Stack>
						)
					} else if (element?.shape === containerShapesEnum.other) {
						return (
							<Stack spacing={1}>
								<Typography variant='subtitle1'>
									Height :{' '}
									{element.height !== null
										? `${element.height} ${unit}`
										: 'None'}
								</Typography>
							</Stack>
						)
					}
					return <Typography variant='subtitle1'>Nope</Typography>
				},
			},
			{
				field: 'volume',
				headerName: 'Volume (ltr)',
				minWidth: 100,
				flex: 1,
			},
		],
		[]
	)

	// const handleRowClick: GridEventListener<'rowClick'> = (params) => {
	// 	// future use
	// }

	return (
		<StyledContainer>
			<CustomDataGrid
				showPagination={true}
				showPaginationDetails={false}
				rows={allMeasuringContainers?.containers || []}
				rowCount={allMeasuringContainers?.count ?? 0}
				columns={column}
				headerComponent={null}
				// loading={isLoading} export allKilns -> with multiple functions
				// onRowClick={handleRowClick}
			/>
		</StyledContainer>
	)
}

const StyledContainer = styled(Stack)(({ theme }) => ({
	padding: theme.spacing(2, 0),
	'.grid-header-component': {
		flexDirection: 'row',
		alignItems: 'center',
		'.search-textFiled': {
			minWidth: 334,
			width: '100%',
			'.MuiInputBase-root': {
				height: theme.spacing(4.5),
				borderRadius: theme.spacing(1.25),
			},
		},
		'.form-controller': {
			margin: theme.spacing(0.125),
			minWidth: theme.spacing(18),
			width: '100%',
			'.MuiOutlinedInput-notchedOutline': {
				borderRadius: theme.spacing(1.25),
			},
		},
	},
}))
