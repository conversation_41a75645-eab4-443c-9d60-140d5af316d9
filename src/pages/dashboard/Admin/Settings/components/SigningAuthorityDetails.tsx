import {
	Stack,
	Typo<PERSON>,
	TextField,
	IconButton,
	FormHelperText,
} from '@mui/material'
import { Controller, useFormContext } from 'react-hook-form'
import RemoveIcon from '@mui/icons-material/Remove'
import { useTheme } from '@mui/material/styles'
import { CustomFileUploader } from '@/components'
import { ImageFileTypes } from './GenerateCertificate'
import { TGenerateCertificateSchema } from './certificateSchema'

type SigningAuthorityDetails = {
	signingAuthorityName: string
	signingAuthorityPosition: string
	signingAuthoritySignature: string
	signingAuthoritySignatureUrl?: string
}

const formFields: {
	label: string
	name: keyof SigningAuthorityDetails
}[] = [
	{
		label: 'Name:',
		name: `signingAuthorityName`,
	},
	{
		label: 'Position:',
		name: `signingAuthorityPosition`,
	},
]

const SigningAuthorityItem = ({
	index,
	item,
	remove,
	PrefilledData,
}: {
	index: number
	item: { id: string }
	remove: (index: number) => void
	PrefilledData?: TGenerateCertificateSchema
}) => {
	const theme = useTheme()
	const {
		control,
		setValue,
		formState: { errors },
	} = useFormContext<TGenerateCertificateSchema>()

	return (
		<Stack className='paddedStack' key={item.id} position='relative'>
			<Stack className='dashedBox'>
				<Stack className='threeRows'>
					{formFields.map((field, i) => (
						<Stack className='gapStack' spacing={0} key={i}>
							<Stack className='leftCol'>
								<Typography>{field.label}</Typography>
							</Stack>

							<Stack className='rightCol' spacing={0}>
								<>
									<Controller
										name={`signingAuthorityDetails.${index}.${field.name}`}
										control={control}
										render={({ field: controllerField }) => (
											<TextField
												className='inputs'
												fullWidth
												{...controllerField}
											/>
										)}
									/>
									<FormHelperText error>
										{field.name &&
											errors?.signingAuthorityDetails?.[index]?.[field.name]
												?.message}
									</FormHelperText>
								</>
							</Stack>
						</Stack>
					))}
				</Stack>

				<Stack width='20%'>
					<CustomFileUploader
						directionColumnReverse
						imageUrl={
							PrefilledData?.signingAuthorityDetails?.[index]
								?.signingAuthoritySignatureUrl ?? ''
						}
						acceptFileTypes={ImageFileTypes}
						heading='Sign'
						sx={{
							height: theme.spacing(15),
							width: theme.spacing(15),
							borderRadius: 3,
						}}
						imageHeight={100}
						setUploadData={({ id }) => {
							setValue(
								`signingAuthorityDetails.${index}.signingAuthoritySignature`,
								id,
								{
									shouldDirty: true,
									shouldTouch: true,
								}
							)
						}}
						mediaType='image'
					/>
					{errors?.signingAuthorityDetails?.[index]?.signingAuthoritySignature
						?.message && (
						<FormHelperText error={true}>
							{
								errors?.signingAuthorityDetails?.[index]
									?.signingAuthoritySignature?.message
							}
						</FormHelperText>
					)}
				</Stack>
			</Stack>

			<IconButton onClick={() => remove(index)} className='removeBtn'>
				<RemoveIcon className='removeIcon'/>
			</IconButton>
		</Stack>
	)
}

export default SigningAuthorityItem
