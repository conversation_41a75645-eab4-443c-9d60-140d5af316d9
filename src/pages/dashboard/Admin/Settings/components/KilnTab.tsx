import { CustomDataGrid } from '@/components'
import { Stack, styled, Typography } from '@mui/material'
import { GridColDef, GridValidRowModel } from '@mui/x-data-grid'
import { useEffect, useMemo } from 'react'
import { useSettings } from '../useSettings'
import { kilnShapeEnum, KilnShapeLabelValue } from '@/utils/constant'
import { IGlobalKiln } from '@/interfaces'
import { convertToMMonBoolean } from '@/utils/helper'

const shapeDimensions = (kilnShape: kilnShapeEnum) => {
	switch (kilnShape) {
		case kilnShapeEnum.Rectangular:
			return {
				'Short Base': 'lowerBase',
				'Long Base': 'upperBase',
				Length: 'frustumLength',
				Depth: 'depth',
			}
		case kilnShapeEnum.Cylindrical:
			return {
				Depth: 'depth',
				Diameter: 'diameter',
			}
		case kilnShapeEnum.Conical:
			return {
				Depth: 'depth',
				'Upper Surface Diameter': 'upperSurfaceDiameter',
				'Lower Surface Diameter': 'lowerSurfaceDiameter',
			}
		case kilnShapeEnum.Pyramidal:
			return {
				Depth: 'depth',
				'Upper Side': 'upperSide',
				'Lower Side': 'lowerSide',
			}
	}
}

export const KilnTab = () => {
	const { allKilns } = useSettings()

	useEffect(() => {
		allKilns?.data?.kilns?.forEach((element: IGlobalKiln, index: number) => {
			element.sno = index + 1

			const k = KilnShapeLabelValue?.filter((item) => {
				return item.value === element?.kilnShape ? true : false
			})
			element.kilnShapeName = k[0].label
		})
	}, [allKilns])

	const column: GridColDef<GridValidRowModel>[] = useMemo(
		() => [
			{
				field: 'sno',
				headerName: 'sno',
				maxWidth: 100,
				flex: 1,
			},
			{
				field: 'name',
				headerName: 'Name',
				minWidth: 100,
				flex: 1,
			},
			{
				field: 'kilnType',
				headerName: 'Type',
				minWidth: 100,
				flex: 1,
			},
			{
				field: 'kilnShapeName',
				headerName: 'Shape',
				minWidth: 100,
				flex: 1,
			},
			{
				field: 'dimensions',
				headerName: 'Dimensions',
				minWidth: 200,
				flex: 1,
				renderCell: (params) => {
					const element = params?.row
					const unit = element.depthUnit == 'mm' ? 'm' : element.depthUnit
					const isMM = element.depthUnit == 'mm' ? true : false
					const dataToShow = Object.entries(shapeDimensions(element?.kilnShape))
					return (
						<Stack spacing={1}>
							{dataToShow.map(([label, key]) => (
								<Typography variant='subtitle1' key={key}>
									{label}:{' '}
									{element[key] !== null && element[key] !== undefined
										? `${convertToMMonBoolean(element[key], isMM)} ${unit}`
										: 'None'}
								</Typography>
							))}
						</Stack>
					)
				},
			},
			{
				field: 'volume',
				headerName: 'Volume (ltr)',
				minWidth: 100,
				flex: 1,
			},
		],
		[]
	)

	return (
		<StyledContainer>
			<CustomDataGrid
				showPagination={true}
				showPaginationDetails={false}
				rows={allKilns?.data?.kilns || []}
				rowCount={allKilns?.data?.count ?? 0}
				columns={column}
				headerComponent={null}
				loading={allKilns.isLoading}
			/>
		</StyledContainer>
	)
}

const StyledContainer = styled(Stack)(({ theme }) => ({
	padding: theme.spacing(2, 0),
	'.grid-header-component': {
		flexDirection: 'row',
		alignItems: 'center',
		'.search-textFiled': {
			minWidth: 334,
			width: '100%',
			'.MuiInputBase-root': {
				height: theme.spacing(4.5),
				borderRadius: theme.spacing(1.25),
			},
		},
		'.form-controller': {
			margin: theme.spacing(0.125),
			minWidth: theme.spacing(18),
			width: '100%',
			'.MuiOutlinedInput-notchedOutline': {
				borderRadius: theme.spacing(1.25),
			},
		},
	},
}))
