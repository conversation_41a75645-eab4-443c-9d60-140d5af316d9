import * as Yup from 'yup'

export const CertificateSchema = Yup.object().shape({
	// cSinkManagerId: Yup.string().required('CSink Manager is required'),
	cSinkManagerCompanyName: Yup.string().required('Company name is required'),
	email: Yup.string()
		.email('Invalid email')
		.required('Company email is required'),
	countryCode: Yup.string().required('countryCode is required.'),
	phoneNumber: Yup.string()
		.matches(/^\d+$/, 'Phone number must contain only digits')
		.required('Contact number is required'),
	cSinkManagerCompanyLogo: Yup.string().required('Company logo is required.'),
	cSinkManagerCompanyLogoUrl: Yup.string().optional(),
	signingAuthorityDetails: Yup.array()
		.of(
			Yup.object().shape({
				signingAuthorityName: Yup.string().required('Name is required'),
				signingAuthorityPosition: Yup.string().required('Position is required'),
				signingAuthoritySignature: Yup.string().required(
					'Signature is required'
				),
				signingAuthoritySignatureUrl: Yup.string().optional(),
			})
		)
		.min(1, 'At least 1 signing authority required'),
})

export type SigningAuthority = {
	signingAuthorityName?: string
	signingAuthoritySignatureId?: string
	signingAuthoritySignatureUrl?: string
	signingAuthorityPosition?: string
	signingAuthoritySignature?: string
}

export type CertificateFormData = {
	cSinkManagerId?: string
	cSinkManagerCompanyLogoUrl?: string
	CSinkManagerCompanyName?: string
	cSinkManagerCompanyLogo?: string
	cSinkManagerCompanyName?: string
	email?: string
	countryCode?: string
	phoneNumber?: string
	signingAuthorityDetails?: SigningAuthority[]
}

export type TGenerateCertificateSchema=Yup.InferType<typeof CertificateSchema>