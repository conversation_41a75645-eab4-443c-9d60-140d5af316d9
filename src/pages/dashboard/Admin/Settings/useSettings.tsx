import { authAxios, useAuthContext } from '@/contexts'
import { settingsTab, userRoles } from '@/utils/constant'
import { useQuery } from '@tanstack/react-query'
import { useCallback } from 'react'
import { useSearchParams } from 'react-router-dom'
import { defaultLimit, defaultPage } from '@/utils/constant'
import { GlobalKilnResponse } from '@/interfaces'

const getApiforGlobalKilns = (accountType?: userRoles, networkId?: string) => {
	switch (accountType) {
		case userRoles.BiomassAggregator:
			return `/biomass-aggregator/${networkId}/kiln-template`
		case userRoles.CsinkManager:
			return `/csink-manager/${networkId}/settings/kiln-template`
		case userRoles.Admin:
		default:
			return `/settings/kiln-template`
	}
}
const getApiforMeasuringContainers = (
	accountType?: userRoles,
	networkId?: string
) => {
	switch (accountType) {
		case userRoles.BiomassAggregator:
			return `/biomass-aggregator/${networkId}/measuring-container-template`
		case userRoles.CsinkManager:
			return `/csink-manager/${networkId}/settings/measuring-container-template`
		case userRoles.Admin:
		default:
			return `/settings/measuring-container-template`
	}
}
const getAllGlobalKilns = (
	paramsLimit: string,
	paramsPage: string,
	accountType: string | undefined,
	csinkManagerId: string | undefined,
	baId: string | undefined
) => {
	const queryParams = new URLSearchParams({
		limit: paramsLimit,
		page: paramsPage,
	})

	const endpoint = `${getApiforGlobalKilns(
		accountType as userRoles,
		accountType === userRoles.CsinkManager ? csinkManagerId : baId
	)}?${queryParams}`
	const response = authAxios.get<GlobalKilnResponse>(endpoint)

	return response
}

const getAllGlobalMeasuringContainers = (
	paramsLimit: string,
	paramsPage: string,
	accountType: string | undefined,
	csinkManagerId: string | undefined,
	baId: string | undefined
) => {
	const queryParams = new URLSearchParams({
		limit: paramsLimit,
		page: paramsPage,
	})

	const endpoint = `${getApiforMeasuringContainers(
		accountType as userRoles,
		accountType === userRoles.CsinkManager ? csinkManagerId : baId
	)}?${queryParams}`
	const response = authAxios.get(endpoint)

	return response
}

export const useSettings = () => {
	const { userDetails } = useAuthContext()
	const [searchParams, setSearchParams] = useSearchParams()

	const paramsLimit = searchParams.get('limit') || defaultLimit.toString()
	const paramsPage = searchParams.get('page') || defaultPage.toString()

	const paramsTab = searchParams.get('tab') ?? settingsTab.kiln

	const allKilns = useQuery({
		queryKey: ['allKilns', paramsLimit, paramsPage, userDetails],
		queryFn: () => {
			return getAllGlobalKilns(
				paramsLimit,
				paramsPage,
				userDetails?.accountType,
				userDetails?.csinkManagerId,
				userDetails?.biomassAggregatorId
			)
		},
		select: ({ data }) => data,
		enabled: true,
	})

	const allMeasuringContainer = useQuery({
		queryKey: ['allMeasuringContainer', paramsLimit, paramsPage, userDetails],
		queryFn: () => {
			return getAllGlobalMeasuringContainers(
				paramsLimit,
				paramsPage,
				userDetails?.accountType,
				userDetails?.csinkManagerId,
				userDetails?.biomassAggregatorId
			)
		},
		select: ({ data }) => data,
		enabled: true,
	})

	const handleTabChange = useCallback(
		(_: unknown, newValue: settingsTab) => {
			// reset()
			setSearchParams(
				(prev) => ({
					...prev,
					tab: newValue,
				}),
				{ replace: true }
			)
		},
		[setSearchParams]
	)

	return {
		paramsTab,
		userDetails,
		allKilns,
		allMeasuringContainerContext: allMeasuringContainer,
		allMeasuringContainers: allMeasuringContainer.data,
		handleTabChange,
	}
}
