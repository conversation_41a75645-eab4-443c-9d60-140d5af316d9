import { <PERSON><PERSON>, <PERSON><PERSON><PERSON>, <PERSON><PERSON>, styled, Tab } from '@mui/material'
import {
	ActionInformationDrawer,
	AddKiln,
	CustomHeader,
	TwoColumnLayout,
} from '@/components'
import { settingsTab, userRoles, userRolesName } from '@/utils/constant'
import { TabContext, Tab<PERSON>ist, TabPanel } from '@mui/lab'
import { theme } from '@/lib/theme/theme'
import { useSettings } from './useSettings'
import { KilnTab, MeasuringContainerTab } from './components'
import { AddRounded } from '@mui/icons-material'
import { useMemo, useState } from 'react'
import { AddContainer } from '@/components/AddContainer'
import GenerateCertificate from './components/GenerateCertificate'
import { AddConstants } from '../AddConstants'

export const Settings = () => {
	const { paramsTab, userDetails, handleTabChange } = useSettings()
	const [isOpenDrawer, setIsOpenDrawer] = useState(false)

	const tabs = useMemo(() => {
		return [
			{
				label: 'Kilns',
				value: settingsTab.kiln,
				hidden: false,
			},
			{
				label: 'Measuring Container',
				value: settingsTab.measuringContainer,
				hidden: false,
			},
			// {
			// 	label: 'Sampling Container',
			// 	value: settingsTab.samplingContainer,
			// 	hidden: false,
			// },
			...([userRoles.Admin, userRoles.CsinkManager].includes(
				userDetails?.accountType as userRoles
			)
				? [
						{
							label: 'Generate Certificate',
							value: settingsTab.generateCertificate,
							hidden: false,
						},
						{
							label: 'Add Constants',
							value: settingsTab.addConstants,
							hidden: false,
						},
				  ]
				: []),
		]
	}, [userDetails])

	const handelOpenDrawer = () => {
		setIsOpenDrawer(!isOpenDrawer)
	}

	const handleCloseDrawer = () => {
		setIsOpenDrawer(false)
	}

	const HeaderEndButtons = () => {
		return (
			<Stack direction='row' spacing={2}>
				{[settingsTab.kiln, settingsTab.measuringContainer].includes(
					paramsTab as settingsTab
				) ? (
					<Button
						onClick={handelOpenDrawer}
						variant='contained'
						size='small'
						startIcon={<AddRounded />}>
						{paramsTab === settingsTab.kiln
							? 'Add Kiln Template'
							: 'Add Measuring Container Template'}
					</Button>
				) : null}
			</Stack>
		)
	}

	return (
		<>
			<ActionInformationDrawer
				open={isOpenDrawer && paramsTab === settingsTab.kiln}
				onClose={handleCloseDrawer}
				anchor='right'
				component={
					<AddKiln
						handleClose={() => setIsOpenDrawer(false)}
						subheading={`Global Kiln for ${
							userDetails?.accountType &&
							userRolesName[
								userDetails?.accountType as keyof typeof userRolesName
							]
								? userRolesName[
										userDetails?.accountType as keyof typeof userRolesName
								  ]
								: 'Admin'
						}`}
						isGlobalAddKiln={true}
					/>
				}
			/>
			<ActionInformationDrawer
				open={isOpenDrawer && paramsTab === settingsTab.measuringContainer}
				onClose={handleCloseDrawer}
				anchor='right'
				component={
					<AddContainer
						subheading={`Global Measuring Container for ${
							userDetails?.accountType &&
							userRolesName[
								userDetails?.accountType as keyof typeof userRolesName
							]
								? userRolesName[
										userDetails?.accountType as keyof typeof userRolesName
								  ]
								: 'Admin'
						}`}
						handleCloseDrawer={handleCloseDrawer}
						isGlobal={true}
					/>
				}
			/>
			<ActionInformationDrawer
				open={isOpenDrawer && paramsTab === settingsTab.samplingContainer}
				onClose={handleCloseDrawer}
				anchor='right'
				component={<h1>Sample Container Drawer</h1>}
			/>
			<CustomizedWrapperSettings>
				<CustomHeader
					showBottomBorder={true}
					heading='Settings'
					endComponent={<HeaderEndButtons />}
				/>
				<Divider />
				<Stack padding={theme.spacing(2)}>
					<TabContext value={paramsTab}>
						<TabList className='tabList' onChange={handleTabChange}>
							{tabs.map(
								({ label, value, hidden }, index) =>
									!hidden && <Tab key={index} label={label} value={value} />
							)}
						</TabList>
						<TabPanel value={settingsTab.kiln} sx={{ padding: 0 }}>
							<KilnTab />
						</TabPanel>
						<TabPanel
							value={settingsTab.measuringContainer}
							sx={{ padding: 0 }}>
							<MeasuringContainerTab />
						</TabPanel>
						<TabPanel value={settingsTab.samplingContainer} sx={{ padding: 0 }}>
							<TwoColumnLayout
								left={
									<Stack className='content-section'>
										Sampling Container Tab
									</Stack>
								}
								right={<></>}
								gridBreakpoints={[7, 5]}
							/>
						</TabPanel>
						<TabPanel
							value={settingsTab.generateCertificate}
							sx={{ padding: 0 }}>
							<GenerateCertificate />
						</TabPanel>
						<TabPanel value={settingsTab.addConstants} sx={{ padding: 0 }}>
							{/* <GenerateCertificate /> */}
							<AddConstants />
						</TabPanel>
					</TabContext>
				</Stack>
			</CustomizedWrapperSettings>
		</>
	)
}

export const CustomizedWrapperSettings = styled(Stack)(({ theme }) => ({
	gap: theme.spacing(2),
	paddingTop: theme.spacing(4),
	'.content-section': {
		gap: theme.spacing(3),
		padding: theme.spacing(3),
		height: '100%',
		'.container': {
			'.labelHeading': {
				...theme.typography.body2,
			},
			'.buttonContainer': {
				flexDirection: 'row',
				justifyContent: 'center',
			},
		},
	},
	'.credit-constant-container': {
		flexDirection: 'column',
		gap: theme.spacing(4),
		'.buttonContainer': {
			flexDirection: 'row',
			justifyContent: 'center',
		},
	},
	'.history-fields': {
		flexDirection: 'row',
		gap: theme.spacing(2),
	},
}))
