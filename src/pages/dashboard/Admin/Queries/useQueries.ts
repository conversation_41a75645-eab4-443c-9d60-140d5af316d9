import { authAxios } from '@/contexts'
import { defaultLimit, defaultPage } from '@/utils/constant'
import { useQuery } from '@tanstack/react-query'
import { useSearchParams } from 'react-router-dom'

export const useQueries = () => {
	const [searchParams] = useSearchParams()
	const paramsLimit = searchParams.get('limit') || defaultLimit.toString()
	const paramsPage = searchParams.get('page') || defaultPage.toString()
	const search = searchParams.get('search') || ''

	const allQueries = useQuery({
		queryKey: ['allQueries', paramsLimit, paramsPage, search],
		queryFn: async () => {
			const queryParams = new URLSearchParams({
				limit: paramsLimit,
				page: paramsPage,
				search,
			})
			const { data } = await authAxios.get(`/enquiry?${queryParams.toString()}`)
			return data
		},
		enabled: true,
	})

	return {
		allQueries,
	}
}
