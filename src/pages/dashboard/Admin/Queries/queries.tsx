import { CustomTable } from '@/components/CustomTable'
import { Search } from '@mui/icons-material'
import { Chip, Stack, styled, Typography } from '@mui/material'
import {
	GridColDef,
	GridRenderCellParams,
	GridValidRowModel,
} from '@mui/x-data-grid'
import { format } from 'date-fns'
import { useCallback, useMemo } from 'react'
import { QueryInput } from '../../../../components'
import { useQueries } from './useQueries'

export const Queries = () => {
	// const theme = useTheme()
	const { allQueries } = useQueries()

	const DataColumn: GridColDef<GridValidRowModel>[] = useMemo(
		() => [
			{
				field: 'name',
				headerName: 'Name',
				minWidth: 150,
				flex: 1,

				renderCell: (params: GridRenderCellParams) => {
					return (
						<Typography textTransform='capitalize'>
							{!params?.row?.firstName && !params?.row?.lastName
								? '-'
								: `${params?.row?.firstName} ${params?.row?.lastName}`}
						</Typography>
					)
				},
			},
			{
				field: 'email',
				headerName: 'Email',
				minWidth: 180,
				flex: 1,

				renderCell: (params: GridRenderCellParams) => {
					return <Typography>{params.row.email ?? ''}</Typography>
				},
			},
			{
				field: 'phoneNumber',
				headerName: 'Phone Number',
				minWidth: 180,
				flex: 1,

				renderCell: (params: GridRenderCellParams) => {
					return (
						<Typography>
							{params?.row?.countryCode && params?.row?.phoneNo
								? `${params?.row?.countryCode}${params?.row?.phoneNo}`
								: '-'}
						</Typography>
					)
				},
			},
			{
				field: 'QueryTopic',
				headerName: 'Query Topic For?',
				minWidth: 150,
				flex: 1,
				maxWidth: 300,
				renderCell: (params) =>
					(params?.row?.QueryTopic ?? [])?.length < 1 ? (
						'-'
					) : (
						<Stack direction='row' columnGap={1} rowGap={2} flexWrap='wrap'>
							{params?.row?.QueryTopic?.map((query: string, idx: number) => (
								<Chip key={idx} label={query} />
							))}
						</Stack>
					),
			},
			{
				field: 'query',
				headerName: 'Description',
				minWidth: 200,
				flex: 1,

				renderCell: (params: GridRenderCellParams) => {
					return (
						<Typography className='first_letter_capitalize'>
							{params?.value
								? `${params?.value?.slice(0, 80)}${
										params?.value?.length > 80 ? '...' : ''
								  }`
								: '-'}
						</Typography>
					)
				},
			},
			{
				field: 'createdAt',
				headerName: 'Date',
				minWidth: 120,
				flex: 1,

				renderCell: (params: GridRenderCellParams) => {
					return (
						<Typography>
							{params?.value
								? `${format(new Date(params?.value), 'dd.MMM.yyyy')} ${format(
										new Date(params?.value),
										'hh:mm:ss'
								  )}`
								: '-'}
						</Typography>
					)
				},
			},
		],
		[]
	)

	const handleRowClick = useCallback((params: any) => {
		params.setOpen(!params?.open)
		const collapseDetails = {
			description: params?.row?.query,
		}
		params.setHookData(collapseDetails)
	}, [])

	return (
		<StyledContainer>
			<Stack className='container'>
				<CustomTable
					columns={DataColumn}
					count={allQueries?.data?.count}
					rows={allQueries.data?.enquiry || []}
					isComponent
					component='description'
					handleRowClick={handleRowClick}
					showPagination
					showPaginationDetails
					headerComponent={<GridHeaderComponents />}
					isLoading={allQueries?.isPending}
				/>
			</Stack>
		</StyledContainer>
	)
}

const GridHeaderComponents = () => {
	return (
		<Stack className='grid-header-component' columnGap={2}>
			<QueryInput
				className='search-textFiled'
				queryKey='search'
				placeholder='Search'
				setPageOnSearch
				InputProps={{
					startAdornment: <Search fontSize='small' />,
				}}
			/>
		</Stack>
	)
}

const StyledContainer = styled(Stack)(({ theme }) => ({
	gap: theme.spacing(2),
	'.header': {
		padding: theme.spacing(4, 0),
		borderBottom: `${theme.spacing(0.125)} solid ${
			theme.palette.neutral['100']
		}`,
	},
	'.container': {
		padding: theme.spacing(0, 3),
		gap: theme.spacing(2),
		'.grid-header-component': {
			flexDirection: 'row',
			alignItems: 'center',
			'.search-textFiled': {
				width: 334,
				'.MuiInputBase-root': {
					height: theme.spacing(4.5),
					borderRadius: theme.spacing(1.25),
				},
			},
		},
	},
}))
