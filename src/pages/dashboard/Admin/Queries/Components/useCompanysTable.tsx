import { authAxios } from '@/contexts'
import { CompanyListResponse } from '@/types'

import { defaultLimit, defaultPage } from '@/utils/constant'
import { useMutation, useQuery, useQueryClient } from '@tanstack/react-query'
import { AxiosError } from 'axios'
import { useSearchParams } from 'react-router-dom'
import { toast } from 'react-toastify'

export const useCompanysTable = () => {
	const [searchParams] = useSearchParams()
	const paramsLimit = searchParams.get('limit') || defaultLimit.toString()
	const paramsPage = searchParams.get('page') || defaultPage.toString()
	const queryClient = useQueryClient()

	const allCompanys = useQuery({
		queryKey: ['allCompanys', paramsLimit, paramsPage],
		queryFn: async () => {
			const queryParams = new URLSearchParams({
				limit: paramsLimit,
				page: paramsPage,
			})
			const { data } = await authAxios.get<CompanyListResponse>(
				`/company/request?${queryParams.toString()}`
			)
			return data
		},
		enabled: true,
	})

	const assessCompanyRequestMutation = useMutation({
		mutationKey: ['assessCompanyRequestMutation'],
		mutationFn: async ({ id, status }: { id: string; status: string }) => {
			const payload = {
				status: status,
			}
			return authAxios.post(`/company/request/${id}/assess`, payload)
		},
		onSuccess: (data) => {
			toast(data?.data?.message)
			queryClient.refetchQueries({ queryKey: ['allCompanys'] })
		},
		onError: (err: AxiosError) => {
			toast((err?.response?.data as { messageToUser: string })?.messageToUser)
		},
	})

	return {
		paramsLimit,
		allCompanys,
		assessCompanyRequestMutation,
	}
}
