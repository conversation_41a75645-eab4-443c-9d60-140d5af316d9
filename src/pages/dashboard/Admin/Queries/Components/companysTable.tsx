import { But<PERSON>, <PERSON><PERSON>, Typography } from '@mui/material'
import {
	GridColDef,
	GridRenderCellParams,
	GridValidRowModel,
} from '@mui/x-data-grid'
import { useMemo } from 'react'
import { useCompanysTable } from './useCompanysTable'
import { getSerialNumber } from '@/utils/helper'
import { ArrowLeftRounded } from '@mui/icons-material'
import { CustomDataGrid, CustomHeader } from '@/components'
import { theme } from '@/lib/theme/theme'
import { useNavigate } from 'react-router-dom'
import { format } from 'date-fns'

export const CompanysTable = () => {
	const { allCompanys, paramsLimit, assessCompanyRequestMutation } =
		useCompanysTable()
	const navigate = useNavigate()

	const DataColumn: GridColDef<GridValidRowModel>[] = useMemo(
		() => [
			{
				field: 'serialNo',
				headerName: `Serial No`,
				minWidth: 150,

				renderCell: (params) => {
					return (
						<Typography variant='subtitle1' sx={{ paddingX: 2 }}>
							{getSerialNumber(params, Number(paramsLimit))}
						</Typography>
					)
				},
			},
			{
				field: 'name',
				headerName: 'Name',
				minWidth: 150,
				flex: 1,

				renderCell: (params: GridRenderCellParams) => {
					return (
						<Typography textTransform='capitalize'>
							{!params?.row?.name ? '-' : `${params?.row?.name}`}
						</Typography>
					)
				},
			},
			{
				field: 'createdAt',
				headerName: 'Created At',
				minWidth: 150,
				flex: 1,
				renderCell: (params) => (
					<Typography variant='subtitle1'>
						{format(new Date(params?.value), 'yyyy-MM-dd')}
					</Typography>
				),
			},
			{
				field: 'email',
				headerName: 'Email',
				minWidth: 180,
				flex: 1,

				renderCell: (params: GridRenderCellParams) => {
					return <Typography>{params.row.email ?? ''}</Typography>
				},
			},
			{
				field: 'phoneNumber',
				headerName: 'Phone Number',
				minWidth: 180,
				flex: 1,

				renderCell: (params: GridRenderCellParams) => {
					return (
						<Typography>
							{params?.row?.countryCode && params?.row?.phoneNumber
								? `${params?.row?.countryCode}${params?.row?.phoneNumber}`
								: '-'}
						</Typography>
					)
				},
			},
			{
				field: 'Approve',
				headerName: 'Approve',
				minWidth: 120,
				flex: 1,
				renderCell: (params: GridRenderCellParams) => {
					return (
						<Stack sx={{ padding: 1 }}>
							<Button
								sx={{ Padding: '10px' }}
								variant='contained'
								color='primary'
								onClick={() =>
									assessCompanyRequestMutation.mutate({
										id: params.row.id,
										status: 'approved',
									})
								}>
								Approve
							</Button>
						</Stack>
					)
				},
			},
			{
				field: 'Reject',
				headerName: 'Reject',
				minWidth: 120,
				flex: 1,
				renderCell: (params: GridRenderCellParams) => {
					return (
						<Stack sx={{ padding: 1 }}>
							<Button
								variant='outlined'
								onClick={() =>
									assessCompanyRequestMutation.mutate({
										id: params.row.id,
										status: 'rejected',
									})
								}>
								Reject
							</Button>
						</Stack>
					)
				},
			},
		],
		[assessCompanyRequestMutation, paramsLimit]
	)
	return (
		<Stack className='container'>
			<CustomHeader
				showBottomBorder={false}
				headingComponent={
					<Stack sx={{ paddingY: 3 }} direction='row' alignItems='center'>
						<Button
							variant='text'
							startIcon={<ArrowLeftRounded />}
							sx={{
								padding: 0,
								...theme.typography.body1,
								color: theme.palette.neutral['300'],
							}}
							onClick={() => navigate(-1)}>
							Queries
						</Button>
						<Typography variant='h6' color={theme.palette.neutral['500']}>
							&nbsp;/ Requests
						</Typography>
					</Stack>
				}
			/>
			<CustomDataGrid
				sx={{ padding: 1 }}
				loading={allCompanys?.isPending}
				showPagination={true}
				rows={allCompanys.data?.requests || []}
				columns={DataColumn}
				rowCount={allCompanys?.data?.count ?? 0}
				headerComponent={
					<Stack sx={{ padding: 2 }} gap={2} flexWrap='wrap'>
						<Typography variant='h6'>
							Company Registerations Requests
						</Typography>
					</Stack>
				}
			/>
		</Stack>
	)
}
