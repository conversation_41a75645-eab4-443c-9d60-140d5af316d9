import { FC } from 'react'
import { useFormContext } from 'react-hook-form'
import { TCreateProject } from '../schema'
import { FormControl, FormHelperText, Stack } from '@mui/material'
import { MarkdownEditor } from './MarkdownEditor'

export const Details: FC<{ editable: boolean }> = ({ editable }) => {
	const {
		watch,
		formState: { errors },
		clearErrors,
		setValue,
	} = useFormContext<TCreateProject>()
	return (
		<Stack direction='column' gap={2}>
			<Stack direction='row' gap={3}>
				<Stack width='50%'>
					<FormControl>
						<MarkdownEditor
							placeholder='Annual Carbon Removal Capacity'
							value={watch('projectDetails.annualCarbonRemoval') || ''}
							onChange={(markdown) => {
								setValue('projectDetails.annualCarbonRemoval', markdown)
								clearErrors('projectDetails.annualCarbonRemoval')
							}}
							editMode={editable}
						/>
						<FormHelperText
							error={!!errors?.projectDetails?.annualCarbonRemoval?.message}>
							{errors?.projectDetails?.annualCarbonRemoval?.message}
						</FormHelperText>
					</FormControl>
				</Stack>
				<Stack width='50%'>
					<FormControl>
						<MarkdownEditor
							placeholder='Application Details'
							value={watch('projectDetails.applicationDetails') || ''}
							onChange={(markdown) => {
								setValue('projectDetails.applicationDetails', markdown)
								clearErrors('projectDetails.applicationDetails')
							}}
							editMode={editable}
						/>
						<FormHelperText
							error={!!errors?.projectDetails?.applicationDetails?.message}>
							{errors?.projectDetails?.applicationDetails?.message}
						</FormHelperText>
					</FormControl>
				</Stack>
			</Stack>
			<Stack direction='row' gap={3}>
				<Stack width='50%'>
					<FormControl>
						<MarkdownEditor
							placeholder='Methane mitigation strategy details'
							value={watch('projectDetails.methaneMitigationDetails') || ''}
							onChange={(markdown) => {
								setValue('projectDetails.methaneMitigationDetails', markdown)
								clearErrors('projectDetails.methaneMitigationDetails')
							}}
							editMode={editable}
						/>
						<FormHelperText
							error={
								!!errors?.projectDetails?.methaneMitigationDetails?.message
							}>
							{errors?.projectDetails?.methaneMitigationDetails?.message}
						</FormHelperText>
					</FormControl>
				</Stack>
			</Stack>
		</Stack>
	)
}
