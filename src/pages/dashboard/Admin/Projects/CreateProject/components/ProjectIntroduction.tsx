import { DatePicker } from '@mui/x-date-pickers/DatePicker'
import {
	Box,
	Chip,
	FormControl,
	FormHelperText,
	InputAdornment,
	InputLabel,
	ListItemIcon,
	MenuItem,
	OutlinedInput,
	Select,
	SelectChangeEvent,
	Stack,
	styled,
	TextField,
	Typography,
} from '@mui/material'
import React, { FC, useCallback } from 'react'
import { useFormContext, useWatch } from 'react-hook-form'
import moment from 'moment'
import { IDevelopmentGoalsList } from '@/interfaces'
import { useQuery } from '@tanstack/react-query'
import { authAxios } from '@/contexts'
import { toast } from 'react-toastify'
import { createProjectSchema, TCreateProject } from '../schema'
import { LocalizationProvider } from '@mui/x-date-pickers/LocalizationProvider'
import { AdapterMoment } from '@mui/x-date-pickers/AdapterMoment'
import { theme } from '@/lib/theme/theme'
import { CustomTextField } from '@/utils/components'

const projectStatusOptions = [
	'active reserved',

	'active sold out',

	'active available',

	'upcoming reserved',

	'upcoming sold out',

	'upcoming available',
]

const ITEM_HEIGHT = 48
const ITEM_PADDING_TOP = 8
const MenuProps = {
	PaperProps: {
		style: {
			maxHeight: ITEM_HEIGHT * 4.5 + ITEM_PADDING_TOP,
			width: 250,
		},
	},
}

export const ProjectIntroduction: FC<{ editable: boolean }> = ({
	editable,
}) => {
	const {
		register,
		watch,
		formState: { errors },
		clearErrors,
		getValues,
		setError,
		setValue,
		control,
	} = useFormContext<TCreateProject>()

	const getGoalsIcon = (fileName: string) =>
		`/images/un_goals_icons/${fileName}`

	const projectIntroValues = useWatch({
		control: control,
		name: 'projectIntroduction',
	})

	const handleStatusChange = useCallback(
		(e: React.ChangeEvent<HTMLInputElement | HTMLTextAreaElement>) => {
			setValue('projectIntroduction.nextAvailableDate', undefined)
			setValue('projectIntroduction.projectStatus', e.target.value)
			if (
				(e.target.value === 'active reserved' ||
					e.target.value === 'upcoming reserved') &&
				projectIntroValues?.publishedStatus === 'publish'
			) {
				setError('projectIntroduction.nextAvailableDate', {
					message: 'This Field is Required',
				})
			} else {
				clearErrors('projectIntroduction.nextAvailableDate')
			}
		},
		[setValue, projectIntroValues?.publishedStatus, setError, clearErrors]
	)

	const unDevelopmentGoalList = useQuery({
		queryKey: ['unDevelopmentGoalList'],
		queryFn: async () => {
			try {
				const { data } = await authAxios.get<IDevelopmentGoalsList[]>(
					`/un-goals`
				)
				return data
			} catch (err: any) {
				toast(err.response?.data?.messageToUser)
				return []
			}
		},
	})

	const handleGoalsChange = (event: SelectChangeEvent<number[]>) => {
		const {
			target: { value },
		} = event
		setValue('projectIntroduction.developmentGoals', value as number[])
	}

	return (
		<Stack direction='row' gap={2}>
			<Stack gap={3} width='50%'>
				<Stack direction='row' gap={2}>
					<FormControl fullWidth>
						<TextField
							id='SeoNameUrl'
							label='SEO Name URL'
							variant='outlined'
							{...(editable &&
								!watch('projectIntroduction.seoNameUrl') && {
									placeholder: watch('projectIntroduction.name') || '',
									focused: true,
								})}
							{...(editable && {
								InputLabelProps: {
									shrink: true,
								},
							})}
							{...register('projectIntroduction.seoNameUrl')}
							value={watch('projectIntroduction.seoNameUrl')}
							onChange={(event) => {
								const { value } = event.target
								const sanitizedValue = value
									.replace(/[^a-zA-Z0-9 _-]/g, '')
									.replace(/^\s+/, '')
								setValue('projectIntroduction.seoNameUrl', sanitizedValue)
							}}
							fullWidth
							error={!!errors?.projectIntroduction?.seoNameUrl}
							helperText={errors?.projectIntroduction?.seoNameUrl?.message}
						/>
					</FormControl>
					<TextField
						label='Project Name'
						variant='outlined'
						{...register('projectIntroduction.name')}
						fullWidth
						InputLabelProps={{
							shrink: !!watch('projectIntroduction.name'),
						}}
						error={!!errors.projectIntroduction?.name}
						helperText={errors?.projectIntroduction?.name?.message}
					/>
				</Stack>
				<Stack direction='row' gap={2}>
					<CustomTextField
						watch={watch}
						type='number'
						id='available'
						hideNumberArrows
						schema={createProjectSchema}
						fullWidth
						helperText={errors?.projectIntroduction?.availableCredits?.message}
						error={!!errors?.projectIntroduction?.availableCredits}
						label='Available Credits'
						InputProps={{
							endAdornment: (
								<InputAdornment position='end'>
									tCO<sub style={{ marginTop: '10px' }}>2</sub>e
								</InputAdornment>
							),
						}}
						{...register('projectIntroduction.availableCredits', {
							valueAsNumber: true,
						})}
						InputLabelProps={{
							shrink: watch('projectIntroduction.availableCredits') == 0,
						}}
					/>

					<CustomTextField
						watch={watch}
						type='number'
						id='rate'
						hideNumberArrows
						schema={createProjectSchema}
						fullWidth
						error={!!errors?.projectIntroduction?.rate}
						helperText={errors?.projectIntroduction?.rate?.message}
						label='Project Rate'
						{...register('projectIntroduction.rate', {
							valueAsNumber: true,
						})}
						InputProps={{
							endAdornment: (
								<InputAdornment position='end'>
									$/tCO<sub style={{ marginTop: '10px' }}>2</sub>e
								</InputAdornment>
							),
						}}
						InputLabelProps={{
							shrink: watch('projectIntroduction.rate') == 0,
						}}
					/>
				</Stack>
				<Stack direction='row' gap={2}>
					<TextField
						id='technology'
						label='Technology'
						variant='outlined'
						{...register('projectIntroduction.technology')}
						fullWidth
						error={!!errors?.projectIntroduction?.technology}
						helperText={errors?.projectIntroduction?.technology?.message}
						InputLabelProps={{
							shrink: !!watch('projectIntroduction.technology'),
						}}
					/>
					<TextField
						id='application'
						label='Application'
						variant='outlined'
						{...register('projectIntroduction.application')}
						fullWidth
						error={!!errors?.projectIntroduction?.application}
						helperText={errors?.projectIntroduction?.application?.message}
						InputLabelProps={{
							shrink: !!watch('projectIntroduction.application'),
						}}
					/>
				</Stack>
				<Stack flexDirection='row' gap={2}>
					{['active reserved', 'upcoming reserved'].includes(
						watch('projectIntroduction.projectStatus') ?? ''
					) ? (
						<FormControl fullWidth>
							<LocalizationProvider dateAdapter={AdapterMoment}>
								<DatePicker
									label='Next Available Date'
									value={
										getValues('projectIntroduction.nextAvailableDate')
											? moment(
													getValues('projectIntroduction.nextAvailableDate')
											  )
											: null
									}
									onChange={(newValue) => {
										setValue(
											'projectIntroduction.nextAvailableDate',
											moment(newValue).set('hour', 11).toDate()
										)
										if (
											!newValue &&
											projectIntroValues?.publishedStatus === 'publish'
										) {
											setError('projectIntroduction.nextAvailableDate', {
												message: 'This Field is required',
											})
										} else {
											clearErrors('projectIntroduction.nextAvailableDate')
										}
									}}
									format='DD/MM/YYYY'
									slotProps={{
										layout: {
											sx: {
												'.MuiPickersDay-root': {
													color: 'black',
													fontWeight: 450,
												},
												'.MuiPickersDay-root.Mui-selected': {
													color: 'white',
												},
												'.MuiPickersDay-root.Mui-disabled': {
													color: 'gray',
												},
											},
										},
									}}
								/>
							</LocalizationProvider>

							<FormHelperText
								error={
									!!errors?.projectIntroduction?.nextAvailableDate?.message
								}>
								{errors?.projectIntroduction?.nextAvailableDate?.message}
							</FormHelperText>
						</FormControl>
					) : null}
					<FormControl fullWidth>
						<TextField
							id='methaneMitigation'
							label='Methane Mitigation Strategy'
							variant='outlined'
							{...register('projectIntroduction.methaneMitigation')}
							fullWidth
							error={!!errors?.projectIntroduction?.methaneMitigation}
							helperText={
								errors?.projectIntroduction?.methaneMitigation?.message
							}
							InputLabelProps={{
								shrink: !!watch('projectIntroduction.methaneMitigation'),
							}}
						/>
					</FormControl>
				</Stack>
			</Stack>
			<Stack gap={3} width='50%'>
				<Stack direction='row' gap={2}>
					<TextField
						fullWidth
						select
						label='Project Status'
						{...register('projectIntroduction.projectStatus')}
						value={watch('projectIntroduction.projectStatus')}
						onChange={(e) => {
							handleStatusChange(e)
						}}
						id='select-project-status'
						error={!!errors?.projectIntroduction?.projectStatus}
						helperText={errors?.projectIntroduction?.projectStatus?.message}>
						{projectStatusOptions?.map((item, index) => (
							<MenuItem key={index} value={item}>
								<Typography textTransform='capitalize'>{item}</Typography>
							</MenuItem>
						))}
					</TextField>
					<CustomTextField
						watch={watch}
						type='number'
						id='capacity'
						hideNumberArrows
						schema={createProjectSchema}
						fullWidth
						error={!!errors?.projectIntroduction?.totalCredits}
						helperText={errors?.projectIntroduction?.totalCredits?.message}
						label='Credits Issued'
						{...register('projectIntroduction.totalCredits', {
							valueAsNumber: true,
						})}
						InputProps={{
							endAdornment: (
								<InputAdornment position='end'>
									tCO<sub style={{ marginTop: '10px' }}>2</sub>e
								</InputAdornment>
							),
						}}
						InputLabelProps={{
							shrink: watch('projectIntroduction.totalCredits') == 0,
						}}
					/>
				</Stack>
				<Stack direction='row' gap={2}>
					<TextField
						id='address'
						label='Project Address'
						{...register('projectIntroduction.address')}
						variant='outlined'
						fullWidth
						error={!!errors?.projectIntroduction?.address}
						helperText={errors?.projectIntroduction?.address?.message}
						InputLabelProps={{
							shrink: !!watch('projectIntroduction.address'),
						}}
					/>
					<TextField
						id='locationMap'
						label='Location Url'
						variant='outlined'
						{...register('projectIntroduction.projectMapLocationURL')}
						fullWidth
						error={!!errors?.projectIntroduction?.projectMapLocationURL}
						helperText={
							errors?.projectIntroduction?.projectMapLocationURL?.message
						}
						InputLabelProps={{
							shrink: !!watch('projectIntroduction.projectMapLocationURL'),
						}}
					/>
				</Stack>
				<Stack direction='row' gap={2}>
					<FormControl fullWidth>
						<InputLabel id='demo-multiple-checkbox-label'>
							United Nations sustainable development goals
						</InputLabel>
						<Select
							labelId='demo-multiple-checkbox-label'
							value={
								(watch('projectIntroduction.developmentGoals') ??
									[]) as number[]
							}
							multiple
							onChange={handleGoalsChange}
							input={
								<OutlinedInput label='United Nations sustainable development goals' />
							}
							sx={{
								height: theme.spacing(7),
								padding: theme.spacing(0.7),
								'.MuiSelect-select': {
									padding: 0,
								},
							}}
							renderValue={(selected) => (
								<StyledChipContainer>
									{selected.map((value, index) => {
										const matchedItem = unDevelopmentGoalList?.data?.find(
											(item) => value === item?.id
										)
										return matchedItem ? (
											<Chip
												key={`${value}-${index}`}
												label={matchedItem?.name}
												size='small'
												className='selectedChip'
											/>
										) : null
									})}
								</StyledChipContainer>
							)}
							MenuProps={MenuProps}>
							{unDevelopmentGoalList?.data?.map((item) => (
								<MenuItem key={item?.id} value={Number(item?.id)}>
									<ListItemIcon>
										<Box
											component='img'
											height={30}
											width={30}
											src={getGoalsIcon(item?.iconFilename ?? '')}
										/>
									</ListItemIcon>
									{item?.name}
								</MenuItem>
							))}
						</Select>
						<FormHelperText
							error={!!errors?.projectIntroduction?.developmentGoals}>
							{errors?.projectIntroduction?.developmentGoals?.message}
						</FormHelperText>
					</FormControl>
					<FormControl fullWidth>
						<TextField
							id='Permanence'
							label='Permanence'
							variant='outlined'
							{...register('projectIntroduction.permanence')}
							fullWidth
							error={!!errors?.projectIntroduction?.permanence}
							helperText={errors?.projectIntroduction?.permanence?.message}
							InputLabelProps={{
								shrink: !!watch('projectIntroduction.permanence'),
							}}
						/>
					</FormControl>
				</Stack>
				<Stack>
					<TextField
						id='registryLink'
						label='Link to registry'
						variant='outlined'
						{...register('projectIntroduction.linkToRegistry')}
						fullWidth
						error={!!errors?.projectIntroduction?.linkToRegistry}
						helperText={errors?.projectIntroduction?.linkToRegistry?.message}
						InputLabelProps={{
							shrink: !!watch('projectIntroduction.linkToRegistry'),
						}}
					/>
				</Stack>
			</Stack>
		</Stack>
	)
}

// const StyledTextField = styled(TextField)(() => ({
// 	'& input::-webkit-outer-spin-button, & input::-webkit-inner-spin-button': {
// 		display: 'none',
// 	},
// 	'& input[type=number]': {
// 		MozAppearance: 'textfield',
// 	},
// }))

// export const SelectChips = styled(Select)(({ theme }) => ({
// 	height: theme.spacing(7),
// 	padding: theme.spacing(0.5),
// 	'.MuiSelect-select': {
// 		padding: 0,
// 	},
// }))

export const StyledChipContainer = styled(Box)(({ theme }) => ({
	display: 'flex',
	flexWrap: 'wrap',
	gap: theme.spacing(0.5),
	height: theme.spacing(5.5),
	overflowY: 'auto',
	overflowX: 'hidden',
	paddingRight: 0,

	'.selectedChip': {
		fontSize: theme.spacing(1.5),
		height: theme.spacing(3),
		borderRadius: theme.spacing(1.2),
	},
}))
