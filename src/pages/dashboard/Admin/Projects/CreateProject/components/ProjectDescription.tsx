import { FormHelperText, Stack } from '@mui/material'
import { useFormContext } from 'react-hook-form'
import { TCreateProject } from '../schema'
import { MarkdownEditor } from './MarkdownEditor'
import { FC } from 'react'

export const ProjectDescription: FC<{ editable: boolean }> = ({ editable }) => {
	const {
		watch,
		formState: { errors },
		clearErrors,
		setValue,
	} = useFormContext<TCreateProject>()
	return (
		<Stack direction='row' gap={2}>
			<Stack width='50%'>
				<MarkdownEditor
					placeholder='Short Description'
					value={watch('projectDescription.shortDescription') || ''}
					onChange={(markdown) => {
						setValue('projectDescription.shortDescription', markdown)
						clearErrors('projectDescription.shortDescription')
					}}
					editMode={editable}
				/>
				<FormHelperText error={!!errors?.projectDescription?.message}>
					{errors?.projectDescription?.message}
				</FormHelperText>
			</Stack>
			<Stack width='50%'>
				<MarkdownEditor
					placeholder='Project Description'
					value={watch('projectDescription.description') || ''}
					onChange={(markdown) => {
						setValue('projectDescription.description', markdown)
						clearErrors('projectDescription.description')
					}}
					editMode={editable}
				/>
				<FormHelperText
					error={!!errors?.projectDescription?.description?.message}>
					{errors?.projectDescription?.description?.message}
				</FormHelperText>
			</Stack>
		</Stack>
	)
}
