import { handleImageUpload } from '@/utils/helper'
import { AddCircleOutline } from '@mui/icons-material'
import { Box, FormHelperText, Typography, styled } from '@mui/material'
import { useCallback, useEffect, useState } from 'react'
import { useFormContext } from 'react-hook-form'
import { toast } from 'react-toastify'

export const AddProjectImages = ({
	index,
	imgUrl,
}: {
	index: number
	imgUrl: string
}) => {
	const {
		watch,
		setValue,
		formState: { errors },
		clearErrors,
	} = useFormContext()
	const [url, setUrl] = useState<string>(imgUrl)
	useEffect(() => {
		if (!imgUrl) return
		setUrl(imgUrl)
	}, [imgUrl])
	const uploadPicture = useCallback(
		async (event: any) => {
			const file = event.target.files[0]
			if (!file) return
			try {
				const data = await handleImageUpload(file)
				const updatedProjectImg = watch('projectImg')

				const updatedImages = watch('ProjectImages')
				console.log(watch('projectImages'))

				updatedImages[index] = data
				setValue('ProjectImages', updatedImages)
				updatedProjectImg[index] = data.id

				setValue('projectImg', updatedProjectImg)
				setUrl(data.url)
				clearErrors()
			} catch (err: any) {
				console.log(err)

				toast('Image upload failed')
			}
		},
		[clearErrors, index, setValue, watch]
	)

	return (
		<Box
			component='label'
			position='relative'
			sx={{
				display: 'flex',
				flexDirection: 'column',
				justifyContent: 'center',
				alignItems: 'center',
			}}>
			{url ? (
				<>
					<Box
						component='img'
						src={url}
						style={{ objectFit: 'contain' }}
						alt='Uploaded'
						className='image'
						height={161}
						width={161}
					/>
					<Box
						component='img'
						src='/images/editProject.svg'
						alt='editIcon'
						position='absolute'
						sx={{ cursor: 'pointer' }}
					/>
				</>
			) : (
				<StyledAddBox>
					<AddCircleOutline sx={{ color: 'grey.800' }} />
					<Typography>Image {index + 1}</Typography>
				</StyledAddBox>
			)}

			<input
				id={`header-imageInput-${index}`}
				type='file'
				accept='.png, .jpeg, .jpg, .heic'
				style={{ display: 'none' }}
				onChange={(e) => uploadPicture(e)}
			/>
			{!!errors.projectImg && (
				<FormHelperText
					sx={{ textAlign: 'center' }}
					error={!!errors.projectImg}>
					{
						(
							errors?.projectImg as {
								[key: number]: {
									message: string
								}
							}
						)?.[index]?.message
					}
				</FormHelperText>
			)}
		</Box>
	)
}
const StyledAddBox = styled(Box)(({ theme }) => ({
	display: 'flex',
	flexDirection: 'column',
	component: 'label',
	justifyContent: 'center',
	height: 161,
	width: 161,
	alignItems: 'center',
	border: `1px dashed ${theme.palette.grey[800]}`,
	gap: 3,
	padding: 3,
	borderRadius: '8px',
	cursor: 'pointer',
}))
