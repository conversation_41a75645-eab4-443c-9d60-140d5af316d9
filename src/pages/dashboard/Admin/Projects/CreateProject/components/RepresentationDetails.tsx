import React, { FC, useCallback } from 'react'
import { useFormContext } from 'react-hook-form'
import { TCreateProject } from '../schema'
import {
	Box,
	FormControl,
	FormHelperText,
	Grid,
	InputLabel,
	MenuItem,
	OutlinedInput,
	Select,
	SelectChangeEvent,
	styled,
	Typography,
} from '@mui/material'
import { AddCircleOutline } from '@mui/icons-material'
import { ContactCard } from './ContactCard'
import { IContactDetails } from '@/interfaces'

const ITEM_HEIGHT = 48
const ITEM_PADDING_TOP = 8
const MenuProps = {
	PaperProps: {
		style: {
			maxHeight: ITEM_HEIGHT * 4.5 + ITEM_PADDING_TOP,
			width: 250,
		},
	},
}

export const RepresentationDetails: FC<{
	allContacts: IContactDetails[]
	contacts: IContactDetails[]
	setContacts: React.Dispatch<React.SetStateAction<IContactDetails[]>>
}> = ({ allContacts, contacts, setContacts }) => {
	const {
		watch,
		formState: { errors },
		setValue,
		clearErrors,
	} = useFormContext<TCreateProject>()

	const handleCallCard = () => {
		setValue('contactDetails', [
			...(watch('contactDetails') ?? []),
			{
				contactEmail: '',
				contactName: '',
				contactPhoto: '',
				contactUrl: '',
				linkedinLink: '',
			},
		])
		clearErrors('contactDetails')
	}

	const handleSelectContacts = useCallback(
		(event: SelectChangeEvent<string[]>) => {
			const {
				target: { value },
			} = event

			const selectedIds = value as string[]

			const selectedContacts = allContacts.filter((item) =>
				selectedIds.includes(item?.id ?? '')
			)

			setContacts(selectedContacts)
		},
		[allContacts, setContacts]
	)
	return (
		<>
			<Grid container columnSpacing={3} rowSpacing={3} pt={1} width='100%'>
				{(watch('contactDetails') ?? [])?.map((item, index) => (
					<Grid item xs={12} sm={6} md={4} lg={3} key={index}>
						<ContactCard
							cardNumber={index}
							contactUrl={item.contactUrl ?? ''}
						/>
					</Grid>
				))}
				{contacts?.map((item, index) => (
					<Grid item xs={12} sm={6} md={4} lg={3} key={index}>
						<ContactCard
							contact={item}
							contactsFromId={contacts}
							setContactFromId={setContacts}
							cardNumber={index}
							contactUrl={item?.contactUrl ?? ''}
							isDisabled
						/>
					</Grid>
				))}
				<Grid item xs={12} sm={6} md={4} lg={3}>
					<StyledAddBox
						sx={{
							borderColor: errors.contactDetails ? 'error.main' : null,
						}}
						onClick={handleCallCard}>
						<AddCircleOutline sx={{ color: 'grey.800' }} />
						<Typography> Add Contacts</Typography>
					</StyledAddBox>
				</Grid>
				<Grid item xs={12} sm={6} md={4} lg={3}>
					<FormControl fullWidth>
						<InputLabel id='SelectRepresentatives'>
							Select Representatives
						</InputLabel>
						<Select
							labelId='SelectRepresentatives'
							value={contacts?.map((item) => item?.id ?? '')}
							multiple
							onChange={handleSelectContacts}
							input={<OutlinedInput label='Select Representatives' />}
							renderValue={(selected) => {
								const selectedContacts = allContacts.filter((item) =>
									selected.includes(item?.id ?? '')
								)
								const selectedContactNames = selectedContacts.map(
									(item) => item.contactName
								)
								return selectedContactNames?.join(',')
							}}
							MenuProps={MenuProps}>
							{allContacts?.map((item, index) => (
								<MenuItem key={index} value={item?.id}>
									{`${item?.contactName} (${item?.contactEmail})`}
								</MenuItem>
							))}
						</Select>
					</FormControl>
				</Grid>
			</Grid>
			{!!errors.contactDetails && (
				<FormHelperText
					sx={{ textAlign: 'center' }}
					error={!!errors.contactDetails}>
					{errors?.contactDetails?.message}
				</FormHelperText>
			)}
		</>
	)
}

const StyledAddBox = styled(Box)(({ theme }) => ({
	display: 'flex',
	flexDirection: 'column',
	justifyContent: 'center',
	alignItems: 'center',
	border: `1px dashed ${theme.palette.grey[800]}`,
	height: '400px',
	width: '100%',
	gap: 3,
	padding: 3,
	borderRadius: '8px',
	cursor: 'pointer',
}))
