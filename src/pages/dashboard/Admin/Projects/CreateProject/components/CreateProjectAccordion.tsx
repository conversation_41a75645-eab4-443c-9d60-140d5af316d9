import { ProjectAccordion } from '@/interfaces'
import { Add, Remove } from '@mui/icons-material'
import {
	Accordion,
	AccordionDetails,
	AccordionSummary,
	Typography,
} from '@mui/material'
import { FC, PropsWithChildren } from 'react'
import { useFormContext } from 'react-hook-form'

type TProps = {
	handleChange: (
		panel: ProjectAccordion
	) => (_: React.SyntheticEvent, isExpanded: boolean) => void
	accordionId: ProjectAccordion
	expendedAccord: ProjectAccordion | null
	accordionLabel: string
	className?: string
}

export const CreateProjectAccordion: FC<PropsWithChildren<TProps>> = ({
	accordionId,
	handleChange,
	children,
	expendedAccord,
	accordionLabel,
	className,
}) => {
	const {
		formState: { errors },
	} = useFormContext()

	const renderIcon = (panel: string) =>
		expendedAccord === panel ? <Remove /> : <Add />

	return (
		<Accordion
			className={`accordion`}
			expanded={expendedAccord === accordionId || !!errors?.[accordionId]}
			onChange={handleChange(accordionId)}>
			<AccordionSummary
				expandIcon={errors.projectDetails ? null : renderIcon(accordionId)}>
				<Typography variant='body1' fontWeight={600}>
					{accordionLabel}
				</Typography>
			</AccordionSummary>
			<AccordionDetails sx={{ maxHeight: '650px', overflow: 'auto', pt: 2 }} className={className ?? ''}>
				{children}
			</AccordionDetails>
		</Accordion>
	)
}
