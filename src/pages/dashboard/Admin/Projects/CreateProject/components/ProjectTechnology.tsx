import { FC } from 'react'
import { useFormContext } from 'react-hook-form'
import { TCreateProject } from '../schema'
import { FormControl, FormHelperText, Stack } from '@mui/material'
import { MarkdownEditor } from './MarkdownEditor'
import { CustomImageBox } from './CustomImageBox'

export const ProjectTechnology: FC<{ editable: boolean; status: boolean }> = ({
	editable,
	status,
}) => {
	const {
		watch,
		formState: { errors },
		clearErrors,
		setValue,
	} = useFormContext<TCreateProject>()
	return (
		<Stack direction='column' gap={2}>
			<Stack gap={3} direction='row' alignItems='center'>
				<Stack width='50%'>
					<FormControl>
						<MarkdownEditor
							placeholder='Biochar Technology'
							value={watch('projectTechnology.biocharTechnology') || ''}
							onChange={(markdown) => {
								setValue('projectTechnology.biocharTechnology', markdown)
								clearErrors('projectTechnology.biocharTechnology')
							}}
							editMode={editable}
						/>
						<FormHelperText
							error={!!errors?.projectTechnology?.biocharTechnology?.message}>
							{errors?.projectTechnology?.biocharTechnology?.message}
						</FormHelperText>
					</FormControl>
				</Stack>

				<Stack width='50%' direction='column'>
					<CustomImageBox
						status={status}
						edit={editable}
						imageField='projectTechnology.biocharImage'
						imgUrl={watch('projectTechnology.biocharImage.url') ?? ''}
					/>
					{!!errors.projectTechnology?.biocharImage && (
						<FormHelperText
							sx={{ textAlign: 'center' }}
							error={!!errors.projectTechnology?.biocharImage}>
							{errors?.projectTechnology?.biocharImage?.id?.message}
						</FormHelperText>
					)}
				</Stack>
			</Stack>
			<Stack direction='row' gap={2} alignItems='center'>
				<Stack width='50%'>
					<FormControl>
						<MarkdownEditor
							placeholder='Durability Technology'
							value={watch('projectTechnology.durabilityTechnology') || ''}
							onChange={(markdown) => {
								setValue('projectTechnology.durabilityTechnology', markdown)
								clearErrors('projectTechnology.durabilityTechnology')
							}}
							editMode={editable}
						/>
						<FormHelperText
							error={
								!!errors?.projectTechnology?.durabilityTechnology?.message
							}>
							{errors?.projectTechnology?.durabilityTechnology?.message}
						</FormHelperText>
					</FormControl>
				</Stack>
				<Stack width='50%' direction='column'>
					<CustomImageBox
						edit={editable}
						status={status}
						imageField='projectTechnology.durabilityImage'
						imgUrl={watch('projectTechnology.durabilityImage.url') ?? ''}
					/>
					{!!errors.projectTechnology?.durabilityImage && (
						<FormHelperText
							sx={{ textAlign: 'center' }}
							error={!!errors.projectTechnology?.durabilityImage}>
							{errors?.projectTechnology?.durabilityImage?.id?.message}
						</FormHelperText>
					)}
				</Stack>
			</Stack>
		</Stack>
	)
}
