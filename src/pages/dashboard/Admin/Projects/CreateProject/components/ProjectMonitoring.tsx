import { FC } from 'react'
import { MarkdownEditor } from './MarkdownEditor'
import { FormHelperText, Stack } from '@mui/material'
import { useFormContext } from 'react-hook-form'
import { TCreateProject } from '../schema'

export const ProjectMonitoring: FC<{ editable: boolean }> = ({ editable }) => {
	const {
		watch,
		formState: { errors },
		clearErrors,
		setValue,
	} = useFormContext<TCreateProject>()
	return (
		<Stack>
			<MarkdownEditor
				placeholder='Project Monitoring Description'
				value={watch('projectMonitoring.monitoring') || ''}
				onChange={(markdown) => {
					setValue('projectMonitoring.monitoring', markdown)
					clearErrors('projectMonitoring.monitoring')
				}}
				editMode={editable}
			/>
			<FormHelperText error={!!errors?.projectMonitoring?.monitoring?.message}>
				{errors?.projectMonitoring?.monitoring?.message}
			</FormHelperText>
		</Stack>
	)
}
