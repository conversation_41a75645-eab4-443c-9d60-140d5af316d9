import { handleImageUpload } from '@/utils/helper'
import {
	Box,
	CircularProgress,
	Input,
	Stack,
	Typography,
	styled,
	useTheme,
} from '@mui/material'
import { useCallback, useReducer } from 'react'
import { useFormContext } from 'react-hook-form'
import { toast } from 'react-toastify'

const simpleReducer = (
	prevValue: IDocuments,
	nextValue: Partial<IDocuments>
) => ({
	...prevValue,
	...nextValue,
})
interface IDocuments {
	circonomyPDD: boolean
	dMrvendorsement: boolean
	ceresCertificate: boolean
	officialPDD: boolean
}

const initialState: IDocuments = {
	circonomyPDD: false,
	dMrvendorsement: false,
	ceresCertificate: false,
	officialPDD: false,
}

export const ProjectDoc = () => {
	const theme = useTheme()

	const { watch, setValue, getFieldState, clearErrors } = useFormContext()

	const [isLoading, setIsLoading] = useReducer(simpleReducer, initialState)

	const handleUploadAttachment = useCallback(
		async (event: any, key: string, loader: keyof IDocuments) => {
			const file = event.target.files[0]
			setIsLoading({ [loader]: true })

			try {
				const data = await handleImageUpload(file)
				setValue(key, data)
				clearErrors(key)
			} catch (err) {
				toast('Image upload failed')
			}
			setIsLoading({ [loader]: false })
		},
		[clearErrors, setValue]
	)

	return (
		<Stack direction='row' gap={8} width='100%'>
			<StyledBox>
				<Stack
					direction='column'
					gap={2}
					height='100%'
					justifyContent='space-around'>
					<Stack gap={1}>
						<Typography variant='subtitle1' fontWeight={600}>
							Upload Circonomy PDD
						</Typography>

						<Box className='watchAttachmentBox'>
							<label htmlFor={`attachment`}>
								<Stack
									direction='row'
									justifyContent='space-between'
									sx={{ cursor: 'pointer' }}>
									<Typography
										variant='subtitle1'
										fontWeight={600}
										color={theme.palette.grey[800]}>
										{' '}
										{watch('projectDoc.circonomyPDD')?.fileName
											? watch('projectDoc.circonomyPDD')?.fileName
											: 'Upload Circonomy PDD'}
									</Typography>
									<Input
										id={`attachment`}
										type='file'
										style={{ display: 'none' }}
										onChange={(e) =>
											handleUploadAttachment(
												e,
												'projectDoc.circonomyPDD',
												'circonomyPDD'
											)
										}
									/>
									{!!getFieldState(`projectDoc.circonomyPDD.url`).error && (
										<Typography color='error'>
											{
												getFieldState(`projectDoc.circonomyPDD.url`).error
													?.message
											}
										</Typography>
									)}
									{watch(`projectDoc.circonomyPDD.fileName `) ? (
										<Box
											component='img'
											onClick={(e) => {
												e.preventDefault()
												window.location.href =
													watch('projectDoc.circonomyPDD.url') || ''
											}}
											src='/images/cloud_download.svg'
											sx={{ cursor: 'pointer' }}
										/>
									) : (
										<Box
											component='img'
											src='/images/cloud_upload.svg'
											sx={{ cursor: 'pointer' }}
										/>
									)}

									{isLoading.circonomyPDD && <CircularProgress />}
								</Stack>
							</label>{' '}
						</Box>
					</Stack>
					<Stack gap={1}>
						<Typography variant='subtitle1' fontWeight={600}>
							Upload CERES Certificate
						</Typography>

						<Box className='watchAttachmentBox'>
							<label htmlFor={`ceres`}>
								<Stack
									direction='row'
									justifyContent='space-between'
									sx={{ cursor: 'pointer' }}>
									<Typography
										variant='subtitle1'
										fontWeight={600}
										color={theme.palette.grey[800]}>
										{' '}
										{watch('projectDoc.ceresCertificate')?.fileName
											? watch('projectDoc.ceresCertificate')?.fileName
											: 'Upload CERES Certificate'}
									</Typography>
									<Input
										id={`ceres`}
										type='file'
										style={{ display: 'none' }}
										onChange={(e) =>
											handleUploadAttachment(
												e,
												'projectDoc.ceresCertificate',
												'ceresCertificate'
											)
										}
									/>
									{!!getFieldState(`projectDoc.ceresCertificate.url`).error && (
										<Typography color='error'>
											{
												getFieldState(`projectDoc.ceresCertificate.url`).error
													?.message
											}
										</Typography>
									)}
									{watch(`projectDoc.ceresCertificate.fileName `) ? (
										<Box
											component='img'
											onClick={(e) => {
												e.preventDefault()
												window.location.href =
													watch('projectDoc.ceresCertificate.url') || ''
											}}
											src='/images/cloud_download.svg'
											sx={{ cursor: 'pointer' }}
										/>
									) : (
										<Box
											component='img'
											src='/images/cloud_upload.svg'
											sx={{ cursor: 'pointer' }}
										/>
									)}

									{isLoading.ceresCertificate && <CircularProgress />}
								</Stack>
							</label>{' '}
						</Box>
					</Stack>
				</Stack>
			</StyledBox>
			<StyledBox>
				<Stack
					direction='column'
					gap={2}
					height='100%'
					justifyContent='space-around'>
					<Stack gap={1}>
						<Typography variant='subtitle1' fontWeight={600}>
							{' '}
							Upload Official PDD
						</Typography>

						<Box className='watchAttachmentBox'>
							<label htmlFor={`officialPdd`}>
								<Stack
									direction='row'
									justifyContent='space-between'
									sx={{ cursor: 'pointer' }}>
									<Typography
										variant='subtitle1'
										fontWeight={600}
										color={theme.palette.grey[800]}>
										{' '}
										{watch('projectDoc.officialPDD')?.fileName
											? watch('projectDoc.officialPDD')?.fileName
											: 'Upload Official PDD'}
									</Typography>
									<Input
										id={`officialPdd`}
										type='file'
										style={{ display: 'none' }}
										onChange={(e) =>
											handleUploadAttachment(
												e,
												'projectDoc.officialPDD',
												'officialPDD'
											)
										}
									/>
									{!!getFieldState(`projectDoc.officialPDD.url`).error && (
										<Typography color='error'>
											{
												getFieldState(`projectDoc.officialPDD.url`).error
													?.message
											}
										</Typography>
									)}
									{watch(`projectDoc.officialPDD `)?.url ? (
										<Box
											component='img'
											onClick={(e) => {
												e.preventDefault()
												window.location.href =
													watch('projectDoc.officialPDD')?.url || ''
											}}
											src='/images/cloud_download.svg'
											sx={{ cursor: 'pointer' }}
										/>
									) : (
										<Box
											component='img'
											src='/images/cloud_upload.svg'
											sx={{ cursor: 'pointer' }}
										/>
									)}

									{isLoading.officialPDD && <CircularProgress />}
								</Stack>
							</label>{' '}
						</Box>
					</Stack>
					<Stack gap={1}>
						<Typography variant='subtitle1' fontWeight={600}>
							{' '}
							Upload dMRV Endorsement letter
						</Typography>

						<Box className='watchAttachmentBox'>
							<label htmlFor={`dmrvDoc`}>
								<Stack
									direction='row'
									justifyContent='space-between'
									sx={{ cursor: 'pointer' }}>
									<Typography
										variant='subtitle1'
										fontWeight={600}
										color={theme.palette.grey[800]}>
										{' '}
										{watch('projectDoc.dMrvendorsement')?.fileName
											? watch('projectDoc.dMrvendorsement')?.fileName
											: 'Upload dMRV Endorsement letter'}
									</Typography>
									<Input
										id={`dmrvDoc`}
										type='file'
										style={{ display: 'none' }}
										onChange={(e) =>
											handleUploadAttachment(
												e,
												'projectDoc.dMrvendorsement',
												'dMrvendorsement'
											)
										}
									/>
									{!!getFieldState(`projectDoc.dMrvendorsement.url`).error && (
										<Typography color='error'>
											{
												getFieldState(`projectDoc.dMrvendorsement.url`).error
													?.message
											}
										</Typography>
									)}
									{watch(`projectDoc.dMrvendorsement `)?.url ? (
										<Box
											component='img'
											onClick={(e) => {
												e.preventDefault()
												window.location.href =
													watch(`projectDoc.dMrvendorsement `)?.url || ''
											}}
											src='/images/cloud_download.svg'
											sx={{ cursor: 'pointer' }}
										/>
									) : (
										<Box
											component='img'
											src='/images/cloud_upload.svg'
											sx={{ cursor: 'pointer' }}
										/>
									)}

									{isLoading.dMrvendorsement && <CircularProgress />}
								</Stack>
							</label>{' '}
						</Box>
					</Stack>
				</Stack>
			</StyledBox>
		</Stack>
	)
}

const StyledBox = styled(Box)(({ theme }) => ({
	width: '50%',
	minHeight: '400px',
	maxHeight: '600px',
	overflow: 'auto',
	display: 'flex',
	flexDirection: 'column',
	// border: '1px dashed grey',
	gap: '24px',
	padding: '24px',
	borderRadius: '8px',

	'.watchAttachmentBox': {
		border: '1px dashed grey',
		padding: '16px',
		borderRadius: '8px',
		position: 'relative',
	},

	'.addButton': {
		textAlign: 'right',
		color: theme.palette.grey[800],
		cursor: 'pointer',
	},
}))
