import { IContactDetails } from '@/interfaces'
import { handleImageUpload } from '@/utils/helper'
import { CancelOutlined } from '@mui/icons-material'
import {
	Box,
	CircularProgress,
	IconButton,
	Input,
	TextField,
	Typography,
	styled,
} from '@mui/material'
import { useCallback, useEffect, useState } from 'react'
import { FieldError, useFormContext } from 'react-hook-form'
import { toast } from 'react-toastify'

interface IProps {
	cardNumber: number
	contactUrl: string
	isDisabled?: boolean
	contactsFromId?: IContactDetails[]
	setContactFromId?: React.Dispatch<React.SetStateAction<IContactDetails[]>>
	contact?: IContactDetails
}

export const ContactCard = ({
	cardNumber,
	contactUrl,
	contactsFromId,
	setContactFromId,
	isDisabled = false,
	contact,
}: IProps) => {
	const {
		register,
		watch,
		setValue,
		clearErrors,
		formState: { errors },
	} = useFormContext()

	const [loading, setLoading] = useState<boolean>(false)

	const [url, setUrl] = useState<string>('')
	useEffect(() => {
		if (!contactUrl) return
		setUrl(contactUrl)
	}, [contactUrl])

	const handleImageChange = useCallback(
		async (event: any) => {
			const file = event.target.files[0]
			if (!file) return
			setLoading(true)
			try {
				const data = await handleImageUpload(file)
				setValue(`contactDetails.${cardNumber}.contactPhoto`, data?.id)

				setUrl(data.url)
				clearErrors(`contactDetails.${cardNumber}.contactPhoto`)
			} catch (err) {
				toast('Image upload failed')
			}
			setLoading(false)
		},
		[cardNumber, clearErrors, setValue]
	)

	const handleCross = useCallback(
		(cardNumber: number) => {
			const updatedContactDetails = watch('contactDetails')?.filter(
				(_: any, index: number) => index !== cardNumber
			)
			const updatedContactFromId = contactsFromId?.filter(
				(_: any, index: number) => index !== cardNumber
			)

			if (isDisabled) setContactFromId?.(updatedContactFromId ?? [])
			else setValue('contactDetails', updatedContactDetails)
		},
		[contactsFromId, isDisabled, setContactFromId, setValue, watch]
	)

	return (
		<StyledBox>
			<StyledIcon onClick={() => handleCross(cardNumber)}>
				<CancelOutlined />
			</StyledIcon>
			<label htmlFor={`imageInput-${cardNumber}${isDisabled ? 'n' : ''}`}>
				<Box className='inputBox'>
					{url ? (
						<>
							<Box
								component='img'
								src={url}
								alt='Uploaded'
								className='image'
								border='1px solid'
							/>
							<Box
								component='img'
								src='/images/editPencil.svg'
								alt='editIcon'
								className='editPen'
							/>
						</>
					) : (
						<Box
							component='img'
							src='/images/contactpic.svg'
							alt='upload'
							className='image'
						/>
					)}
				</Box>
				{!isDisabled ? (
					<Input
						id={`imageInput-${cardNumber}${isDisabled ? 'n' : ''}`}
						type='file'
						disabled={isDisabled}
						style={{ display: 'none' }}
						onChange={(e) => handleImageChange(e)}
						inputProps={{
							accept: '.png, .jpeg, .jpg, .heic',
						}}
					/>
				) : null}
			</label>
			{!!(
				errors?.contactDetails as {
					[key: number]: {
						contactPhoto?: FieldError
					}
				}
			)?.[cardNumber]?.contactPhoto && (
				<Typography color='error' fontSize='14px'>
					Upload Contact Photo
				</Typography>
			)}
			{loading && <CircularProgress />}

			<TextField
				label='Name'
				id='name'
				type='text'
				disabled={isDisabled}
				InputLabelProps={{
					shrink: isDisabled
						? true
						: !!watch(`contactDetails.${cardNumber}.contactName`),
				}}
				{...(isDisabled
					? { value: contact?.contactName }
					: { ...register(`contactDetails.${cardNumber}.contactName`) })}
				error={
					!!(
						errors?.contactDetails as {
							[key: number]: {
								contactName?: FieldError
							}
						}
					)?.[cardNumber]?.contactName
				}
				helperText={
					(
						errors?.contactDetails as {
							[key: number]: {
								contactName?: FieldError
							}
						}
					)?.[cardNumber]?.contactName?.message
				}
			/>
			<TextField
				label='Email'
				id='email'
				disabled={isDisabled}
				type='email'
				InputLabelProps={{
					shrink: isDisabled
						? true
						: !!watch(`contactDetails.${cardNumber}.contactEmail`),
				}}
				{...(isDisabled
					? { value: contact?.contactEmail }
					: { ...register(`contactDetails.${cardNumber}.contactEmail`) })}
				error={
					!!(
						errors?.contactDetails as {
							[key: number]: {
								contactEmail?: FieldError
							}
						}
					)?.[cardNumber]?.contactEmail
				}
				helperText={
					(
						errors?.contactDetails as {
							[key: number]: {
								contactEmail?: FieldError
							}
						}
					)?.[cardNumber]?.contactEmail?.message
				}
			/>

			<TextField
				label='LinkedIn URL'
				id='linkedIn'
				disabled={isDisabled}
				InputLabelProps={{
					shrink: isDisabled
						? true
						: !!watch(`contactDetails.${cardNumber}.linkedinLink`),
				}}
				{...(isDisabled
					? { value: contact?.contactName }
					: { ...register(`contactDetails.${cardNumber}.linkedinLink`) })}
			/>
		</StyledBox>
	)
}

const StyledBox = styled(Box)(() => ({
	position: 'relative',
	display: 'flex',
	flexDirection: 'column',
	justifyContent: 'center',
	alignItems: 'center',
	border: '1px dashed grey',
	height: '400px',
	width: '100%',
	gap: '20px',
	padding: '12px',
	borderRadius: '8px',
	'.inputBox': {
		borderRadius: '50%',
		height: '64px',
		width: '64px',
		overflow: 'visible',
		position: 'relative',
		display: 'flex',
		alignItems: 'center',
		justifyContent: 'center',
	},
	'.image': {
		width: '100%',
		height: '100%',
		borderRadius: '50%',
		cursor: 'pointer',
	},
	'.editPen': {
		position: 'absolute',
		top: '40px',
		right: '-15px',
		height: '24px',
		cursor: 'pointer',
	},
}))

const StyledIcon = styled(IconButton)(({ theme }) => ({
	position: 'absolute',
	top: '-5%',
	right: '-5%',
	cursor: 'pointer',
	color: theme.palette.grey[800],
}))
