import { FC } from 'react'
import { useFormContext } from 'react-hook-form'
import { TCreateProject } from '../schema'
import { FormHelperText, Stack, TextField } from '@mui/material'
import { MarkdownEditor } from './MarkdownEditor'

export const ProjectCertification: FC<{ editable: boolean }> = ({
	editable,
}) => {
	const {
		register,
		watch,
		formState: { errors },
		clearErrors,
		setValue,
	} = useFormContext<TCreateProject>()

	return (
		<>
			<Stack direction='row' gap={2}>
				<Stack gap={3} width='50%'>
					<Stack direction='row' gap={2}>
						<TextField
							label='Endorsing Agent Text'
							variant='outlined'
							{...register('projectCertification.certifierText')}
							fullWidth
							error={!!errors.projectCertification?.certifierText}
							helperText={errors?.projectCertification?.certifierText?.message}
							InputLabelProps={{
								shrink: !!watch('projectCertification.certifierText'),
							}}
						/>
						<TextField
							label='Endorsing Agent Link'
							variant='outlined'
							{...register('projectCertification.certifierLink')}
							fullWidth
							error={!!errors.projectCertification?.certifierLink}
							helperText={errors?.projectCertification?.certifierLink?.message}
							InputLabelProps={{
								shrink: !!watch('projectCertification.certifierLink'),
							}}
						/>
					</Stack>
					<Stack direction='row' gap={2}>
						<TextField
							label='Standard Text'
							variant='outlined'
							{...register('projectCertification.standardText')}
							fullWidth
							error={!!errors.projectCertification?.standardText}
							helperText={errors?.projectCertification?.standardText?.message}
							InputLabelProps={{
								shrink: !!watch('projectCertification.standardText'),
							}}
						/>
						<TextField
							label='Standard Link'
							variant='outlined'
							{...register('projectCertification.standardLink')}
							fullWidth
							error={!!errors.projectCertification?.standardLink}
							helperText={errors?.projectCertification?.standardLink?.message}
							InputLabelProps={{
								shrink: !!watch('projectCertification.standardLink'),
							}}
						/>
					</Stack>

					<Stack direction='row' gap={2}>
						<TextField
							label='Current verifier of project outcomes'
							variant='outlined'
							{...register('projectCertification.currentVerifierText')}
							fullWidth
							error={!!errors.projectCertification?.currentVerifierText}
							helperText={
								errors?.projectCertification?.currentVerifierText?.message
							}
							InputLabelProps={{
								shrink: !!watch('projectCertification.currentVerifierText'),
							}}
						/>
					</Stack>
				</Stack>
				<Stack gap={3} width='50%'>
					<Stack direction='row' gap={2}>
						<TextField
							label='Endorsing Agent Text '
							variant='outlined'
							{...register('projectCertification.certifierLinkText')}
							fullWidth
							error={!!errors.projectCertification?.certifierLinkText}
							helperText={
								errors?.projectCertification?.certifierLinkText?.message
							}
							InputLabelProps={{
								shrink: !!watch('projectCertification.certifierLinkText'),
							}}
						/>
					</Stack>
					<Stack direction='row' gap={2}>
						<TextField
							label='Standard Link Text'
							variant='outlined'
							{...register('projectCertification.standardLinkText')}
							fullWidth
							error={!!errors.projectCertification?.standardLinkText}
							helperText={
								errors?.projectCertification?.standardLinkText?.message
							}
							InputLabelProps={{
								shrink: !!watch('projectCertification.standardLinkText'),
							}}
						/>
					</Stack>
				</Stack>
			</Stack>
			<Stack width='50%' mt={2}>
				<MarkdownEditor
					placeholder='Certification Details'
					value={watch('projectCertification.certificationDetails') || ''}
					onChange={(markdown) => {
						setValue('projectCertification.certificationDetails', markdown)
						clearErrors('projectCertification.certificationDetails')
					}}
					editMode={editable}
				/>
				<FormHelperText
					error={!!errors?.projectCertification?.certificationDetails?.message}>
					{errors?.projectCertification?.certificationDetails?.message}
				</FormHelperText>
			</Stack>
		</>
	)
}
