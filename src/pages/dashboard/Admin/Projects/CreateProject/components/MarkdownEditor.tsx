import {
	BoldItalicUnderlineToggles,
	ListsToggle,
	MDXEditor,
	MDXEditorMethods,
	MDXEditorProps,
	UndoRedo,
	listsPlugin,
	quotePlugin,
	thematicBreakPlugin,
	toolbarPlugin,
} from '@mdxeditor/editor'
import '@mdxeditor/editor/style.css'
import { Box, styled } from '@mui/material'
import { useEffect, useRef } from 'react'

const ToolbarContent = () => (
	<>
		<UndoRedo />
		<BoldItalicUnderlineToggles />
		<ListsToggle />
	</>
)

type TProps = {
	value: string
	editMode?: boolean
} & Omit<MDXEditorProps, 'markdown'>

export const MarkdownEditor = ({
	value,
	plugins = [],
	editMode = false,
	...props
}: TProps) => {
	const markdownRef = useRef<MDXEditorMethods>(null)

	useEffect(() => {
		if (!editMode || !markdownRef.current || !value) return
		markdownRef.current.setMarkdown(value)
	}, [editMode, value])

	return (
		<StyledBox>
			<MDXEditor
				ref={markdownRef}
				markdown={value}
				className='editor_style'
				contentEditableClassName='editable_style'
				plugins={[
					listsPlugin(),
					quotePlugin(),
					thematicBreakPlugin(),
					toolbarPlugin({
						toolbarContents: ToolbarContent,
					}),
					...plugins,
				]}
				{...props}
			/>
		</StyledBox>
	)
}

const StyledBox = styled(Box)(() => ({
	'.editor_style': {
		width: '100%',
	},
	'.editable_style': {
		minHeight: '200px',
	},
}))
