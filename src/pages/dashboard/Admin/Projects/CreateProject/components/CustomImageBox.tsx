import { handleImageUpload } from '@/utils/helper'
import { AddCircleOutline } from '@mui/icons-material'
import { Box, Stack, styled, Typography } from '@mui/material'
import { useCallback, useEffect, useRef, useState } from 'react'
import { useFormContext } from 'react-hook-form'
import { toast } from 'react-toastify'
import CancelTwoToneIcon from '@mui/icons-material/CancelTwoTone'

export const CustomImageBox = ({
	imageField,
	imgUrl,
	status,
	edit,
}: {
	imageField: string
	imgUrl: string
	status?: boolean
	edit?: boolean
}) => {
	const { setValue, clearErrors } = useFormContext()
	const [url, setUrl] = useState<string>(imgUrl ?? '')
	const fileInputRef = useRef<HTMLInputElement | null>(null)

	useEffect(() => {
		if (imgUrl) setUrl(imgUrl)
	}, [imgUrl])

	const uploadPicture = useCallback(
		async (event: any) => {
			const file = event.target.files[0]
			if (!file) return

			try {
				const data = await handleImageUpload(file)
				setValue(imageField, data)

				setUrl(data.url)
				clearErrors()
			} catch (err) {
				console.log(err)
				toast('Image upload failed')
			} finally {
				fileInputRef.current!.value = ''
			}
		},
		[clearErrors, setValue, imageField]
	)

	const handleOpenFilePicker = () => {
		fileInputRef.current?.click()
	}

	return (
		<Box
			position='relative'
			sx={{
				width: '100%',
				display: 'flex',
				flexDirection: 'column',
				justifyContent: 'center',
				alignItems: 'center',
			}}>
			{url ? (
				<>
					<Box
						component='img'
						src={url}
						style={{ objectFit: 'contain' }}
						alt='Uploaded'
						className='image'
						height={161}
						width={161}
						onClick={handleOpenFilePicker}
					/>
					<Stack position='absolute' direction={'row'}>
						<Box
							component='img'
							src='/images/editProject.svg'
							alt='editIcon'
							sx={{ cursor: 'pointer' }}
							onClick={handleOpenFilePicker}
						/>
						{(!edit || !status) && (
							<StyledRemoveIcon
								onClick={(e) => {
									e.stopPropagation()
									setValue(imageField, { fileName: '', id: '', url: '' })
									setUrl('')
								}}
							/>
						)}
					</Stack>
				</>
			) : (
				<StyledAddBox sx={{ height: 200 }} onClick={handleOpenFilePicker}>
					<AddCircleOutline sx={{ color: 'grey.800' }} />
					<Typography> Upload Image </Typography>
				</StyledAddBox>
			)}

			<input
				ref={fileInputRef}
				type='file'
				accept='.png, .jpeg, .jpg, .heic'
				style={{ display: 'none' }}
				onChange={(e) => uploadPicture(e)}
			/>
		</Box>
	)
}

const StyledAddBox = styled(Box)(({ theme }) => ({
	display: 'flex',
	flexDirection: 'column',
	justifyContent: 'center',
	alignItems: 'center',
	border: `1px dashed ${theme.palette.grey[800]}`,
	height: '400px',
	width: '100%',
	gap: 3,
	padding: 3,
	borderRadius: '8px',
	cursor: 'pointer',
}))

const StyledRemoveIcon = styled(CancelTwoToneIcon)`
	cursor: pointer;

	path:first-of-type {
		fill: #000000;
		opacity: 1 !important;
	}
	path:last-of-type {
		fill: #ffffff;
	}
`
