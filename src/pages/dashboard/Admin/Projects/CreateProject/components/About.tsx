import { TCreateProject } from '../schema'
import { useFormContext } from 'react-hook-form'
import { FormControl, FormHelperText, Stack, TextField } from '@mui/material'
import { LocalizationProvider } from '@mui/x-date-pickers/LocalizationProvider'
import { AdapterMoment } from '@mui/x-date-pickers/AdapterMoment'
import { DatePicker } from '@mui/x-date-pickers/DatePicker'
import moment from 'moment'
import { CustomImageBox } from './CustomImageBox'
import { FC } from 'react'

export const About = ({ status, edit }: { status: boolean; edit: boolean }) => {
	const {
		watch,
		formState: { errors },
		setValue,
		register,
		getValues,
	} = useFormContext<TCreateProject>()
	return (
		<LocalizationProvider dateAdapter={AdapterMoment}>
			<Stack direction='row' gap={2}>
				<Stack gap={3} width='50%'>
					<Stack direction='row' gap={2}>
						<TextField
							label='Operator Number'
							variant='outlined'
							{...register('projectAbout.operatorId')}
							fullWidth
							error={!!errors.projectAbout?.operatorId}
							helperText={errors?.projectAbout?.operatorId?.message}
						/>
						<TextField
							label='Registry Link'
							variant='outlined'
							{...register('projectAbout.registryLink')}
							fullWidth
							error={!!errors.projectAbout?.registryLink}
							helperText={errors?.projectAbout?.registryLink?.message}
							InputLabelProps={{
								shrink: !!watch('projectAbout.registryLink'),
							}}
						/>
					</Stack>
					<Stack direction='column' gap={2}>
						<CustomImageBox
							status={status}
							edit={edit}
							imageField='projectAbout.aboutImg'
							imgUrl={watch('projectAbout.aboutImg.url') ?? ''}
						/>
						{!!errors.projectAbout?.aboutImg && (
							<FormHelperText
								sx={{ textAlign: 'center' }}
								error={!!errors.projectAbout?.aboutImg}>
								{errors?.projectAbout?.aboutImg?.id?.message}
							</FormHelperText>
						)}
					</Stack>
					<Stack direction='row' pt={3} gap={2}>
						<FormControl sx={{ flex: 1 }}>
							<CustomDatePicker
								label='Project Registration Date'
								value={
									getValues('projectAbout.projectRegistrationDate')
										? moment(getValues('projectAbout.projectRegistrationDate'))
										: null
								}
								onChange={(newValue) => {
									setValue(
										'projectAbout.projectRegistrationDate',
										moment(newValue).set('hour', 11).toDate()
									)
								}}
							/>
							<FormHelperText
								error={!!errors.projectAbout?.projectRegistrationDate?.message}>
								{errors.projectAbout?.projectRegistrationDate?.message}
							</FormHelperText>
						</FormControl>
						<FormControl sx={{ flex: 1 }}>
							<CustomDatePicker
								label='Start Crediting Period Term'
								value={
									getValues('projectAbout.CreditingStartDate')
										? moment(getValues('projectAbout.CreditingStartDate'))
										: null
								}
								onChange={(newValue) => {
									setValue(
										'projectAbout.CreditingStartDate',
										moment(newValue).set('hour', 11).toDate()
									)
								}}
							/>
							<FormHelperText
								error={!!errors.projectAbout?.CreditingStartDate?.message}>
								{errors.projectAbout?.CreditingStartDate?.message}
							</FormHelperText>
						</FormControl>
					</Stack>
				</Stack>
				<Stack gap={3} width='50%' justifyContent='space-between'>
					<Stack direction='row' gap={2}>
						<TextField
							label='Registry Link Description'
							variant='outlined'
							{...register('projectAbout.registryIdDesc')}
							fullWidth
							error={!!errors.projectAbout?.registryIdDesc}
							helperText={errors?.projectAbout?.registryIdDesc?.message}
						/>
					</Stack>
					<Stack>
						<FormControl sx={{ flex: 1 }}>
							<CustomDatePicker
								label='End Crediting Period Term'
								value={
									getValues('projectAbout.CreditingEndDate')
										? moment(getValues('projectAbout.CreditingEndDate'))
										: null
								}
								onChange={(newValue) => {
									setValue(
										'projectAbout.CreditingEndDate',
										moment(newValue).set('hour', 11).toDate()
									)
								}}
							/>
							<FormHelperText
								error={!!errors.projectAbout?.CreditingEndDate?.message}>
								{errors.projectAbout?.CreditingEndDate?.message}
							</FormHelperText>
						</FormControl>
					</Stack>
				</Stack>
			</Stack>
		</LocalizationProvider>
	)
}

const CustomDatePicker: FC<{
	value: moment.Moment | null
	onChange: (newValue: any) => void
	label: string
}> = ({ value, onChange, label }) => (
	<DatePicker
		label={label}
		value={value}
		onChange={onChange}
		format='DD/MM/YYYY'
		slotProps={{
			layout: {
				sx: {
					'.MuiPickersDay-root': {
						color: 'black',
						fontWeight: 450,
					},
					'.MuiPickersDay-root.Mui-selected': {
						color: 'white',
					},
					'.MuiPickersDay-root.Mui-disabled': {
						color: 'gray',
					},
				},
			},
		}}
	/>
)
