import * as Yup from 'yup'

export const createProjectSchema = Yup.object().shape({
	projectIntroduction: Yup.object()
		.shape({
			name: Yup.string().notRequired(),
			projectStatus: Yup.string().notRequired(),
			totalCredits: Yup.number()
				.transform((value) => (Number.isNaN(value) ? null : value))
				.notRequired(),
			availableCredits: Yup.number()
				.transform((value) => (Number.isNaN(value) ? null : value))
				.notRequired()
				.test('not-zero', 'Available Credits cannot be zero', (value) => {
					return value !== 0
				}),
			publishedStatus: Yup.string().notRequired(),
			methaneMitigation: Yup.string().notRequired(),
			rate: Yup.number()
				.nullable()
				.transform((value) => (Number.isNaN(value) ? null : value))
				.when(['publishedStatus', 'projectStatus'], {
					is: (publishedStatus: string, projectStatus: string) =>
						publishedStatus === 'publish' &&
						['active reserved', 'active sold out', 'active available'].includes(
							projectStatus
						),
					then: (schema) => schema.required('Project rate is required'),
					otherwise: (schema) =>
						schema.test('not-zero', 'Rate cannot be zero', (value) => {
							return value == null || value !== 0
						}),
				}),
			address: Yup.string().notRequired(),
			projectMapLocationURL: Yup.string().notRequired(),
			linkToRegistry: Yup.string().notRequired(),
			technology: Yup.string().notRequired(),
			application: Yup.string().notRequired(),
			developmentGoals: Yup.array().of(Yup.number()).nullable(),
			permanence: Yup.string().notRequired(),
			nextAvailableDate: Yup.date().when(['projectStatus'], {
				is: (projectStatus: string) =>
					['active reserved', 'upcoming reserved'].includes(projectStatus),
				then: (schema) => schema,
				otherwise: (schema) => schema,
			}),
			seoNameUrl: Yup.string().nullable(),
		})
		.notRequired(),
	projectDescription: Yup.object()
		.shape({
			description: Yup.string().notRequired(),
			shortDescription: Yup.string().notRequired(),
		})
		.notRequired(),
	projectAbout: Yup.object().shape({
		operatorId: Yup.string().notRequired(),

		registryLink: Yup.string().notRequired(),

		registryIdDesc: Yup.string().notRequired(),
		aboutImg: Yup.object()
			.shape({
				id: Yup.string().notRequired(),
				url: Yup.string().notRequired(),
				fileName: Yup.string().notRequired(),
			})
			.notRequired(),
		projectRegistrationDate: Yup.date().notRequired(),
		CreditingStartDate: Yup.date().notRequired(),
		CreditingEndDate: Yup.date().notRequired(),
	}),
	projectCertification: Yup.object().shape({
		certifierText: Yup.string().notRequired(),

		certifierLink: Yup.string().notRequired(),
		certifierLinkText: Yup.string().notRequired(),
		standardText: Yup.string().notRequired(),

		standardLink: Yup.string().notRequired(),
		standardLinkText: Yup.string().notRequired(),
		currentVerifierText: Yup.string().notRequired(),
		certificationDetails: Yup.string().notRequired(),
	}),
	projectMonitoring: Yup.object().shape({
		monitoring: Yup.string().notRequired(),
	}),
	projectDetails: Yup.object().shape({
		annualCarbonRemoval: Yup.string().notRequired(),

		applicationDetails: Yup.string().notRequired(),

		methaneMitigationDetails: Yup.string().notRequired(),
	}),
	projectTechnology: Yup.object().shape({
		biocharTechnology: Yup.string().notRequired(),

		durabilityTechnology: Yup.string().notRequired(),
		biocharImage: Yup.object()
			.shape({
				id: Yup.string().notRequired(),
				url: Yup.string().notRequired(),
				fileName: Yup.string().notRequired(),
			})
			.notRequired(),

		durabilityImage: Yup.object()
			.shape({
				id: Yup.string().notRequired(),
				url: Yup.string().notRequired(),
				fileName: Yup.string().notRequired(),
			})
			.notRequired(),
	}),

	contactDetails: Yup.array()
		.of(
			Yup.object().shape({
				id: Yup.string().notRequired(),
				contactName: Yup.string().notRequired(),
				contactEmail: Yup.string().notRequired(),
				contactUrl: Yup.string().notRequired(),
				contactPhoto: Yup.string().notRequired(),
				linkedinLink: Yup.string().notRequired(),
			})
		)
		.notRequired(),
	projectDoc: Yup.object()
		.shape({
			circonomyPDD: Yup.object()
				.shape({
					fileName: Yup.string().notRequired(),
					url: Yup.string().notRequired(),
					id: Yup.string().notRequired(),
				})
				.notRequired(),
			dMrvendorsement: Yup.object()
				.shape({
					fileName: Yup.string().notRequired(),
					url: Yup.string().notRequired(),
					id: Yup.string().notRequired(),
				})
				.notRequired(),

			ceresCertificate: Yup.object()
				.shape({
					fileName: Yup.string().notRequired(),
					url: Yup.string().notRequired(),
					id: Yup.string().notRequired(),
				})
				.notRequired(),
			officialPDD: Yup.object()
				.shape({
					fileName: Yup.string().notRequired(),
					url: Yup.string().notRequired(),
					id: Yup.string().notRequired(),
				})
				.notRequired(),
		})
		.notRequired(),
	projectImg: Yup.array().of(Yup.string()),
	ProjectImages: Yup.array()
		.of(
			Yup.object().shape({
				id: Yup.string(),
				url: Yup.string(),
				fileName: Yup.string(),
			})
		)
		.notRequired(),
})

export type TCreateProject = Yup.InferType<typeof createProjectSchema>
