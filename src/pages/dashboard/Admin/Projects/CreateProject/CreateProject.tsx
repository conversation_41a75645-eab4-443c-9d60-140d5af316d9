import { CustomHeader } from '@/components'
import { <PERSON>, Button, FormHelperText, Stack, styled } from '@mui/material'
import { FC } from 'react'
import { useCreateProject } from './useCreateProject'
import { FormProvider } from 'react-hook-form'
import { ChevronLeft } from '@mui/icons-material'
import { ProjectAccordion } from '@/interfaces'
import {
	About,
	AddProjectImages,
	CreateProjectAccordion,
	Details,
	ProjectCertification,
	ProjectDescription,
	ProjectDoc,
	ProjectIntroduction,
	ProjectMonitoring,
	ProjectTechnology,
	RepresentationDetails,
} from './components'
import { LoadingButton } from '@mui/lab'

export const CreateProject: FC<{ edit?: boolean }> = ({ edit = false }) => {
	const {
		form,
		handleChange,
		expandedAccord,
		allContacts,
		contacts,
		setContacts,
		handleSave,
		navigate,
		projectInfo,
	} = useCreateProject(edit)

	const accordionList = [
		{
			label: 'Project Introduction',
			value: ProjectAccordion.projectIntroduction,
			component: <ProjectIntroduction editable={edit} />,
		},
		{
			label: 'Project Description',
			value: ProjectAccordion.projectDescription,
			component: <ProjectDescription editable={edit} />,
			className: 'accordionNoPadding',
		},
		{
			label: 'About',
			value: ProjectAccordion.projectAbout,
			component: (
				<About status={projectInfo?.data?.state == 'publish'} edit={edit} />
			),
		},
		{
			label: 'Project Certification',
			value: ProjectAccordion.projectCertification,
			component: <ProjectCertification editable={edit} />,
			className: 'accordionNoPadding',
		},
		{
			label: 'Project Monitoring',
			value: ProjectAccordion.projectMonitoring,
			component: <ProjectMonitoring editable={edit} />,
			className: 'accordionNoPadding',
		},
		{
			label: 'Project Details',
			value: ProjectAccordion.projectDetails,
			component: <Details editable={edit} />,
			className: 'accordionNoPadding',
		},
		{
			label: 'Project Technology',
			value: ProjectAccordion.projectTechnology,
			component: (
				<ProjectTechnology
					editable={edit}
					status={projectInfo?.data?.state == 'publish'}
				/>
			),
			className: 'accordionNoPadding',
		},
		{
			label: 'Representatives Details',
			value: ProjectAccordion.contactDetails,
			component: (
				<RepresentationDetails
					contacts={contacts}
					setContacts={setContacts}
					allContacts={allContacts}
				/>
			),
		},
		{
			label: 'Project Related Documents',
			value: ProjectAccordion.documentDetails,
			component: <ProjectDoc />,
		},
		{
			label: 'Header Images',
			value: ProjectAccordion.projectImg,
			component: (
				<>
					<Stack gap='34px' sx={{ flexDirection: 'row' }}>
						{Array.from({ length: 4 }).map((_, index) => (
							<Stack key={index}>
								<Box
									component='label'
									display='flex'
									justifyContent='center'
									width={161}
									alignItems='center'
									borderRadius={1}
									textTransform='none'>
									<AddProjectImages
										index={index}
										imgUrl={form.watch(`ProjectImages.${index}.url`) ?? ''}
									/>
								</Box>
							</Stack>
						))}
					</Stack>
					{!!form?.formState.errors.projectImg && (
						<FormHelperText
							sx={{ textAlign: 'center' }}
							error={!!form.formState.errors.projectImg}>
							{form.formState.errors?.projectImg?.message}
						</FormHelperText>
					)}
				</>
			),
		},
	]

	return (
		<StyledContainer>
			<Box className='header'>
				<CustomHeader
					showBottomBorder={true}
					heading={`${edit ? 'Edit' : 'Create'} Project`}
					showButton={false}
				/>
			</Box>
			<Stack gap={2} px={{ xs: 3, md: 6 }}>
				<Button
					sx={{ mt: 2, pt: 1, pl: 0, alignSelf: 'self-start' }}
					variant='text'
					startIcon={<ChevronLeft />}
					onClick={() => navigate(-1)}>
					Back
				</Button>
			</Stack>
			<FormProvider {...form}>
				<Stack
					component='form'
					onSubmit={form.handleSubmit(handleSave)}
					mt={3}
					mb={10}
					gap={2}
					px={{ xs: 3, md: 6 }}>
					{accordionList.map((accordion) => (
						<CreateProjectAccordion
							className={accordion.className}
							accordionId={accordion.value}
							key={accordion.value}
							expendedAccord={expandedAccord}
							handleChange={handleChange}
							accordionLabel={accordion.label}>
							{accordion.component || null}
						</CreateProjectAccordion>
					))}
					<Stack alignItems='self-end'>
						<LoadingButton variant='contained' type='submit'>
							Save
						</LoadingButton>
					</Stack>
				</Stack>
			</FormProvider>
		</StyledContainer>
	)
}

const StyledContainer = styled(Stack)(({ theme }) => ({
	'.header': {
		padding: theme.spacing(4, 0),
		borderBottom: `${theme.spacing(0.125)} solid ${
			theme.palette.neutral['100']
		}`,
	},
	'.accordion': {
		boxShadow: '0',
		borderRadius: theme.spacing(1),
	},
	'.accordionNoPadding': {
		paddingTop:0,
	},
}))
