import { useCallback, useState } from 'react'
import { useForm } from 'react-hook-form'
import { createProjectSchema, TCreateProject } from './schema'
import { useMutation, useQuery } from '@tanstack/react-query'
import { proxyImage } from '@/utils/helper'
import { authAxios } from '@/contexts'
import { toast } from 'react-toastify'
import {
	IContactDetails,
	IContactPeople,
	IProjectDetails,
	ProjectAccordion,
} from '@/interfaces'
import { isDate } from 'date-fns'
import { useNavigate, useParams } from 'react-router-dom'
import { yupResolver } from '@hookform/resolvers/yup'

const projectDoc = {
	fileName: '',
	url: '',
	id: '',
}

const defaultValues = {
	projectIntroduction: {
		totalCredits: undefined,
		name: '',
		address: '',
		availableCredits: undefined,
		projectMapLocationURL: '',
		linkToRegistry: '',
		permanence: '1000+ years',
		methaneMitigation: '',
		technology: '',
		rate: undefined,
		publishedStatus: undefined,
		application: '',
		developmentGoals: [],
		projectStatus: '',
		nextAvailableDate: undefined,
	},
	projectAbout: {
		aboutImg: {
			id: '',
			url: '',
			fileName: '',
		},
		CreditingStartDate: undefined,
		projectRegistrationDate: undefined,
		operatorId: 'CSI-000589',
		registryIdDesc: ' CSI Endorsement Certification',
		registryLink: '',
	},
	projectTechnology: {
		biocharImage: {
			id: '',
			url: '',
			fileName: '',
		},
		durabilityImage: {
			id: '',
			url: '',
			fileName: '',
		},
		biocharTechnology:
			'A Kon-Tiki kiln is a specialized device used to produce biochar through pyrolysis. Kon-Tiki kilns are known for their efficiency, affordability, and sustainability in biochar production, making them valuable tools in regenerative agriculture and environmental conservation.',
		durabilityTechnology:
			'The durability of the project is 1,000 years. Many scientific studies prove that biochar in soils does indeed meet the higher ends of permanence. When retiring a c-sink via the registry, it calculates the persistence over 1,000 years',
	},
	contactDetails: [],
	projectDoc: {
		circonomyPDD: projectDoc,
		dMrvendorsement: projectDoc,
		officialPDD: projectDoc,
		ceresCertificate: projectDoc,
	},
	projectImg: [],
	ProjectImages: [
		{ id: '1', url: '', fileName: '' },
		{ id: '2', url: '', fileName: '' },
		{ id: '3', url: '', fileName: '' },
		{ id: '4', url: '', fileName: '' },
	],

	projectCertification: {
		certifierLink: '',
		certifierLinkText: '',
		certifierText: 'Carbon Standards International',
		currentVerifierText: 'CERES-Cert',
		standardLink: '',
		standardLinkText: '',
		standardText: 'Global Artisan C-Sink',
		certificationDetails:
			'Project certification ensures that each listed project meets rigorous high quality and integrity standards, verified by an independent agent',
	},
	projectMonitoring: {
		monitoring:
			'Circonomy has developed a digital Monitoring, Reporting and Verification system (dMRV), endorsed by CSI. Our dMRV continuously track project data, document project activity and provide access for independent assessment of project activity',
	},
	projectDetails: {
		annualCarbonRemoval: '',
		methaneMitigationDetails: '',
		applicationDetails: '',
	},
	projectDescription: {
		shortDescription: '',
		description: '',
	},
}

export const useCreateProject = (edit: boolean) => {
	const navigate = useNavigate()
	const { id: projectId } = useParams()
	const [expandedAccord, setExpandAccord] = useState<ProjectAccordion | null>(
		null
	)
	const [contacts, setContacts] = useState<IContactDetails[]>([])

	const form = useForm<TCreateProject>({
		defaultValues,
		mode: 'all',
		resolver: yupResolver(createProjectSchema),
	})

	const contactRepresentatives = useQuery({
		queryKey: ['contactRepresentatives'],
		queryFn: async () => {
			try {
				const { data } = await authAxios.get<IContactPeople[]>(
					`/projects/contacts`
				)
				return data
			} catch (err: any) {
				toast(err?.response?.data?.messageToUser)
				return []
			}
		},
		select: (data) => {
			return data
				? data?.reduce(
						(acc: IContactDetails[], curr: IContactPeople) => [
							...acc,
							{
								id: curr?.id,
								contactName: curr?.name,
								contactEmail: curr?.email,
								contactPhoto: curr?.imageId,
								contactUrl: proxyImage(curr?.imagePath),
								linkedinLink: curr?.linkedinLink,
							},
						],
						[]
				  )
				: []
		},
	})

	const projectInfo = useQuery({
		queryKey: ['projectInfo'],
		queryFn: async () => {
			try {
				const { data } = await authAxios.get<IProjectDetails>(
					`/projects/${projectId}/v2`
				)

				const projectIntroduction = {
					totalCredits: data?.creditsIssued ?? undefined,
					name: data?.name ?? '',
					address: data?.address ?? '',
					availableCredits: data?.available ?? undefined,
					projectMapLocationURL: data?.projectMapLocationURL ?? '',
					linkToRegistry: data?.linkToRegistry ?? '',
					technology: data?.technology ?? '',
					rate: data?.rate ?? undefined,
					methaneMitigation: data?.methaneMitigation,
					application: data?.application ?? '',
					developmentGoals: data?.unGoalIds,
					projectStatus: data?.projectStatus,
					permanence: data?.permanence,
					publishedStatus: data?.state ?? '',
					nextAvailableDate: data?.nextAvailableDate
						? new Date(data?.nextAvailableDate)
						: undefined,
					seoNameUrl: data?.seoNameUrl || '',
				}
				const projectDescription = {
					shortDescription: data?.shortDescription ?? '',
					description: data?.description ?? '',
				}

				const projectAbout = {
					aboutImg: {
						id: data?.aboutProjectImageId ?? '',
						url: data?.aboutProjectImageProxyUrl
							? proxyImage(data?.aboutProjectImageProxyUrl, '600:0')
							: data?.aboutProjectImageUrl,
						fileName: '',
					},
					CreditingStartDate: data?.creditingStartDate
						? new Date(data?.creditingStartDate)
						: undefined,
					projectRegistrationDate: data?.registrationDate
						? new Date(data?.registrationDate)
						: undefined,
					operatorId: data?.operatorId || '',
					registryIdDesc: data?.registryDescription || '',
					registryLink: data?.registryUrl || '',
					CreditingEndDate: data?.creditingEndDate
						? new Date(data?.creditingEndDate)
						: undefined,
				}

				const projectCertification = {
					certifierLink: data?.endorsingAgentUrl ?? '',
					certifierLinkText: data?.endorsingAgentDescription ?? '',
					certifierText: data?.endorsingAgentName ?? '',
					currentVerifierText: data?.currentVerifier ?? '',
					standardLink: data?.standardUrl ?? '',
					standardLinkText: data?.standardDescription ?? '',
					standardText: data?.standardName ?? '',
					certificationDetails: data?.certificationDetails ?? '',
				}

				const projectMonitoring = {
					monitoring: data?.monitoring,
				}

				const projectDetails = {
					annualCarbonRemoval: data?.annualCarbonRemovalCapacity ?? '',
					methaneMitigationDetails:
						data?.methaneMitigationStrategyDetails ?? '',
					applicationDetails: data?.applicationDetails,
				}
				const projectTechnology = {
					biocharImage: {
						id: data?.biocharImageId ?? '',
						url: data?.biocharImageProxyUrl
							? proxyImage(data?.biocharImageProxyUrl, '600:0')
							: data?.biocharImageUrl ?? '',
						fileName: '',
					},
					durabilityImage: {
						id: data?.durabilityImageId ?? '',
						url: data?.durabilityImageProxyUrl
							? proxyImage(data?.durabilityImageProxyUrl, '600:0')
							: data?.durabilityImageUrl ?? '',
						fileName: '',
					},
					biocharTechnology: data?.biocharDescription ?? '',
					durabilityTechnology: data?.durabilityDescription ?? '',
				}

				const contactDetails = data?.contacts?.map((item) => ({
					contactName: item.name,
					id: item?.id,
					contactEmail: item.email,
					contactPhoto: item.imageId,
					contactUrl: item.imageURL,
					linkedinLink: item.linkedinLink,
				}))

				const projectDoc = {}
				const projectDocForLabels: { [key: string]: string } = {
					'Circonomy PDD': 'circonomyPDD',
					'dMRV Endorsement letter': 'dMrvendorsement',
					'Official PDD': 'officialPDD',
					'CERES Certificate': 'ceresCertificate',
				}
				data?.documents?.forEach((doc) => {
					const item = {
						id: doc.imageId,
						url: doc.imageURL,
						fileName: doc.imagePath,
					}
					Object.assign(projectDoc, { [projectDocForLabels[doc.name]]: item })
				})
				const headerImages = data?.headerImages?.map((img) => ({
					id: img?.id,
					url: img?.path ? proxyImage(img?.path ?? '', '600:0') : img?.url,
					path: img?.path,
				}))

				const headerImageIds = data?.headerImages?.map((item) => item?.id)

				form.setValue('projectImg', headerImageIds ?? [])
				form.setValue('ProjectImages', headerImages ?? [])

				form.setValue('projectIntroduction', projectIntroduction)
				form.setValue('projectDescription', projectDescription)
				form.setValue('projectAbout', projectAbout)
				form.setValue('projectCertification', projectCertification)
				form.setValue('projectMonitoring', projectMonitoring)
				form.setValue('projectDetails', projectDetails)
				form.setValue('projectTechnology', projectTechnology)
				form.setValue('contactDetails', contactDetails)
				form.setValue('projectDoc', projectDoc)
				return data
			} catch (err: any) {
				toast(err.response?.data?.messageToUser)
				return undefined
			}
		},
		enabled: edit,
	})

	const createOrEditProject = useMutation({
		mutationKey: [edit ? 'editProject' : 'createProject', edit, projectId],
		mutationFn: async (formValues: TCreateProject) => {
			const payload = {
				name: generatePayloadValue(formValues?.projectIntroduction?.name),
				creditsIssued: generatePayloadValue(
					formValues?.projectIntroduction?.totalCredits
				),
				availableCredits: generatePayloadValue(
					formValues?.projectIntroduction?.availableCredits
				),
				permanence: generatePayloadValue(
					formValues?.projectIntroduction?.permanence
				),
				address: generatePayloadValue(formValues?.projectIntroduction?.address),
				projectStatus: generatePayloadValue(
					formValues?.projectIntroduction?.projectStatus
				),
				rate: generatePayloadValue(formValues?.projectIntroduction?.rate),

				linkToRegistry: generatePayloadValue(
					formValues?.projectIntroduction?.linkToRegistry ?? ''
				),
				technology: generatePayloadValue(
					formValues?.projectIntroduction?.technology
				),
				application: generatePayloadValue(
					formValues?.projectIntroduction?.application
				),
				nextAvailableDate: generatePayloadValue(
					formValues?.projectIntroduction?.nextAvailableDate ?? null
				),
				methaneMitigationStrategyDetails: generatePayloadValue(
					formValues.projectDetails.methaneMitigationDetails
				),
				methaneMitigation: generatePayloadValue(
					formValues?.projectIntroduction?.methaneMitigation
				),
				headerImageIds: generatePayloadValue(
					(formValues?.projectImg ?? []) as string[]
				),
				projectMapLocationURL: generatePayloadValue(
					formValues?.projectIntroduction?.projectMapLocationURL
				),
				shortDescription: generatePayloadValue(
					formValues?.projectDescription?.shortDescription
				),
				description: generatePayloadValue(
					formValues?.projectDescription?.description
				),
				operatorId: generatePayloadValue(formValues.projectAbout.operatorId),
				registryUrl: generatePayloadValue(formValues.projectAbout.registryLink),
				registryDescription: generatePayloadValue(
					formValues.projectAbout.registryIdDesc
				),
				registrationDate: generatePayloadValue(
					formValues.projectAbout.projectRegistrationDate?.toISOString()
				),
				creditingStartDate: generatePayloadValue(
					formValues.projectAbout.CreditingStartDate?.toISOString()
				),
				creditingEndDate: generatePayloadValue(
					formValues.projectAbout.CreditingEndDate?.toISOString() ?? null
				),
				aboutProjectImageId: generatePayloadValue(
					formValues?.projectAbout?.aboutImg?.id
				),
				endorsingAgentUrl: generatePayloadValue(
					formValues.projectCertification.certifierLink
				),
				endorsingAgentName: generatePayloadValue(
					formValues.projectCertification.certifierText
				),
				endorsingAgentDescription: generatePayloadValue(
					formValues.projectCertification.certifierLinkText
				),
				standardUrl: generatePayloadValue(
					formValues.projectCertification.standardLink
				),
				standardName: generatePayloadValue(
					formValues.projectCertification.standardText
				),
				standardDescription: generatePayloadValue(
					formValues.projectCertification.standardLinkText
				),
				currentVerifier: generatePayloadValue(
					formValues.projectCertification.currentVerifierText
				),
				certificationDetails: generatePayloadValue(
					formValues.projectCertification.certificationDetails
				),
				biocharImageID: generatePayloadValue(
					formValues?.projectTechnology?.biocharImage?.id
				),
				biocharDescription: generatePayloadValue(
					formValues.projectTechnology.biocharTechnology
				),
				durabilityImageID: generatePayloadValue(
					formValues?.projectTechnology?.durabilityImage?.id
				),
				durabilityDescription: generatePayloadValue(
					formValues.projectTechnology.durabilityTechnology
				),
				annualCarbonRemovalCapacity: generatePayloadValue(
					formValues.projectDetails.annualCarbonRemoval
				),
				applicationDetails: generatePayloadValue(
					formValues.projectDetails.applicationDetails
				),
				unGoalIds: generatePayloadValue(
					(formValues?.projectIntroduction?.developmentGoals ?? []) as number[]
				),
				monitoring: generatePayloadValue(
					formValues.projectMonitoring?.monitoring
				),
				contacts: formValues?.contactDetails?.map((value) => ({
					id: generatePayloadValue(value?.id),
					name: generatePayloadValue(value.contactName),
					imageId: generatePayloadValue(value.contactPhoto),
					description: generatePayloadValue(''),
					email: generatePayloadValue(value.contactEmail),
					linkedinLink: generatePayloadValue(value?.linkedinLink),
				})),
				contactIds: contacts?.map((value) => value?.id) ?? [],
				documents: [
					{
						name: 'Circonomy PDD',
						imageId: formValues?.projectDoc?.circonomyPDD?.id,
					},
					{
						name: 'CERES Certificate',
						imageId: formValues?.projectDoc?.ceresCertificate?.id,
					},
					{
						name: 'Official PDD',
						imageId: formValues?.projectDoc?.officialPDD?.id,
					},
					{
						name: 'dMRV Endorsement letter',
						imageId: formValues?.projectDoc?.dMrvendorsement?.id,
					},
				].filter((item) => !!item.imageId),
				seoNameUrl: generatePayloadValue(
					formValues?.projectIntroduction?.seoNameUrl?.trim()
				),
			}
			if (!edit) {
				await authAxios.post(`/projects/v2`, payload)
			} else {
				await authAxios.put(`projects/${projectId}`, payload)
			}
		},
		onError: (err: any) => {
			toast(err?.response?.data?.messageToUser)
		},
		onSuccess: () => {
			toast('Created Successfully')
			navigate(-1)
		},
	})

	const handleSave = useCallback(
		(values: TCreateProject) => {
			createOrEditProject.mutate(values)
		},
		[createOrEditProject]
	)

	const handleChange =
		(panel: ProjectAccordion) =>
		(_: React.SyntheticEvent, isExpanded: boolean) => {
			setExpandAccord(isExpanded ? panel : null)
		}

	return {
		form,
		handleChange,
		expandedAccord,
		allContacts: contactRepresentatives?.data || [],
		contacts,
		setContacts,
		handleSave,
		navigate,
		projectInfo,
	}
}

const generatePayloadValue = <T>(value: T) => {
	if (value === '' || value === null) return undefined
	if (
		typeof value !== 'number' &&
		!isDate(value) &&
		(value as string[] | number[])?.length === 0
	)
		return undefined
	return value
}
