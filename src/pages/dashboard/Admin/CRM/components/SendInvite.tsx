import {
	Dialog,
	DialogTitle,
	DialogContent,
	DialogActions,
	TextField,
	Button,
	IconButton,
	Stack,
	Typography,
	CircularProgress,
} from '@mui/material'
import CloseIcon from '@mui/icons-material/Close'
import { useForm } from 'react-hook-form'
import { theme } from '@/lib/theme/theme'
import { emailRegex } from '@/utils/constant'

interface InviteFormValues {
	companyName: string
	managerEmail: string
	managerName: string
}

interface InviteProps {
	onClose: () => void
	onSubmit: (data: InviteFormValues) => void
	open: boolean
	loading: boolean
}

export default function SendInvite({
	onClose,
	onSubmit,
	open,
	loading,
}: InviteProps) {
	const {
		register,
		handleSubmit,
		formState: { errors },
	} = useForm<InviteFormValues>({
		defaultValues: {
			companyName: '',
			managerEmail: '',
			managerName: '',
		},
		mode: 'onBlur',
	})

	return (
		<Dialog
			open={open}
			onClose={onClose}
			fullWidth
			maxWidth='sm'
			PaperProps={{
				sx: {
					borderRadius: 4,
					padding: 3,
				},
			}}>
			<DialogTitle>
				<Typography variant='h5' textAlign='center'>
					Send Invite
				</Typography>
				<IconButton
					onClick={onClose}
					sx={{ position: 'absolute', right: 8, top: 8 }}>
					<CloseIcon />
				</IconButton>
			</DialogTitle>

			<form onSubmit={handleSubmit(onSubmit)}>
				<DialogContent>
					<Stack spacing={2}>
						<TextField
							label='Company name'
							fullWidth
							{...register('companyName', {
								required: 'Company name is required',
							})}
							error={!!errors.companyName}
							helperText={errors.companyName?.message}
						/>

						<Stack direction='row' spacing={2}>
							<TextField
								label='Email'
								fullWidth
								{...register('managerEmail', {
									required: 'Email is required',
									pattern: {
										value: emailRegex,
										message: 'Enter a valid email',
									},
								})}
								error={!!errors.managerEmail}
								helperText={errors.managerEmail?.message}
							/>

							<TextField
								label='Admin Name'
								fullWidth
								{...register('managerName', {
									required: 'Admin name is required',
								})}
								error={!!errors.managerName}
								helperText={errors.managerName?.message}
							/>
						</Stack>
					</Stack>
				</DialogContent>

				<DialogActions sx={{ justifyContent: 'center' }}>
					<Button
						type='submit'
						variant='contained'
						sx={{ bgcolor: theme.palette.primary.main, width: '40%' }}
						disabled={loading} 
					>
            {loading ? <CircularProgress size={20} sx={{ color: theme.palette.primary.main }} /> : 'Send'}
					</Button>
				</DialogActions>
			</form>
		</Dialog>
	)
}
