import { CustomDataGrid, CustomTagComponent } from '@/components'
import { CustomFilter } from '@/components/CustomFilter'
import { MultipleAvatarWrapper } from '@/components/MultipleAvatarWrapper'
import { theme } from '@/lib/theme/theme'
import { Admin } from '@/types'
import { ServiceTypeNames } from '@/utils/constant'
import EmailOutlinedIcon from '@mui/icons-material/EmailOutlined'
import LocalPhoneOutlinedIcon from '@mui/icons-material/LocalPhoneOutlined'
import { Avatar, Box, Chip, Stack, Tooltip, Typography } from '@mui/material'
import {
	GridColDef,
	GridEventListener,
	GridRenderCellParams,
} from '@mui/x-data-grid'
import { format } from 'date-fns'
import { useCallback } from 'react'
import { useNavigate } from 'react-router-dom'
import { stageEnum, useCRM } from '../useCRM'

const renderContactInfo = (icon: JSX.Element, text: string) => (
	<Stack direction='row' alignItems='center' gap={1}>
		{icon}
		<Typography variant='subtitle1' color={theme.palette.grey.A700}>
			{text}
		</Typography>
	</Stack>
)

const renderCompanyDetails = (params: GridRenderCellParams) => {
	const { row } = params
	return (
		<Stack direction='row' alignItems='center' padding={1} gap={3}>
			<Avatar src={row?.companyLogo?.url} />
			<Stack gap={0.5}>
				<Typography variant='body1'>{row?.name ?? '-'}</Typography>
				{renderContactInfo(
					<EmailOutlinedIcon
						sx={{ width: 12, height: 12, color: theme.palette.grey.A700 }}
					/>,
					row?.email ?? '-'
				)}
				{renderContactInfo(
					<LocalPhoneOutlinedIcon
						sx={{ width: 12, height: 12, color: theme.palette.grey.A700 }}
					/>,
					row?.countryCode && row?.phoneNumber
						? `${row.countryCode} ${row.phoneNumber}`
						: '-'
				)}
			</Stack>
		</Stack>
	)
}

const renderAdmins = (params: GridRenderCellParams) => (
	<CustomTagComponent
		label=''
		value={
			<Box
				component={Stack}
				alignItems='end'
				gap={1}
				sx={{ cursor: 'pointer', pointerEvents: 'auto' }}>
				<Tooltip
					title={
						<Stack direction='column'>
							{(params?.row?.admins ?? []).map((admin: Admin) => (
								<Typography key={admin.id} variant='caption'>
									{admin.name} {admin.email}
									<br />
									{admin.countryCode && admin.phoneNumber
										? `(${admin.countryCode}-${admin.phoneNumber})`
										: ''}
								</Typography>
							))}
						</Stack>
					}
					placement='bottom-start'
					arrow>
					<span>
						<MultipleAvatarWrapper
							images={
								params?.row?.admins?.map((a: Admin) => a?.profileImage) ||
								[]
							}
							length={params?.row?.admins?.length || 0}
						/>
					</span>
				</Tooltip>
			</Box>
		}
		lighterHeading
	/>
)
const columns: GridColDef[] = [
	{
		field: 'companyDetails',
		headerName: 'Company Details',
		minWidth: 150,
		flex: 2,
		renderCell: renderCompanyDetails,
	},
	{
		field: 'admins',
		headerName: 'Admins',
		minWidth: 150,
		flex: 1,
		renderCell: renderAdmins,
	},
	{
		field: 'requestedDate',
		headerName: 'Requested Date',
		minWidth: 150,
		flex: 1,
		renderCell: (params) => (
			<Typography variant='subtitle1'>
				{format(new Date(params.row?.requestedAt), 'yyyy-MM-dd')}
			</Typography>
		),
	},
	{
		field: 'stage',
		headerName: 'Stage',
		minWidth: 180,
		flex: 1,
		renderCell: (params) => {
			const stageKey = params?.row?.stage as keyof typeof stageEnum
			return <Typography>{stageEnum[stageKey]}</Typography>
		},
	},
	{
		field: 'status',
		headerName: 'Status',
		minWidth: 180,
		flex: 1,
		renderCell: (params) => {
			const isApproved = params?.row?.stageStatus === 'approved'
			return (
				<Chip
					label={isApproved ? 'Approved' : 'Pending'}
					sx={{
						color: isApproved
							? theme.palette.success.dark
							: theme.palette.warning.dark,
						background: isApproved
							? theme.palette.success.light
							: theme.palette.warning.light,
					}}
				/>
			)
		},
	},
	{
		field: 'appType',
		headerName: 'App Type',
		minWidth: 120,
		flex: 1,
		renderCell: (params) => (
			<Typography className='details'>
				{ServiceTypeNames[
					params?.row?.serviceType as keyof typeof ServiceTypeNames
				] || params?.row?.serviceType}
			</Typography>
		),
	},
]

const stagesList = [
	{ label: stageEnum.contract, value: 'contract' },
	{ label: stageEnum.dmrv_customization, value: 'dmrv_customization' },
	{ label: stageEnum.registration, value: 'registration' },
]

const statusList = [
	{ label: 'Approved', value: 'approved' },
	{ label: 'Pending', value: 'pending' },
]

export const AccountManagement = () => {
	const { allCompanys: allCompanies } = useCRM()
	const navigate = useNavigate()

	const handleRowClick: GridEventListener<'rowClick'> = useCallback(
		(params) => {
			const stage = params?.row?.stage
			const stageStatus = params?.row?.stageStatus
			const status = params?.row?.status
			let isApproved = false
			let isShowContractDetails = false

			switch (stage) {
				case 'registration':
					isApproved = stageStatus === 'approved'
					isShowContractDetails = status === 'approved'
					break

				case 'contract':
				case 'dmrv_customization':
					isApproved = stageStatus === 'pending' || stageStatus === 'approved'
					isShowContractDetails = status === 'pending' || status === 'approved'
					break

				default:
					isApproved = false
					isShowContractDetails = false
			}

			navigate(
				`/dashboard/admin/company-details/${params?.row?.id}?isApproved=${isApproved}&isShowContractDetails=${isShowContractDetails}`
			)
		},
		[navigate]
	)

	return (
		<Stack className='container'>
			<CustomDataGrid
				sx={{ padding: 1 }}
				loading={allCompanies?.isPending}
				showPagination
				rows={
					allCompanies?.data?.companies?.map((item) => ({
						...item,
						id: item.id ?? item.requestId,
					})) || []
				}
				columns={columns}
				showRowsPerPage={false}
				rowCount={allCompanies?.data?.count ?? 0}
				headerStyle={{ alignSelf: 'start', paddingX: 2 }}
				headerEndComponent={
					<>
						<CustomFilter
							queryKey='stages'
							filtersToReset={['page', 'limit']}
							label='Stage'
							options={stagesList}
						/>
						<CustomFilter
							queryKey='statuses'
							filtersToReset={['page', 'limit']}
							label='Status'
							options={statusList}
						/>
					</>
				}
				onRowClick={handleRowClick}
			/>
		</Stack>
	)
}
