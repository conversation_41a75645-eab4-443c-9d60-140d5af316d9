import { TabContext, TabList } from '@mui/lab'
import { Button, Stack, styled, Tab, Typography } from '@mui/material'
import { Queries } from '../Queries'
import { AccountManagement } from './components'
import { useCRM } from './useCRM'
import SendInvite from './components/SendInvite'
import { tabCrmEnum } from './useCRM'
import AddIcon from '@mui/icons-material/Add'

const { VITE_PUBLIC_APP_ENV } = import.meta.env

const showAccountManagementTab = ['DEV', 'QA'].includes(VITE_PUBLIC_APP_ENV)

export const CRM = () => {
	const {
		tabs,
		handleChange,
		paramsTab,
		showInvite,
		setShowInvite,
		handleInviteSubmit,
		isLoadingInvitaion,
	} = useCRM()
	return (
		<StyledContainer>
			<Stack className='tab-container'>
				<Typography variant='h2'>CRM</Typography>
				<Stack
					className='tab-header'
					direction='row'
					alignItems='center'
					justifyContent='space-between'>
					<TabContext value={paramsTab}>
						{showAccountManagementTab ? (
							<TabList className='tabList' onChange={handleChange}>
								{tabs.map(({ label, value }) => (
									<Tab key={value} label={label} value={value} />
								))}
							</TabList>
						) : null}
					</TabContext>
					{paramsTab === tabCrmEnum.accountsManagement && (
						<Button
							variant='contained'
							color='primary'
							startIcon={<AddIcon />}
							onClick={() => setShowInvite(true)}>
							Send Invitation
						</Button>
					)}
				</Stack>
			</Stack>
			{paramsTab === tabCrmEnum.queries ? <Queries /> : <AccountManagement />}
			{showInvite && (
				<SendInvite
					open={showInvite}
					onClose={() => setShowInvite(false)}
					onSubmit={handleInviteSubmit}
					loading={isLoadingInvitaion}
				/>
			)}
		</StyledContainer>
	)
}
const StyledContainer = styled(Stack)(({ theme }) => ({
	gap: theme.spacing(2),
	'.tab-container': {
		padding: theme.spacing(2),
		// border: `${theme.spacing(0.125)} solid ${theme.palette.neutral['100']}`,
	},
	'.tabList': {
		'.MuiTab-root': {
			minWidth: 'fit-content',
			'&.Mui-selected': {
				color: theme.palette.primary.main,
			},
			'&:not(.Mui-selected)': {
				color: theme.palette.neutral['500'],
			},
		},
	},
	'.tabList .MuiTabs-indicator': {
		backgroundColor: theme.palette.primary.main,
	},
	'.tabList .MuiTabs-flexContainer': {
		gap: theme.spacing(2),
	},
	'.tabList .MuiTabs-scrollableX': {
		overflowX: 'auto',
	},
	'.tabList .MuiTabs-scrollableX::-webkit-scrollbar': {
		display: 'none',
	},

	// '.tabList .MuiTabs-scrollableX': {
	// 	overflowX: 'auto',
	// 	overflowY: 'hidden',
	// },
	// '.tabList .MuiTabs-scrollableX::-webkit-scrollbar': {
	// 	display: 'none',
	// },
	// '.tabList .MuiTabs-scrollableX': {
	// 	overflowX: 'auto',
	// },
}))
