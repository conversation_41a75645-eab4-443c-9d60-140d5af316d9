import { authAxios } from '@/contexts'
import { CompanyListResponseV2 } from '@/types'
import { defaultLimit, defaultPage } from '@/utils/constant'
import { showAxiosErrorToast } from '@/utils/helper'
import { useMutation, useQuery } from '@tanstack/react-query'
import { AxiosError } from 'axios'
import { useCallback, useState } from 'react'
import { useSearchParams } from 'react-router-dom'
import { toast } from 'react-toastify'

export enum tabCrmEnum {
	queries = 'queries',
	accountsManagement = 'accountsManagement',
}
export enum stageEnum {
	registration = 'Registration',

	contract = 'Contract',
	dmrv_customization = 'dMRV Customization',
}

const tabs = [
	{ label: 'Queries', value: tabCrmEnum.queries },
	{
		label: 'Accounts Management',
		value: tabCrmEnum.accountsManagement,
	},
]
export const useCRM = () => {
	const [searchParams, setSearchParams] = useSearchParams()
	const paramsTab = searchParams.get('tab') || tabCrmEnum.queries
	const paramsLimit = searchParams.get('limit') || defaultLimit.toString()
	const paramsPage = searchParams.get('page') || defaultPage.toString()
	const paramsStages = searchParams.getAll('stages') || []
	const paramsStatus = searchParams.getAll('statuses') || []
	const [showInvite, setShowInvite] = useState(false)

	const handleChange = useCallback(
		(_: unknown, newValue: tabCrmEnum) => {
			setSearchParams((prev) => {
				prev.set('tab', newValue)
				prev.set('page', '0')
				prev.set('limit', '10')
				return prev
			})
		},
		[setSearchParams]
	)
	const allCompanys = useQuery({
		queryKey: [
			'allCompanys',
			paramsLimit,
			paramsPage,
			paramsStages,
			paramsStatus,
		],
		queryFn: async () => {
			const queryParams = new URLSearchParams({
				limit: paramsLimit,
				page: paramsPage,
				stages: paramsStages.join(','),
				status: paramsStatus.join(','),
			})
			const { data } = await authAxios.get<CompanyListResponseV2>(
				`/company?${queryParams.toString()}`
			)
			return data
		},
		enabled: true,
	})

	const { mutateAsync: handleInviteSubmit, isPending: isLoadingInvitaion } =
		useMutation({
			mutationFn: async (formData: {
				companyName: string
				managerEmail: string
				managerName: string
			}) => {
				const response = await authAxios.post(
					`/company/public/registration-invitation`,
					formData
				)
				return response.data
			},
			onError: (error: AxiosError) => {
				showAxiosErrorToast(error)
			},
			onSuccess: (data: { message?: string }) => {
				toast.success(data?.message)
				setShowInvite(() => false)
			},
		})

	return {
		tabs,
		paramsTab,
		handleChange,
		allCompanys,
		showInvite,
		setShowInvite,
		handleInviteSubmit,
		isLoadingInvitaion,
	}
}
