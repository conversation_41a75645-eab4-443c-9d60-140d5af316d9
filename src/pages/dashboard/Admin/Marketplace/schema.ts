import * as yup from 'yup'

export const addTeamMemberSchema = yup.object().shape({
	profileImage: yup.object().shape({
		id: yup.string().required('Profile image is required'),
		url: yup.string().optional(),
		path: yup.string().optional(),
	}),
	name: yup.string().required('Name is required'),
	position: yup.string().required('Position is required'),
	linkedinUrl: yup
		.string()
		.url('Enter a valid LinkedIn URL')
		.required('LinkedIn URL is required'),
})
