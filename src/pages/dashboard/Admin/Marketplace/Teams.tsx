import { useMemo, useState } from 'react'
import {
	St<PERSON>,
	Typography,
	Avatar,
	Button,
	IconButton,
	Box,
	debounce,
} from '@mui/material'


import { GridColDef, GridValidRowModel } from '@mui/x-data-grid'
import { CustomDataGrid } from '@/components/CustomDatagrid'
import { theme } from '@/lib/theme/theme'
import { ActionInformationDrawer, QueryInput } from '@/components'
import { AddRounded, Search } from '@mui/icons-material'
import { AddTeamMember } from './AddTeamMemberDrawer'
import {  useMarketplace } from './useMarketplace'
import { Edit, Delete } from '@mui/icons-material'
import { useSearchParams } from 'react-router-dom'
import { IAddTeamMember } from './useMarketplace'
import { Confirmation } from '@/components/Confirmation'

export const Teams = () => {
	const [isAddTeamDrawer, setIsAddTeamDrawer] = useState<boolean>(false)
	const [selectedMemberId, setSelectedMemberId] = useState<string | null>(null)
	const [searchParams, setSearchParams] = useSearchParams()
	const search = searchParams.get('search')?.toLowerCase() || ''
	const [openConfirmDialog, setOpenConfirmDialog] = useState(false)
	const [memberToDelete, setMemberToDelete] = useState<string | null>(null)
	const { deleteMember,teamsData, isLoading} =
		useMarketplace()
	const handleSearchChange = debounce((value: string) => {
		setSearchParams(value ? { search: value } : {})
	}, 300)

	//delete function
	const handleDelete = async (id: string) => {
		deleteMember(id)
	}
	//search function
	const filteredRows =
		teamsData?.members?.filter((member: IAddTeamMember) => {
			if (!search) return true

			const name = member.name?.toLowerCase() || ''
			const position = member.position?.toLowerCase() || ''

			return name.includes(search) || position.includes(search)
		}) ?? []
		
		const handleConfirmDelete = async () => {
			if (memberToDelete) {
				await handleDelete(memberToDelete)
			}
			setOpenConfirmDialog(false)
			setMemberToDelete(null)
		}
	const teamsColumn: GridColDef<GridValidRowModel>[] = useMemo(
		() => [
			{
				field: 'memberDetails',
				headerName: 'Member Details',
				minWidth: 350,
				flex: 1,
				renderCell: (params) => (
					<Stack flexDirection='row' alignItems='center' gap={1}>
						<Avatar src={params.row.profileImage.url} />
						<Stack display={'flex'} flexDirection={'column'}>
							<Typography variant='subtitle2'>{params.row.name}</Typography>
							<Typography variant='subtitle1'>{params.row.position}</Typography>
						</Stack>
					</Stack>
				),
			},
			{
				field: 'actions',
				headerName: 'Actions',
				minWidth: 150,
				flex: 1,
				sortable: false,
				filterable: false,
				renderCell: (params) => (
					<Box display='flex' gap={1}>
						<IconButton
							color='primary'
							size='small'
							onClick={() => {
								setSelectedMemberId(params.row.id)
								setIsAddTeamDrawer(true)
							}}>
							<Edit fontSize='small' />
						</IconButton>

						<IconButton
							color='primary'
							size='small'
							onClick={() => {
								setMemberToDelete(params.row.id)
								setOpenConfirmDialog(true)
							}}>
							<Delete fontSize='small' />
						</IconButton>
					</Box>
				),
			},
		],
		[]
	)

	return (
		<>
			<ActionInformationDrawer
				open={isAddTeamDrawer}
				onClose={() => {
					setIsAddTeamDrawer(false)
					setSelectedMemberId(null)
				}}
				anchor='right'
				component={
					<AddTeamMember
						memberId={selectedMemberId ?? undefined}
						mode={selectedMemberId ? 'edit' : 'add'}
						onClose={() => {
							setIsAddTeamDrawer(false)
							setSelectedMemberId(null)
						}}
					/>
				}
			/>

			<Box
				className='header'
				display='flex'
				justifyContent='space-between'
				alignItems='center'>
				<QueryInput
					className='search-textFiled'
					queryKey='search'
					placeholder='Search'
					setPageOnSearch
					sx={{
						height: 36,
						ml: 1.5,
						'& .MuiInputBase-root': {
							height: 36,
						},
					}}
					InputProps={{
						startAdornment: <Search fontSize='small' />,
					}}
					onChange={(e) => handleSearchChange(e.target.value)}
				/>

				<Stack direction='row' spacing={2}>
					<Button
						onClick={() => setIsAddTeamDrawer(true)}
						variant='contained'
						size='small'
						startIcon={<AddRounded />}>
						Add Team Member
					</Button>
				</Stack>
			</Box>

			<Stack padding={theme.spacing(2)}>
				<CustomDataGrid
					columns={teamsColumn}
					showRowsPerPage={false}
					loading={isLoading}
					rows={filteredRows}
					showPagination={false}
				/>
			</Stack>
			<Confirmation
				open={openConfirmDialog}
				handleClose={() => {
					setOpenConfirmDialog(false)
					setMemberToDelete(null)
				}}
				title='Confirm Deletion'
				confirmationText='Are you sure you want to delete this team member? This action cannot be undone.'
				confirmationSuccessBtnText='Delete'
				confirmationNotSuccessBtnText='Cancel'
				successBtnVariant='contained'
				handleYesClick={handleConfirmDelete}
				handleNoClick={() => {
					setOpenConfirmDialog(false)
					setMemberToDelete(null)
				}}
			/>
		</>
	)
}
