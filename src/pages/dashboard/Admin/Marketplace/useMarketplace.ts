import { useCallback } from 'react'
import { useSearchParams } from 'react-router-dom'
import {
	QueryFunctionContext,
	useMutation,
	useQuery,
	useQueryClient,
} from '@tanstack/react-query'
import { authAxios } from '@/contexts'
import { toast } from 'react-toastify'
import { showAxiosErrorToast } from '@/utils/helper'
import { AxiosError } from 'axios'

export interface IAddTeamMember {
	profileImage: {
		id: string
		url?: string
		path?: string
	}
	name: string
	position: string
	linkedinUrl: string
}
export enum tabMarketplaceEnum {
	projects = 'projects',
	teams = 'teams',
}

const tabs = [
	{ label: 'Projects', value: tabMarketplaceEnum.projects },
	{
		label: 'Teams',
		value: tabMarketplaceEnum.teams,
	},
]
const fetchTeams = async () => {
	try {
		console.log('All Members')
		const response = await authAxios.get(`/members`)
		return response.data
	} catch (err: any) {
		showAxiosErrorToast(err)
	}
}
//get member by id
const getMemberById = async ({ queryKey }: QueryFunctionContext) => {
	try {
		const id = queryKey[1]
		const response = await authAxios.get(`/members/${id}`)
		return response
	} catch (error) {
		console.error('Failed to fetch member by id:', error)
		throw error
	}
}

export const useMarketplace = (
	memberId?: string | null,
	isEnabledMember?: boolean,
	setIsAddTeamDrawer?: (value: boolean) => void 
) => {
	const [searchParams, setSearchParams] = useSearchParams()
	const paramsTab = searchParams.get('tab') || tabMarketplaceEnum.projects
	const queryClient = useQueryClient()

	const { data: teamsData, isLoading } = useQuery({
		queryKey: ['team-members', paramsTab],
		queryFn: () => fetchTeams(),
		enabled: paramsTab === tabMarketplaceEnum.teams && !isEnabledMember,
	})

	const { data: memberData } = useQuery({
		queryKey: ['team-members', memberId],
		queryFn: getMemberById,
		enabled: !!memberId,
	})

	//add member
	const { mutate: addTeamMember, isPending: isAddMemberPending } = useMutation({
		mutationFn: async (values: IAddTeamMember) => {
			const payload = {
				name: values.name,
				position: values.position,
				linkedinUrl: values.linkedinUrl,
				profileImage: values.profileImage.id,
			}
			const { data } = await authAxios.post('/members', payload)
			toast.success(data?.message || 'Member added successfully')
			return data
		},
		onSuccess: () => {
			queryClient.invalidateQueries({ queryKey: ['team-members'] })
			if (setIsAddTeamDrawer) {
                setIsAddTeamDrawer(false) 
            }
		},
		onError: (err: AxiosError) => {
			showAxiosErrorToast(err)
		},
	})

	//delete member
	const { mutate: deleteMember, isPending: isDeleteMemberPending } =
		useMutation({
			mutationFn: async (id: string) => {
				await authAxios.delete(`/members/${id}`)
			},
			onSuccess: () => {
				toast.success('Member deleted successfully')
				queryClient.invalidateQueries({ queryKey: ['team-members'] })
			},
			onError: (err: AxiosError) => {
				showAxiosErrorToast(err)
			},
		})

	//edit member
	const { mutate: editMember, isPending: isEditMemberPending } = useMutation({
		mutationFn: async ([id, values]: [string, IAddTeamMember]) => {
			const payload = {
				name: values.name,
				position: values.position,
				linkedinUrl: values.linkedinUrl,
				profileImage: values.profileImage.id,
			}
			const { data } = await authAxios.put(`/members/${id}`, payload)
			return data
		},
		onSuccess: () => {
			toast.success('Member edit successfully')
			queryClient.invalidateQueries({ queryKey: ['team-members'] })
			if (setIsAddTeamDrawer) {
                setIsAddTeamDrawer(false) 
            }
		},
		onError: (err: AxiosError) => {
			showAxiosErrorToast(err)
		},
	})

	const handleChange = useCallback(
		(_: unknown, newValue: tabMarketplaceEnum) => {
			setSearchParams((prev) => {
				prev.set('tab', newValue)
				prev.set('page', '0')
				prev.set('limit', '10')
				return prev
			})
		},
		[setSearchParams]
	)

	return {
		tabs,
		paramsTab,
		handleChange,
		memberData,
		teamsData,
		isLoading,
		addTeamMember,
		getMemberById,
		deleteMember,
		editMember,
		isAddMemberPending,
		isEditMemberPending,
		isDeleteMemberPending,
	}
}
