import {
	<PERSON><PERSON>,
	CircularP<PERSON>ress,
	Icon<PERSON>utton,
	<PERSON>ack,
	TextField,
	Typography,
	styled,
} from '@mui/material'
import { Controller, useForm } from 'react-hook-form'
import { yupResolver } from '@hookform/resolvers/yup'
import { Close } from '@mui/icons-material'
import { addTeamMemberSchema } from './schema'
import { CustomProfileElement } from '@/components'
import { useMarketplace } from './useMarketplace'
import { useEffect } from 'react'
import { IAddTeamMember } from './useMarketplace'

interface IAddMemberProps {
	onClose: () => void
	memberId?: string
	mode?: 'add' | 'edit'
}

export const AddTeamMember = ({
	onClose,
	memberId,
	mode = 'add',
}: IAddMemberProps) => {
	const {
		addTeamMember,
		memberData,
		editMember,
		isAddMemberPending,
		isEditMemberPending,
	} = useMarketplace(memberId ,true,onClose)
	const isSubmitting = isAddMemberPending || isEditMemberPending

	const {
		handleSubmit,
		formState: { errors },
		setValue,
		clearErrors,
		reset,
		control,
		watch,
	} = useForm<IAddTeamMember>({
		defaultValues: {
			profileImage: {
				id: '',
				url: '',
				path: '',
			},
			name: '',
			position: '',
			linkedinUrl: '',
		},
		resolver: yupResolver(addTeamMemberSchema),
		mode: 'onChange',
	})

	useEffect(() => {
		if (memberData) {
			reset({
				profileImage: {
					id: memberData.data.profileImage?.id || '',
					url: memberData.data.profileImage?.url || '',
					path: memberData.data.profileImage?.path || '',
				},
				name: memberData.data.name,
				position: memberData.data.position,
				linkedinUrl: memberData.data.linkedinUrl,
			})
		}
	}, [memberData, reset])

	const submitForm = (values: IAddTeamMember) => {
		if (mode === 'edit' && memberId) {
			editMember([memberId, values])
		} else {
			addTeamMember(values)
		}
	}

	return (
		<StyleContainer>
			<Stack className='header'>
				<Typography variant='h4'>
					{mode === 'edit' ? 'Edit Member' : 'Add Member'}
				</Typography>
				<IconButton onClick={onClose}>
					<Close />
				</IconButton>
			</Stack>

			<Stack
				component='form'
				className='container'
				onSubmit={handleSubmit(submitForm)}
				rowGap={2}>
				<CustomProfileElement
					{...(mode === 'edit' &&
						!!watch('profileImage.id') && {
							value: watch('profileImage'),
						})}
					errorMessage={errors?.profileImage?.id?.message}
					setValue={(id: string, url: string, path?: string) => {
						setValue('profileImage', {
							id,
							url,
							path,
						})
						clearErrors('profileImage.id')
					}}
					clearErrors={() => clearErrors('profileImage.id')}
				/>
				<Controller
					name='name'
					control={control}
					render={({ field }) => (
						<TextField
							{...field}
							label='Name'
							fullWidth
							error={!!errors.name}
							helperText={errors.name?.message}
						/>
					)}
				/>

				<Controller
					name='position'
					control={control}
					render={({ field }) => (
						<TextField
							{...field}
							label='Position'
							fullWidth
							error={!!errors.position}
							helperText={errors.position?.message}
						/>
					)}
				/>

				<Controller
					name='linkedinUrl'
					control={control}
					render={({ field }) => (
						<TextField
							{...field}
							label='LinkedIn URL'
							fullWidth
							error={!!errors.linkedinUrl}
							helperText={errors.linkedinUrl?.message}
						/>
					)}
				/>
				<Button
					type='submit'
					variant='contained'
					color='primary'
					disabled={isSubmitting}
					startIcon={isSubmitting ? <CircularProgress size={20} /> : null}>
					{mode === 'edit' ? 'Edit' : 'Add'}
				</Button>
			</Stack>
		</StyleContainer>
	)
}

const StyleContainer = styled(Stack)(({ theme }) => ({
	gap: theme.spacing(2),
	'.header': {
		flexDirection: 'row',
		justifyContent: 'space-between',
		alignItems: 'center',
		padding: theme.spacing(4.5, 2),
		borderBottom: `${theme.spacing(0.125)} solid ${
			theme.palette.neutral['100']
		}`,
	},
	'.container': {
		padding: theme.spacing(2, 4),
		gap: theme.spacing(3),
		'.buttonContainer button': {
			width: theme.spacing(30),
			height: theme.spacing(4.5),
		},
	},
}))
