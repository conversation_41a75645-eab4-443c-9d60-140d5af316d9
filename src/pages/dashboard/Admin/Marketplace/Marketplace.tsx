import { Stack, styled, Tab, Typography } from '@mui/material'
import { Projects } from '../Projects'
import { useMarketplace} from './useMarketplace'
import { TabContext, TabList } from '@mui/lab'
import { Teams }from './Teams'

export const Marketplace = () => {
	const { tabs, handleChange, paramsTab } = useMarketplace()
	return (
		<StyledContainer>
			<Stack className='tab-container'>
				<Typography variant='h2'>Website</Typography>

				<Stack
					className='tab-header'
					direction='row'
					alignItems='center'
					justifyContent='space-between'>
					<TabContext value={paramsTab}>
						<TabList className='tabList' onChange={handleChange}>
							{tabs.map(({ label, value }) => (
								<Tab key={value} label={label} value={value} />
							))}
						</TabList>
					</TabContext>
				</Stack>
			</Stack>
			{paramsTab === 'projects' ? <Projects /> : <Teams />}
		</StyledContainer>
	)
}
const StyledContainer = styled(Stack)(({ theme }) => ({
	gap: theme.spacing(2),
	'.tab-container': {
		padding: theme.spacing(2),
	},
	'.tabList': {
		'.MuiTab-root': {
			minWidth: 'fit-content',
			'&.Mui-selected': {
				color: theme.palette.primary.main,
			},
			'&:not(.Mui-selected)': {
				color: theme.palette.neutral['500'],
			},
		},
	},
	'.tabList .MuiTabs-indicator': {
		backgroundColor: theme.palette.primary.main,
	},
	'.tabList .MuiTabs-flexContainer': {
		gap: theme.spacing(2),
	},
	'.tabList .MuiTabs-scrollableX': {
		overflowX: 'auto',
	},
	'.tabList .MuiTabs-scrollableX::-webkit-scrollbar': {
		display: 'none',
	},
}))
