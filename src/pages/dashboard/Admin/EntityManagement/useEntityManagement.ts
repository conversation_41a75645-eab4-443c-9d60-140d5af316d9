import { authAxios, useAuthContext } from '@/contexts'
import {
	IBiomassAggregatorResponse,
	ICSinkManagerResponse,
	IAPNResponse,
	IAPsResponse,
	ICSinkNetworkResponse,
	ILocation,
	EntityEnum,
	CropforCsinkManager,
	IEntitytabsCount,
	ManagerDetails,
} from '@/interfaces'
import { IBaArtisanProNetworks } from '@/interfaces/BiomassAggregator.type'
import { BiomassOptionType, ICrop } from '@/types'
import { defaultLimit, defaultPage, userRoles } from '@/utils/constant'
import { GridEventListener } from '@mui/x-data-grid'
import { useMutation, useQuery } from '@tanstack/react-query'
import { AxiosError } from 'axios'
import { useCallback, useMemo, useState } from 'react'
import { useNavigate, useSearchParams } from 'react-router-dom'
import { toast } from 'react-toastify'

enum tabEnum {
	cSinkManager = 'cSinkManager',
	ba = 'ba',
	apn = 'apn',
	cSinkNetwork = 'csn',
	artisanpros = 'aps',
}
interface AssignBiomassState {
	id: string
	biomass: CropforCsinkManager[]
}

const getToggleSuspendUrl = (
	tab: string,
	id: string,
	isSuspend: boolean
): string => {
	switch (tab) {
		case tabEnum.cSinkNetwork:
			return `/cs-network/${id}/toggle-suspend?isSuspend=${isSuspend}`
		case tabEnum.ba:
			return `/biomass-aggregator/${id}/toggle-suspend?isSuspend=${isSuspend}`
		case tabEnum.apn:
			return `/artisan-pro-network/${id}/toggle-suspend?isSuspend=${isSuspend}`
		case tabEnum.artisanpros:
			return `/artisian-pro/${id}/toggle-suspend?isSuspend=${isSuspend}`
		default:
			return `/csink-manager/${id}/toggle-suspend?isSuspend=${isSuspend}`
	}
}

export const useEntityManagement = () => {
	const [searchParams, setSearchParams] = useSearchParams()
	const page = searchParams.get('page') || defaultPage
	const limit = searchParams.get('limit') || defaultLimit
	// const tabName = searchParams.get('tab')
	const search = searchParams.get('search') || ''
	const { userDetails } = useAuthContext()
	const [isActionInfoDrawer, setIsActionInfoDrawer] = useState<boolean>(false)
	const [selectedId, setSelectedId] = useState<string>('')

	const [assignBiomass, setAssignBiomass] = useState<AssignBiomassState | null>(
		null
	)

	const [showAdminsDrawer, setShowAdminsDrawer] = useState<
		ManagerDetails[] | null
	>(null)
	const [showAssignedBiomassDrawer, setshowAssignedBiomassDrawer] = useState<{
		id: string
		biomass: CropforCsinkManager[]
	} | null>(null)

	const [showBiomassReferenceDialog, setshowBiomassReferenceDialog] = useState<{
		id: string
		type: EntityEnum
	} | null>(null)

	const [farmCoordinates, setFarmCoordinates] = useState<
		google.maps.LatLng[] | google.maps.LatLngLiteral[]
	>([])
	const navigate = useNavigate()
	const [showArtisaProListDrawer, setShowArtisaProListDrawer] =
		useState<IBaArtisanProNetworks | null>(null)

	const [showMap, setShowMap] = useState<boolean>(false)

	const entityTabsCountQuery = useQuery({
		queryKey: ['entityTabsCountQuery'],
		queryFn: async () => {
			const { data } = await authAxios.get<IEntitytabsCount>(
				`/get-entities-count`
			)
			return data
		},
	})
	const tabValueAccordingToUserRole = useMemo(() => {
		const role = userDetails?.accountType as userRoles

		const baCount = entityTabsCountQuery?.data?.baCount ?? 0
		const apnCount = entityTabsCountQuery?.data?.artisanProNetworkCount ?? 0
		const apCount = entityTabsCountQuery?.data?.artisanProCount ?? 0
		const csnCount = entityTabsCountQuery?.data?.csinkNetworkCount ?? 0

		switch (role) {
			case userRoles.Admin:
				return tabEnum.cSinkManager

			case userRoles.CsinkManager:
				if (baCount > 0) return tabEnum.ba
				if (apnCount > 0) return tabEnum.apn
				if (csnCount > 0) return tabEnum.cSinkNetwork
				if (apCount > 0) return tabEnum.artisanpros
				return tabEnum.apn

			case userRoles.BiomassAggregator:
				if (apnCount > 0) return tabEnum.apn
				if (csnCount > 0) return tabEnum.cSinkNetwork
				if (apCount > 0) return tabEnum.artisanpros
				return tabEnum.apn

			case userRoles.artisanProNetworkManager:
			case userRoles.ArtisanPro:
				if (apCount > 0) return tabEnum.artisanpros
				return tabEnum.artisanpros

			case userRoles.cSinkNetwork:
				if (csnCount > 0) return tabEnum.cSinkNetwork
				return tabEnum.cSinkNetwork

			default:
				return tabEnum.cSinkManager
		}
	}, [
		userDetails?.accountType,
		entityTabsCountQuery?.data?.baCount,
		entityTabsCountQuery?.data?.artisanProNetworkCount,
		entityTabsCountQuery?.data?.artisanProCount,
		entityTabsCountQuery?.data?.csinkNetworkCount,
	])
	const tabParams = useMemo(
		() =>
			searchParams.get('tab') ||
			tabValueAccordingToUserRole ||
			tabEnum.cSinkManager,
		[searchParams, tabValueAccordingToUserRole]
	)

	const tabs = useMemo(
		() => [
			{
				label: 'CSink Manager',
				value: tabEnum.cSinkManager,
				count: entityTabsCountQuery?.data?.csinkManagerCount,
				show:
					[userRoles.Admin].includes(userDetails?.accountType as userRoles) &&
					!!entityTabsCountQuery?.data?.csinkManagerCount,
			},
			{
				label: 'Biomass Aggregator',
				value: tabEnum.ba,
				count: entityTabsCountQuery?.data?.baCount,
				show:
					[userRoles.Admin, userRoles.CsinkManager].includes(
						userDetails?.accountType as userRoles
					) && !!entityTabsCountQuery?.data?.baCount,
			},
			{
				label: 'Artisan Pro Network',
				value: tabEnum.apn,
				count: entityTabsCountQuery?.data?.artisanProNetworkCount,
				show:
					[
						userRoles.Admin,
						userRoles.CsinkManager,
						userRoles.cSinkNetwork,
						userRoles.BiomassAggregator,
					].includes(userDetails?.accountType as userRoles) &&
					!!entityTabsCountQuery?.data?.artisanProNetworkCount,
			},
			{
				label: 'CSink Network',
				value: tabEnum.cSinkNetwork,
				count: entityTabsCountQuery?.data?.csinkNetworkCount,
				show:
					[
						userRoles.Admin,
						userRoles.CsinkManager,
						userRoles.BiomassAggregator,
					].includes(userDetails?.accountType as userRoles) &&
					!!entityTabsCountQuery?.data?.csinkNetworkCount,
			},
			{
				label: 'Artisan Pros',
				value: tabEnum.artisanpros,
				count: entityTabsCountQuery?.data?.artisanProCount,
				show:
					[
						userRoles.Admin,
						userRoles.CsinkManager,
						userRoles.BiomassAggregator,
						userRoles.artisanProNetworkManager,
						userRoles.ArtisanPro,
					].includes(userDetails?.accountType as userRoles) &&
					!!entityTabsCountQuery?.data?.artisanProCount,
			},
		],
		[userDetails?.accountType, entityTabsCountQuery?.data]
	)

	const permissionForAPiCall = useMemo(
		() => ({
			cSinkManager: [userRoles.Admin].includes(
				userDetails?.accountType as userRoles
			),
			ba: [userRoles.Admin, userRoles.CsinkManager].includes(
				userDetails?.accountType as userRoles
			),
			apn: [
				userRoles.Admin,
				userRoles.CsinkManager,
				userRoles.BiomassAggregator,
			].includes(userDetails?.accountType as userRoles),
			network: [
				userRoles.Admin,
				userRoles.CsinkManager,
				userRoles.BiomassAggregator,
			].includes(userDetails?.accountType as userRoles),
			ap: [
				userRoles.Admin,
				userRoles.CsinkManager,
				userRoles.BiomassAggregator,
				userRoles.artisanProNetworkManager,
			].includes(userDetails?.accountType as userRoles),
		}),
		[userDetails?.accountType]
	)

	const AllBaQuery = useQuery({
		queryKey: ['allBA', page, limit, search],
		queryFn: async () => {
			const queryParams = new URLSearchParams({
				limit: limit.toString(),
				page: page.toString(),
				search: search,
			})
			const { data } = await authAxios.get<IBiomassAggregatorResponse>(
				`/biomass-aggregator?${queryParams.toString()}`
			)
			return data
		},
		enabled: tabParams === tabEnum.ba && permissionForAPiCall.ba,
	})

	const AllCSinkManagerQuery = useQuery({
		queryKey: ['allCsinkManager', page, limit, search],
		queryFn: async () => {
			const queryParams = new URLSearchParams({
				limit: limit.toString(),
				page: page.toString(),
				search: search,
			})
			const { data } = await authAxios.get<ICSinkManagerResponse>(
				`/csink-manager?${queryParams.toString()}`
			)
			return data
		},
		enabled:
			tabParams === tabEnum.cSinkManager && permissionForAPiCall.cSinkManager,
	})

	const useSuspendMutation = () =>
		useMutation({
			mutationFn: async ({
				id,
				isSuspend,
				tab,
			}: {
				id: string
				isSuspend: boolean
				tab: string
			}) => {
				const { data } = await authAxios.patch(
					getToggleSuspendUrl(tab, id, isSuspend)
				)
				return data
			},
			onSuccess: (data) => {
				toast(data.message)
				if (tabParams === tabEnum.ba) AllBaQuery.refetch()
				if (tabParams === tabEnum.cSinkManager) AllCSinkManagerQuery.refetch()
				if (tabParams === tabEnum.apn) AllAPN.refetch()
				if (tabParams === tabEnum.cSinkNetwork) AllCSinkNetwork.refetch()
				if (tabParams === tabEnum.artisanpros) AllAPs.refetch()
			},
			onError: (error: AxiosError) => {
				toast(
					(error?.response?.data as { messageToUser: string })?.messageToUser
				)
			},
		})

	const suspendMutation = useSuspendMutation()

	const handleSuspendEntity = useCallback(
		(id: string, isSuspend: boolean) => {
			const tab = tabParams
			suspendMutation.mutate({
				id,
				isSuspend,
				tab,
			})
		},
		[tabParams, suspendMutation]
	)

	const AllAPN = useQuery({
		queryKey: ['allAPN', page, limit, search],
		queryFn: async () => {
			const queryParams = new URLSearchParams({
				limit: limit.toString(),
				page: page.toString(),
				search: search,
			})
			const { data } = await authAxios.get<IAPNResponse>(
				`/new/artisanpro-networks?${queryParams.toString()}`
			)
			return data
		},
		enabled: tabParams === tabEnum.apn && permissionForAPiCall.apn,
	})

	const AllCSinkNetwork = useQuery({
		queryKey: ['allcSinkNetwork', page, limit, search],
		queryFn: async () => {
			const queryParams = new URLSearchParams({
				limit: limit.toString(),
				page: page.toString(),
				search: search,
			})
			const { data } = await authAxios.get<ICSinkNetworkResponse>(
				`/new/csink-network?${queryParams.toString()}`
			)
			return data
		},
		enabled: tabParams === tabEnum.cSinkNetwork && permissionForAPiCall.network,
	})

	const AllAPs = useQuery({
		queryKey: ['allaps', page, limit, search],
		queryFn: async () => {
			const queryParams = new URLSearchParams({
				limit: limit.toString(),
				page: page.toString(),
				search: search,
			})
			const { data } = await authAxios.get<IAPsResponse>(
				`/new/artisan-pros?${queryParams.toString()}`
			)
			return data
		},
		enabled: tabParams === tabEnum.artisanpros && permissionForAPiCall.ap,
	})

	const { data: fetchBiomassTypeList } = useQuery<
		{ count: number; cropDetails: ICrop[] },
		Error,
		BiomassOptionType[]
	>({
		queryKey: ['fetchBiomassTypeList'],
		queryFn: async () => {
			const { data } = await authAxios.get(`/drop-down/crops`)
			return data
		},
		select: (data) =>
			data?.cropDetails?.map((item) => ({
				label: item?.name,
				value: item?.id,
			})),
	})

	const handleTabChange = useCallback(
		(_: unknown, newValue: tabEnum) => {
			setSearchParams(
				(prev) => ({
					...prev,
					tab: newValue,
				}),
				{ replace: true }
			)
		},
		[setSearchParams]
	)

	const handleCloseDrawer = useCallback(() => {
		setIsActionInfoDrawer(false)
		setSelectedId('')
	}, [])

	const handelOpenDrawer = useCallback(() => {
		setIsActionInfoDrawer(true)
	}, [])

	const handleSaveKmlMutation = useMutation({
		mutationKey: ['SaveKml'],
		mutationFn: async (mapData: ILocation[]) => {
			const networkId = searchParams.get('networkId')
			if (!networkId) {
				return
			}
			const payload = {
				kmlCoordinates: mapData,
			}
			const api =
				tabParams === tabEnum.artisanpros
					? `/artisian-pro/${networkId}/kml-coordinates`
					: `/cs-network/${networkId}/kml-coordinates`
			await authAxios.put(api, payload)
		},
		onSuccess: () => {
			tabParams === tabEnum.artisanpros
				? AllAPs.refetch()
				: AllCSinkNetwork.refetch()
			toast('Farm KML added')
		},
		onError: (error: AxiosError) => {
			toast((error?.response?.data as { userToMessage: string })?.userToMessage)
		},
	})
	const handleSaveKml = useCallback(
		async (mapData: ILocation[]) => {
			await handleSaveKmlMutation.mutateAsync(mapData)
		},
		[handleSaveKmlMutation]
	)
	const handleRowClick = (params: any) => {
		params?.setOpen?.(!params?.open)
		params?.setHookData?.(params?.row?.id)
	}
	const handleAPNRowClick: GridEventListener<'rowClick'> = useCallback(
		(params) => {
			setShowArtisaProListDrawer(params?.row)
		},
		[setShowArtisaProListDrawer]
	)
	const handleCsinkNetworkRowClick: GridEventListener<'rowClick'> = useCallback(
		(params) => {
			navigate(`/dashboard/c-sink-network/${params?.id}/details`)
		},
		[navigate]
	)
	const handleArtisanProRowClick: GridEventListener<'rowClick'> = useCallback(
		(params) => {
			navigate(`/dashboard/artisan-pro/${params?.id}/details`)
		},
		[navigate]
	)
	const handleExcelDownload = () =>
		useMutation({
			mutationKey: ['downloadBAExcel'],
			mutationFn: async (id: string) => {
				const response = await authAxios.get(
					`/biomass-aggregator/${id}/excel-data`,
					{
						responseType: 'blob',
					}
				)

				const contentDisposition = response.headers['content-disposition']
				const filename = contentDisposition
					? contentDisposition.split('filename=')[1].replace(/['"]/g, '')
					: 'process.xlsx'

				const blob = new Blob([response.data], {
					type: 'application/octet-stream',
				})

				const link = document.createElement('a')
				link.href = URL.createObjectURL(blob)
				link.download = filename
				document.body.appendChild(link)
				link.click()
				document.body.removeChild(link)

				URL.revokeObjectURL(link.href)

				return response
			},
			onSuccess: () => {
				toast('Excel file downloaded successfully!')
			},
			onError: (error: unknown) => {
				console.error('Error downloading Excel file:', error)
				toast('Failed to download Excel file. Please try again.')
			},
		})

	// useEffect(() => {
	// 	if (!userDetails?.accountType) return
	// 	setSearchParams(
	// 		(prev) => ({
	// 			...prev,
	// 			tab: tabValueAccordingToUserRole[userDetails?.accountType as string],
	// 		}),
	// 		{ replace: true }
	// 	)
	// }, [setSearchParams, tabParams, userDetails?.accountType])

	return {
		AllBaQuery,
		AllCSinkManagerQuery,
		AllAPN,
		tabs,
		tabParams,
		tabEnum,
		page,
		limit,
		handleTabChange,
		AllCSinkNetwork,
		navigate,
		AllAPs,
		isActionInfoDrawer,
		handleCloseDrawer,
		assignBiomass,
		setAssignBiomass,
		handelOpenDrawer,
		showMap,
		setShowMap,
		handleSaveKml,
		farmCoordinates,
		setFarmCoordinates,
		selectedId,
		setSelectedId,
		setSearchParams,
		showBiomassReferenceDialog,
		showAssignedBiomassDrawer,
		setshowAssignedBiomassDrawer,
		setshowBiomassReferenceDialog,
		handleExcelDownload,
		handleAPNRowClick,
		handleRowClick,
		handleCsinkNetworkRowClick,
		handleArtisanProRowClick,
		showArtisaProListDrawer,
		setShowArtisaProListDrawer,
		entityTabsCountQuery,
		showAdminsDrawer,
		setShowAdminsDrawer,
		handleSuspendEntity,
		fetchBiomassTypeList,
	}
}
