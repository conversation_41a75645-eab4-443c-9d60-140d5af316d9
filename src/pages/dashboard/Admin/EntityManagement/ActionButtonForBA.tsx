import {
	Add,
	Block,
	CheckCircle,
	Edit,
	MoreVertOutlined,
} from '@mui/icons-material'
import {
	Box,
	CircularProgress,
	IconButton,
	ListItemIcon,
	ListItemText,
	Menu,
	MenuItem,
	Stack,
	Typography,
} from '@mui/material'
import { useState } from 'react'
import { GridCellParams } from '@mui/x-data-grid'
import { EntityEnum } from '@/interfaces'
import downloadRed from '@/assets/icons/download_red.svg'
import { UseMutationResult } from '@tanstack/react-query'
import { AxiosResponse } from 'axios'
import { theme } from '@/lib/theme/theme'

export const ActionButtonForBA = ({
	params,
	setshowBiomassReferenceDialog,
	handleExcelDownload,
	handleEdit,
	onSuspend,
	onActivate,
}: {
	params: GridCellParams
	handleExcelDownload: () => UseMutationResult<
		AxiosResponse<any, any>,
		unknown,
		string,
		unknown
	>
	setshowBiomassReferenceDialog: (
		value: React.SetStateAction<{
			id: string
			type: EntityEnum
		} | null>
	) => void
	handleEdit: () => void
	onSuspend: (id: string) => void
	onActivate: (id: string) => void
}) => {
	const { mutateAsync, isPending } = handleExcelDownload()
	const [anchorEl, setAnchorEl] = useState<null | HTMLElement>(null)
	const open = Boolean(anchorEl)
	const handleOpenMenu = (event: React.MouseEvent<HTMLButtonElement>) => {
		setAnchorEl(event.currentTarget)
	}
	const handleCloseMenu = () => {
		setAnchorEl(null)
	}

	const handleSuspend = () => {
		onSuspend(params.row.id)
		handleCloseMenu()
	}

	const handleActivate = () => {
		onActivate(params.row.id)
		handleCloseMenu()
	}
	return (
		<Stack
			style={{
				display: 'flex',
				flexDirection: 'row',
			}}>
			<IconButton
				onClick={(e) => {
					e.stopPropagation()
					handleOpenMenu(e)
				}}>
				<MoreVertOutlined />
			</IconButton>
			<Menu
				id='basic-menu'
				anchorEl={anchorEl}
				open={open}
				onClose={handleCloseMenu}
				MenuListProps={{
					'aria-labelledby': 'basic-button',
				}}>
				<MenuItem
					sx={{
						gap: theme.spacing(1.5),
					}}
					onClick={(e) => {
						e.stopPropagation()
						handleCloseMenu()
						handleEdit()
					}}>
					<Edit color='primary' fontSize='small' />
					<Typography>Edit</Typography>
				</MenuItem>
				<MenuItem
					sx={{
						gap: theme.spacing(1.5),
					}}
					onClick={(e) => {
						e.stopPropagation()
						handleCloseMenu()

						setshowBiomassReferenceDialog({
							id: params?.row?.id,
							type: EntityEnum.ba,
						})
					}}>
					<Add fontSize='small' color='primary' />
					<Typography>Biomass Refernce</Typography>
				</MenuItem>

				<MenuItem
					disabled={isPending}
					sx={{
						gap: theme.spacing(1.5),
					}}
					onClick={(e) => {
						e.stopPropagation()
						const id = params?.row?.id
						mutateAsync(id)
						handleCloseMenu()
					}}>
					{isPending ? (
						<CircularProgress size='30px' />
					) : (
						<Box component='img' src={downloadRed} height={20} width={20} />
					)}
					<Typography>Download</Typography>
				</MenuItem>
				{params?.row.suspended ? (
					<MenuItem onClick={handleActivate}>
						<ListItemIcon>
							<CheckCircle fontSize='small' />
						</ListItemIcon>
						<ListItemText>Activate</ListItemText>
					</MenuItem>
				) : (
					<MenuItem onClick={handleSuspend}>
						<ListItemIcon>
							<Block fontSize='small' />
						</ListItemIcon>
						<ListItemText>Suspend</ListItemText>
					</MenuItem>
				)}
			</Menu>
		</Stack>
	)
}
