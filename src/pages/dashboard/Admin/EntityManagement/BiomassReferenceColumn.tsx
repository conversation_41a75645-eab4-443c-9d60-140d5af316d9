import { BiomassReference } from '@/interfaces'
import { Add } from '@mui/icons-material'
import { IconButton, Stack, Typography } from '@mui/material'

interface IProps {
	onClick: () => void
	biomassReference: BiomassReference[]
}
export const BiomassReferenceColumn = ({
	onClick,
	biomassReference,
}: IProps) => {
	const length = biomassReference?.length
	const biomassName = biomassReference?.[0]?.biomassName
	const quantity = biomassReference?.[0]?.biomassQuantity || 0
	return (
		<Stack
			onClick={(e) => {
				e.stopPropagation()
				onClick()
			}}
			alignItems='center'>
			{length ? (
				<Typography>
					{biomassName} : {quantity}kgs {length > 1 ? '..' : ''}
				</Typography>
			) : (
				<IconButton
					sx={{
						width: 'fit-content',
					}}>
					<Add color='primary' />
				</IconButton>
			)}
		</Stack>
	)
}
