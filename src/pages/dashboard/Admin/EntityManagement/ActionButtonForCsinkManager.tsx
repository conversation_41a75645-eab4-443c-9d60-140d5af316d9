import { MoreVert, Edit, CheckCircle, Block } from '@mui/icons-material'
import {
	IconButton,
	Menu,
	MenuItem,
	ListItemIcon,
	ListItemText,
} from '@mui/material'
import { GridValidRowModel } from '@mui/x-data-grid'
import { useState } from 'react'

export const ActionButtonForCsinkManager = ({
	row,
	onEdit,
	onSuspend,
	onActivate,
}: {
	row: GridValidRowModel
	onEdit: (id: string) => void
	onSuspend: (id: string) => void
	onActivate: (id: string) => void
}) => {
	const [anchorEl, setAnchorEl] = useState<null | HTMLElement>(null)
	const open = Boolean(anchorEl)

	const handleMenuClick = (event: React.MouseEvent<HTMLButtonElement>) => {
		setAnchorEl(event.currentTarget)
	}

	const handleClose = () => {
		setAnchorEl(null)
	}

	const handleEdit = () => {
		onEdit(row.id)
		handleClose()
	}

	const handleSuspend = () => {
		onSuspend(row.id)
		handleClose()
	}

	const handleActivate = () => {
		onActivate(row.id)
		handleClose()
	}

	return (
		<>
			<IconButton onClick={handleMenuClick}>
				<MoreVert />
			</IconButton>
			<Menu anchorEl={anchorEl} open={open} onClose={handleClose}>
				<MenuItem onClick={handleEdit}>
					<ListItemIcon>
						<Edit fontSize='small' />
					</ListItemIcon>
					<ListItemText>Edit</ListItemText>
				</MenuItem>
				{row?.suspended ? (
					<MenuItem onClick={handleActivate}>
						<ListItemIcon>
							<CheckCircle fontSize='small' />
						</ListItemIcon>
						<ListItemText>Activate</ListItemText>
					</MenuItem>
				) : (
					<MenuItem onClick={handleSuspend}>
						<ListItemIcon>
							<Block fontSize='small' />
						</ListItemIcon>
						<ListItemText>Suspend</ListItemText>
					</MenuItem>
				)}
			</Menu>
		</>
	)
}
