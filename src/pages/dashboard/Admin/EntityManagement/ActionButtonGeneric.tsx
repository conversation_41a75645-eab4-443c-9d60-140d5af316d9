import { downloadRed } from '@/assets/icons'
import { EntityEnum } from '@/interfaces'
import { theme } from '@/lib/theme/theme'
import { MoreVert, Edit, CheckCircle, Block, Add } from '@mui/icons-material'
import {
	IconButton,
	Menu,
	MenuItem,
	ListItemIcon,
	ListItemText,
	Box,
	CircularProgress,
	Typography,
} from '@mui/material'
import { GridValidRowModel } from '@mui/x-data-grid'
import { UseMutationResult } from '@tanstack/react-query'
import { AxiosResponse } from 'axios'
import { useState } from 'react'

export const ActionButtonGeneric = ({
	setshowBiomassReferenceDialog,
	handleExcelDownload,
	row,
	onEdit,
	onSuspend,
	onActivate,
	isBa = false,
}: {
	handleExcelDownload?: () => UseMutationResult<
		AxiosResponse<any, any>,
		unknown,
		string,
		unknown
	>
	setshowBiomassReferenceDialog?: (
		value: React.SetStateAction<{
			id: string
			type: EntityEnum
		} | null>
	) => void
	row: GridValidRowModel
	onEdit: (id: string) => void
	onSuspend: (id: string) => void
	onActivate: (id: string) => void
	isBa?: boolean
}) => {
	const { mutateAsync, isPending } = handleExcelDownload
		? handleExcelDownload()
		: { mutateAsync: async () => {}, isPending: false }
	const [anchorEl, setAnchorEl] = useState<null | HTMLElement>(null)
	const open = Boolean(anchorEl)

	const handleMenuClick = (event: React.MouseEvent<HTMLButtonElement>) => {
		setAnchorEl(event.currentTarget)
	}

	const handleClose = () => {
		setAnchorEl(null)
	}

	const handleEdit = () => {
		onEdit(row.id)
		handleClose()
	}

	const handleSuspend = () => {
		onSuspend(row.id)
		handleClose()
	}

	const handleActivate = () => {
		onActivate(row.id)
		handleClose()
	}

	return (
		<>
			<IconButton
				onClick={(e) => {
					e.stopPropagation()
					handleMenuClick(e)
				}}>
				<MoreVert />
			</IconButton>
			<Menu anchorEl={anchorEl} open={open} onClose={handleClose}>
				<MenuItem onClick={handleEdit}>
					<ListItemIcon>
						<Edit fontSize='small' />
					</ListItemIcon>
					<ListItemText>Edit</ListItemText>
				</MenuItem>
				{isBa && (
					<>
						<MenuItem
							sx={{
								gap: theme.spacing(1.5),
							}}
							onClick={(e) => {
								e.stopPropagation()
								handleClose()
								setshowBiomassReferenceDialog &&
									setshowBiomassReferenceDialog({
										id: row?.id,
										type: EntityEnum.ba,
									})
							}}>
							<Add fontSize='small' color='primary' />
							<Typography>Biomass Refernce</Typography>
						</MenuItem>

						<MenuItem
							disabled={isPending}
							sx={{
								gap: theme.spacing(1.5),
							}}
							onClick={(e) => {
								e.stopPropagation()
								const id = row?.id
								mutateAsync(id)
								handleClose()
							}}>
							{isPending ? (
								<CircularProgress size='30px' />
							) : (
								<Box component='img' src={downloadRed} height={20} width={20} />
							)}
							<Typography>Download</Typography>
						</MenuItem>
					</>
				)}
				{row?.suspended ? (
					<MenuItem onClick={handleActivate}>
						<ListItemIcon>
							<CheckCircle fontSize='small' />
						</ListItemIcon>
						<ListItemText>Activate</ListItemText>
					</MenuItem>
				) : (
					<MenuItem onClick={handleSuspend}>
						<ListItemIcon>
							<Block fontSize='small' />
						</ListItemIcon>
						<ListItemText>Suspend</ListItemText>
					</MenuItem>
				)}
			</Menu>
		</>
	)
}
