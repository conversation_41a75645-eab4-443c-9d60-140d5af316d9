import { IFileData } from '@/interfaces'
import { Company, ICompanyDetailsAccountManagement, Stage } from '@/types'
import {
	CheckCircleOutline,
	FileDownloadOutlined,
	FileUploadOutlined,
} from '@mui/icons-material'
import CheckCircleOutlineRoundedIcon from '@mui/icons-material/CheckCircleOutlineRounded'
import CircleOutlinedIcon from '@mui/icons-material/CircleOutlined'
import SettingsOutlinedIcon from '@mui/icons-material/SettingsOutlined'
import { LoadingButton } from '@mui/lab'
import { Box, Button, Chip, Divider, Stack, Typography } from '@mui/material'
import { styled } from '@mui/system'
import { UseMutationResult, UseQueryResult } from '@tanstack/react-query'
import { AxiosError, AxiosResponse } from 'axios'
import { useMemo } from 'react'
import { FileUploader } from 'react-drag-drop-files'
import { DownloadFileBox } from './FileDownloadBox'

const formatDate = (date: Date | null) =>
	date?.toLocaleDateString('en-US', {
		year: 'numeric',
		month: 'long',
		day: 'numeric',
	}) +
	', ' +
	date?.toLocaleTimeString('en-US', { hour: 'numeric', minute: '2-digit' })

const StyledContractSection = styled(Stack)(({ theme }) => ({
	flexDirection: 'row',
	gap: theme.spacing(3),

	'.leftPanel': {
		flex: 2,
		border: '1px solid #e0e0e0',
		borderRadius: theme.spacing(1),
		padding: theme.spacing(3),
	},

	'.rightPanel': {
		flex: 1,
		border: '1px solid #e0e0e0',
		borderRadius: theme.spacing(1),
		padding: theme.spacing(3),
		maxHeight: 'fit-content',
	},

	'.header': {
		display: 'flex',
		flexDirection: 'row',
		justifyContent: 'space-between',
		alignItems: 'center',
	},

	'.headerLeft': {
		display: 'flex',
		flexDirection: 'row',
		alignItems: 'center',
		padding: theme.spacing(1),
		gap: theme.spacing(8),
	},

	'.statusChip': {
		'&.approved': {
			color: theme.palette.success.dark,
			backgroundColor: theme.palette.success.light,
		},
		'&.pending': {
			color: theme.palette.warning.dark,
			backgroundColor: theme.palette.warning.light,
		},
	},

	'.approveButton': {
		borderRadius: theme.spacing(2),
	},

	'.customizationOptions': {
		marginTop: theme.spacing(2),
	},

	'.divider': {
		paddingTop: theme.spacing(2),
		paddingBottom: theme.spacing(2),
	},

	'.requestedServices': {
		marginTop: theme.spacing(2),
	},

	'.servicesChips': {
		marginTop: theme.spacing(1),
		display: 'flex',
		flexWrap: 'wrap',
		gap: theme.spacing(1),
		flexDirection: 'row',
	},
	'.uploadedFileCard': {
		border: `1px solid #BBF7D0`,
		backgroundColor: '#F0FDF4',
		borderRadius: theme.spacing(1),
		padding: theme.spacing(2.5),
		display: 'flex',
		justifyContent: 'space-between',
		alignItems: 'center',
	},
	'.actionButton': {
		textTransform: 'capitalize',
		backgroundColor: theme.palette.background.paper,
		fontWeight: 300,
		borderColor: theme.palette.custom.grey[300],
		color: theme.palette.text.primary,
	},
}))
interface StageUpdateProps {
	stageInfo?: Stage
	handleUpdateStatus: UseMutationResult<
		{
			data: {
				message: string
			}
			stage: string
		},
		AxiosError,
		{
			stage: string
		},
		unknown
	>
	isSignedDocUploaded: boolean
}

const ContractHeader = ({
	stageInfo,
	handleUpdateStatus,
	isSignedDocUploaded,
}: StageUpdateProps) => {
	return (
		<Stack className='header'>
			<Stack className='headerLeft'>
				<Typography variant='h6'>Contract</Typography>
				<Chip
					label={stageInfo?.status === 'approved' ? 'Approved' : 'Pending'}
					className={`statusChip ${
						stageInfo?.status === 'approved' ? 'approved' : 'pending'
					}`}
				/>
			</Stack>
			{stageInfo?.status === 'pending' && (
				<LoadingButton
					loading={handleUpdateStatus.isPending}
					disabled={handleUpdateStatus.isPending || !isSignedDocUploaded}
					onClick={() =>
						handleUpdateStatus.mutate({
							stage: stageInfo?.stage,
						})
					}
					variant='contained'
					color='success'
					size='small'
					className='approveButton'>
					Approve
				</LoadingButton>
			)}
		</Stack>
	)
}
interface StageInfoProps {
	stageInfo?: Stage
	handleUpdateStatus: UseMutationResult<
		{
			data: {
				message: string
			}
			stage: string
		},
		AxiosError,
		{
			stage: string
		},
		unknown
	>
}
const CustomizationHeader = ({
	stageInfo,
	handleUpdateStatus,
}: StageInfoProps) => (
	<Stack className='header'>
		<SettingsOutlinedIcon />
		<Typography variant='h5'>dMRV Customization</Typography>
		<Stack direction='row' spacing={1} alignItems='center'>
			<Chip
				label={stageInfo?.status === 'approved' ? 'Approved' : 'Pending'}
				className={`statusChip ${
					stageInfo?.status === 'approved' ? 'approved' : 'pending'
				}`}
			/>
			{stageInfo?.status === 'pending' && (
				<LoadingButton
					loading={handleUpdateStatus.isPending}
					onClick={() =>
						handleUpdateStatus.mutate({
							stage: stageInfo?.stage,
						})
					}
					variant='contained'
					color='success'
					size='small'
					className='approveButton'>
					Approve
				</LoadingButton>
			)}
		</Stack>
	</Stack>
)

const UploadedDocumentList = ({
	documents,
	handleDownload,
}: {
	documents?: IFileData[]
	handleDownload: (url: string, fileName: string) => void
}) => {
	return (
		<>
			{documents?.map((doc, idx) => (
				<DownloadFileBox
					key={doc?.id ?? idx}
					document={doc}
					handleDownload={handleDownload}
				/>
			))}
		</>
	)
}

const StyledUploadBox = styled(Box)(({ theme }) => ({
	display: 'flex',
	flexDirection: 'column',
	alignItems: 'center',
	border: '2px dashed #ccc',
	borderRadius: theme.spacing(1),
	textAlign: 'center',
	padding: theme.spacing(3),
	cursor: 'pointer',
	marginY: theme.spacing(2),
}))

const StyledCaption = styled(Typography)(({ theme }) => ({
	marginTop: theme.spacing(1),
	color: theme.palette.neutral[300],
}))

const UploadBox = ({
	handleFileChange,
}: {
	handleFileChange: (file: File) => Promise<AxiosResponse>
}) => (
	<FileUploader
		name='file'
		types={['PDF', 'DOC', 'DOCX']}
		handleChange={(file: File) => handleFileChange(file)}>
		<StyledUploadBox>
			<FileUploadOutlined fontSize='large' />
			<Typography variant='body1' mt={1}>
				Drag and drop your signed NDA here, or
			</Typography>
			<Button
				variant='outlined'
				size='small'
				startIcon={<FileUploadOutlined />}>
				Choose File
			</Button>
			<StyledCaption variant='caption'>PDF, DOC, DOCX up to 10MB</StyledCaption>
		</StyledUploadBox>
	</FileUploader>
)

const CustomizationOptions = ({
	selectedType,
}: {
	selectedType?: {
		id: number
		name: string
		shortName: string
		isChecked: boolean
	}[]
}) => (
	<>
		{selectedType?.map((item) => (
			<Stack key={item.id} alignItems='center' direction='row' gap={1}>
				{item?.isChecked ? (
					<CheckCircleOutlineRoundedIcon color='success' />
				) : (
					<CircleOutlinedIcon />
				)}
				<Typography color={item.isChecked ? 'success.main' : 'text.secondary'}>
					{item.name}
				</Typography>
			</Stack>
		))}
	</>
)

const RequestedServices = ({
	selectedType,
}: {
	selectedType?: {
		id: number
		name: string
		shortName: string
		isChecked: boolean
	}[]
}) => (
	<Box className='requestedServices'>
		<Typography fontSize={13} color='gray'>
			Requested Services:
		</Typography>
		{selectedType?.some((i) => i.isChecked) ? (
			<Stack className='servicesChips'>
				{selectedType
					.filter((i) => i.isChecked)
					.map((type) => (
						<Chip
							key={type.id}
							label={type.shortName}
							className='statusChip approved'
						/>
					))}
			</Stack>
		) : (
			<Typography fontSize={12} color='gray' mt={1}>
				No services requested
			</Typography>
		)}
	</Box>
)
interface ComponentProps {
	handleDownload: (fileUrl?: string, fileName?: string) => Promise<void>
	getCompanyDetails: UseQueryResult<
		ICompanyDetailsAccountManagement | Company | null,
		Error
	>
	handleFileChange: (file: File) => Promise<AxiosResponse>
	isshowSignedDoc: boolean
	handleUpdateStatus: UseMutationResult<
		{
			data: {
				message: string
			}
			stage: string
		},
		AxiosError,
		{
			stage: string
		},
		unknown
	>
}
export const ContractDetails = ({
	handleDownload,
	getCompanyDetails,
	handleFileChange,
	isshowSignedDoc,
	handleUpdateStatus,
}: ComponentProps) => {
	const companyDetails =
		getCompanyDetails?.data as ICompanyDetailsAccountManagement
	const UploadedFiles = [
		companyDetails?.ndaDocument,
		companyDetails?.commercialDocument,
	].filter(Boolean)

	const selectedType = useMemo(() => {
		const selectedItems = [
			{
				id: 1,
				name: 'TEA (Technical Emissions Assessment)',
				shortName: 'TEA',
				isChecked: companyDetails?.isTEAEnabled ?? false,
			},
			{
				id: 2,
				name: 'LCA (Life Cycle Assessment)',
				shortName: 'LCA',
				isChecked: companyDetails?.isLCAEnabled ?? false,
			},
			{
				id: 3,
				name: 'FT (Finance Tool)',
				shortName: 'LCA',
				isChecked: companyDetails?.isFinanceToolEnabled ?? false,
			},
		]
		return selectedItems
	}, [
		companyDetails?.isFinanceToolEnabled,
		companyDetails?.isLCAEnabled,
		companyDetails?.isTEAEnabled,
	])
	const contractStageInfo = useMemo(() => {
		const info = companyDetails?.stages?.find(
			(item) => item?.stage === 'contract'
		)
		return info
	}, [companyDetails?.stages])
	const dMRVCustomizationStageInfo = useMemo(() => {
		const info = companyDetails?.stages?.find(
			(item) => item?.stage === 'dmrv_customization'
		)
		return info
	}, [companyDetails?.stages])

	return (
		<StyledContractSection>
			<Box className='leftPanel'>
				<ContractHeader
					stageInfo={contractStageInfo}
					handleUpdateStatus={handleUpdateStatus}
					isSignedDocUploaded={isshowSignedDoc}
				/>
				<Stack gap={2}>
					<UploadedDocumentList
						documents={UploadedFiles}
						handleDownload={handleDownload}
					/>
					{contractStageInfo?.status === 'approved' && (
						<DownloadFileBox
							key={companyDetails?.ndaSignedDocument?.id}
							document={companyDetails?.ndaSignedDocument}
							handleDownload={handleDownload}
						/>
					)}
					<DocumentUploadSection
						isshowSignedDoc={isshowSignedDoc}
						companyDetails={companyDetails}
						handleDownload={handleDownload}
						handleFileChange={handleFileChange}
						stageInfo={contractStageInfo}
					/>
				</Stack>
			</Box>

			{contractStageInfo?.status === 'approved' && (
				<Box className='rightPanel'>
					<CustomizationHeader
						stageInfo={dMRVCustomizationStageInfo}
						handleUpdateStatus={handleUpdateStatus}
					/>
					<Box className='customizationOptions'>
						<CustomizationOptions selectedType={selectedType} />
					</Box>
					<Divider className='divider' />
					<RequestedServices selectedType={selectedType} />
				</Box>
			)}
		</StyledContractSection>
	)
}
type DocumentSectionProps = {
	isshowSignedDoc: boolean
	companyDetails?: ICompanyDetailsAccountManagement
	handleDownload: (
		fileUrl?: string | undefined,
		fileName?: string
	) => Promise<void>
	handleFileChange: (file: File) => Promise<AxiosResponse>
	stageInfo?: Stage | undefined
}
const DocumentUploadSection = ({
	isshowSignedDoc,
	companyDetails,
	handleDownload,
	handleFileChange,
	stageInfo,
}: DocumentSectionProps) => {
	switch (true) {
		case isshowSignedDoc:
			return (
				<Box className='uploadedFileCard'>
					<Box display='flex' alignItems='center' gap={1.5}>
						<Box
							padding={1}
							display='flex'
							alignItems='center'
							justifyContent='center'>
							<CheckCircleOutline color='success' fontSize='small' />
						</Box>
						<Box>
							<Typography fontWeight={600}>
								{companyDetails?.ndaSignedDocument?.fileName}
							</Typography>
							<Typography variant='caption' color='text.secondary'>
								{formatDate(
									companyDetails?.ndaSignedDocument?.createdAt
										? new Date(companyDetails.ndaSignedDocument.createdAt)
										: null
								)}
							</Typography>
						</Box>
					</Box>

					<Stack direction='row' spacing={1}>
						<Button
							variant='outlined'
							size='small'
							onClick={() =>
								handleDownload(
									companyDetails?.ndaSignedDocument?.url ?? undefined,
									companyDetails?.ndaSignedDocument?.fileName ?? undefined
								)
							}
							startIcon={<FileDownloadOutlined />}
							className='actionButton'>
							Download
						</Button>

						<FileUploader
							handleChange={(file: File) => handleFileChange(file)}
							name='file'
							types={['PDF', 'DOC', 'DOCX']}>
							<Button
								variant='outlined'
								size='small'
								startIcon={<FileUploadOutlined />}
								className='actionButton'>
								Replace
							</Button>
						</FileUploader>
					</Stack>
				</Box>
			)

		case stageInfo?.status === 'pending':
			return <UploadBox handleFileChange={handleFileChange} />

		default:
			return null
	}
}
