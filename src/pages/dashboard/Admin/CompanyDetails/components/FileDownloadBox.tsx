import { IFileData } from '@/interfaces'
import { StyledCard } from '@/pages/CompanyRegistration/components/companyRegistration'
import { dateFormats } from '@/utils/constant'
import { getFormattedDate } from '@/utils/helper'
import CalendarTodayOutlinedIcon from '@mui/icons-material/CalendarTodayOutlined'
import DescriptionOutlinedIcon from '@mui/icons-material/DescriptionOutlined'
import FileDownloadOutlinedIcon from '@mui/icons-material/FileDownloadOutlined'
import { Button, Stack, Typography } from '@mui/material'

export const DownloadFileBox = ({
	document,
	handleDownload,
}: {
	document?: IFileData
	handleDownload: (url: string, fileName: string) => void
}) => {
	return (
		<StyledCard>
			<Stack className='fw'>
				<Stack className='cardRow'>
					<Stack className='logoContainer'>
						<DescriptionOutlinedIcon className='logo' />
					</Stack>

					<Stack className='fileDetails'>
						<Typography className='filename'>{document?.fileName}</Typography>
						<Stack className='fileDateContainer'>
							<CalendarTodayOutlinedIcon className='icon' />
							<Typography className='fileDate'>
								{getFormattedDate(
									document?.createdAt ?? '',
									dateFormats.MMM_dd_yyyy
								)}
							</Typography>
						</Stack>
					</Stack>
					<Stack className='buttonContainer'>
						<Button
							startIcon={<FileDownloadOutlinedIcon />}
							className='btn'
							onClick={() =>
								handleDownload(document?.url ?? '', document?.fileName ?? '')
							}>
							Download
						</Button>
					</Stack>
				</Stack>
			</Stack>
		</StyledCard>
	)
}
