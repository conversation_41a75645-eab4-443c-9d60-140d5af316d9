import { theme } from '@/lib/theme/theme'
import { StyledCard } from '@/pages/CompanyRegistration/components/companyRegistration/subComponents/StyledCard'
import {
	Admin,
	Company,
	ICompanyCommonInfo,
	ICompanyDetailsAccountManagement,
	Stage,
} from '@/types'
import { RegistryTypeNames, ServiceTypeNames } from '@/utils/constant'
import EmailOutlinedIcon from '@mui/icons-material/EmailOutlined'
import PersonOutlineOutlinedIcon from '@mui/icons-material/PersonOutlineOutlined'
import PhoneOutlinedIcon from '@mui/icons-material/PhoneOutlined'
import PlaceOutlinedIcon from '@mui/icons-material/PlaceOutlined'
import { LoadingButton } from '@mui/lab'
import {
	Box,
	Chip,
	CircularProgress,
	Divider,
	Stack,
	SvgIconTypeMap,
	Typography,
} from '@mui/material'
import { OverridableComponent } from '@mui/material/OverridableComponent'
import { UseMutationResult, UseQueryResult } from '@tanstack/react-query'
import { AxiosError, AxiosResponse } from 'axios'
import { useMemo } from 'react'
import { DownloadFileBox } from './FileDownloadBox'

const CompanyDetailsData = [
	{ icon: PlaceOutlinedIcon, title: 'Address', fieldName: 'address' },
	{ icon: PhoneOutlinedIcon, title: 'Phone Number', fieldName: 'phoneNumber' },
	{ icon: null, title: 'Pin Code', fieldName: 'pinCode' },
	{ icon: null, title: 'State', fieldName: 'state' },
	{ icon: EmailOutlinedIcon, title: 'Email Address', fieldName: 'email' },
	{ icon: null, title: 'Country', fieldName: 'country' },
]
const CompanyStatusHeader = ({
	companyDetails,
	stageInfo,
}: {
	companyDetails?: ICompanyCommonInfo
	stageInfo?:
		| Stage
		| {
				stage: string
				status: string
		  }
		| undefined
}) => {
	const isApproved = stageInfo?.status === 'approved'
	return (
		<>
			<Stack className='titleContainer'>
				<Typography className='title'>Registration</Typography>
				<Chip
					label={isApproved ? 'Approved' : 'Pending'}
					sx={{
						color: isApproved
							? theme.palette.success.dark
							: theme.palette.warning.dark,
						background: isApproved
							? theme.palette.success.light
							: theme.palette.warning.light,
					}}
				/>
			</Stack>
			<Stack direction='row' alignItems='center' gap={2}>
				<Box
					component='img'
					height={40}
					width={40}
					className='logo'
					src={companyDetails?.companyLogo?.url ?? ''}
				/>
				<Stack className='companyHeader'>
					<Typography className='comTitle'>{companyDetails?.name}</Typography>
					<Typography className='comSubHeading'>Registered Company</Typography>
				</Stack>
			</Stack>
		</>
	)
}

const CompanyInfoRow = ({
	item,
	companyDetails,
}: {
	item:
		| {
				icon: OverridableComponent<SvgIconTypeMap<object, 'svg'>> & {
					muiName: string
				}
				title: string
				fieldName: string
		  }
		| {
				icon: null
				title: string
				fieldName: string
		  }
	companyDetails?: ICompanyCommonInfo
}) => {
	const value = companyDetails?.[item.fieldName as keyof ICompanyCommonInfo]
	const displayValue =
		typeof value === 'string' || typeof value === 'number' ? value : '-'

	return (
		<Stack
			className={`companyDetails ${
				item.fieldName === 'address' ? 'twoColChild' : ''
			}`}>
			<Stack className='detailsHeader'>
				{item.icon && <item.icon className='icon' />}
				<Typography className='detailsTitle'>{item.title}</Typography>
			</Stack>
			<Typography className='details'>{displayValue}</Typography>
		</Stack>
	)
}

const CompanyInfoSection = ({
	companyDetails,
}: {
	companyDetails?: ICompanyCommonInfo
}) => {
	return (
		<Stack className='threeCol'>
			{CompanyDetailsData.map((item) => (
				<CompanyInfoRow
					key={item?.title}
					item={item}
					companyDetails={companyDetails}
				/>
			))}
			<Stack key='appType' className={`companyDetails`}>
				<Stack className='detailsHeader'>
					<Typography className='detailsTitle'>App Type</Typography>
				</Stack>
				<Typography className='details'>
					{ServiceTypeNames[
						companyDetails?.serviceType as keyof typeof ServiceTypeNames
					] || companyDetails?.serviceType}
				</Typography>
			</Stack>
			<Stack key='registryType' className={`companyDetails`}>
				<Stack className='detailsHeader'>
					<Typography className='detailsTitle'>Registry Type</Typography>
				</Stack>
				<Typography className='details'>
					{
						RegistryTypeNames[
							companyDetails?.registry as keyof typeof RegistryTypeNames
						]
					}
				</Typography>
			</Stack>
		</Stack>
	)
}

const AdminCard = ({ manager }: { manager: Admin }) => (
	<Stack className='cardRow'>
		<Stack className='managerTitle'>
			<Box
				component='img'
				className='logo'
				src={manager?.profileImage?.url ?? ''}
			/>
			<Typography className='managerName'>{manager.name}</Typography>
		</Stack>
		<Stack className='managerRow'>
			<EmailOutlinedIcon className='icon' />
			<Typography className='managerDetail'>{manager.email}</Typography>
		</Stack>
		<Stack className='managerRow'>
			<PhoneOutlinedIcon className='icon' />
			<Typography className='managerDetail'>
				{manager.countryCode} {manager.phoneNumber}
			</Typography>
		</Stack>
	</Stack>
)

const CompanyAdminList = ({
	admins,
	handleUpdateStatus,
	stageInfo,
}: {
	admins?: Admin[]
	handleUpdateStatus: UseMutationResult<
		AxiosResponse,
		AxiosError,
		void,
		unknown
	>
	stageInfo?:
		| Stage
		| {
				stage: string
				status: string
		  }
		| undefined
}) => {
	return (
		<Stack sx={{ paddingLeft: 2 }}>
			<Stack
				sx={{
					padding: 2,
					// flexDirection: 'row',
					alignSelf: 'end',
				}}>
				{stageInfo?.status === 'pending' && (
					<LoadingButton
						loading={handleUpdateStatus?.isPending}
						onClick={() => handleUpdateStatus.mutate()}
						variant='contained'
						color='success'
						size='small'
						className='approveButton'>
						Approve
					</LoadingButton>
				)}
			</Stack>
			<Stack className='titleContainer'>
				<PersonOutlineOutlinedIcon color='primary' className='titleIcon' />
				<Typography className='title'>Manager Details</Typography>
			</Stack>
			<Stack className='fc'>
				{admins?.map((admin) => (
					<AdminCard key={admin?.id} manager={admin} />
				))}
			</Stack>
		</Stack>
	)
}
interface ComponentProps {
	handleDownload: (fileUrl?: string, fileName?: string) => Promise<void>
	getCompanyDetails: UseQueryResult<
		ICompanyDetailsAccountManagement | Company | null,
		Error
	>
	assessCompanyRequestMutation: UseMutationResult<
		AxiosResponse<{
			companyId: string
		}>,
		AxiosError,
		void,
		unknown
	>
	isApproved: boolean
}
export const CompanyDetailsWithStatus = ({
	handleDownload,
	getCompanyDetails,
	assessCompanyRequestMutation,
	isApproved,
}: ComponentProps) => {
	const companyDetails = getCompanyDetails?.data as ICompanyCommonInfo
	const adminDetails = (getCompanyDetails?.data?.admins as Admin[]) ?? []

	const registrationStageInfo = useMemo(() => {
		const info = isApproved
			? {
					stage: 'registration',
					status: 'approved',
			  }
			: {
					stage: 'registration',
					status: 'pending',
			  }
		return info
	}, [isApproved])

	return (
		<StyledCard>
			{getCompanyDetails?.isLoading ? (
				<CircularProgress sx={{ alignSelf: 'center' }} />
			) : (
				<Stack direction='row' justifyContent='space-between'>
					<Stack className='leftPanel'>
						<CompanyStatusHeader
							companyDetails={companyDetails}
							stageInfo={registrationStageInfo}
						/>
						<Divider className='divider' />
						<CompanyInfoSection companyDetails={companyDetails} />
						<DownloadFileBox
							document={companyDetails?.coiDocument}
							handleDownload={handleDownload}
						/>
					</Stack>
					<CompanyAdminList
						admins={adminDetails}
						handleUpdateStatus={assessCompanyRequestMutation}
						stageInfo={registrationStageInfo}
					/>
				</Stack>
			)}
		</StyledCard>
	)
}
