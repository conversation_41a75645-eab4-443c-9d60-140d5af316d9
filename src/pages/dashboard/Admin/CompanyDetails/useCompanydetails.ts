import { authAxios, publicAxios } from '@/contexts'
import { Company, ICompanyDetailsAccountManagement } from '@/types'
import { handleImageUpload, showAxiosErrorToast } from '@/utils/helper'
import { useMutation, useQuery } from '@tanstack/react-query'
import { AxiosError } from 'axios'
import { useCallback, useState } from 'react'
import { useNavigate, useParams, useSearchParams } from 'react-router-dom'
import { toast } from 'react-toastify'

export const useCompanyDetails = () => {
	const { id } = useParams()

	const [searchParams] = useSearchParams()
	const isApproved = searchParams.get('isApproved')
	const isShowContractDetails = searchParams.get('isShowContractDetails')

	const getCompanyDetailsQueryFn = async (): Promise<
		ICompanyDetailsAccountManagement | Company | null
	> => {
		switch (isApproved) {
			case 'true': {
				const { data } = await authAxios.get<ICompanyDetailsAccountManagement>(
					`/company/${id}`
				)
				return data
			}
			case 'false': {
				const { data } = await authAxios.get<Company>(`/company/request/${id}`)
				return data
			}
			default:
				throw new Error('Invalid isApproved parameter')
		}
	}

	const getCompanyDetails = useQuery({
		queryKey: ['companyDetails', id, isApproved],
		queryFn: getCompanyDetailsQueryFn,

		enabled: !!id, // make sure id exists before fetching
	})

	const handleDownload = useCallback(
		async (fileUrl?: string, fileName: string = 'document.pdf') => {
			if (!fileUrl) return

			try {
				const response = await publicAxios.get(fileUrl, {
					responseType: 'blob',
				})

				const blobUrl = URL.createObjectURL(response.data)
				const link = document.createElement('a')
				link.href = blobUrl
				link.download = fileName
				document.body.appendChild(link)
				link.click()
				document.body.removeChild(link)
				URL.revokeObjectURL(blobUrl)
			} catch (error) {
				toast.error('Failed to download file. Please try again.')
			}
		},
		[]
	)
	const [isshowSignedDoc, setIsShowSignedDoc] = useState<boolean>(false)
	const handleUploadDoc = useMutation({
		mutationKey: ['uploadSignedNDADoc'],
		mutationFn: async (uploadedFileId: string) => {
			return await authAxios.put(`/company/${id}/signed-contract-documents`, {
				NdaSignedDocumentId: uploadedFileId,
			})
		},
		onError: (error: AxiosError) => {
			showAxiosErrorToast(error)
		},
		onSuccess: ({ data }) => {
			toast(data?.message)
			getCompanyDetails.refetch()
			setIsShowSignedDoc(true)
		},
	})
	const navigate = useNavigate()
	const handleUpdateStatus = useMutation({
		mutationKey: ['updateStatus'],
		mutationFn: async ({ stage }: { stage: string }) => {
			const payload = {
				stage: stage,
				status: 'approved',
			}
			const data: { message: string } = await authAxios.put(
				`/company/${id}/stage/status`,
				payload
			)

			return {
				data,
				stage,
			}
		},
		onError: (error: AxiosError) => {
			showAxiosErrorToast(error)
		},
		onSuccess: ({ data }) => {
			toast(data?.message)

			getCompanyDetails.refetch()
			setIsShowSignedDoc(false)
		},
	})
	const assessCompanyRequestMutation = useMutation({
		mutationKey: ['assessCompanyRequestMutation'],
		mutationFn: async () => {
			const payload = {
				status: 'approved',
			}
			return authAxios.post<{ companyId: string }>(
				`/company/request/${id}/assess`,
				payload
			)
		},
		onSuccess: (data) => {
			const url = `/dashboard/admin/company-details/${data.data.companyId}?isApproved=true&isShowContractDetails=${isShowContractDetails}`
			navigate(url, { replace: true })
		},
		onError: (err: AxiosError) => {
			showAxiosErrorToast(err)
		},
	})
	const handleFileChange = async (file: File) => {
		try {
			const data = await handleImageUpload(file)

			const res = await handleUploadDoc.mutateAsync(data?.id)
			return res
		} catch (error) {
			toast.error('Failed to upload file. Please try again.')
			throw error
		}
	}
	return {
		getCompanyDetails,
		handleDownload,
		handleFileChange,
		isApproved: isApproved === 'true',
		isshowSignedDoc,
		handleUpdateStatus,
		assessCompanyRequestMutation,
		isShowContractDetails: isShowContractDetails === 'true',
	}
}
