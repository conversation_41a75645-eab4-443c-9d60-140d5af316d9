import { IPropsConstantLeftSection } from '@/interfaces'
import { theme } from '@/lib/theme/theme'
import { CustomTextField } from '@/utils/components'
import {
	Button,
	FormControl,
	Stack,
	Typography,
} from '@mui/material'

export const AddCarbonCredit = ({
	handleSubmit,
	formState: { errors, isDirty },
	register,
	onSubmit,
	watch
}: IPropsConstantLeftSection) => {
	return (
		<Stack
			className='container'
			component='form'
			onSubmit={handleSubmit(onSubmit)}
			gap={theme.spacing(2.5)}>
			<Stack className='credit-constant-container'>
				<FormControl fullWidth className='formControl'>
					<Typography className='label' variant='subtitle1'>
						Carbon Credit Numerator
					</Typography>

					<CustomTextField
						watch={watch}
						hideNumberArrows
						id='ccConstantNumerator'
						placeholder='Enter Value'
						variant='outlined'
						type='number'
						InputProps={{
							inputProps: {
								step: 'any',
							},
						}}
						error={!!errors?.ccConstantNumerator}
						helperText={errors?.ccConstantNumerator?.message ?? ''}
						{...register('ccConstantNumerator')}
					/>
				</FormControl>
				<FormControl fullWidth className='formControl'>
					<Typography className='label' variant='subtitle1'>
						Carbon Credit Denominator
					</Typography>

					<CustomTextField
						watch={watch}
						hideNumberArrows
						id='ccConstantDenominator'
						placeholder='Enter Value'
						variant='outlined'
						type='number'
						InputProps={{
							inputProps: {
								step: 'any',
							},
						}}
						error={!!errors?.ccConstantDenominator}
						helperText={errors?.ccConstantDenominator?.message ?? ''}
						{...register('ccConstantDenominator')}
					/>
				</FormControl>
				<FormControl fullWidth className='formControl'>
					<Typography className='label' variant='subtitle1'>
						PAC Fraction
					</Typography>

					<CustomTextField
						watch={watch}
						hideNumberArrows
						id='pacFraction'
						placeholder='Enter Value'
						variant='outlined'
						type='number'
						InputProps={{
							inputProps: {
								step: 'any',
							},
						}}
						error={!!errors?.pacFraction}
						helperText={errors?.pacFraction?.message ?? ''}
						{...register('pacFraction')}
					/>
				</FormControl>
				<FormControl fullWidth className='formControl'>
					<Typography className='label' variant='subtitle1'>
						SPC Fraction
					</Typography>

					<CustomTextField
						watch={watch}
						hideNumberArrows
						id='spcFraction'
						placeholder='Enter Value'
						variant='outlined'
						type='number'
						InputProps={{
							inputProps: {
								step: 'any',
							},
						}}
						error={!!errors?.spcFraction}
						helperText={errors?.spcFraction?.message ?? ''}
						{...register('spcFraction')}
					/>
				</FormControl>
				<Stack className='buttonContainer'>
					<Button variant='contained' type='submit' disabled={!isDirty}>
						Update Constants
					</Button>
				</Stack>
			</Stack>
		</Stack>
	)
}
