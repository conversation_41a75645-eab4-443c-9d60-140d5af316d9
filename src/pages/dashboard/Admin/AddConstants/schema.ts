import { addConstantTabs } from '@/utils/constant'
import * as yup from 'yup'

export const addConstantSchema = (type: addConstantTabs) => {
	const addProductionConstantSchema = yup.object().shape({
		csinkManagerId: yup.string().required('Please Select Your Csink Manager'),
		// Moisture
		csinkNetworkMoistureRequired: yup.boolean().nullable(),
		csinkNetworkMaximumMoisture: yup
			.number()
			.required('Please enter your csinkNetworkMaximumMoisture')
			.nullable()
			.typeError('Please enter a valid number for csinkNetworkMaximumMoisture'),
		artisanProMaximumMoisture: yup
			.number()
			.required('Please enter your artisanProMaximumMoisture')
			.nullable()
			.typeError('Please enter a valid number for artisanProMaximumMoisture'),

		// Temperature
		csinkNetworkTemperatureRequired: yup.boolean().nullable(),
		csinkNetworkMinimumTemperatureImages: yup
			.number()
			.required('Please enter your csinkNetworkMinimumTemperatureImages')
			.nullable()
			.typeError(
				'Please enter a valid number for csinkNetworkMinimumTemperatureImages'
			),
		csinkNetworkMinimumTemperature: yup
			.number()
			.required('Please enter your csinkNetworkMinimumTemperature')
			.nullable()
			.typeError(
				'Please enter a valid number for csinkNetworkMinimumTemperature'
			),
		artisanProMinimumTemperatureImages: yup
			.number()
			.required('Please enter your artisanProMinimumTemperatureImages')
			.nullable()
			.typeError(
				'Please enter a valid number for artisanProMinimumTemperatureImages'
			),
		artisanProMinimumTemperature: yup
			.number()
			.required('Please enter your artisanProMinimumTemperature')
			.nullable()
			.typeError(
				'Please enter a valid number for artisanProMinimumTemperature'
			),

		// Quenching Image
		csinkNetworkSeparateQuenchingImage: yup.boolean().nullable(),
		artisanProSeparateQuenchingImage: yup.boolean().nullable(),
		csinkNetworkPreQuenchingImageRequired: yup.boolean().nullable(),
		artisanProPreQuenchingImageRequired: yup.boolean().nullable(),

		// Firing Video, Chimney Image, and others
		csinkNetworkFiringVideosRequired: yup.boolean().nullable(),
		csinkNetworkSeparateChimneyImage: yup.boolean().nullable(),
		artisanProSeparateChimneyImage: yup.boolean().nullable(),
		artisanProMaximumVideoLengthInSeconds: yup
			.number()
			.required('Please enter your artisanProMaximumVideoLengthInSeconds')
			.nullable()
			.typeError(
				'Please enter a valid number for artisanProMaximumVideoLengthInSeconds'
			),
		csinkNetworkMaximumVideoLengthInSeconds: yup
			.number()
			.required('Please enter your csinkNetworkMaximumVideoLengthInSeconds')
			.nullable()
			.typeError(
				'Please enter a valid number for csinkNetworkMaximumVideoLengthInSeconds'
			),

		csinkNetworkMinimumFiringVideos: yup
			.number()
			.required('Please enter your csinkNetworkMinimumFiringVideos')
			.nullable()
			.typeError(
				'Please enter a valid number for csinkNetworkMinimumFiringVideos'
			),
		csinkNetworkMinimumFiringImages: yup
			.number()
			.required('Please enter your csinkNetworkMinimumFiringImages')
			.nullable()
			.typeError(
				'Please enter a valid number for csinkNetworkMinimumFiringImages'
			),
		artisanProMinimumFiringImages: yup
			.number()
			.required('Please enter your artisanProMinimumFiringImages')
			.nullable()
			.typeError(
				'Please enter a valid number for artisanProMinimumFiringImages'
			),
		artisanProMinimumFiringVideos: yup
			.number()
			.required('Please enter your artisanProMinimumFiringVideos')
			.nullable()
			.typeError(
				'Please enter a valid number for artisanProMinimumFiringVideos'
			),

		// Credits
		creditsToRegister: yup
			.number()
			.required('Please enter your creditsToRegister')
			.nullable()
			.typeError('Please enter a valid number for creditsToRegister'),
		//Email booleans
		isBatchRejectionEmailEnabled: yup.boolean().nullable(),
		isBatchCommentEmailEnabled: yup.boolean().nullable(),
	})

	const addCarbonConstantSchema = yup.object({
		ccConstantNumerator: yup
			.number()
			.required('Carbon Credit Numerator is required')
			.nullable()
			.typeError('this field is required'),

		ccConstantDenominator: yup
			.number()
			.required('Carbon Credit Denominator is required')
			.nullable()
			.typeError('this field is required'),

		pacFraction: yup
			.number()
			.required('PAC Fraction is required')
			.nullable()
			.typeError('this field is required'),
		spcFraction: yup
			.number()
			.required('SPC Fraction is required')
			.nullable()
			.typeError('this field is required'),
	})

	switch (type) {
		case addConstantTabs.productionConstants:
			return addProductionConstantSchema
		case addConstantTabs.carbonCreditConstants:
			return addCarbonConstantSchema
	}
}
