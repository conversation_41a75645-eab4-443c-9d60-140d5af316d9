import { IAllConstants } from '@/interfaces'
import { theme } from '@/lib/theme/theme'
import { addConstantTabs } from '@/utils/constant'
import { Stack, Typography } from '@mui/material'
import { ConstantsHistoryTable } from './ConstantsHistoryTable'

interface IProps {
	tab: addConstantTabs
	constants?: IAllConstants[]
}
export const HistoryConstant = ({ tab, constants }: IProps) => {
	switch (tab) {
		case addConstantTabs.productionConstants:
			return <ConstantsHistoryTable />
		case addConstantTabs.carbonCreditConstants:
			return (
				<Stack gap={theme.spacing(2)}>
					<Stack className='history-fields'>
						<Typography variant='subtitle1'>
							Carbon Credit Numerator:
						</Typography>
						<Typography variant='body2'>
							{constants
								?.filter((item) => item?.ccConstantNumerator)
								?.map((x) => x?.ccConstantNumerator)
								.join(', ')}
						</Typography>
					</Stack>
					<Stack className='history-fields'>
						<Typography variant='subtitle1'>
							Carbon Credit Denominator:
						</Typography>
						<Typography variant='body2'>
							{constants
								?.filter((item) => item?.ccConstantNumerator)
								?.map((x) => x?.ccConstantNumerator)
								.join(', ')}
						</Typography>
					</Stack>
					<Stack className='history-fields'>
						<Typography variant='subtitle1'>PAC Fraction:</Typography>
						<Typography variant='body2'>
							{constants
								?.filter((item) => item?.pacFraction)
								?.map((x) => x?.pacFraction)
								.join(', ')}
						</Typography>
					</Stack>
					<Stack className='history-fields'>
						<Typography variant='subtitle1'>SPC Fraction:</Typography>
						<Typography variant='body2'>
							{constants
								?.filter((item) => item?.spcFraction)
								?.map((x) => x?.spcFraction)
								.join(', ')}
						</Typography>
					</Stack>
				</Stack>
			)
		default:
			return
	}
}
