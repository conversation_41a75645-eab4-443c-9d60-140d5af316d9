import { useAddConstants } from './useAddConstants'
import { Stack, styled, Tab, Typography } from '@mui/material'
import { TwoColumnLayout } from '@/components'
import { IAllConstants } from '@/interfaces'
import { addConstantTabs, userRoles } from '@/utils/constant'
import { TabContext, TabList, TabPanel } from '@mui/lab'
import { HistoryConstant } from './HistoryConstants'
import { IUserContext } from '@/contexts/Auth/type'
import { AddCarbonCredit } from './AddCarbonCredit'
import { AddProductionConstant } from './AddProductionConstants'

const tabs = (userDetails?: IUserContext) => [
	{
		label: 'Production Constants',
		value: addConstantTabs.productionConstants,
		hidden: false,
	},
	{
		label: 'Carbon Credit Constants',
		value: addConstantTabs.carbonCreditConstants,
		hidden: userDetails?.accountType !== userRoles.Admin,
	},
]

export const AddConstants = () => {
	const {
		handleSubmit,
		setValue,
		formState,
		register,
		onSubmit,
		fetchCsinkManagers,
		isCsinkManagerLogin,
		paramsCsinkManagerId,
		CsinkManagerName,
		watch,
		clearErrors,
		reset,
		setSearchParams,
		handleTabChange,
		userDetails,
		paramsTab,
		globalConstantQuery,
		fetchCsinkManagerDetails,
	} = useAddConstants()

	const leftSectionProps = {
		isCsinkManagerLogin,
		paramsCsinkManagerId,
		handleSubmit,
		setValue,
		register,
		onSubmit,
		fetchCsinkManagers,
		CsinkManagerName,
		reset,
		formState,
		watch,
		clearErrors,
		setSearchParams,
	}

	return (
		<CustomizedWrapper>
			{/* <CustomHeader showBottomBorder={true} heading='Add Constants' /> */}
			{/* <Divider /> */}
			<Stack>
				<TabContext value={paramsTab}>
					<TabList className='tabList' onChange={handleTabChange}>
						{tabs(userDetails).map(
							({ label, value, hidden }, index) =>
								!hidden && <Tab key={index} label={label} value={value} />
						)}
					</TabList>
					<TabPanel
						value={addConstantTabs.productionConstants}
						sx={{ padding: 0 }}>
						<TwoColumnLayout
							left={
								<Stack className='content-section'>
									<AddProductionConstant {...leftSectionProps} />
									<>
										<Typography variant='h5' textAlign='left' sx={{ px: 2 }}>
											History
										</Typography>
										<HistoryConstant
											tab={paramsTab as addConstantTabs}
											constants={
												fetchCsinkManagerDetails?.data
													?.csinkManagerPreviousConstant as IAllConstants[]
											}
										/>
									</>
								</Stack>
							}
							right={<></>}
							gridBreakpoints={[7, 5]}
						/>
					</TabPanel>
					<TabPanel
						value={addConstantTabs.carbonCreditConstants}
						sx={{ padding: 0 }}>
						<TwoColumnLayout
							left={
								<Stack className='content-section'>
									<AddCarbonCredit {...leftSectionProps} />
									<>
										<Typography variant='h5' textAlign='center'>
											History of Constants
										</Typography>
										<HistoryConstant
											tab={paramsTab as addConstantTabs}
											constants={
												globalConstantQuery?.data
													?.earlierConstant as IAllConstants[]
											}
										/>
									</>
								</Stack>
							}
							right={<></>}
							gridBreakpoints={[7, 5]}
						/>
					</TabPanel>
				</TabContext>
			</Stack>
		</CustomizedWrapper>
	)
}

export const CustomizedWrapper = styled(Stack)(({ theme }) => ({
	gap: theme.spacing(2),
	paddingTop: theme.spacing(1),
	'.content-section': {
		gap: theme.spacing(3),
		padding: theme.spacing(3),
		height: '100%',
		'.container': {
			'.labelHeading': {
				...theme.typography.body2,
			},
			'.buttonContainer': {
				flexDirection: 'row',
				justifyContent: 'center',
			},
		},
	},
	'.credit-constant-container': {
		flexDirection: 'column',
		gap: theme.spacing(4),
		'.buttonContainer': {
			flexDirection: 'row',
			justifyContent: 'center',
		},
	},
	'.history-fields': {
		flexDirection: 'row',
		gap: theme.spacing(2),
	},
}))
