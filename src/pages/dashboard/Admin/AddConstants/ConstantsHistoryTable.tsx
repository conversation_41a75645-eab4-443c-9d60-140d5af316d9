import { ActionInformationDrawer, CustomDataGrid } from '@/components'
import { authAxios } from '@/contexts'
import { theme } from '@/lib/theme/theme'
import { Close } from '@mui/icons-material'
import {
	Table,
	TableBody,
	TableCell,
	TableHead,
	TableRow,
	CircularProgress,
	Typography,
	alpha,
	styled,
	Stack,
	IconButton,
} from '@mui/material'
import { useQuery } from '@tanstack/react-query'
import { format } from 'date-fns'
import { useMemo, useState } from 'react'
import { formFields } from './ProductionConstantsForm'
import { TAddConstant } from '@/interfaces'
import { Nullable } from '@/types'

interface UpdatedBy {
	id: string
	name: string
	email: string
}

interface ConstantsHistoryEntry {
	csinkManagerId: string
	csinkManagerName: string
	updatedBy: UpdatedBy
	updatedAt: string
	constants: TAddConstant & { id?: Nullable<string> }
	csinkManagerCurrentConstants: TAddConstant & { id?: Nullable<string> }
}

interface ConstantsHistoryResponse {
	constantsHistory: ConstantsHistoryEntry[]
}

function yesNoNumeric(value: boolean | number): string | number {
	if (value === true) return 'Yes'
	if (value === false) return 'No'
	return value
}

export const ConstantsHistoryTable = () => {
	const {
		data: constantsQueryData,
		isLoading,
		isError,
	} = useQuery<ConstantsHistoryResponse>({
		queryKey: ['constantsHistoryQuery'],
		queryFn: async () => {
			const response: any = await authAxios.get<ConstantsHistoryResponse>(
				'/csink-manager/constants-history'
			)
			return response.data
		},
	})
	const [showModal, setShowModal] = useState(false)
	const [constantsData, setData] = useState<TAddConstant>(null!)
	const [currentConstantsData, setcurrentConstantsData] =
		useState<TAddConstant>(null!)

	const handleRowClick = (constantsId?: Nullable<string>) => {
		if (!constantsQueryData?.constantsHistory) return

		const constantsData = constantsQueryData.constantsHistory.find(
			(entry) => entry.constants.id === constantsId
		)?.constants
		const constantsCurrentData = constantsQueryData.constantsHistory.find(
			(entry) => entry.constants.id === constantsId
		)?.csinkManagerCurrentConstants

		if (constantsData && constantsCurrentData) {
			setcurrentConstantsData(constantsCurrentData)
			setData(constantsData)
			setShowModal(true)
		}
	}

	const columns = [
		{
			field: 'categoryName',
			headerName: '',
			flex: 1,
			sortable: false,
		},
		{
			field: 'artisanValue',
			headerName: 'Artisan Pro',
			flex: 1,
			sortable: false,
			renderCell: (params: any) => {
				const value = params.row.artisanValue
				const current = params.row.currentArtisanValue

				return (
					<Typography
						variant='subtitle1'
						sx={{
							color: value ? 'green' : 'black',
						}}>
						{yesNoNumeric(value ?? current ?? '-')}
					</Typography>
				)
			},
		},
		{
			field: 'csinkValue',
			headerName: 'Csink Network',
			flex: 1,
			sortable: false,
			renderCell: (params: any) => {
				if (params.row.bothNone) return null

				const value = params.row.csinkValue
				const current = params.row.currentCsinkValue
				return (
					<Typography
						variant='subtitle1'
						sx={{
							color: value ? 'green' : 'black',
						}}>
						{yesNoNumeric(value ?? current ?? '-')}
					</Typography>
				)
			},
		},
	]

	const rows = useMemo(() => {
		return formFields.map((category, index) => {
			const artisanKey = category.artisan?.name as keyof TAddConstant
			const csinkKey = category.csink?.name as keyof TAddConstant

			const artisanType = category.artisan?.type
			const csinkType = category.csink?.type

			return {
				id: index,
				categoryName: category.categoryName,
				artisanValue: constantsData?.[artisanKey],
				currentArtisanValue: currentConstantsData?.[artisanKey],
				csinkValue: constantsData?.[csinkKey],
				currentCsinkValue: currentConstantsData?.[csinkKey],
				bothNone: artisanType === 'none' && csinkType === 'none',
			}
		})
	}, [constantsData, currentConstantsData])

	return (
		<>
			{' '}
			<Table>
				<TableHead
					sx={{ borderBottom: `1px solid ${theme.palette.primary.light}` }}>
					<TableRow>
						<TableCell sx={{ fontWeight: 'bold' }}>Network</TableCell>
						<TableCell sx={{ fontWeight: 'bold' }}>Updated by</TableCell>
						<TableCell sx={{ fontWeight: 'bold' }}>Updated At</TableCell>
					</TableRow>
				</TableHead>
				<TableBody>
					{isLoading ? (
						<TableRow>
							<TableCell colSpan={3} align='center'>
								<CircularProgress />
							</TableCell>
						</TableRow>
					) : isError ? (
						<TableRow>
							<TableCell colSpan={3} align='center' sx={{ color: 'red' }}>
								Failed to load data.
							</TableCell>
						</TableRow>
					) : (
						constantsQueryData?.constantsHistory?.map((entry) => (
							<TableRow
								key={entry.constants.id}
								onClick={() => handleRowClick(entry?.constants?.id)}
								sx={{
									'&:hover': {
										backgroundColor: alpha(theme.palette.primary.light, 0.05),
									},
								}}>
								<TableCell>
									<Typography>{entry.csinkManagerName}</Typography>
								</TableCell>
								<TableCell>
									<Typography>{entry.updatedBy.name}</Typography>
									<Typography>{entry.updatedBy.email}</Typography>
								</TableCell>
								<TableCell>
									<Typography>
										<TableCell>
											<Typography>
												{entry.updatedAt !== '0001-01-01T00:00:00Z'
													? format(
															new Date(entry.updatedAt),
															'dd MMM yyyy (hh:mm a)'
													  )
													: 'N/A'}
											</Typography>
										</TableCell>
									</Typography>
								</TableCell>
							</TableRow>
						))
					)}
				</TableBody>
			</Table>
			<ActionInformationDrawer
				open={showModal}
				onClose={() => setShowModal(false)}
				anchor='right'
				component={
					<StyleContainer>
						<Stack className='header'>
							<Stack
								direction='row'
								spacing={1}
								alignItems='center'
								width='100%'
								justifyContent='space-between'>
								<Stack>
									<Typography variant='h5'>Constant Values</Typography>
								</Stack>
								<IconButton onClick={() => setShowModal(false)}>
									<Close />
								</IconButton>
							</Stack>
						</Stack>
						<Stack sx={{ px: 2 }}>
							<CustomDataGrid
								columns={columns}
								rows={rows}
								showPagination={false}
								showRowsPerPage={false}
								disableRowSelectionOnClick
								sx={{
									'.MuiDataGrid-columnHeadersInner': {
										background: 'transparent',
									},
									'.MuiDataGrid-columnHeaderTitle': {
										color: 'inherit',
										textOverflow: 'ellipsis',
										whiteSpace: 'nowrap',
									},
									'.MuiDataGrid-row:hover': {
										cursor: 'default',
									},
								}}
							/>
						</Stack>
					</StyleContainer>
				}
			/>
		</>
	)
}

const StyleContainer = styled(Stack)(({ theme }) => ({
	gap: theme.spacing(2),
	'.header': {
		flexDirection: 'row',
		justifyContent: 'space-between',
		alignItems: 'center',
		padding: theme.spacing(4.5, 2),
		borderBottom: `${theme.spacing(0.125)} solid ${
			theme.palette.neutral['100']
		}`,
	},
}))
