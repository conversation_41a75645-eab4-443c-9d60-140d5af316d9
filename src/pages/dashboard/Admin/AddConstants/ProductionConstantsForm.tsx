import { IAllConstants } from '@/interfaces'
import { theme } from '@/lib/theme/theme'
import { CustomTextField } from '@/utils/components'
import {
	TableHead,
	TableRow,
	TableCell,
	TableBody,
	Table,
	RadioGroup,
	Stack,
	FormControlLabel,
	Radio,
	Typography,
} from '@mui/material'
import {
	FieldErrors,
	UseFormRegister,
	UseFormSetValue,
	UseFormWatch,
} from 'react-hook-form'

type Props = {
	register: UseFormRegister<IAllConstants>
	watch: UseFormWatch<IAllConstants>
	errors: FieldErrors<IAllConstants>
	setValue: UseFormSetValue<IAllConstants>
}

const renderInputField = (
	type: string,
	name: string,
	register: UseFormRegister<IAllConstants>,
	watch: UseFormWatch<IAllConstants>,
	errors: FieldErrors<IAllConstants>,
	setValue: UseFormSetValue<IAllConstants>,
	style?: React.CSSProperties
) => {
	if (type === 'checkbox') {
		return (
			<>
				<RadioGroup
					row
					style={style}
					value={watch(name as keyof IAllConstants)}
					onChange={(e) => {
						setValue(name as keyof IAllConstants, e.target.value === 'true', {
							shouldDirty: true,
						})
					}}>
					<Stack direction='row' spacing={2} alignItems='center'>
						<FormControlLabel value={true} control={<Radio />} label='Yes' />
						<FormControlLabel
							value={false}
							control={<Radio value={false} />}
							label='No'
						/>
					</Stack>
				</RadioGroup>

				{errors?.[name as keyof FieldErrors<IAllConstants>] && (
					<Typography color='error'>
						{errors[name as keyof FieldErrors<IAllConstants>]?.message}
					</Typography>
				)}
			</>
		)
	} else if (type === 'label') {
		return (
			<CustomTextField
				fullWidth
				hideNumberArrows
				watch={watch}
				id={name}
				placeholder='Enter Value'
				variant='outlined'
				type='number'
				{...register(name as keyof UseFormRegister<IAllConstants>)}
				error={!!errors?.[name as keyof FieldErrors<IAllConstants>]}
				helperText={
					errors?.[name as keyof FieldErrors<IAllConstants>]?.message || ''
				}
			/>
		)
	}
	return <></>
}

export const formFields = [
	{
		categoryName: 'Record Moisture',

		artisan: {
			type: 'none',
		},
		csink: {
			type: 'checkbox',
			name: 'csinkNetworkMoistureRequired',
		},
	},
	{
		categoryName: 'Max Moisture Value',
		csink: {
			type: 'label',
			name: 'csinkNetworkMaximumMoisture',
		},
		artisan: {
			type: 'label',

			name: 'artisanProMaximumMoisture',
		},
	},
	{
		categoryName: 'Record Temperature',

		csink: {
			type: 'checkbox',
			name: 'csinkNetworkTemperatureRequired',
		},
		artisan: {
			type: 'none',
			name: '',
		},
	},
	{
		categoryName: 'Min Temperature Images',

		csink: {
			type: 'label',
			name: 'csinkNetworkMinimumTemperatureImages',
		},
		artisan: {
			type: 'label',
			name: 'artisanProMinimumTemperatureImages',
		},
	},
	{
		categoryName: 'Min Temperature',
		csink: {
			type: 'label',
			name: 'csinkNetworkMinimumTemperature',
		},
		artisan: {
			type: 'label',
			name: 'artisanProMinimumTemperature',
		},
	},
	{
		categoryName: 'Min Firing Images',

		csink: {
			type: 'label',
			name: 'csinkNetworkMinimumFiringImages',
		},
		artisan: {
			type: 'label',
			name: 'artisanProMinimumFiringImages',
		},
	},
	{
		categoryName: 'Record Videos',
		csink: {
			type: 'checkbox',
			name: 'csinkNetworkFiringVideosRequired',
		},
		artisan: {
			type: 'none',
			name: '',
		},
	},
	{
		categoryName: 'Min Firing Videos',

		csink: {
			type: 'label',
			name: 'csinkNetworkMinimumFiringVideos',
		},
		artisan: {
			type: 'label',
			name: 'artisanProMinimumFiringVideos',
		},
	},
	{
		categoryName: 'Record Chimney Images',

		csink: {
			type: 'checkbox',
			name: 'csinkNetworkSeparateChimneyImage',
		},
		artisan: {
			type: 'checkbox',
			name: 'artisanProSeparateChimneyImage',
		},
	},
	{
		categoryName: 'Record Pre Quenching Image',

		csink: {
			type: 'checkbox',
			name: 'csinkNetworkPreQuenchingImageRequired',
		},
		artisan: {
			type: 'checkbox',
			name: 'artisanProPreQuenchingImageRequired',
		},
	},
	{
		categoryName: 'Record Quenching Image',

		csink: {
			type: 'checkbox',
			name: 'csinkNetworkSeparateQuenchingImage',
		},
		artisan: {
			type: 'checkbox',
			name: 'artisanProSeparateQuenchingImage',
		},
	},

	{
		categoryName: 'Max Video in Seconds',
		csink: {
			type: 'label',
			name: 'csinkNetworkMaximumVideoLengthInSeconds',
		},
		artisan: {
			type: 'label',
			name: 'artisanProMaximumVideoLengthInSeconds',
		},
	},
	{
		categoryName: 'Min Carbon Credits',
		type: 'label',
		fullWidth: true,
		name: 'creditsToRegister',
		csink: {
			type: 'none',
			name: 'creditsToRegister',
		},
		artisan: {
			type: 'none',
			name: 'creditsToRegister',
		},
	},
	{
		categoryName: 'Is Batch rejection Email Enabled',
		name: 'isBatchRejectionEmailEnabled',
		type: 'checkbox',
		fullWidth: true,
		csink: {
			type: 'none',
			name: 'isBatchRejectionEmailEnabled',
		},
		artisan: {
			type: 'none',
			name: 'isBatchRejectionEmailEnabled',
		},
		style: { justifyContent: 'center' },
	},
	{
		categoryName: 'Is Bacth Comment Email Enabled',
		name: 'isBatchCommentEmailEnabled',
		type: 'checkbox',
		fullWidth: true,
		csink: {
			type: 'none',
			name: 'isBatchCommentEmailEnabled',
		},
		artisan: {
			type: 'none',
			name: 'isBatchCommentEmailEnabled',
		},
		style: {
			justifyContent: 'center',
		},
	},
]

export const ProductionConstantsForm = ({
	register,
	watch,
	errors,
	setValue,
}: Props) => {
	return (
		<Table
			sx={{
				tableLayout: 'fixed',
			}}>
			<TableHead
				sx={{
					borderBottom: `1px solid ${theme.palette.primary.light}`,
				}}>
				<TableRow>
					<TableCell></TableCell>
					<TableCell sx={{ fontWeight: 'bold' }}>Artisan Pro</TableCell>
					<TableCell sx={{ fontWeight: 'bold' }}>Csink Network</TableCell>
				</TableRow>
			</TableHead>
			<TableBody>
				{formFields.map(
					({ categoryName, artisan, csink, fullWidth, name, type, style }) => (
						<TableRow key={categoryName}>
							<TableCell>{categoryName}</TableCell>
							{fullWidth ? (
								<TableCell colSpan={2} align='center'>
									{renderInputField(
										type,
										name,
										register,
										watch,
										errors,
										setValue,
										style
									)}
								</TableCell>
							) : (
								<>
									<TableCell>
										{artisan.name &&
											renderInputField(
												artisan.type,
												artisan.name,
												register,
												watch,
												errors,
												setValue
											)}
									</TableCell>
									<TableCell>
										{renderInputField(
											csink.type,
											csink.name,
											register,
											watch,
											errors,
											setValue
										)}
									</TableCell>
								</>
							)}
						</TableRow>
					)
				)}
			</TableBody>
		</Table>
	)
}
