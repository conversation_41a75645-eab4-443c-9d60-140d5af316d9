import { IAllConstants } from '@/interfaces'
// import styled from '@emotion/styled'
import {
	TableContainer,
	TableHead,
	TableRow,
	TableCell,
	Typography,
	TableBody,
	Table,
    styled,
	alpha,
} from '@mui/material'

export const ConstantsTable = ({ constants = [] }: { constants?: IAllConstants[] }) => {
	const rows = [
		{
			label: 'Moisture included',
			csinkKey: 'csinkNetworkMoistureRequired',
			artisanKey: null,
			fallback: {
				csinkKey: 'yes',
				artisanKey: 'yes',
			},
		},
		{
			label: 'Max Moisture',
			csinkKey: 'csinkNetworkMaximumMoisture',
			artisanKey: 'artisanProMaximumMoisture',
			fallback: {
				csinkKey: 20,
				artisanKey: 20,
			},
		},
		{
			label: 'Temprerature Included',
			csinkKey: 'csinkNetworkTemperatureRequired',
			artisanKey: null,
			fallback: {
				csinkKey: 'yes',
				artisanKey: 'yes',
			},
		},
		{
			label: 'Min Temperature',
			csinkKey: 'csinkNetworkMinimumTemperature',
			artisanKey: 'artisanProMinimumTemperature',
			fallback: {
				csinkKey: 650,
				artisanKey: 650,
			},
		},
		{
			label: 'Min Temperature Image',
			csinkKey: 'csinkNetworkMinimumTemperatureImages',
			artisanKey: 'artisanProMinimumTemperatureImages',
			fallback: {
				csinkKey: 'No',
				artisanKey: 'No',
			},
		},
		{
			label: 'Min Firing Images',
			csinkKey: 'csinkNetworkMinimumFiringImages',
			artisanKey: 'artisanProMinimumFiringImages',
			fallback: {
				csinkKey: 3,
				artisanKey: 3,
			},
		},
		{
			label: 'Firing Videos included',
			csinkKey: 'csinkNetworkFiringVideosIncluded',
			artisanKey: 'artisanProFiringVideosIncluded',
			fallback: {
				csinkKey: 'yes',
				artisanKey: 'yes',
			},
		},
		{
			label: 'Min Firing Videos',
			csinkKey: 'csinkNetworkMinimumFiringVideos',
			artisanKey: 'artisanProMinimumFiringVideos',
			fallback: {
				csinkKey: 3,
				artisanKey: 3,
			},
		},
		{
			label: 'Min Carbon Credits',
			csinkKey: 'creditsToRegister',
			artisanKey: null,
		},
		{
			label: 'Min Images videos',
			csinkKey: 'csinkNetworkMinimumImagesVideos',
			artisanKey: 'artisanProMinimumImagesVideos',
		},
		{
			label: 'Chimney Image Separately',
			csinkKey: 'csinkNetworkSeparateChimneyImage',
			artisanKey: 'artisanProSeparateChimneyImage',
			fallback: {
				csinkKey: 'No',
				artisanKey: 'No',
			},
		},
		{
			label: 'Quenching Image Separately',
			csinkKey: 'csinkNetworkSeparateQuenchingImage',
			artisanKey: 'artisanProSeparateQuenchingImage',
			fallback: {
				csinkKey: 'No',
				artisanKey: 'No',
			},
		},
		{
			label: 'Network Videos Required',
			csinkKey: 'csinkNetworkFiringVideosRequired',
			artisanKey: null,
		},
	]

	const getValues = (
		key: string | null,
		type: 'csinkKey' | 'artisanKey',
		fallback?: Record<string, any>
	) => {
		if (!key) return '-'

		const value = constants?.[0]?.[key as keyof IAllConstants]

		if (value === false || value == null) {
			return fallback?.[type] ?? 'false'
		}
		return value.toString()
	}

	return (
		<TableContainer>
			<StyledTable>
				<TableHead>
					<TableRow>
						<TableCell>
							<Typography variant='subtitle1' fontWeight='bold'>
								Parameter
							</Typography>
						</TableCell>
						<TableCell>
							<Typography variant='subtitle1' fontWeight='bold'>
								Csink Network
							</Typography>
						</TableCell>
						<TableCell>
							<Typography variant='subtitle1' fontWeight='bold'>
								Artisan Pro Network
							</Typography>
						</TableCell>
					</TableRow>
				</TableHead>
				<TableBody>
					{rows.map((row, index) => (
						<TableRow
							key={row.label}
							style={{
								backgroundColor: index % 2 === 0 ? '#f9f9f9' : 'white',
							}}>
							<TableCell>
								<Typography variant='subtitle1'>{row.label}</Typography>
							</TableCell>
							<TableCell>
								<Typography variant='subtitle1'>
									{getValues(row.csinkKey, 'csinkKey', row.fallback)}
								</Typography>
							</TableCell>
							<TableCell>
								<Typography variant='subtitle1'>
									{getValues(row.artisanKey, 'artisanKey', row.fallback)}
								</Typography>
							</TableCell>
						</TableRow>
					))}
				</TableBody>
			</StyledTable>
		</TableContainer>
	)
}

const StyledTable = styled(Table)(({ theme }) => ({
	borderRadius: 0,
	'.MuiTableCell-root': {
		...theme.typography.subtitle1,
		color: theme.palette.neutral['900'],
		paddingBottom: theme.spacing(1),
		borderBottom: `1px solid ${alpha(theme.palette.primary.light, 0.09)}`,
		paddingTop: theme.spacing(2),
		overflowWrap: 'anywhere',
	},
	'.MuiTableHead-root': {
		background: alpha(theme.palette.primary.light, 0.05),
	},
	'.MuiTableCell-head': {
		color: theme.palette.primary.dark,
		...theme.typography.subtitle1,
		fontWeight: theme.typography.fontWeightBold,
		textOverflow: 'clip',
		whiteSpace: 'break-spaces',
		lineHeight: 1,
	},
	'.MuiTableRow-root': {
		borderBottom: `1px solid ${theme.palette.divider}`,
		borderColor: theme.palette.divider,
		backgroundColor: 'transparent',
	},
	'.MuiTableRow-root:nth-of-type(odd)': {
		backgroundColor: 'transparent !important',
	},
	'.MuiTableRow-root:nth-of-type(even)': {
		backgroundColor: 'transparent !important',
	},
	'.MuiTableCell-root, .MuiTablePagination-selectLabel, .MuiTablePagination-displayedRows':
		{
			fontWeight: theme.typography.body1.fontWeight,
		},
	'.MuiTableCell-root:focus, .MuiTableCell-root:focus-within': {
		outline: 0,
	},
	'.MuiTableRow-hover:hover': {
		color: theme.palette.primary.main,
		cursor: 'pointer',
	},
	'.MuiTable-root': {
		flexDirection: 'column-reverse',
		minHeight: 400,
	},
}))
