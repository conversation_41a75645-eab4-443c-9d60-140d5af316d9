import { authAxios } from "@/contexts"
import { defaultLimit, defaultPage } from "@/utils/constant"
import { useMutation, useQuery, useQueryClient } from "@tanstack/react-query"
import { useState } from "react"
import { useSearchParams } from "react-router-dom"
import { toast } from "react-toastify"

export const useEmailBypass=()=>{
    const [searchParams]=useSearchParams()
    
	const search=searchParams.get('search') ??''

    const [deleteEmail,setdeleteEmail]=useState<string| null>(null)

	const queryClient = useQueryClient()
	const paramsLimit = searchParams.get('limit') || defaultLimit
	const paramsPage = searchParams.get('page') || defaultPage
    const bypassEmailQuery=useQuery({
        queryKey:['getEmailBypassList',paramsLimit,paramsPage,search],
        queryFn:async()=>{
            const { data } = await authAxios(`/by-pass/emails?limit=${paramsLimit}&page=${paramsPage}&search=${search}`)
                return data
        
        },
        enabled:true,
        
    })
    
    const deleteBypassEmail = useMutation({
        mutationKey: ['deleteBypassEmail'],
		mutationFn: (email: string) =>
			authAxios.delete(`/by-pass/email`,{data:{email}}),
		onSuccess: (data) => {
            
            toast(data?.data?.message)
			queryClient.refetchQueries({
                queryKey: ['getEmailBypassList'],
                // exact: true,
                // refetchType:'active'
			})
            setdeleteEmail(null)
            
		},
		onError: (error: Error) => {
            toast(error?.message)
		},
	})
    
    return {
        byPassList:bypassEmailQuery.data?.emails ?? [],
        byPassListCount:bypassEmailQuery.data?.count ?? 0,
        loading: bypassEmailQuery.isLoading,
        deleteBypassEmail,
        deleteEmail,setdeleteEmail
    }

}