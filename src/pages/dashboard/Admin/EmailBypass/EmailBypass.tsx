import { <PERSON>, Button, Stack, styled, Typography, useTheme } from '@mui/material'
import { GridColDef, GridDeleteIcon, GridValidRowModel } from '@mui/x-data-grid'
import { ActionInformationDrawer, CustomHeader } from '../../../../components'
import { CustomTable } from '@/components/CustomTable'
import { useMemo, useState } from 'react'
import { AddCircle } from '@mui/icons-material'
import { Confirmation } from '@/components/Confirmation'
import { useEmailBypass } from './useEmailBypass'
import { getFormattedDate } from '@/utils/helper/getFormattedDate'
import { dateFormats } from '@/utils/constant'
import { AddEmailBypass } from '@/components/AddEmailBypass/AddEmailBypass'

export const EmailBypass = () => {
	const theme = useTheme()

	const {
		byPassList,
		deleteBypassEmail,
		deleteEmail,
		setdeleteEmail,
		byPassListCount,
		loading,
	} = useEmailBypass()
	const [openEmailBypassDialog, setOpenEmailBypassDialog] = useState(false)

	const columns: GridColDef<GridValidRowModel>[] = useMemo(
		() => [
			{
				field: 'id',
				headerName: 'S. No',
				flex: 0.5,
				minWidth: 80,
			},
			{
				field: 'email',
				headerName: 'Email',
				flex: 1,
				minWidth: 150,
			},
			{
				field: 'accountType',
				headerName: 'Role',
				flex: 1,
				minWidth: 150,
				renderCell: (params) =>
					params?.value ? (
						<Typography textTransform='capitalize'>
							{' '}
							{params?.value?.replace(/[-_]/g, ' ')}{' '}
						</Typography>
					) : (
						<Typography>-</Typography>
					),
			},
			{
				field: 'createdAt',
				headerName: 'Created At',
				flex: 1,
				minWidth: 250,
				renderCell: (params) => (
					<Typography>
						{params?.value
							? `${getFormattedDate(params?.value, dateFormats.dd_MMM_yyyy)}`
							: '-'}
					</Typography>
				),
			},
			{
				field: 'action',
				headerName: 'Action',
				flex: 1,
				minWidth: 150,
				renderCell: (params) => (
					<Stack flexDirection='row' columnGap={2}>
						<Button
							onClick={() => setdeleteEmail(params?.row?.email)}
							startIcon={<GridDeleteIcon />}>
							Delete
						</Button>
					</Stack>
				),
			},
		],
		[setdeleteEmail]
	)

	// function HeaderEndButtons() {
	// 	return (
	// 		<Stack direction='row' spacing={theme.spacing(2)}>
	// 			<IconButton
	// 				sx={{
	// 					border: `${theme.spacing(0.125)} solid ${
	// 						theme.palette.neutral['100']
	// 					}`,
	// 					borderRadius: theme.spacing(1.2),
	// 				}}>
	// 				<Box component='img' src={DownloadIcon} height={20} width={20} />
	// 			</IconButton>
	// 		</Stack>
	// 	)
	// }

	const GridHeaderComponents = () => {
		return (
			<Stack className='grid-header-component' columnGap={theme.spacing(2)}>
				<Button
					startIcon={<AddCircle />}
					onClick={() => setOpenEmailBypassDialog(true)}
					variant='contained'>
					Add Bypass Email
				</Button>
			</Stack>
		)
	}

	return (
		<StyledContainer>
			<Box className='header'>
				<CustomHeader
					showBottomBorder={true}
					heading='Email Bypass'
					showButton={false}
					// endComponent={<HeaderEndButtons />}
				/>
			</Box>

			<ActionInformationDrawer
				open={openEmailBypassDialog}
				onClose={() => setOpenEmailBypassDialog(false)}
				anchor='right'
				component={
					<AddEmailBypass close={() => setOpenEmailBypassDialog(false)} />
				}
			/>
			<Stack className='container'>
				<CustomTable
					columns={columns}
					count={byPassListCount}
					rows={byPassList ?? []}
					showPagination
					showPaginationDetails
					isLoading={loading}
					headerComponent={<GridHeaderComponents />}
				/>
			</Stack>
			{deleteEmail ? (
				<Confirmation
					open={!!deleteEmail}
					confirmationText='Are you sure you want to remove this Email-Bypass '
					handleClose={() => setdeleteEmail(null)}
					handleNoClick={() => setdeleteEmail(null)}
					handleYesClick={() => deleteBypassEmail.mutate(deleteEmail)}
				/>
			) : null}
		</StyledContainer>
	)
}

const StyledContainer = styled(Stack)(({ theme }) => ({
	gap: theme.spacing(2),
	'.header': {
		padding: theme.spacing(4, 0),
		borderBottom: `${theme.spacing(0.125)} solid ${
			theme.palette.neutral['100']
		}`,
	},
	'.container': {
		padding: theme.spacing(4, 3),
		gap: theme.spacing(2),
		'.grid-header-component': {
			flexDirection: 'row',
			alignItems: 'center',
		},
	},
}))
