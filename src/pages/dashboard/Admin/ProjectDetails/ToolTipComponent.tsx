import { Box, Stack, Tooltip, Typography } from '@mui/material'
import { FC, PropsWithChildren, useMemo } from 'react'
import infoIcon from '/images/infoIcon.svg'

enum tooltipType {
    registryId = 'registryId',
    registrationDate = 'registrationDate',
    creditingPeriodTerm = 'creditingPeriodTerm',
    endorsingAgent = 'endorsingAgent',
    standard = 'standard',
    currentVerifierOfProjectsOutcomes = 'currentVerifierOfProjectsOutcomes',
    dmvrApp = 'dmvrApp',
    application = 'application',
    methane = 'methane',
    customText = 'customText',
}

export const ToolTipComponent: FC<PropsWithChildren<{ type: keyof typeof tooltipType; text?: string }>> = ({
    children,
    type,
    text,
}) => {
    const tooltipTextAccordToType: { [key: string]: { title: string; subTextList?: string[] } } = useMemo(
        () => ({
            [tooltipType.registryId]: {
                title: 'The Global  C-Sink Registry contains the physical location of each C-Sink, the year of the carbon removal, the date of application, applied amount of carbon, the carbon persistence, and the C-Sink owner',
            },
            [tooltipType.registrationDate]: {
                title: 'Project registration date reflect the most recent date between project registration to CSI and latest re-certification date. Projects must bre re-certified at least once a year to ensure they are integrating updated changes to methodologies.',
            },
            [tooltipType.creditingPeriodTerm]: {
                title: 'Period during which the project is authorised to generate carbon removal credits. After this period the project must be reassessed by an independent body to continue generating credits.',
            },
            [tooltipType.endorsingAgent]: {
                title: 'Carbon Standards International is in the role of Endorsing agent. As such they:',
                subTextList: [
                    'conducts trainings for the Artisan C-Sink Manager',
                    'endorses the Artisan C-Sink Manager',
                    'endorses tools and methods used by the Artisan C-Sink Manager',
                    'verifies the reporting by the Artisan C-Sink Manager and the Certifier',
                    'conducts trainings for the Certifier',
                    'endorses the Certifier',
                    'endorses the laboratories',
                    'provides registry of the produced biochar and C-sinks',
                ],
            },
            [tooltipType.standard]: {
                title: 'Carbon Standards International has developed detailed framework and criteria to define the structure and verification processes of projects. Following this framework ensures these projects are designed and assessed in a way that maximises their intended impact',
            },
            [tooltipType.currentVerifierOfProjectsOutcomes]: {
                title: 'A Carbon Standards International endorsed third party inspection and certification body. The Certifier:',
                subTextList: [
                    'verifies on a regular basis the correctness and effectiveness of the Artisan C-Sink Manager’s training and monitoring duties',
                    'certifies the Artisan C-Sink Manager',
                    'executes onsite and remote inspections',
                    'certifies stakeholders of the C-Sink ecosystem',
                ],
            },
            [tooltipType.application]: {
                title: 'Application refers to the usage of the biochar. Biochar application improves soil health, boosts agricultural productivity, and enhances carbon sequestration. Circonomy support application of biochar in agriculture or afforestation, reforestation projects',
            },
            [tooltipType.methane]: {
                title: 'In both industrial and artisanal biochar production, small but relevant methane emissions occur. To compensate for this emissions CSI has defined strategies:',
                subTextList: [
                    'Compensation of methane emissions by growing additional biomass',
                    'Avoiding burning crop residues',
                    'Avoiding biomass decomposition',
                ],
            },
            [tooltipType.customText]: {
                title: text ?? '',
            },
        }),
        [text]
    )
    return (
        <Stack direction="row" columnGap={1} alignItems="start">
            {children}
            <Tooltip
                arrow
                placement="right"
                title={
                    <Stack>
                        <Typography fontSize={14}>{tooltipTextAccordToType[String(type)]?.title}</Typography>
                        {(tooltipTextAccordToType[String(type)]?.subTextList ?? [])?.map((i, index) => (
                            <Typography key={index} fontSize={14}>
                                ({index + 1}) {i}
                            </Typography>
                        ))}
                    </Stack>
                }
            >
                <Box component="img" src={infoIcon} width={16} height={16} />
            </Tooltip>
        </Stack>
    )
}
