import { <PERSON>, Container, <PERSON><PERSON><PERSON><PERSON><PERSON>, <PERSON>, <PERSON>ack, Typography } from '@mui/material'
import { FC } from 'react'
import Markdown from 'react-markdown'
import remarkBreaks from 'remark-breaks'
import remarkGfm from 'remark-gfm'
import { ToolTipComponent } from './ToolTipComponent'
import carbonIcon from '/images/carbonIcon.svg'
import certificationBold from '/images/certificationBold.svg'
import linkIcon from '/images/linkIcon.svg'
import tdesign_certificate from '/images/tdesign_certificate.svg'
import { IProjectDetails } from '@/interfaces/projects.type'
import { theme } from '@/lib/theme/theme'

export const ProjectCertificates: FC<{ data?: IProjectDetails }> = ({ data }) => {
    return (
        <Container>
            <Stack pt={8} flexDirection="column" gap={1}>
                <Stack
                    gap={2.5}
                    sx={{
                        '.markdown p': {
                            marginTop: 0,
                            fontWeight: 300,
                            color: theme.palette.neutral[700],
                        },
                    }}
                >
                    <Typography variant="h3" fontFamily="Overpass" color={theme.palette.neutral[700]} fontWeight={600}>
                        Project Certification
                    </Typography>
                    {data?.certificationDetails ? (
                        <Markdown className="markdown" remarkPlugins={[remarkGfm, remarkBreaks]}>
                            {data?.certificationDetails?.replace(/\n/gi, '&nbsp; \n')}
                        </Markdown>
                    ) : null}
                </Stack>
                <Stack gap={2.5}>
                    {data?.endorsingAgentName ? (
                        <Stack direction="row" gap={2} alignItems="flex-start">
                            <Box component="img" alt="" height={25} width={25} src={carbonIcon} />

                            <Stack direction="column">
                                <ToolTipComponent type="endorsingAgent">
                                    <Typography
                                        sx={{ color: theme.palette.neutral[700], fontWeight: 600, fontSize: 20 }}
                                        color="common.black"
                                    >
                                        Endorsing Agent
                                    </Typography>
                                </ToolTipComponent>

                                <Stack>
                                    <Typography variant="subtitle1" color={theme.palette.text.secondary}>
                                        {data?.endorsingAgentName}
                                    </Typography>
                                    <Stack alignItems="center" flexDirection="row" gap={0.5}>
                                        <Link target="_blank" href={data?.endorsingAgentUrl ?? ''}>
                                            <IconButton sx={{ padding: 0 }}>
                                                <Box component="img" height={19} alt="" width={19} src={linkIcon} />
                                            </IconButton>
                                        </Link>
                                        {data?.endorsingAgentDescription ? (
                                            <>
                                                <Typography variant="subtitle1" color={theme.palette.text.secondary} fontWeight={700}>
                                                    Read more:
                                                </Typography>
                                                <Typography
                                                    variant="subtitle1"
                                                    fontWeight={700}
                                                    // maxWidth={15}
                                                    target="_blank"
                                                    // textOverflow='ellipsis'
                                                    component={Link}
                                                    href={data?.endorsingAgentUrl}
                                                    sx={{ textDecoration: 'underline' }}
                                                    color={theme.palette.text.secondary}
                                                >
                                                    {data?.endorsingAgentDescription}
                                                </Typography>
                                            </>
                                        ) : null}
                                    </Stack>
                                </Stack>
                            </Stack>
                        </Stack>
                    ) : null}
                    {data?.standardName ? (
                        <Stack direction="row" gap={2} alignItems="flex-start">
                            <Box component="img" alt="" height={25} width={25} src={tdesign_certificate} />
                            <Stack direction="column">
                                <ToolTipComponent type="standard">
                                    <Typography
                                        sx={{ color: theme.palette.neutral[700], fontWeight: 600, fontSize: 20 }}
                                        color="common.black"
                                    >
                                        Standard
                                    </Typography>
                                </ToolTipComponent>
                                <Stack>
                                    <Typography variant="subtitle1" color={theme.palette.text.secondary}>
                                        {data?.standardName}
                                    </Typography>
                                    <Stack alignItems="center" flexDirection="row" gap={0.5}>
                                        <Link target="_blank" href={data?.standardUrl ?? ''}>
                                            <IconButton sx={{ padding: 0 }}>
                                                <Box component="img" height={19} width={19} src={linkIcon} />
                                            </IconButton>
                                        </Link>
                                        {data?.standardDescription ? (
                                            <>
                                                <Typography variant="subtitle1" color={theme.palette.text.secondary} fontWeight={700}>
                                                    Read more:
                                                </Typography>
                                                <Typography
                                                    variant="subtitle1"
                                                    fontWeight={700}
                                                    target="_blank"
                                                    component={Link}
                                                    href={data?.standardUrl}
                                                    sx={{ textDecoration: 'underline' }}
                                                    color={theme.palette.text.secondary}
                                                >
                                                    {data?.standardDescription}
                                                </Typography>
                                            </>
                                        ) : null}
                                    </Stack>
                                </Stack>
                            </Stack>
                        </Stack>
                    ) : null}

                    <Stack direction="row" gap={2}>
                        <Box component="img" alt="" height={25} width={25} src={certificationBold} />
                        <Stack direction="column">
                            <ToolTipComponent type="currentVerifierOfProjectsOutcomes">
                                <Typography
                                    sx={{ color: theme.palette.neutral[700], fontWeight: 600, fontSize: 20 }}
                                    color="common.black"
                                >
                                    Current verifier of project outcomes
                                </Typography>
                            </ToolTipComponent>
                            <Typography variant="subtitle1" color={theme.palette.text.secondary}>
                                {data?.currentVerifier}
                            </Typography>
                        </Stack>
                    </Stack>
                </Stack>
            </Stack>
        </Container>
    )
}
