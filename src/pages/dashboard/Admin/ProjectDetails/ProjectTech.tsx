import { IProjectDetails, IProjectDocuments } from '@/interfaces/projects.type'
import { theme } from '@/lib/theme/theme'
import { proxyImage } from '@/utils/helper'
import { Box, Container, Stack, Typography } from '@mui/material'
import { FC, useMemo } from 'react'
import Markdown from 'react-markdown'
import remarkBreaks from 'remark-breaks'
import remarkGfm from 'remark-gfm'

export const ProjectTech: FC<{ data?: IProjectDetails; projectDocuments: IProjectDocuments }> = ({ data }) => {
    const list = useMemo(
        () => [
            {
                title: 'Biochar',
                url: data?.biocharImageProxyUrl ?? '',
                text: data?.biocharDescription ?? '',
                projectDuration: false,
            },
            {
                title: 'Durability',
                url: data?.durabilityImageProxyUrl ?? '',
                projectDuration: true,
                text: data?.durabilityDescription ?? '',
            },
        ],
        [data]
    )
    return (
        <Container id="technology">
            <Stack pt={8} flexDirection="column" gap={3.5}>
                <Typography variant="h3" fontFamily="Overpass" color={theme.palette.neutral[700]} fontWeight={600}>
                    Project Technology
                </Typography>
                {list?.map((item) => (
                    <Stack direction={{ xs: 'column', md: 'row' }} gap={3.5} key={item.title}>
                        <Box
                            component="img"
                            alt="about image"
                            loading="lazy"
                            style={{
                                borderRadius: '12px',
                                width: 200,
                                objectFit: 'cover',
                                objectPosition: 'center',
                                maxHeight: 160,
                            }}
                            src={proxyImage(item?.url, '800:0')}
                        />
                        <Stack
                            flexDirection="column"
                            width={{ md: '50%', lg: '60%' }}
                            sx={{
                                '.markdown p': {
                                    margin: '8px 0',
                                    color: theme.palette.text.secondary,
                                },
                            }}
                        >
                            <Typography variant="h5" sx={{ color: theme.palette.neutral[700], fontWeight: 600 }}>
                                {item?.title}
                            </Typography>
                            {item?.projectDuration && data?.permanence ? (
                                <Typography color={theme.palette.text.secondary} fontWeight={700} pt={1.2} variant="subtitle2">
                                    {data?.permanence}
                                </Typography>
                            ) : null}

                            <Markdown className="markdown" remarkPlugins={[remarkGfm, remarkBreaks]}>
                                {item?.text?.replace(/\n/gi, '&nbsp; \n')}
                            </Markdown>
                        </Stack>
                    </Stack>
                ))}
            </Stack>
        </Container>
    )
}
