import { Box, Container, Stack, Typography } from '@mui/material'
import { FC } from 'react'
import Markdown from 'react-markdown'
import remarkBreaks from 'remark-breaks'
import remarkGfm from 'remark-gfm'
import { CustomProjectDocumentButton } from './CustomProjectDocumentButton'
import dmvrSectionIcon from '/images/carbonIcon.svg'
import { IProjectDetails, IProjectDocuments } from '@/interfaces/projects.type'
import { theme } from '@/lib/theme/theme'

export const ProjectMonitoring: FC<{ data?: IProjectDetails; projectDocuments: IProjectDocuments }> = ({ data, projectDocuments }) => {
    return (
        <Container id="monitoring">
            <Stack
                pt={8}
                flexDirection="column"
                gap={1}
                sx={{
                    '.markdown p': {
                        marginTop: 0,
                        color: theme.palette.text.secondary,
                        fontWeight: 300,
                    },
                }}
            >
                <Stack gap={2.5}>
                    <Typography variant="h3" fontFamily="Overpass" fontWeight={600} color={theme.palette.neutral[700]}>
                        Project Monitoring
                    </Typography>
                    {data?.monitoring ? (
                        <Markdown className="markdown" remarkPlugins={[remarkGfm, remarkBreaks]}>
                            {data?.monitoring?.replace(/\n/gi, '&nbsp; \n')}
                        </Markdown>
                    ) : null}
                </Stack>

                <Stack direction="row" columnGap={3} alignItems="start">
                    <Box component="img" src={dmvrSectionIcon} />
                    <Stack direction="column" gap={3.5}>
                        <Stack rowGap={0.5} alignItems="start" justifyContent="center">
                            {/* <ToolTipComponent type='dmvrApp'> */}

                            <Typography
                                textAlign="start"
                                sx={{ color: theme.palette.neutral[700], fontWeight: 600, fontSize: 20 }}
                                color="common.black"
                            >
                                dMRV
                            </Typography>
                            {/* </ToolTipComponent> */}
                            <Typography color={theme.palette.text.secondary}>Circonomy App</Typography>
                        </Stack>
                        <CustomProjectDocumentButton
                            header="dMRV endorsement certificate"
                            documentUrl={projectDocuments?.dmrvEndorsement?.imageURL ?? ''}
                        />
                    </Stack>
                </Stack>
            </Stack>
        </Container>
    )
}
