import { theme } from '@/lib/theme/theme'
import { FileDownloadOutlined } from '@mui/icons-material'
import { Box, BoxProps, Stack, Typography } from '@mui/material'
interface Iprops extends BoxProps {
    header: string
    isDocumentHeader?: boolean
    documentUrl: string
}
export function CustomProjectDocumentButton({ header, documentUrl, isDocumentHeader = false, ...props }: Iprops) {
    if (!documentUrl) return null
    return (
        <Box
            component={'a'}
            download
            href={documentUrl}
            sx={{
                textDecoration: 'none',
                ...(isDocumentHeader ? { width: { md: '230px', xs: '100%' } } : { maxWidth: '250px' }),
            }}
        >
            <Box
                {...props}
                sx={{
                    ...(isDocumentHeader ? { width: { md: '230px', xs: '100%' } } : { width: '290px' }),
                    border: '2px solid',
                    borderColor: 'grey.400',
                    borderRadius: '4px',
                    height: '42px',

                    display: 'flex',
                    alignItems: 'center',
                    justifyContent: 'space-between',
                    p: '16px 12px',
                    cursor: 'pointer',
                    ':hover': {
                        border: '1px solid #504945',
                    },
                }}
            >
                <Typography color={isDocumentHeader ? 'grey.600' : 'common.black'}>{header}</Typography>
                <Stack direction="row" columnGap={1}>
                    <FileDownloadOutlined sx={{ color: isDocumentHeader ? 'grey.600' : theme.palette.text.secondary }} />
                </Stack>
            </Box>
        </Box>
    )
}
