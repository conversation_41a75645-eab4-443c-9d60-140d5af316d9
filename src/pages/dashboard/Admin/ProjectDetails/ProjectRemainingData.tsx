import { Box, Container, Stack, Typography } from '@mui/material'
import { FC } from 'react'
import { ToolTipComponent } from './ToolTipComponent'
import { IProjectDetails, IProjectDocuments } from '@/interfaces/projects.type';
import { theme } from '@/lib/theme/theme';

export const ProjectRemainingData: FC<{ data?: IProjectDetails; projectDocuments: IProjectDocuments }> = ({ data }) => {
    const getGoalsIcon = (name: string) => `/images/un_goals_icons/${name}`
    return (
        <Container>
            <Stack pt={8} flexDirection="column" gap={3.5}>
                <Typography variant="h3" fontFamily="Overpass" fontWeight={600} color={theme.palette.neutral[700]}>
                    United Nations sustainable development goals
                </Typography>
                {data?.unGoals?.map((item, index) => (
                    <Stack flexDirection="row" gap={3.5} key={index}>
                        <Box component="img" height={50} width={50} src={getGoalsIcon(item?.iconFilename)} />
                        <Stack flexDirection="column" gap={0.5}>
                            <ToolTipComponent type="customText" text={item?.topic ?? ''}>
                                <Typography variant="h5">{item?.name}</Typography>
                            </ToolTipComponent>
                            <Typography color="grey.600" variant="subtitle2" lineHeight={1.3}>
                                {item?.description}
                            </Typography>
                        </Stack>
                    </Stack>
                ))}
            </Stack>
        </Container>
    )
}
