import { IProjectDetails, IProjectDocuments } from '@/interfaces/projects.type'
import { Container, Stack } from '@mui/material'
import { FC } from 'react'
import { NewHeader } from './NewHeader'
import { ProjectCertificates } from './ProjectCertificates'
import { ProjectMonitoring } from './ProjectMonitoring'
import { ProjectTech } from './ProjectTech'
import { ProjectDetails } from './ProjectDetails'
import { ProjectRemainingData } from './ProjectRemainingData'
import { AboutProject } from './AboutProject'
import { StickyBuyBox } from './StickyBuyBox'
interface IProp {
    projectInfo?: IProjectDetails
    projectDocuments: IProjectDocuments
    headerHeight: number
}
export const ProjectComponentsWrapper: FC<IProp> = ({ projectInfo, projectDocuments, headerHeight }) => {
    return (
        <Container
            sx={{
                padding: 0,
                '&.MuiContainer-root': {
                    paddingLeft: '0',
                    paddingRight: '0',
                },
            }}
        >
            <Stack direction={{ xs: 'column', md: 'row' }} columnGap={5} flexWrap="nowrap">
                <Stack width={{ md: '60%', xs: '100%' }} p={0}  position="relative">
                    <NewHeader data={projectInfo} projectDocuments={projectDocuments} />
                    <AboutProject data={projectInfo} projectDocuments={projectDocuments} />
                    <ProjectCertificates data={projectInfo} />
                    <ProjectMonitoring data={projectInfo} projectDocuments={projectDocuments} />
                    <ProjectTech data={projectInfo} projectDocuments={projectDocuments} />
                    <ProjectDetails data={projectInfo} projectDocuments={projectDocuments} />
                    <ProjectRemainingData data={projectInfo} projectDocuments={projectDocuments} />
                </Stack>
                <Stack>
                    <StickyBuyBox data={projectInfo} projectDocuments={projectDocuments} headerHeight={headerHeight} />
                </Stack>
            </Stack>
        </Container>
    )
}
