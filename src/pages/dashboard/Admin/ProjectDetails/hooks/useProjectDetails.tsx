import { authAxios } from '@/contexts'
import { IProjectDetails } from '@/interfaces/projects.type'
import { useMutation, useQuery } from '@tanstack/react-query'
import { useCallback } from 'react'
import { useParams } from 'react-router-dom'
import { toast } from 'react-toastify'

export const useProjectDetails = () => {
	const { id } = useParams()
	const projectDetailsQuery = useQuery({
		queryKey: ['projectDetails', id],
		queryFn: async () => {
			const { data: project } = await authAxios<IProjectDetails>(
				`/projects/${id}/v2`
			)
			return project
		},
		enabled: !!id,
	})

	const publishProject = useMutation({
		mutationKey: ['publishProject'],
		mutationFn: async (id: string) =>
			await authAxios.put(`/projects/${id}/publish`),
		onSuccess: () => {
			projectDetailsQuery.refetch()
			toast('Project published successfully')
		},
		onError: (err: any) => {
			toast(err?.response?.data?.messageToUser)
		},
	})

	const unPublishProject = useMutation({
		mutationKey: ['unPublishProject'],
		mutationFn: async (id: string) =>
			await authAxios.put(`/projects/${id}/unpublish`),
		onSuccess: () => {
			projectDetailsQuery.refetch()
			toast('Project unPublished successfully')
		},
		onError: (err: any) => {
			toast(err?.response?.data?.messageToUser)
		},
	})

	const handlePublishUnPublishProject = useCallback(
		(id: string, status: string) =>
			status === 'draft'
				? publishProject.mutate(id)
				: unPublishProject.mutate(id),
		[publishProject, unPublishProject]
	)

	return {
		projectInfo: projectDetailsQuery.data,
		loading: projectDetailsQuery.isLoading,
		handlePublishUnPublishProject,
	}
}
