import { Box, Stack, Typography } from '@mui/material'
import { FC } from 'react'
import Markdown from 'react-markdown'
import remarkBreaks from 'remark-breaks'
import remarkGfm from 'remark-gfm'

import { ToolTipComponent } from './ToolTipComponent'
import applicationBold from '/images/applicationBold.svg'
import barChart from '/images/barChart.svg'
import methaneIconBold from '/images/methaneBold.svg'
import { IProjectDetails, IProjectDocuments } from '@/interfaces/projects.type'
import { theme } from '@/lib/theme/theme'
interface IProp {
	data?: IProjectDetails
	projectDocuments: IProjectDocuments
}

export const ProjectDetails: FC<IProp> = ({ data }) => {
	return (
		<Stack
			pt={8}
			px={3}
			flexDirection='column'
			gap={3.5}
			id='details'
			sx={{
				'.markdown p ': {
					color: theme.palette.neutral[700],
					margin: '8px 0',
				},
			}}>
			<Typography
				variant='h3'
				fontFamily='Overpass'
				fontWeight={600}
				color={theme.palette.neutral[700]}>
				Project Details
			</Typography>
			<Stack direction='row' gap={3.2} alignItems='flex-start'>
				<Box component='img' alt='' height={25} width={25} src={barChart} />
				<Stack direction='column'>
					<Typography
						variant='h5'
						sx={{ color: theme.palette.neutral[700], fontWeight: 600 }}>
						Annual Carbon Removal Capacity
					</Typography>

					<Markdown
						className='markdown'
						remarkPlugins={[remarkGfm, remarkBreaks]}>
						{data?.annualCarbonRemovalCapacity?.replace(/\n/gi, '&nbsp; \n')}
					</Markdown>
				</Stack>
			</Stack>

			<Stack direction='row' gap={3.2} alignItems='flex-start'>
				<Box
					component='img'
					alt=''
					height={25}
					width={25}
					src={applicationBold}
				/>
				<Stack direction='column'>
					<ToolTipComponent type='application'>
						<Typography
							variant='h5'
							sx={{ color: theme.palette.neutral[700], fontWeight: 600 }}>
							Application
						</Typography>
					</ToolTipComponent>

					<Markdown
						className='markdown'
						remarkPlugins={[remarkGfm, remarkBreaks]}>
						{(data?.applicationDetails ?? '')?.replace(/\n/gi, '&nbsp; \n')}
					</Markdown>
				</Stack>
			</Stack>

			<Stack direction='row' gap={3.2} alignItems='flex-start'>
				<Box
					component='img'
					alt=''
					height={25}
					width={25}
					src={methaneIconBold}
				/>
				<Stack direction='column'>
					<ToolTipComponent type='methane'>
						<Typography
							variant='h5'
							sx={{ color: theme.palette.neutral[700], fontWeight: 600 }}>
							Methane mitigation strategy
						</Typography>
					</ToolTipComponent>

					<Markdown
						className='markdown'
						remarkPlugins={[remarkGfm, remarkBreaks]}>
						{(data?.methaneMitigationStrategyDetails ?? '')?.replace(
							/\n/gi,
							'&nbsp; \n'
						)}
					</Markdown>
				</Stack>
			</Stack>
		</Stack>
	)
}
