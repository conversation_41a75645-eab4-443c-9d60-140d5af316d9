import { CircularProgress, Typography } from '@mui/material'
import { GoogleMap, InfoWindow, MarkerF, useLoadScript } from '@react-google-maps/api'
import React from 'react'
import { IProjectDetails } from '@/interfaces/projects.type';

export function ProjectMap({ data, coordinates }: { data?: IProjectDetails; coordinates: { lat: number; lng: number } }) {
    const initialPosition = {
        lat: coordinates.lat,
        lng: coordinates.lng,
    }

    const [mapPosition] = React.useState(initialPosition)

    const { VITE_GOOGLE_MAPS_API_KEY } = import.meta.env

	const { isLoaded } = useLoadScript({
		id: 'google-map-script',
		googleMapsApiKey: VITE_GOOGLE_MAPS_API_KEY,
	})
    if (!isLoaded) return <CircularProgress />

    return (
        <GoogleMap
            mapContainerClassName="google_map_style"
            extraMapTypes={[]}
            options={{
                streetViewControl: false,
                draggable: true,
                zoomControlOptions: { position: 9 },
                keyboardShortcuts: false,
                scaleControl: false,
                scrollwheel: true,
                disableDefaultUI: true,
                mapTypeId: 'terrain',
                zoom: 6,
                disableDoubleClickZoom: true,
            }}
            // zoom={6}
            center={mapPosition}
        >
            <MarkerF position={mapPosition}>
                <InfoWindow
                    options={{
                        pixelOffset: new window.google.maps.Size(0, -40),
                    }}
                    position={mapPosition}
                >
                    <Typography>{data?.address}</Typography>
                </InfoWindow>
            </MarkerF>
        </GoogleMap>
    )
}
