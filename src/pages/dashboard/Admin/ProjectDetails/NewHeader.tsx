
import { LocationOn } from '@mui/icons-material'
import {
    <PERSON>,
    Chip,
    Container,
    ImageList,
    ImageListItem,
    Link,
    Stack,
    Typography,
    styled,
} from '@mui/material'
import { FC } from 'react'
import Markdown from 'react-markdown'
import remarkBreaks from 'remark-breaks'
import remarkGfm from 'remark-gfm'
import { CustomProjectDocumentButton } from './CustomProjectDocumentButton'

import { proxyImage } from '@/utils/helper'
import { IProjectDetails, IProjectDocuments } from '@/interfaces/projects.type'
import registryIcon from '/images/registryIcon.svg'
import { theme } from '@/lib/theme/theme'
interface IProp {
    data?: IProjectDetails
    projectDocuments: IProjectDocuments
}
export const NewHeader: FC<IProp> = ({ data, projectDocuments }) => {


    return (
        <Stack>

            <StyledContainer>
         
                <Markdown className="markdown" remarkPlugins={[remarkGfm, remarkBreaks]}>
                    {(data?.shortDescription ?? '')?.replace(/\n/gi, '&nbsp; \n')}
                </Markdown>
                <Stack direction="row" gap={2.5}>
                    <Stack direction="row" alignItems="center" columnGap={1}>
                        <LocationOn sx={{ fontSize: '16px',color:theme.palette.text.secondary}} />
                        <Link
                            rel="noopener"
                            target="blank"
                            href={data?.projectMapLocationURL}
                            color={theme.palette.text.secondary}
                            underline="hover"
                        >
                            <Typography>{data?.address}</Typography>
                        </Link>
                    </Stack>
                    {data?.linkToRegistry ? (
                        <Chip
                            component={Link}
                            target="_blank"
                            href={data?.linkToRegistry ?? ''}
                            label="Link To registry"
                            icon={<Box component="img" src={registryIcon} />}
                        />
                    ) : null}
                </Stack>
                <Stack gap={3.4} direction={{ xs: 'column', md: 'row' }} mt={2}>
                    <CustomProjectDocumentButton
                        documentUrl={projectDocuments?.circonomyPDD?.imageURL ?? ''}
                        header="Circonomy PDD"
                        isDocumentHeader
                    />
                    <CustomProjectDocumentButton
                        documentUrl={projectDocuments?.ceresCertificate?.imageURL ?? ''}
                        header="CERES Certificate"
                        isDocumentHeader
                    />
                </Stack>

                <Stack mt={2}>
                    <ImageList
                        sx={{
                            borderRadius: '20px',
                            width: '100%',
                            overflowY: 'visible',
                        }}
                        variant="quilted"
                        cols={6}
                        rowHeight={250}
                        gap={26}
                    >
                        <>
                            <ImageListItem key={1} cols={3} rows={1}>
                                <Box
                                    component="img"
                                    src={proxyImage(data?.headerImageProxyUrls?.[0] ?? '', '800:0')}
                                    alt="project image"
                                    loading="lazy"
                                    style={{
                                        borderRadius: '12px',
                                        width: '100%',
                                        height: '100%',
                                        objectFit: 'cover',
                                        objectPosition: 'center',
                                    }}
                                />
                            </ImageListItem>
                            <ImageListItem key={2} cols={3} rows={1}>
                                <Box
                                    component="img"
                                    src={proxyImage(data?.headerImageProxyUrls?.[1] ?? '', '800:0')}
                                    alt="project image"
                                    loading="lazy"
                                    style={{
                                        borderRadius: '12px',
                                        width: '100%',
                                        height: '100%',
                                        objectFit: 'cover',
                                        objectPosition: 'center',
                                    }}
                                />
                            </ImageListItem>
                            <ImageListItem key={3} cols={6} rows={1}>
                                <Box
                                    component="img"
                                    src={proxyImage(data?.headerImageProxyUrls?.[2] ?? '', '1000:0')}
                                    alt="project image"
                                    loading="lazy"
                                    style={{
                                        borderRadius: '12px',
                                        width: '100%',
                                        height: '100%',
                                        objectFit: 'cover',
                                        objectPosition: 'center',
                                    }}
                                />
                            </ImageListItem>
                        </>
                    </ImageList>
                </Stack>
            </StyledContainer>
        </Stack>
    )
}
const StyledContainer = styled(Container)(({ theme }) => ({
    // marginTop: theme.spacing(5),
    '.markdown p': {
        margin: theme.spacing(0, 0, 2),
        color: theme.palette.neutral[700],
    },
}))
