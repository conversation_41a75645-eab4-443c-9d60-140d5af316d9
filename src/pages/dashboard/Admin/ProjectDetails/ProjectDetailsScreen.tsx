import { useMemo, useState } from 'react'

import { ProjectFixedHeaderWrapper } from './ProjectFixedHeaderWrapper'
import { CircularProgress, Stack } from '@mui/material'
import { IProjectDocuments } from '@/interfaces/projects.type'
import { LocationAndRepresentative } from './LocationAndRepresentative'
import { ProjectComponentsWrapper } from './ProjectComponentsWrapper'
import { useProjectDetails } from './hooks'

export default function ProjectDetailsScreen() {
	const { loading, projectInfo } = useProjectDetails()
	const [headerHeight, setHeaderHeight] = useState(0)

	const projectDocuments = useMemo<IProjectDocuments>(() => {
		const document: IProjectDocuments = {
			circonomyPDD: null,
			ceresCertificate: null,
			officialPDD: null,
			dmrvEndorsement: null,
		}
		projectInfo?.documents?.forEach((item) => {
			if (item?.name === 'Official PDD') {
				document.officialPDD = item
			}
			if (item?.name === 'CERES Certificate') {
				document.ceresCertificate = item
			}
			if (item?.name === 'dMRV Endorsement letter') {
				document.dmrvEndorsement = item
			}
			if (item?.name === 'Circonomy PDD') {
				document.circonomyPDD = item
			}
		})
		return document
	}, [projectInfo])

	if (loading)
		return (
			<Stack alignItems='center' justifyContent='center' height='100%'>
				<CircularProgress sx={{ height: '70vh' }} />
			</Stack>
		)

	return (
		<>
			<ProjectFixedHeaderWrapper
				data={projectInfo}
				setHeaderHeight={(height: number) => setHeaderHeight(height)}>
				<ProjectComponentsWrapper
					projectDocuments={projectDocuments}
					projectInfo={projectInfo}
					headerHeight={headerHeight}
				/>
				<LocationAndRepresentative data={projectInfo} />
			</ProjectFixedHeaderWrapper>
		</>
	)
}
