import { IProjectDetails } from '@/interfaces/projects.type'
import { ArrowBack } from '@mui/icons-material'
import { Box, Button, Container, Stack, Typography } from '@mui/material'
import { PropsWithChildren, useEffect, useRef } from 'react'
import { useNavigate } from 'react-router-dom'
import { useProjectDetails } from './hooks'

interface IProp extends PropsWithChildren {
	data?: IProjectDetails
	setHeaderHeight: (height: number) => void
}

export const ProjectFixedHeaderWrapper = ({
	children,
	data,
	setHeaderHeight,
}: IProp) => {
	const { projectInfo, handlePublishUnPublishProject } = useProjectDetails()
	const ref = useRef<HTMLHeadingElement>(null)
	const navigate = useNavigate()

	useEffect(() => {
		if (ref?.current) {
			setHeaderHeight(ref?.current?.offsetHeight)
		}
	}, [setHeaderHeight])
	return (
		<Box>
			<Container
				sx={{
					display: 'flex',
					justifyContent: 'space-between',
				}}>
				<Button
					startIcon={<ArrowBack />}
					onClick={() => navigate(-1)}
					sx={{ mt: 2 }}>
					Back
				</Button>
				<Button
					variant={'text'}
					onClick={() => {
						handlePublishUnPublishProject(
							projectInfo?.id ?? '',
							projectInfo?.state ?? ''
						)
					}}
					sx={{ mt: 2 }}>
					{projectInfo?.state === 'draft' ? 'Publish it?' : 'Unpublish it?'}
				</Button>
			</Container>
			<Stack
				sx={{
					position: 'sticky',
					top: 0,
					zIndex: 2,
					pb: 2,
					backgroundColor: 'common.white',
				}}>
				<Container ref={ref}>
					<Typography
						variant='h3'
						textTransform='capitalize'
						sx={{ mt: 5, mb: 3, fontWeight: 900, maxWidth: 600 }}>
						{data?.name}
					</Typography>
				</Container>
			</Stack>
			<Box>{children}</Box>
		</Box>
	)
}
