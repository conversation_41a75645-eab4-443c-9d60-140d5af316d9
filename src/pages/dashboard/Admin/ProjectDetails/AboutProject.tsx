import { Box, Container, <PERSON><PERSON><PERSON><PERSON><PERSON>, <PERSON>, Stack, Typography, styled } from '@mui/material'
import { FC } from 'react'
import Markdown from 'react-markdown'
import remarkBreaks from 'remark-breaks'
import remarkGfm from 'remark-gfm'
import { CustomProjectDocumentButton } from './CustomProjectDocumentButton'
import { ToolTipComponent } from './ToolTipComponent'
import barcodeBold from '/images/barcodeBold.svg'

import calendar3 from '/images/calendar3.svg'
import clockIcon from '/images/clock-fill.svg'
import linkIcon from '/images/linkIcon.svg'
import { IProjectDetails, IProjectDocuments } from '@/interfaces/projects.type'
import { proxyImage } from '@/utils/helper'
import { getFormattedDate } from '@/utils/helper/getFormattedDate'
import { dateFormats } from '@/utils/constant'
import { theme } from '@/lib/theme/theme'

export const AboutProject: FC<{ data?: IProjectDetails; projectDocuments: IProjectDocuments }> = ({ data, projectDocuments }) => {
    return (
        <StyledContainer id="about">
            <Stack gap={5}>
                <Stack gap={3.5}>
                    <Typography variant="h3" fontFamily="Overpass" fontWeight={600} color={theme.palette.neutral[700]}>
                        About the Project
                    </Typography>
                    <Markdown className="markdown" remarkPlugins={[remarkGfm, remarkBreaks]}>
                        {data?.description?.replace(/\n/gi, '&nbsp; \n')}
                    </Markdown>
                </Stack>
                <Stack flexDirection={{ xs: 'column', md: 'row' }} gap={6.5}>
                    <Stack>
                        <Box
                            component="img"
                            alt="about image"
                            loading="lazy"
                            sx={{
                                borderRadius: '12px',
                                // width: '100%',
                                height: '100%',
                                objectFit: 'cover',
                                objectPosition: 'center',
                                maxHeight: 300,
                                width: { xs: '100%', md: '200px' },
                            }}
                            src={proxyImage(data?.aboutProjectImageProxyUrl ?? '', '800:0')}
                        />
                    </Stack>
                    <Stack width={{ md: '50%', lg: '60%' }} gap={3.2}>
                        <Stack direction="row" gap={2} alignItems="flex-start">
                            <Box component="img" alt="" height={25} width={25} src={barcodeBold} />
                            <Stack direction="column">
                                <ToolTipComponent type="registryId">
                                    <Typography sx={{ fontSize: 20 }} color={theme.palette.neutral[700]} fontWeight={600}>
                                        Operator Number
                                    </Typography>
                                </ToolTipComponent>
                                <Stack>
                                    <Typography variant="subtitle1" color={theme.palette.text.secondary}>
                                        {data?.registryDescription}
                                    </Typography>
                                    <Stack alignItems="center" flexDirection="row" gap={0.5}>
                                        <Link target="_blank" href={data?.registryUrl ?? ''}>
                                            <IconButton sx={{ padding: 0 }}>
                                                <Box component="img" height={19} width={19} src={linkIcon} />
                                            </IconButton>
                                        </Link>
                                        <Typography
                                            variant="subtitle1"
                                            target="_blank"
                                            href={data?.registryUrl ?? ''}
                                            component={Link}
                                            fontWeight={700}
                                            sx={{ textDecoration: 'underline' }}
                                            color={theme.palette.text.secondary}
                                        >
                                            {data?.operatorId}
                                        </Typography>
                                    </Stack>
                                </Stack>
                            </Stack>
                        </Stack>
                        <Stack direction="row" gap={2} alignItems="flex-start">
                            <Box component="img" alt="" height={25} width={25} src={calendar3} />
                            <Stack direction="column">
                                <ToolTipComponent type="registrationDate">
                                    <Typography sx={{ fontSize: 20 }} color={theme.palette.neutral[700]} fontWeight={600}>
                                        Project Registration Date
                                    </Typography>
                                </ToolTipComponent>
                                <Typography variant="subtitle1" color={theme.palette.text.secondary}>
                                    {getFormattedDate(data?.registrationDate, dateFormats.dd_MMM_yyyy)}
                                </Typography>
                            </Stack>
                        </Stack>

                        <Stack direction="row" gap={2} alignItems="flex-start">
                            {/* <Box
											component='img'
											alt=''
											height={20}
											width={20}
											src='/images/technologyHexagon.png'
										/> */}
                            <Box component="img" alt="" height={25} width={25} src={clockIcon} />
                            <Stack direction="column">
                                <ToolTipComponent type="creditingPeriodTerm">
                                    <Typography sx={{ fontSize: 20 }} color={theme.palette.neutral[700]} fontWeight={600}>
                                        Crediting Period Term
                                    </Typography>
                                </ToolTipComponent>
                                <Typography variant="subtitle1" color={theme.palette.text.secondary}>
                                    {data?.creditingEndDate
                                        ? `${getFormattedDate(
                                              data?.creditingStartDate,
                                              dateFormats.dd_MMM_yyyy
                                          )} -> ${getFormattedDate(data?.creditingEndDate, dateFormats.dd_MMM_yyyy)}`
                                        : getFormattedDate(data?.creditingStartDate, dateFormats.dd_MMM_yyyy)}
                                </Typography>
                            </Stack>
                        </Stack>
                        <CustomProjectDocumentButton
                            documentUrl={projectDocuments?.officialPDD?.imageURL ?? ''}
                            header="Project Development Document"
                        />
                    </Stack>
                </Stack>
            </Stack>
        </StyledContainer>
    )
}

const StyledContainer = styled(Container)(({ theme }) => ({
    paddingTop: theme.spacing(8),
    '.markdown p': {
        margin: theme.spacing(0, 0, 2),
        color: theme.palette.neutral[700],
        fontWeight: 300,
    },
}))
