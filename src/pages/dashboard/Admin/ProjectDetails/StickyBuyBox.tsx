
import { <PERSON>,  Chip, LinearProgress, <PERSON>, Stack, Typography } from '@mui/material'
import { FC, useEffect, useMemo, useState } from 'react'
import hexa from '/images/IconHexagon.svg'
import applicationIcon from '/images/applicationIcon.svg'
import methaneIcon from '/images/methaneIcon.svg'
import { IProjectDetails, IProjectDocuments } from '@/interfaces/projects.type'
import { getFormattedDate } from '@/utils/helper/getFormattedDate'
import { dateFormats } from '@/utils/constant'
import { theme } from '@/lib/theme/theme'

const linkList = ['about', 'monitoring', 'technology', 'details']

interface IProp {
    data?: IProjectDetails
    projectDocuments: IProjectDocuments
    headerHeight: number
}
export const StickyBuyBox: FC<IProp> = ({ data, headerHeight }) => {

    const [top, setTop] = useState(0)


    useEffect(() => {
        const handleScroll = () => {
            if (window.scrollY > 0) {
                setTop(80)
            } else {
                setTop(0)
            }
        }

        window.addEventListener('scroll', handleScroll)
        return () => {
            window.removeEventListener('scroll', handleScroll)
        }
    }, [])

    const CardComponent = () => {
        // const theme = useTheme()
        const showDetailsAccordToStatus = useMemo(() => {
            switch (true) {
                case ['active', 'active available', 'upcoming available'].includes(data?.projectStatus ?? ''):
                    return (
                            <Box
                                sx={{
                                    borderRadius: '16px',
                                    pl: 0.75,
                                }}
                            >
                                <Typography
                                    variant="h1"
                                    sx={{
                                        fontWeight: 900,
                                        fontSize: '64px',
                                    }}
                                >
                                    ${data?.rate}
                                </Typography>
                                <Typography fontSize={15} fontWeight={400}>
                                    {' '}
                                    per tCO<sub>2</sub>e
                                </Typography>
                            </Box>
                    )
                case ['sold out', 'active sold out', 'upcoming sold out'].includes(data?.projectStatus ?? ''):
                    return (
                        <Stack alignItems="start">
                            <Chip
                                label="Sold Out"
                                sx={{
                                    background: theme.palette.custom.lightest.primary,
                                    color: 'primary.main',
                                    fontWeight: 700,
                                    fontSize: '14px',
                                }}
                            />
                        </Stack>
                    )
                case ['upcoming', 'active reserved', 'upcoming reserved'].includes(data?.projectStatus ?? ''):
                    return (
                        <Stack direction="row" columnGap={1}>
                            <Chip
                                label="Reserved"
                                sx={{
                                    background: theme.palette.custom.lightest.primary,
                                    color: 'primary.main',
                                    fontWeight: 700,
                                    fontSize: '14px',
                                }}
                            />
                            <Chip
                                label={`Next credits available: ${getFormattedDate(
                                    data?.nextAvailableDate ?? '',
                                    dateFormats.MM_yyyy_with_dot
                                )}`}
                                
                                sx={{
                                    background: '#F4FAE9',
                                    color: theme.palette.success.main,
                                    fontWeight: 700,
                                    fontSize: '14px',
                                }}
                            />
                        </Stack>
                    )
                default:
                    return null
            }
        }, [])

       

        return (
            <Box
                sx={{
                    minWidth: { md: 350, xs: '100%' },
                    maxWidth: 400,
                }}
            >
                <Stack
                    gap={4}
                    sx={{
                        border: '1px solid #BDBBB94D',
                        borderRadius: '16px',
                        padding: '30px 40px',
                    }}
                >
                    {showDetailsAccordToStatus}

                    <Stack gap={2.2}>
                        <Stack direction="row" gap={1} alignItems="center">
                            <Box component="img" alt="" height={20} width={20} src={hexa} />
                            <Stack direction="column">
                                <Typography sx={{ fontWeight: 700, fontSize: 12 }} color={theme.palette.text.secondary}>
                                    TECHNOLOGY
                                </Typography>
                                <Typography sx={{ fontWeight: 400, fontSize: 12 }} color={theme.palette.text.secondary}>
                                    {data?.technology ?? ''}
                                </Typography>
                            </Stack>
                        </Stack>
                        <Stack direction="row" gap={1}>
                            <Box component="img" alt="" height={20} width={20} src={applicationIcon} />
                            <Stack direction="column">
                                <Typography sx={{ fontWeight: 700, fontSize: 12 }} color={theme.palette.text.secondary}>
                                    APPLICATION
                                </Typography>
                                <Typography sx={{ fontWeight: 400, fontSize: 12 }} color={theme.palette.text.secondary}>
                                    {data?.application}
                                </Typography>
                            </Stack>
                        </Stack>
                        <Stack direction="row" gap={1}>
                            <Box component="img" alt="" height={20} width={20} src={methaneIcon} />
                            <Stack direction="column">
                                <Typography sx={{ fontWeight: 700, fontSize: 12 }} color={theme.palette.text.secondary}>
                                    METHANE MITIGATION
                                </Typography>
                                <Typography sx={{ fontWeight: 400, fontSize: 12 }} color={theme.palette.text.secondary}>
                                    {data?.methaneMitigation}
                                </Typography>
                            </Stack>
                        </Stack>
                    </Stack>
                    {['active available', 'active', 'upcoming', 'upcoming available'].includes(data?.projectStatus ?? '') ? (
                        <Stack>
                            <Stack flexDirection="row" gap={0.5}>
                                <Typography sx={{ fontSize: '16px', color: 'neutral.500' }}>
                                    {data?.available} credits available /{' '}
                                </Typography>

                                <Typography sx={{ fontSize: '14px', color: 'success.main' }}>
                                    {data?.creditsIssued} credits issued
                                </Typography>
                            </Stack>
                            <Box sx={{ width: '100%' }}>
                                <LinearProgress
                                    variant="determinate"
                                    color='success'
                                    sx={{
                                        height: 8,
                                        '&.MuiLinearProgress-determinate': {
                                            backgroundColor: 'neutral.100',
                                            borderRadius: '50px',
                                        },
                                    }}
                                    value={Number((data?.available ?? 0) / (data?.creditsIssued ?? 0)) * 100}
                                />
                            </Box>
                        </Stack>
                    ) : null}
                </Stack>
            </Box>
        )
    }
    return (
        <Box
            flexDirection={'column'}
            rowGap={2}
            sx={{
                position: { md: 'sticky', xs: 'inherit' },
                top: top,
                right: 1,
                zIndex: { md: 8, xs: 0 },
                pb: 2,
                marginTop: { md: `-${headerHeight - 23}px`, xs: 2 },
            }}
        >
            <CardComponent />
            <Stack direction="row" justifyContent="center" columnGap={1} mt={2}>
                {linkList.map((i, idx) => (
                    <Stack columnGap={1} key={i} direction="row">
                        {idx !== 0 ? <Typography sx={{ color: 'neutral.300' }}>&#x2022;</Typography> : null}
                        <Typography
                            component={Link}
                            underline="hover"
                            textTransform="capitalize"
                            href={`#${i}`}
                            sx={{ color: 'neutral.300' }}
                        >
                            {i}
                        </Typography>
                    </Stack>
                ))}
            </Stack>
            
        </Box>
    )
}
