import { LinkedIn } from '@mui/icons-material'
import { Avatar, Box, But<PERSON>, Container, Link, Stack, styled, Typography } from '@mui/material'
import { FC, useMemo } from 'react'
import { IProjectDetails } from '@/interfaces/projects.type'
import { ProjectMap } from './ProjectMap'
import { theme } from '@/lib/theme/theme'

export const LocationAndRepresentative: FC<{ data?: IProjectDetails }> = ({ data }) => {
    const extractLatLong = useMemo(() => {
        const regex = /@(.*?),(.*?),(.*?)z/
        const match = data?.projectMapLocationURL?.match(regex)

        if (match) {
            const lat = parseFloat(match[1])
            const lng = parseFloat(match[2])
            return { lat: lat, lng: lng }
        } else {
            return { lat: 0, lng: 0 }
        }
    }, [data?.projectMapLocationURL])
    return (
        <Container>
            <Stack
                direction="column"
                marginRight={0}
                sx={{
                    backgroundColor: 'neutral.20',
                    my: 6.5,
                    pl: 0,
                    pr: 0,

                    minHeight: 400,
                }}
            >
                <Stack direction="column" justifyContent="space-between" padding={{ md: '40px 60px', xs: '20px' }}>
                    <Stack flexDirection="column" gap={6.5}>
                        <Typography variant="h3" fontFamily="Overpass" fontWeight={600} color={theme.palette.neutral[700]}>
                            Location
                        </Typography>
                        <StyledStack>
                            <ProjectMap data={data} coordinates={extractLatLong} />
                        </StyledStack>
                    </Stack>
                </Stack>

                {data?.contacts?.length !== 0 ? (
                    <Stack gap={6.5} padding={{ md: 8, xs: 3 }}>
                        <Typography variant="h3" fontFamily="Overpass" fontWeight={600} color={theme.palette.neutral[700]}>
                            Our Represntatives
                        </Typography>
                        <Box
                            sx={{
                                p: 0,
                                display: 'flex',
                                flexWrap: 'wrap',

                                gap: 3,
                            }}
                        >
                            {data?.contacts?.map((person) => (
                                <Stack
                                    key={person?.id}
                                    direction="column"
                                    sx={{
                                        boxShadow: '0px 3px 8px -1px #3232470D',
                                    }}
                                    width="285px"
                                    // width='100%'
                                    borderRadius={1}
                                    padding={3}
                                    bgcolor="common.white"
                                    gap={2.5}
                                >
                                    <Avatar
                                        alt={person?.name}
                                        sx={{ width: 104, height: 104 }}
                                        src={person?.imageURL}
                                    />

                                    <Stack pl={2} width="100%">
                                        {person.name && (
                                            <Stack direction="row" alignItems="center" sx={{ mt: '13px' }}>
                                                <Typography variant="h5">{person?.name}</Typography>
                                            </Stack>
                                        )}

                                        {person.email && (
                                            <Link href={`mailto:${person.email}`} fontSize={14}>
                                                <Typography
                                                    variant="h6"
                                                    color={theme.palette.text.secondary}
                                                    gap={1}
                                                    sx={{
                                                        textDecoration: 'none',
                                                        fontSize: 14,
                                                        fontWeight: 400,
                                                    }}
                                                >
                                                    {person.email}
                                                </Typography>
                                            </Link>
                                        )}
                                    </Stack>
                                    <Button
                                        variant="outlined"
                                        target="_blank"
                                        href={person?.linkedinLink}
                                        sx={{
                                            padding: '8px 20px',
                                            color: theme.palette.text.secondary,
                                            fontSize: 14,
                                            fontWeight: 700,
                                            borderColor: 'neutral.200',
                                            textTransform: 'capitalize',
                                        }}
                                        endIcon={<LinkedIn />}
                                    >
                                        Connect on Linkedin
                                    </Button>
                                </Stack>
                            ))}
                        </Box>
                    </Stack>
                ) : null}
            </Stack>
        </Container>
    )
}

const StyledStack = styled(Stack)(({ theme }) => ({
    '.img_style': {
        width: 500,
        height: 400,
        [theme.breakpoints.down('md')]: {
            width: 400,
            height: 300,
        },
        [theme.breakpoints.down('sm')]: {
            height: 200,
            width: 300,
        },
    },
    '.google_map_style': {
        // width: 500,
        width: '100%',

        height: 400,
        position: 'relative',
        borderRadius: 7,
        [theme.breakpoints.down('md')]: {
            // width: 400,
            height: 300,
        },
        [theme.breakpoints.down('sm')]: {
            height: 200,
            // width: 300,
        },
    },
}))
