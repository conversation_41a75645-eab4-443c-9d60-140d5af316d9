import {
	alpha,
	<PERSON>,
	<PERSON><PERSON>,
	<PERSON>,
	Grid,
	Stack,
	styled,
	Typography,
	useTheme,
} from '@mui/material'
import { GridRenderCellParams } from '@mui/x-data-grid'
import { useCallback } from 'react'
import { useNavigate } from 'react-router-dom'
import { CustomDataGrid, CustomHeader } from '../../../components'

const rows = [
	{
		id: '1',
		title: 'Biochar for Climate-resilient Cocoa...',
		tag: '<PERSON>',
		uploadDate: 'today',
	},
	{
		id: '2',
		title: 'Project name',
		tag: '<PERSON>',
		uploadDate: '03 Mar 2024',
	},
	{
		id: '3',
		title: 'Project name',
		tag: '<PERSON>',
		uploadDate: '01 Mar 2024',
	},
	{
		id: '4',
		title: 'Project name',
		tag: '<PERSON>',
		uploadDate: '24 Feb 2024',
	},
	{
		id: '5',
		title: 'Project name',
		tag: '<PERSON>',
		uploadDate: '24 Feb 2024',
	},
	{
		id: '6',
		title: 'Project name',
		tag: '<PERSON>',
		uploadDate: '20 Feb 2024',
	},
]

const queryRows = [
	{
		id: '1',
		email: '<EMAIL>',
		message: 'Hello, we would like to reques..',
	},
	{
		id: '2',
		email: '<EMAIL>',
		message: 'Hello, we would like to reques..',
	},
	{
		id: '3',
		email: '<EMAIL>',
		message: 'Hello, we would like to reques..',
	},
	{
		id: '4',
		email: '<EMAIL>',
		message: 'Hello, we would like to reques..',
	},
	{
		id: '5',
		email: '<EMAIL>',
		message: 'Hello, we would like to reques..',
	},
	{
		id: '6',
		email: '<EMAIL>',
		message: 'Hello, we would like to reques..',
	},
]

export const Admin = () => {
	const navigate = useNavigate()
	const theme = useTheme()

	const handleNavigation = useCallback(
		(url: string) => {
			navigate(url)
		},
		[navigate]
	)

	const columns = [
		{
			field: 'title',
			headerName: 'Title',
			minWidth: 280,
			flex: 1,

			renderCell: (params: GridRenderCellParams) => {
				return (
					<Typography
						variant='subtitle2'
						sx={{
							color: theme.palette.neutral['500'],
						}}>
						{params.row.title ?? ''}
					</Typography>
				)
			},
		},
		{
			field: 'tag',
			headerName: 'Tag',
			minWidth: 100,
			maxWidth: 100,
			flex: 1,

			renderCell: (params: GridRenderCellParams) => {
				return (
					<Typography
						variant='subtitle1'
						sx={{
							color: theme.palette.neutral['500'],
						}}>
						{params.row.tag ?? ''}
					</Typography>
				)
			},
		},
		{
			field: 'uploadDate',
			headerName: 'Uploaded Date',
			minWidth: 120,
			maxWidth: 200,
			flex: 1,

			renderCell: (params: GridRenderCellParams) => {
				return (
					<Typography
						variant='subtitle1'
						sx={{
							color: '#070605',
						}}>
						{params.row.uploadDate ?? ''}
					</Typography>
				)
			},
		},
	]
	const queryColumns = [
		{
			field: 'email',
			headerName: 'Form',
			minWidth: 75,
			flex: 1,

			renderCell: (params: GridRenderCellParams) => {
				return (
					<Typography
						variant='subtitle1'
						sx={{
							color: theme.palette.neutral['500'],
						}}>
						{params.row.email ?? ''}
					</Typography>
				)
			},
		},
		{
			field: 'message',
			headerName: 'Message',
			minWidth: 175,
			flex: 1,

			renderCell: (params: GridRenderCellParams) => {
				return (
					<Typography
						variant='subtitle1'
						sx={{
							color: theme.palette.neutral['500'],
						}}>
						{params.row.message ?? ''}
					</Typography>
				)
			},
		},
	]

	return (
		<StyledAdminContainer>
			<Box className='header'>
				<CustomHeader
					showBottomBorder={true}
					heading='Admin Section'
					showButton={false}
				/>
			</Box>
			<Grid container spacing={2} className='grid-data-container'>
				<Grid item xs={12} sm={12} md={6}>
					<Card elevation={0} className='data-card'>
						<Typography variant='body2'>Training content</Typography>
						<Box className='grid-container'>
							<CustomDataGrid
								showPagination={false}
								rows={rows}
								columns={columns}
								rowCount={6}
							/>
						</Box>
						<Button
							variant='contained'
							size='small'
							onClick={() =>
								handleNavigation('/dashboard/admin/training-content')
							}>
							See All
						</Button>
					</Card>
				</Grid>
				<Grid item xs={12} sm={12} md={6}>
					<Card elevation={0} className='data-card'>
						<Typography variant='body2'>Projects</Typography>
						<Box className='grid-container'>
							<CustomDataGrid
								showPagination={false}
								rows={rows}
								columns={columns}
								rowCount={6}
							/>
						</Box>
						<Button
							variant='contained'
							size='small'
							onClick={() => handleNavigation('/dashboard/admin/projects')}>
							See All
						</Button>
					</Card>
				</Grid>
				<Grid item xs={12} sm={12} md={6}>
					<Card elevation={0} className='data-card'>
						<Typography variant='body2'>Biomass Details</Typography>
						<Box className='grid-container'>
							<CustomDataGrid
								showPagination={false}
								rows={rows}
								columns={columns}
								rowCount={6}
							/>
						</Box>
						<Button
							variant='contained'
							size='small'
							onClick={() =>
								handleNavigation('/dashboard/admin/biomass-details')
							}>
							See All
						</Button>
					</Card>
				</Grid>
				<Grid item xs={12} sm={12} md={6}>
					<Card elevation={0} className='data-card'>
						<Typography variant='body2'>
							Queries/ Certification gerneration
						</Typography>
						<Box className='grid-container'>
							<CustomDataGrid
								showPagination={false}
								rows={queryRows}
								columns={queryColumns}
								rowCount={6}
							/>
						</Box>
						<Button
							variant='contained'
							size='small'
							onClick={() => handleNavigation('/dashboard/admin/queries')}>
							See All
						</Button>
					</Card>
				</Grid>
			</Grid>
		</StyledAdminContainer>
	)
}

const StyledAdminContainer = styled(Stack)(({ theme }) => ({
	gap: theme.spacing(4),
	'.header': {
		padding: theme.spacing(4, 0),
		borderBottom: `${theme.spacing(0.125)} solid ${
			theme.palette.neutral['100']
		}`,
	},
	'.grid-data-container': {
		padding: theme.spacing(0, 3, 2),
		'.data-card': {
			padding: theme.spacing(3, 2),
			borderRadius: theme.spacing(2),
			boxShadow: `0 0 ${theme.spacing(0.25)} 0 ${alpha(
				theme.palette.common.black,
				0.25
			)}`,
		},
		'.grid-container': {
			minHeight: 300,
			maxHeigtt: 300,
		},
	},
}))
