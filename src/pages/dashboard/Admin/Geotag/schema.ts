import * as Yup from 'yup'

export const createGeoTagImage = Yup.object({
	url: Yup.string().required('Please enter Url'),
	longitude: Yup.number().required('Please enter Longitude'),
	latitude: Yup.number().required('Please enter Latitude'),
	elevation: Yup.number().required('Please enter Elevation'),
	accuracy: Yup.number().required('Please enter Accuracy'),
	time: Yup.string().required('Please enter Time'),
	fontSize: Yup.string().required('Please enter Font Size'),
	position: Yup.string().required('Please enter Position'),
	format: Yup.string().required('Please enter Format'),
})

export type TGeotag =Yup.InferType<typeof createGeoTagImage>