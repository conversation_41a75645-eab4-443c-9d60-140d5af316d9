import {
	<PERSON><PERSON>,
	<PERSON>,
	<PERSON><PERSON>,
	Container,
	FormControl,
	IconButton,
	Link,
	MenuItem,
	Stack,
	TextField,
	Typography,
} from '@mui/material'
import { useRef, useState } from 'react'

import { yupResolver } from '@hookform/resolvers/yup'
import { Close, CloudUploadOutlined, CopyAll } from '@mui/icons-material'
import { AdapterDayjs } from '@mui/x-date-pickers/AdapterDayjs'
import { LocalizationProvider } from '@mui/x-date-pickers/LocalizationProvider'
import { DateTimePicker } from '@mui/x-date-pickers/DateTimePicker'

import { useForm } from 'react-hook-form'
import { format } from 'date-fns'
import { authAxios, ContentType } from '@/contexts'
import { FileUploader } from 'react-drag-drop-files'
import { createGeoTagImage, TGeotag } from './schema'
import { LoadingButton } from '@mui/lab'
import dayjs from 'dayjs'
import { theme } from '@/lib/theme/theme'

export const Geotag = () => {
	const [uploadedImageId, setUploadedImageId] = useState<{
		id: string
		url: string
	} | null>(null)
	const canvasRef = useRef<HTMLCanvasElement>(null)
	const {
		register,
		handleSubmit,
		formState,
		setValue,
		watch,
		getValues,
		reset,
		clearErrors,
	} = useForm({
		mode: 'all',
		resolver: yupResolver<TGeotag>(createGeoTagImage),
		defaultValues: {
			elevation: 10,
			accuracy: 10,
		},
	})

	const handleImageLoad = handleSubmit(async (data) => {
		const canvas = canvasRef.current
		if (!canvas) return
		const ctx = canvas.getContext('2d')
		if (!ctx) return
		const image = new Image()

		image.crossOrigin = 'Anonymous'
		image.src = getValues('url')

		image.onload = () => {
			const { width, height } = image
			canvas.width = width
			canvas.height = height

			const fontSize = Number(data.fontSize)

			const geoTagBox = {
				width: fontSize * 12 + fontSize * 2,
				height: fontSize * 6 + fontSize * 2,
				x: 0,
				y: 0,
			}

			switch (data.position) {
				case 'topLeft':
					geoTagBox.x = 0
					geoTagBox.y = 0
					break

				case 'topRight':
					geoTagBox.x = width - geoTagBox.width
					geoTagBox.y = 0
					break

				case 'bottomLeft':
					geoTagBox.x = 0
					geoTagBox.y = height - geoTagBox.height
					break

				case 'bottomRight':
					geoTagBox.x = width - geoTagBox.width
					geoTagBox.y = height - geoTagBox.height
					break

				default:
					break
			}

			ctx.drawImage(image, 0, 0)

			ctx.fillStyle = '#efefef80'
			ctx.fillRect(geoTagBox.x, geoTagBox.y, geoTagBox.width, geoTagBox.height)

			ctx.font = `${fontSize}px Arial`
			ctx.fillStyle = '#000000'
			const content = {
				lat: data.latitude,
				long: data.longitude,
				elevation: data.elevation,
				accuracy: data.accuracy,
				time: format(new Date(getValues('time')), 'dd/MM/yyyy HH:mm:ss'),
			}

			const linesKeys = {
				latitude: 'Lat',
				longitude: 'Long',
				elevation: 'elevation',
				accuracy: 'accuracy',
				time: 'time',
			}

			switch (data.format) {
				case 'oldFormat':
					linesKeys.latitude = 'latitude'
					linesKeys.longitude = 'longitude'
					break

				case 'newFormat':
					linesKeys.latitude = 'Lat'
					linesKeys.longitude = 'Long'
					break

				default:
					break
			}

			const lines = [
				`${linesKeys.latitude}: ${content.lat}`,
				`${linesKeys.longitude}: ${content.long}`,
				`${linesKeys.elevation}: ${content.elevation}`,
				`${linesKeys.accuracy}: ${content.accuracy}`,
				`${linesKeys.time}: ${content.time}`,
			]
			lines.forEach((line, index) => {
				ctx.fillText(
					line,
					geoTagBox.x + 10,
					geoTagBox.y + (fontSize + fontSize / 2.4) * (index + 1)
				)
			})
		}
	})

	const uploadImageToServer = async () => {
		const canvas = canvasRef.current
		if (!canvas) return
		canvas.toBlob(async (file) => {
			if (!file) return
			const newFile = new File([file], `${Number(new Date())}.jpg`, {
				type: 'image/jpeg',
			})
			const formData = new FormData()
			formData.append('file', newFile)
			formData.append('type', 'misc')
			formData.append(
				'geoData',
				JSON.stringify({
					latitude: String(getValues('latitude')),
					longitude: String(getValues('longitude')),
					elevation: String(getValues('elevation')),
					accuracy: String(getValues('accuracy')),
					time: String(getValues('time')),
				})
			)
			const { data } = await authAxios.post('/upload', formData, {
				headers: { 'Content-Type': ContentType.FormData },
			})
			setUploadedImageId(data)
		}, 'image/jpeg')
	}
	return (
		<Container sx={{ textAlign: 'center' }}>
			<Typography variant='h4'>Generate Geo-Tag Images</Typography>
			<Stack
				justifyContent='center'
				gap={theme.spacing(4)}
				flexDirection='row'
				pt={theme.spacing(4)}>
				<Stack
					flex={1}
					maxWidth='40%'
					gap={theme.spacing(4)}
					alignItems='center'>
					<TextField
						id='Url'
						label='Enter Url'
						variant='outlined'
						{...register('url')}
						fullWidth
						InputLabelProps={{ filled: !!watch('url'), shrink: !!watch('url') }}
					/>
					<Stack alignItems='stretch' width='100%'>
						{!watch('url') ? (
							<FileUploader
								handleChange={(file: any) => {
									const url = URL.createObjectURL(file)
									setValue('url', url)
								}}
								name='file'>
								<IconButton
									sx={{
										border: '1px dashed #BBB',
										width: '100%',
										height: theme.spacing(38),
										borderRadius: theme.spacing(1),
									}}>
									<Stack
										direction='row'
										columnGap={theme.spacing(2)}
										alignItems='center'
										flexWrap='wrap'
										justifyContent='center'>
										<Typography variant='body1' flexWrap='wrap'>
											Upload or Drag the image
										</Typography>
										<CloudUploadOutlined />
									</Stack>
								</IconButton>
							</FileUploader>
						) : (
							<Box position='relative'>
								<Stack
									sx={{
										position: 'absolute',
										right: -10,
										top: -10,
										cursor: 'pointer',
										backgroundColor: 'secondary.main',
										color: 'common.white',
										borderRadius: '50%',
										padding: theme.spacing(0.5),
									}}
									onClick={() => {
										setValue('url', '')
									}}>
									<Close fontSize='small' />
								</Stack>
								<Box
									component='img'
									src={watch('url')}
									sx={{
										width: '100%',
										height: theme.spacing(38),
										objectFit: 'contain',
									}}
								/>
							</Box>
						)}
					</Stack>
					<TextField
						id='latitude'
						label=' Latitude'
						variant='outlined'
						{...register('latitude')}
						fullWidth
					/>
					<TextField
						id='longitude'
						label='Longitude'
						variant='outlined'
						{...register('longitude')}
						fullWidth
					/>
					<TextField
						id='elevation'
						label='Elevation'
						variant='outlined'
						{...register('elevation')}
						fullWidth
					/>
					<TextField
						id='accuracy'
						label='Accuracy'
						variant='outlined'
						{...register('accuracy')}
						fullWidth
					/>
					<LocalizationProvider dateAdapter={AdapterDayjs}>
						<DateTimePicker
							label='Time'
							slotProps={{
								textField: {
									fullWidth: true,
								},
							}}
							value={watch('time') ? dayjs(watch('time')) : null}
							onChange={(value) => {
								// value?.setSeconds(new Date().getSeconds())
								setValue('time', value ? value.toISOString() : '')
							}}
						/>
					</LocalizationProvider>
					<TextField
						id='fontSize'
						label='Font Size'
						variant='outlined'
						type='number'
						{...register('fontSize')}
						fullWidth
					/>

					<FormControl fullWidth>
						<TextField
							select
							{...register('position')}
							label='Position'
							id='select-type-position'
							value={watch('position')}
							onChange={(event) => {
								setValue('position', event.target.value)
								clearErrors('position')
							}}
							inputProps={{ MenuProps: { disableScrollLock: true } }}
							error={!!formState.errors.position?.message}
							helperText={formState.errors.position?.message}>
							<MenuItem value='topLeft'>Top Left</MenuItem>
							<MenuItem value='topRight'>Top Right</MenuItem>
							<MenuItem value='bottomLeft'>Bottom Left</MenuItem>
							<MenuItem value='bottomRight'>Bottom Right</MenuItem>
						</TextField>
					</FormControl>
					<FormControl fullWidth>
						<TextField
							select
							{...register('format')}
							label='Format'
							id='select-type-format'
							value={watch('format')}
							onChange={(event) => {
								setValue('format', event.target.value)
								clearErrors('format')
							}}
							error={!!formState.errors.format?.message}
							inputProps={{ MenuProps: { disableScrollLock: true } }}
							helperText={formState.errors.format?.message}>
							<MenuItem value='oldFormat'>Old Format</MenuItem>
							<MenuItem value='newFormat'>New Format</MenuItem>
						</TextField>
					</FormControl>
					<LoadingButton
						variant='outlined'
						sx={{ width: 200 }}
						onClick={handleImageLoad}>
						Generate
					</LoadingButton>
				</Stack>
				<Stack
					component='fieldset'
					flex={1}
					gap={4}
					alignItems='center'
					maxWidth='30%'>
					<Typography component='legend' variant='h5'>
						Preview
					</Typography>
					<Box
						component='canvas'
						ref={canvasRef}
						sx={{ border: '1px dashed divider', maxWidth: '100%' }}
					/>
				</Stack>
			</Stack>
			<Stack
				alignItems='center'
				direction='row'
				justifyContent='center'
				gap={2}
				mt={20}>
				{uploadedImageId ? (
					<>
						<Alert
							variant='outlined'
							color='success'
							sx={{ display: 'flex', alignItems: 'center' }}>
							<Stack flexDirection='row' alignItems='center' gap={1}>
								<Link href={uploadedImageId.url}>Link</Link>
								<Typography>AssetId: {uploadedImageId.id} </Typography>
								<IconButton
									onClick={async () => {
										await navigator.clipboard.writeText(uploadedImageId.id)
									}}>
									<CopyAll />
								</IconButton>
							</Stack>
						</Alert>
						<Button
							disabled={!formState.isSubmitSuccessful}
							variant='contained'
							onClick={() => {
								reset()
								setUploadedImageId(null)
								clearErrors()
								canvasRef?.current
									?.getContext('2d')
									?.clearRect(
										0,
										0,
										canvasRef.current.width,
										canvasRef.current.height
									)
							}}>
							Done
						</Button>
					</>
				) : (
					<Button
						disabled={!formState.isSubmitSuccessful}
						variant='contained'
						onClick={uploadImageToServer}>
						Upload Image
					</Button>
				)}
			</Stack>
		</Container>
	)
}
