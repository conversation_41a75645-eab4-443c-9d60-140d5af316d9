import { GoogleMapsWithNonDraggableMarker } from '@/components/GoogleMap'
import { yupResolver } from '@hookform/resolvers/yup'
import { Autocomplete, Box, Button, Stack, useTheme } from '@mui/material'
import { FC, useCallback, useEffect, useMemo } from 'react'
import { FormProvider, useForm, useWatch } from 'react-hook-form'
import { useMutation, useQuery, useQueryClient } from '@tanstack/react-query'
import { authAxios, useAuthContext } from '@/contexts'
import { toast } from 'react-toastify'
import { LoadingButton } from '@mui/lab'
import { CustomTextField } from '@/utils/components'
import { addEntityFormSchema, TAddEntityForm } from './schema'
import { AxiosError } from 'axios'
import { showAxiosErrorToast } from '@/utils/helper'
import { CsinkManager, ICSinkManagerResponse } from '@/interfaces'
import { EntityEnum, userRoles } from '@/utils/constant'
import { IUserContext } from '@/contexts/Auth/type'
import AddOrRemoveManager from './AddOrRemoveManager'

type AddEntityFormProps = {
	handleCloseDrawer: () => void
	formType: EntityEnum
}

const initialValues = {
	name: '',
	shortCode: '',
	locationName: '',
	createEntityOnly: true,
	createManager: false,
	managerDetails: null,
	latitude: 0,
	longitude: 0,
	selectCompanyId: '',
	bighaInHectare: 3.64,
}

const getApiEndpoint = (formType: EntityEnum, id: string): string => {
	if (formType === EntityEnum.artisanPro) {
		return `/csink-manager/${id}/artisan-pro`
	} else {
		return `/csink-manager/${id}/cs-network`
	}
}

const csinkManagersListQueryFunction = async () => {
	const queryParams = new URLSearchParams({
		limit: '1000',
		page: '0',
	})

	const { data } = await authAxios.get<ICSinkManagerResponse>(
		`/csink-manager?${queryParams.toString()}`
	)
	return data
}

const addEntityFn = async (
	formData: TAddEntityForm,
	userDetails: IUserContext | undefined,
	formType: EntityEnum
) => {
	const {
		name,
		latitude,
		longitude,
		locationName,
		shortCode,
		selectCompanyId,
		managerDetails,
		createEntityOnly,
		bighaInHectare,
		createManager,
	} = formData

	const { phoneNo, ...rest } = managerDetails ?? {}

	const manager =
		!createEntityOnly && managerDetails
			? {
					...rest,
					phoneNo,
					trained: managerDetails.trained === 'yes',
					profileImageId: managerDetails.profileImageId?.id,
					trainingImages:
						managerDetails.trainingImages?.map(({ id }) => id) || [],
			  }
			: undefined

	const payload = {
		name,
		latitude,
		longitude,
		createEntityOnly,
		locationName,
		address: locationName,
		createManager,
		manager,
		shortCode: shortCode || null,
		bighaInHectare,
	}

	const id =
		userDetails?.accountType === 'admin'
			? selectCompanyId
			: userDetails?.csinkManagerId

	const apiEndpoint = getApiEndpoint(formType, id || '')
	return await authAxios.post(apiEndpoint, payload)
}

const coordinateFields = [
	{
		id: 'latitude',
		label: 'Latitude',
	},
	{
		id: 'longitude',
		label: 'Longitude',
	},
]

export const AddEntityForm: FC<AddEntityFormProps> = ({
	handleCloseDrawer,
	formType,
}) => {
	const theme = useTheme()
	const { userDetails } = useAuthContext()
	const queryClient = useQueryClient()
	const isAdmin = userDetails?.accountType === userRoles.Admin
	const form = useForm<TAddEntityForm>({
		defaultValues: initialValues,
		mode: 'all',
		context: { formType, isAdmin },
		resolver: yupResolver(addEntityFormSchema),
	})

	const {
		register,
		formState: { errors },
		watch,
		setValue,
		handleSubmit,
		control,
	} = form

	const createEntityOnly = useWatch({
		control,
		name: 'createEntityOnly',
	})

	const setMapCenter = useCallback(
		(lat: number, lng: number) => {
			setValue('latitude', Number(lat.toFixed(6)))
			setValue('longitude', Number(lng.toFixed(6)))
		},
		[setValue]
	)

	const getCurrentLocation = useCallback(() => {
		navigator.geolocation.getCurrentPosition((position) => {
			setMapCenter(position.coords.latitude, position.coords.longitude)
		})
	}, [setMapCenter])

	const csinkManagersList = useQuery({
		queryKey: ['csinkManagersList'],
		queryFn: csinkManagersListQueryFunction,
		select: (data) =>
			data?.csinkManagers?.map((item: CsinkManager) => ({
				label: `${item.name} (${item.shortName})`,
				value: item.id,
			})),
		enabled: formType !== EntityEnum.company,
	})

	const addEntityMutation = useMutation({
		mutationKey: ['addCompany'],
		mutationFn: (formData: TAddEntityForm) =>
			addEntityFn(formData, userDetails, formType),
		onError: (error: AxiosError) => {
			showAxiosErrorToast(error)
		},
		onSuccess: ({ data }) => {
			toast(data?.message)
			queryClient.invalidateQueries({ queryKey: ['entities'] })
			handleCloseDrawer()
		},
	})

	const handleSave = useCallback(
		(values: TAddEntityForm) => {
			addEntityMutation.mutate(values)
		},
		[addEntityMutation]
	)

	const isLoading = useMemo(
		() => addEntityMutation.isPending,
		[addEntityMutation]
	)

	useEffect(() => {
		getCurrentLocation()
	}, [getCurrentLocation])

	const inputFields = useMemo(
		() => [
			{
				id: 'name',
				label: 'Name',
				type: 'text',
				show: true,
			},
			{
				id: 'shortCode',
				label: 'Short Code',
				type: 'text',
				show: formType === EntityEnum.company,
				inputProps: { maxLength: 4 },
			},
		],
		[formType]
	)

	const showSelectCompanyField = useMemo(() => {
		return (
			formType !== EntityEnum.company &&
			userDetails?.accountType === userRoles.Admin
		)
	}, [formType, userDetails?.accountType])

	const handleRemoveManagerDetails = useCallback(() => {
		setValue('createEntityOnly', true)
		setValue('createManager', false)
		setValue('managerDetails', null)
	}, [setValue])

	const handleAddManagerDetails = useCallback(() => {
		setValue('createEntityOnly', false)
		setValue('createManager', true)
		setValue('managerDetails', {
			name: '',
			email: '',
			phoneNo: null,
			countryCode: null,
			trained: 'no',
			profileImageId: null,
			trainingImages: [],
		})
	}, [setValue])

	return (
		<FormProvider {...form}>
			<Stack gap={5}>
				{showSelectCompanyField && (
					<Autocomplete
						onChange={(_, newValue) => {
							setValue('selectCompanyId', newValue?.value)
						}}
						options={csinkManagersList?.data || []}
						renderInput={(params) => (
							<CustomTextField
								schema={addEntityFormSchema}
								name='selectCompanyId'
								{...params}
								label='Select Company'
								error={!!errors?.selectCompanyId?.message}
								helperText={errors?.selectCompanyId?.message}
							/>
						)}
					/>
				)}

				{inputFields
					.filter((field) => field.show)
					.map((field) => (
						<CustomTextField
							key={field.id}
							id={field.id}
							label={field.label}
							type={field.type}
							variant='outlined'
							fullWidth
							error={!!errors?.[field.id as keyof TAddEntityForm]?.message}
							helperText={errors?.[field.id as keyof TAddEntityForm]?.message}
							{...register(field.id as keyof TAddEntityForm)}
							{...(field.inputProps && { inputProps: field.inputProps })}
						/>
					))}
				<AddOrRemoveManager
					handleAddManagerDetails={handleAddManagerDetails}
					handleRemoveManagerDetails={handleRemoveManagerDetails}
					schema={addEntityFormSchema}
					createEntityOnly={createEntityOnly}
				/>
				<CustomTextField
					key='locationName'
					id='locationName'
					label='Location Name'
					type='text'
					variant='outlined'
					fullWidth
					error={!!errors.locationName?.message}
					helperText={errors.locationName?.message}
					{...register('locationName')}
				/>
				<Stack direction='row' columnGap={2}>
					{coordinateFields.map((field) => (
						<CustomTextField
							key={field.id}
							id={field.id}
							label={field.label}
							variant='outlined'
							fullWidth
							type='number'
							inputProps={{ step: 'any' }}
							hideNumberArrows
							error={!!errors?.[field.id as keyof TAddEntityForm]?.message}
							helperText={errors?.[field.id as keyof TAddEntityForm]?.message}
							{...register(field.id as keyof TAddEntityForm, {
								setValueAs: (value) => Number(parseFloat(value).toFixed(6)),
							})}
						/>
					))}
				</Stack>

				<Box position={'relative'} width='100%'>
					<GoogleMapsWithNonDraggableMarker
						center={{
							lat: Number(watch('latitude')),
							lng: Number(watch('longitude')),
						}}
						setMapCenter={setMapCenter}
						mapContainerStyle={{
							width: '100%',
							height: '28vh',
							position: 'relative',
							textAlign: 'initial',
						}}
					/>
				</Box>

				<Stack
					direction='row'
					gap={3}
					justifyContent='space-between'
					className='buttonContainer'>
					<Button
						onClick={handleCloseDrawer}
						sx={{ border: ` 1px solid ${theme.palette.primary.main}` }}>
						Cancel
					</Button>
					<LoadingButton
						loading={isLoading}
						disabled={isLoading}
						onClick={handleSubmit(handleSave)}
						variant='contained'>
						Save
					</LoadingButton>
				</Stack>
			</Stack>
		</FormProvider>
	)
}
