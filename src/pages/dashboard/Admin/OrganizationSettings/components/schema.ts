import { EntityEnum } from '@/utils/constant'
import * as Yup from 'yup'

export const addEntityFormSchema = Yup.object({
	name: Yup.string().required('Please enter name'),

	shortCode: Yup.string().when('$formType', {
		is: (val: string) => val === EntityEnum.company,
		then: (schema) =>
			schema
				.required('Please enter your Short code')
				.matches(/^[a-zA-Z0-9]+$/, 'Only alphabets and numbers are allowed')
				.max(4, 'Max Limit Reached'),
		otherwise: (schema) => schema.notRequired(),
	}),

	selectCompanyId: Yup.string().when(['$formType', '$isAdmin'], {
		is: (formType: string, isAdmin: boolean) =>
			formType !== EntityEnum.company && isAdmin,
		then: (schema) => schema.required('Please select company'),
		otherwise: (schema) => schema.notRequired(),
	}),
	createEntityOnly: Yup.boolean(),
	createManager: Yup.boolean(),
	managerDetails: Yup.object()
		.shape({
			name: Yup.string().required('Enter Manager Name'),
			email: Yup.string()
				.required('Enter Manager Email')
				.email('Enter valid Email'),
			phoneNo: Yup.string().nullable(),
			countryCode: Yup.string().nullable(),
			trained: Yup.string()
				.required('Enter if manager is Trained')
				.oneOf(['yes', 'no']),
			profileImageId: Yup.object()
				.shape({
					id: Yup.string(),
					url: Yup.string(),
					fileName: Yup.string().nullable(),
				})
				.nullable(),
			trainingImages: Yup.array()
				.of(
					Yup.object().shape({
						id: Yup.string(),
						url: Yup.string(),
						fileName: Yup.string().nullable(),
					})
				)
				.when('trained', {
					is: (trained: string) => {
						return trained === 'yes'
					},
					then: (DocYep) =>
						DocYep.min(1, "Please upload at least one training image"),
					otherwise: (DocYep) => DocYep.optional(),
				}),
		})
		.nullable()
		.when('createEntityOnly', {
			is: false,
			then: (managerDetailsYep) => managerDetailsYep.required(),
			otherwise: (managerDetailsYup) => managerDetailsYup.optional(),
		}),

	latitude: Yup.number().required('Please enter latitude'),
	longitude: Yup.number().required('Please enter longitude'),
	locationName: Yup.string().required('Please enter location name'),
	bighaInHectare: Yup.number().required('Please enter Bigha in Hectare'),
})

export const demoteManagerSchema = Yup.object().shape({
	profileImage: Yup.object({
		id: Yup.string().required("Required"),
		url: Yup.string().required('Required')
	}),
	trained: Yup.boolean().required('Please Select One'),
	trainingImages: Yup.array().when('trained', {
		is: true,
		then: (schema) =>
			schema
				.required('Please enter training images')
				.min(1, 'Please enter training images'),
		otherwise: (schema) => schema,
	}),
	aadhaarNumber: Yup.string().nullable().when('aadhaarImage', {
		is: (val: string | null) => !!val,
		then: (schema) => schema.required('Please Upload Id Number'),
		otherwise: (schema) => schema,
	}),
	aadhaarImage: Yup.string()
		.nullable()
		.when('aadhaarNumber', {
			is: (val: string | null) => !!val,
			then: (schema) => schema.required('Please upload your ID Proof'),
			otherwise: (schema) => schema,
		}),

}, [['aadhaarImage', 'aadhaarNumber']])

export type TDemoteManager = Yup.InferType<typeof demoteManagerSchema>
export const addNoEntityUserSchema = Yup.object({
	name: Yup.string().required('Please enter name'),
	email: Yup.string().email().required('Please enter email'),
	profileImage: Yup.object({
		id: Yup.string(),
		url: Yup.string(),
	}).nullable(),
})

export const addOtherUser = Yup.object().shape(
	{
		id: Yup.string().required('Please Choose one option'),
		name: Yup.string().required('Please enter name'),
		email: Yup.string().email().required('Please enter email'),
		phone: Yup.string().nullable(),
		countryCode: Yup.string().nullable(),
		profileImage: Yup.object({
			id: Yup.string(),
			url: Yup.string(),
		}).nullable().when('$isSupervisor', {
			is: true,
			then: () =>
				Yup.object({
					id: Yup.string().required('Please upload profile image'),
					url: Yup.string(),
				}).required('Please upload profile image'),
			otherwise: (schema) => schema,
		}),

		trained: Yup.boolean()
			.notRequired()
			.when('$isSupervisor', {
				is: true,
				then: (schema) => schema.required('Please select trained status'),
			}),
		trainingImages: Yup.array()
			.nullable()
			.when(['trained', '$isSupervisor'], {
				is: (trained: boolean, isSupervisor: boolean) =>
					isSupervisor && trained === true,
				then: (schema) =>
					schema
						.min(1, 'Please upload at least one training images'),
				otherwise: (schema) => schema,
			}),
		aadhaarImage: Yup.string()
			.nullable()
			.when('aadhaarNumber', {
				is: (aadhaarNumber: string | null) => !!aadhaarNumber,
				then: (schema) => schema.required('Please upload aadhar image'),
			}),

		aadhaarNumber: Yup.string()
			.nullable()
			.when('aadhaarImage', {
				is: (aadhaarImage: string | null) => !!aadhaarImage,
				then: (schema) => schema.required('Please enter Aadhaar Number'),
			}),
	},
	[['aadhaarImage', 'aadhaarNumber']]
)

export type TAddEntityForm = Yup.InferType<typeof addEntityFormSchema>

export type TAddNoEntityUser = Yup.InferType<typeof addNoEntityUserSchema>
export type TAddOtherUser = Yup.InferType<typeof addOtherUser>
