import {
	CustomCard,
	CustomChip,
	CustomPagination,
	CustomPaginationsDetails,
	NoData,
} from '@/components'
import { User } from '@/interfaces'
import { DeleteOutline, EmailOutlined } from '@mui/icons-material'
import EditIcon from '@/assets/icons/editIcon.svg'
import {
	Avatar,
	Box,
	CircularProgress,
	IconButton,
	Tooltip,
	Typography,
	useTheme,
} from '@mui/material'
import { Stack } from '@mui/material'
import LocalPhoneOutlinedIcon from '@mui/icons-material/LocalPhoneOutlined'
import { userRoles, userRolesNameOrganizationSettings } from '@/utils/constant'
import { CustomFilter } from '@/components/CustomFilter'
import { ILabelWithValue } from '@/types'
import { theme } from '@/lib/theme/theme'
import { useMemo } from 'react'

interface UsersListProps {
	isUsersLoading: boolean
	usersList: User[]
	totalUsersCount: number
	userFiltersOption: ILabelWithValue[]
	canEditOrDeleteUser: (value: userRoles, isEditButton: boolean) => boolean
	setShowEditUser: React.Dispatch<React.SetStateAction<User | null>>
	handleOpenDetails: (user: User) => void
	handleDeleteUser: (user: User | null) => void
}

export const UsersList = ({
	isUsersLoading,
	usersList,
	totalUsersCount,
	userFiltersOption,
	canEditOrDeleteUser,
	setShowEditUser,
	handleOpenDetails,
	handleDeleteUser,
}: UsersListProps) => {
	return (
		<>
			<Stack className='sticky-header' justifyContent='flex-end'>
				<Stack
					flexDirection={'row'}
					justifyContent={'flex-end'}
					alignItems={'center'}
					gap={theme.spacing(1)}>
					<CustomFilter
						queryKey='userRole'
						filtersToReset={['page', 'limit']}
						label='Users Type'
						multiple={false}
						options={userFiltersOption}
						style={{width:120}}
						isCompact
					/>
					<CustomPaginationsDetails
						pageName='userPage'
						limitName='userLimit'
						rowCount={totalUsersCount}
						alignRowOrColumn='column'
					/>
				</Stack>
			</Stack>
			<Box className='user-box'>
				{!isUsersLoading && usersList.length === 0 && (
					<NoData
						noUsers={true}
						noDataMessage={
							<>
								No Users added, please add by clicking <br />
								on Add option from top
							</>
						}
					/>
				)}
				{isUsersLoading ? (
					<Stack alignItems={'center'}>
						<Box
							component={CircularProgress}
							alignSelf='center'
							mt={theme.spacing(2)}
						/>
					</Stack>
				) : (
					<>
						<Stack gap={theme.spacing(1)} padding={theme.spacing(1)}>
							{usersList.map((user) => (
								<UsersItem
									key={user.id}
									item={user}
									canEditOrDeleteUser={canEditOrDeleteUser}
									setShowEditUser={setShowEditUser}
									handleOpenDetails={handleOpenDetails}
									handleDeleteUser={handleDeleteUser}
								/>
							))}
						</Stack>
						<Stack flexDirection={'row'} justifyContent={'center'}>
							<CustomPagination
								pageName='userPage'
								limitName='userLimit'
								rowCount={totalUsersCount}
							/>
						</Stack>
					</>
				)}
			</Box>
		</>
	)
}

const UsersItem = ({
	item,
	canEditOrDeleteUser,
	setShowEditUser,
	handleOpenDetails,
	handleDeleteUser,
}: {
	item: User
	canEditOrDeleteUser: (value: userRoles, isEditButton: boolean) => boolean
	setShowEditUser: React.Dispatch<React.SetStateAction<User | null>>
	handleOpenDetails: (item: User) => void
	handleDeleteUser: (user: User | null) => void
}) => {
	const theme = useTheme()
	const entityName = useMemo(() => {
		return (
			item?.artisanProName || item?.csinkNetworkName || item?.csinkManagerName
		)
	}, [item?.artisanProName, item?.csinkNetworkName, item?.csinkManagerName])
	return (
		<CustomCard
			onClick={() => handleOpenDetails(item)}
			key={item.id}
			headerComponent={
				<Stack flexDirection={'row'} gap={theme.spacing(2)}>
					<Stack alignSelf={'center'}>
						<Avatar src={item?.profileImageUrl?.url} />
					</Stack>
					<Stack gap={theme.spacing(0.5)} width={'100%'}>
						<Stack
							direction={'row'}
							justifyContent={'space-between'}
							alignItems={'center'}
							width={'100%'}>
							<Stack
								direction={'column'}
								width={'100%'}
								gap={theme.spacing(0)}>
								<Stack
									direction={'row'}
									justifyContent={'space-between'}
									alignItems={'center'}
									width={'100%'}>
									<Stack direction={'row'} gap={theme.spacing(1)} alignItems={'center'}>
										<Typography
											variant='body1'
											fontWeight={theme.typography.caption.fontWeight}>
											{item.name}
										</Typography>
										<Stack flexDirection={'row'} gap={theme.spacing(1)}>
											<CustomChip
												isSmall
												label={
													userRolesNameOrganizationSettings[
													item?.accountType as userRoles
													]
												}
												appliedClass='lightGrey'
											/>
										</Stack>
										<Typography variant='subtitle1'>{entityName}</Typography>
									</Stack>
									<Stack direction={'row'} gap={theme.spacing(1)}>
										{canEditOrDeleteUser(
											item?.accountType as userRoles,
											true
										) ? (
											<Tooltip key={item.id} title='Edit' placement='top'>
												<IconButton
													onClick={(e) => {
														e.stopPropagation()
														setShowEditUser(item)
													}}
													sx={{ padding: 0 }}>
													<Box
														component='img'
														src={EditIcon}
														sx={{
															filter: 'brightness(0) saturate(100%) invert(0%)',
														}}
														width={theme.spacing(2.5)}
													/>
												</IconButton>
											</Tooltip>
										) : null}
										{canEditOrDeleteUser(
											item?.accountType as userRoles,
											false
										) ? (
											<Tooltip key={item.id} title='Delete' placement='top'>
												<IconButton
													onClick={(e) => {
														e.stopPropagation()
														handleDeleteUser(item)
													}}
													color='primary'
													sx={{ padding: 0 }}>
													<DeleteOutline sx={{ width: theme.spacing(3) }} />
												</IconButton>
											</Tooltip>
										) : null}
									</Stack>
								</Stack>
								<Stack
									flexDirection={'row'}
									alignItems='center'
									gap={theme.spacing(2)}>
									{item?.email && (
										<Stack
											flexDirection={'row'}
											alignItems='center'
											gap={theme.spacing(1)}>
											<EmailOutlined sx={{ width: theme.spacing(2) }} />
											<Typography variant='subtitle1'>{item.email}</Typography>
										</Stack>
									)}
									{item?.number && (
										<Stack
											flexDirection={'row'}
											alignItems='center'
											gap={theme.spacing(1)}>
											<LocalPhoneOutlinedIcon
												sx={{ width: theme.spacing(2) }}
											/>
											<Typography variant='subtitle1'>
												{item?.countryCode ? `(${item.countryCode})` : ''}{' '}
												{item.number}
											</Typography>
										</Stack>
									)}
								</Stack>
							</Stack>
						</Stack>
					</Stack>
				</Stack>
			}
		/>
	)
}
