import { Add, Remove } from '@mui/icons-material'
import { Button, Stack, Typography, useTheme } from '@mui/material'
import { AddManagerDetailsFields } from '@/components/AddManagerDetails'
import { AnyObjectSchema } from 'yup'

type TProps = {
	handleAddManagerDetails: () => void
	handleRemoveManagerDetails: () => void
	schema: AnyObjectSchema
	createEntityOnly: boolean | undefined
}

const AddOrRemoveManager = ({
	handleAddManagerDetails,
	handleRemoveManagerDetails,
	schema,
	createEntityOnly,
}: TProps) => {
	const theme = useTheme()

	const showManagerFields = !createEntityOnly

	return (
		<Stack flexDirection='column'>
			{showManagerFields ? (
				<Stack gap={2}>
					<Button
						variant='text'
						sx={{ justifyContent: 'flex-end' }}
						onClick={handleRemoveManagerDetails}
						startIcon={<Remove color='primary' />}>
						Remove Manager Details
					</Button>
					<Typography variant='body2' pb={theme.spacing(1)}>
						Manager Details:
					</Typography>
					<AddManagerDetailsFields schema={schema} />
				</Stack>
			) : (
				<Button
					variant='text'
					sx={{ justifyContent: 'flex-end' }}
					onClick={handleAddManagerDetails}
					startIcon={<Add color='primary' />}>
					Add Manager Details
				</Button>
			)}
		</Stack>
	)
}

export default AddOrRemoveManager
