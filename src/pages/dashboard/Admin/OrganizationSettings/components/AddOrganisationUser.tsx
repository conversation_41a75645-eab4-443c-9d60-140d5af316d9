import { Close } from '@mui/icons-material'
import {
	I<PERSON><PERSON>utton,
	MenuItem,
	Stack,
	styled,
	TextField,
	Typography,
} from '@mui/material'
import React, { FC, useMemo, useState } from 'react'
import { useAuthContext } from '@/contexts'
import { OrganizationUser, userRoles } from '@/utils/constant'
import { AddNoEntityUser } from './AddNoEntityUser'
import { AddOtherUser } from './AddOtherUser'
import { AddCsinkFarmerOperator } from '@/components'

const RenderForm: FC<{
	type: OrganizationUser
	setIsActionInfoDrawer: React.Dispatch<React.SetStateAction<boolean>>
}> = ({ type, setIsActionInfoDrawer }) => {
	if (
		type === OrganizationUser.other ||
		type === OrganizationUser.manager ||
		type === OrganizationUser.supervisor
	) {
		return (
			<AddOtherUser setIsActionInfoDrawer={setIsActionInfoDrawer} type={type} />
		)
	}
	return (
		<AddNoEntityUser
			setIsActionInfoDrawer={setIsActionInfoDrawer}
			type={type}
		/>
	)
}

type TProps = {
	setIsActionInfoDrawer: React.Dispatch<React.SetStateAction<boolean>>
	isCsinkNetworkFarmer?: boolean
}

export const AddOrganisationUser: FC<TProps> = ({
	setIsActionInfoDrawer,
	isCsinkNetworkFarmer,
}) => {
	const { userDetails } = useAuthContext()
	const [selectedOption, setSelectedOption] = useState<
		OrganizationUser | string
	>('')

	const isAdmin = useMemo(
		() => userDetails?.accountType === OrganizationUser.admin,
		[userDetails?.accountType]
	)
	const isCsinkManager = useMemo(
		() => userDetails?.accountType === userRoles.CsinkManager,
		[userDetails?.accountType]
	)
	const isArtisanProAdmin = useMemo(
		() => userDetails?.accountType === userRoles.ArtisanPro,
		[userDetails?.accountType]
	)
	const isCSinkNetwork = useMemo(
		() => userDetails?.accountType === userRoles.cSinkNetwork,
		[userDetails?.accountType]
	)
	const isManager = useMemo(
		() => userDetails?.accountType === userRoles.Manager,
		[userDetails?.accountType]
	)

	const options = useMemo(
		() => [
			{
				label: 'Admin',
				value: OrganizationUser.admin,
				show: isAdmin,
			},
			{
				label: 'Circonomy Employee',
				value: OrganizationUser.circonomy_employee,
				show: isAdmin,
			},
			{
				label: 'Compliance Manager',
				value: OrganizationUser.compliance_manager,
				show: isAdmin,
			},
			{
				label: 'CERES Auditor',
				value: OrganizationUser.ceres_auditor,
				show: isAdmin,
			},
			{
				label: 'Other',
				value: OrganizationUser.other,
				show: isAdmin,
			},
			{
				label: 'Supervisor',
				value: OrganizationUser.supervisor,
				show: isArtisanProAdmin || isCSinkNetwork || isManager,
			},
			{
				label: 'Manager',
				value: OrganizationUser.manager,
				show: isArtisanProAdmin || isCSinkNetwork || isManager,
			},
		],
		[isAdmin, isArtisanProAdmin, isCSinkNetwork, isManager]
	)
	return (
		<StyleContainer>
			<Stack className='header'>
				<Stack
					direction='row'
					spacing={1}
					alignItems='center'
					width='100%'
					justifyContent='space-between'>
					<Typography variant='h4'>Add User</Typography>
					<IconButton onClick={() => setIsActionInfoDrawer(false)}>
						<Close />
					</IconButton>
				</Stack>
			</Stack>
			<Stack className='container'>
				{isCsinkNetworkFarmer ? (
					<AddCsinkFarmerOperator
						setIsActionInfoDrawer={setIsActionInfoDrawer}
					/>
				) : (
					<>
						{(isAdmin || isArtisanProAdmin || isCSinkNetwork || isManager) && (
							<TextField
								value={selectedOption}
								onChange={(e) => setSelectedOption(e.target.value)}
								select
								label='Select User Type'>
								{options.map((option) =>
									option.show ? (
										<MenuItem key={option.value} value={option.value}>
											{option.label}
										</MenuItem>
									) : null
								)}
							</TextField>
						)}

						{selectedOption !== '' && (
							<RenderForm
								setIsActionInfoDrawer={setIsActionInfoDrawer}
								type={selectedOption as OrganizationUser}
							/>
						)}

						{isCsinkManager && (
							<AddOtherUser setIsActionInfoDrawer={setIsActionInfoDrawer} />
						)}
					</>
				)}
			</Stack>
		</StyleContainer>
	)
}

const StyleContainer = styled(Stack)(({ theme }) => ({
	gap: theme.spacing(2),
	'.header': {
		flexDirection: 'row',
		justifyContent: 'space-between',
		alignItems: 'center',
		padding: theme.spacing(4.5, 2),
		borderBottom: `${theme.spacing(0.125)} solid ${
			theme.palette.neutral['100']
		}`,
	},
	'.container': {
		textAlign: 'center',
		padding: theme.spacing(2, 4),
		gap: theme.spacing(4),
		'.formcontrol': {
			gap: theme.spacing(0.6),

			'.label': {
				color: theme.palette.neutral[500],
			},
		},
		'.buttonContainer': {
			gap: theme.spacing(2),
			button: {
				width: theme.spacing(30),
				height: theme.spacing(4.5),
				padding: theme.spacing(1, 2.5),
			},
		},
	},
}))
