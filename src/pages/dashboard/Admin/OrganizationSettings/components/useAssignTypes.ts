import { authAxios } from '@/contexts'
import { entitiesRoles, FieldTypeEnum } from '@/utils/constant'
import { showAxiosErrorToast } from '@/utils/helper'
import { useMutation, useQuery, useQueryClient } from '@tanstack/react-query'
import { AxiosError } from 'axios'
import { AssignedValue } from './AssignTypesDialog'
import { toast } from 'react-toastify'

const getFetchUrl = (
	field: FieldTypeEnum,
	csinkManagerId?: string,
	entityType?: string | null
  ) => {
	switch (field) {
	  case FieldTypeEnum.assignedBiomass:
		return '/crops?limit=1000'
  
	  case FieldTypeEnum.applicationTypes:
		return entityType === entitiesRoles.CsinkManager
		  ? '/application-types'
		  : csinkManagerId
			? `/csink-manager/${csinkManagerId}/application-types`
			: undefined
  
	  case FieldTypeEnum.mixingTypes:
		return entityType === entitiesRoles.CsinkManager
		  ? '/packaging-types'
		  : csinkManagerId
			? `/csink-manager/${csinkManagerId}/mixing-types`
			: undefined
  
	  default:
		throw new Error('Invalid field')
	}
  }
  
  

const getAssignUrl = (
	field: FieldTypeEnum,
	entityId: string,
	entityType: string
) => {
	const getBasePath = () => {
		if (entityType === entitiesRoles.cSinkNetwork) {
			return `/cs-network/${entityId}`
		}
		if (entityType === entitiesRoles.ArtisanPro) {
			return `/artisian-pro/${entityId}`
		}
		return `/csink-manager/${entityId}`
	}

	const basePath = getBasePath()

	switch (field) {
		case FieldTypeEnum.assignedBiomass:
			return entityType === entitiesRoles.CsinkManager
				? `${basePath}/assign-crops`
				: `${basePath}/preferred-biomass-type`
		case FieldTypeEnum.applicationTypes:
			return `${basePath}/application-types/assign`
		case FieldTypeEnum.mixingTypes:
			return `${basePath}/mixing-types/assign`
		default:
			throw new Error('Invalid field')
	}
}
const getPayloadKey = (field: FieldTypeEnum, entityType: string): string => {
	switch (field) {
		case FieldTypeEnum.assignedBiomass:
			return entityType === entitiesRoles.CsinkManager
				? 'cropIds'
				: 'biomassTypeIds'
		case FieldTypeEnum.applicationTypes:
			return 'applicationTypeIds'
		case FieldTypeEnum.mixingTypes:
			return 'mixingTypeIds'
		default:
			throw new Error('Invalid field type')
	}
}
export const useAssignTypes = (
	field: FieldTypeEnum,
	open: boolean = true,
	onClose: () => void,
	entityId?: string,
	entityType?: string | null,
	csinkManagerId?: string
) => {
	
	const queryClient = useQueryClient()
	const assignOptionsQuery = useQuery<AssignedValue[]>({
		queryKey: ['assignOptions', field],
		queryFn: async () => {
			const apiUrl = getFetchUrl(field, csinkManagerId,entityType)
			if (apiUrl) {
				const res = await authAxios.get(apiUrl)
	
				if (field === FieldTypeEnum.assignedBiomass)
					return res.data?.crops ?? []
	
				if (entityType === entitiesRoles.CsinkManager && field === FieldTypeEnum.mixingTypes)
					return res.data?.types ?? []
	
				return res.data ?? []
			}
			return null
		},
		enabled: open,
	})

	const saveAssignMutation = useMutation({
		mutationKey: ['saveAssignTypes', field, entityId],
		mutationFn: (selectedIds: string[]) => {
			if (!entityId) throw new Error('Entity Id is missing')

			const key = getPayloadKey(field, entityType ?? '')
			const payload = { [key]: selectedIds }

			return authAxios.put(
				getAssignUrl(field, entityId, entityType ?? ''),
				payload
			)
		},
		onSuccess: ({ data }) => {
			if (data?.message) {
				toast(data.message)
			}
			queryClient.invalidateQueries({
				queryKey: ['OrganizationSettingEntityUsers'],
			})
			onClose()
		},
		onError: (error: AxiosError) => {
			showAxiosErrorToast(error)
		},
	})

	return {
		assignOptionsQuery,
		saveAssignMutation,
	}
}
