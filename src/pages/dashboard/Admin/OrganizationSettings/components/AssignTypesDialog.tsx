import {
	<PERSON>alog,
	DialogTitle,
	DialogContent,
	<PERSON>alogActions,
	Button,
	CircularProgress,
	IconButton,
	Stack,
	Autocomplete,
	TextField,
} from '@mui/material'
import { Cancel } from '@mui/icons-material'
import { useEffect, useState } from 'react'
import { useAssignTypes } from './useAssignTypes'
import { FieldTypeEnum } from '@/utils/constant'
import { ICsinkApplicationType, ICsinkMixingType } from '@/interfaces'
import { AssignedBiomass } from '@/interfaces/entityUserManagement'
import { useSearchParams } from 'react-router-dom'

export type AssignedValue =
	| AssignedBiomass
	| ICsinkMixingType
	| ICsinkApplicationType
interface AssignTypesDialogProps {
	open: boolean
	onClose: () => void
	entityId: string
	field: FieldTypeEnum
	assignedValues: AssignedValue[]
	csinkManagerId?: string
}

const getChipLabel = (item: any, field: FieldTypeEnum): string => {
	switch (field) {
		case FieldTypeEnum.assignedBiomass:
			return item?.cropName ?? item?.name ?? ''
		case FieldTypeEnum.applicationTypes:
			return item?.type ?? ''
		case FieldTypeEnum.mixingTypes:
			return item?.mixingType ?? item?.name ?? ''
		default:
			return ''
	}
}

export const AssignTypesDialog = ({
	open,
	onClose,
	entityId,
	field,
	assignedValues,
	csinkManagerId,
}: AssignTypesDialogProps) => {
	const [searchParams] = useSearchParams()
	const entityType = searchParams.get('selectedEntityType')
	const [selectedIds, setSelectedIds] = useState<string[]>([])
	useEffect(() => {
		if (assignedValues) {
			setSelectedIds(assignedValues.map((v) => v.id) ?? [])
		}
	}, [assignedValues])
	const { assignOptionsQuery, saveAssignMutation } = useAssignTypes(
		field,
		open,
		onClose,
		entityId,
		entityType,
		csinkManagerId
	)

	const label = (() => {
		switch (field) {
			case FieldTypeEnum.assignedBiomass:
				return 'Biomass'
			case FieldTypeEnum.applicationTypes:
				return 'Application Type'
			case FieldTypeEnum.mixingTypes:
				return 'Mixing Type'
			default:
				return ''
		}
	})()	
	return (
		<Dialog open={open} onClose={onClose} fullWidth maxWidth='sm'>
			<IconButton onClick={onClose} sx={{ position: 'absolute', right: 10 }}>
				<Cancel />
			</IconButton>
			<DialogTitle textAlign='center'>Assign {label}</DialogTitle>
			<DialogContent>
				<Stack marginTop={3} rowGap={3}>
					<Autocomplete
						multiple
						disableCloseOnSelect
						loading={assignOptionsQuery.isLoading}
						options={assignOptionsQuery.data || []}
						getOptionLabel={(option) => getChipLabel(option, field) || ''}
						value={
							assignOptionsQuery.data?.filter((opt: AssignedValue) =>
								selectedIds.includes(opt.id)
							) || []
						}
						onChange={(_, newValue: AssignedValue[]) => {
							const ids = newValue.map((v) => v.id)
							setSelectedIds(ids)
						}}
						renderInput={(params) => (
							<TextField
								{...params}
								label='Select'
								placeholder='Search and select'
								InputProps={{
									...params.InputProps,
									endAdornment: (
										<>
											{assignOptionsQuery.isLoading ? (
												<CircularProgress color='inherit' size={20} />
											) : null}
											{params.InputProps.endAdornment}
										</>
									),
								}}
							/>
						)}
					/>
				</Stack>
			</DialogContent>
			<DialogActions sx={{ justifyContent: 'center' }}>
				<Button
					onClick={() => saveAssignMutation.mutate(selectedIds)}
					variant='contained'
					disabled={saveAssignMutation.isPending}>
					Save
				</Button>
			</DialogActions>
		</Dialog>
	)
}
