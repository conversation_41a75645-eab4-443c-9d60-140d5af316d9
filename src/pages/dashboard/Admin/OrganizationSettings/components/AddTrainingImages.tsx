import { Add, Call, Close, <PERSON>Outline, Remove } from '@mui/icons-material'
import {
	Box,
	Button,
	Divider,
	FormControl,
	FormHelperText,
	IconButton,
	MenuItem,
	Stack,
	Typography,
} from '@mui/material'
import { useForm, UseFormGetValues } from 'react-hook-form'
import { yupResolver } from '@hookform/resolvers/yup'
import { Dispatch, SetStateAction, useEffect, useState } from 'react'
import { CustomTextField } from '@/utils/components'
import { styled } from '@mui/material/styles'
import { demoteManagerSchema, TDemoteManager } from './schema'
import { MultipleFileUploader } from '@/components/MultipleFileUploader'
import { User } from '@/interfaces'
import { CustomFileUploader, CustomProfileElement } from '@/components'
import { authAxios } from '@/contexts'
import { useMutation, useQueryClient } from '@tanstack/react-query'
import { AxiosError } from 'axios'
import { showAxiosErrorToast } from '@/utils/helper'
import { toast } from 'react-toastify'
import { userRoles } from '@/utils/constant'
import { useSearchParams } from 'react-router-dom'

type Props = {
	setShowTrainingImagesDrawer: Dispatch<SetStateAction<boolean>>
	selectedUser: User | null
	closeDrawer: () => void
}

const demoteNetworkAdminToOperatorFn = async ({
	getValues,
	selectedUser,
}: {
	getValues: UseFormGetValues<TDemoteManager>
	selectedUser: User | null
}) => {
	const values = getValues()

	const payload = {
		profileImageId: values?.profileImage?.id || null,
		aadhaarNumber: values.aadhaarNumber === '' ? null : values.aadhaarNumber,
		aadhaarImageID: values.aadhaarImage === '' ? null : values.aadhaarImage,
		trainingImages: values.trainingImages?.map((trainingId) => trainingId?.id),
		networkId: selectedUser?.csinkNetworkId,
	}

	const apiUrl =
		selectedUser?.accountType === userRoles.ArtisanPro
			? `/artisian-pro/${selectedUser?.artisanProId}/manager/${selectedUser?.id}/demote-to-operator`
			: `/cs-network-manager/${selectedUser?.id}/demote-to-operator`

	const { data } = await authAxios.put(apiUrl, payload)
	return data
}

export const AddTrainingImages = ({
	setShowTrainingImagesDrawer,
	selectedUser,
	closeDrawer,
}: Props) => {
	const queryClient = useQueryClient()
	const [addIdentificationNumber, setAddIdentificationNumber] =
		useState<boolean>(false)
	const [selectedEntitieTypeParams] = useSearchParams()
	const entityId = selectedEntitieTypeParams.get('entityId') || null

	const form = useForm<TDemoteManager>({
		resolver: yupResolver(demoteManagerSchema),
		mode: 'all',
		defaultValues: {
			trained: false,
			trainingImages: [],
			aadhaarNumber: '',
			aadhaarImage: '',
			profileImage: {},
		},
	})

	const {
		handleSubmit,
		watch,
		setValue,
		clearErrors,
		register,
		getValues,
		formState: { errors },
	} = form

	const demoteNetworkAdminToOperator = useMutation({
		mutationKey: ['demoteNetworkAdminToOperator'],
		mutationFn: () =>
			demoteNetworkAdminToOperatorFn({
				getValues,
				selectedUser,
			}),
		onError: (error: AxiosError) => {
			showAxiosErrorToast(error)
		},
		onSuccess: (response) => {
			toast(response?.message)
			if (entityId) {
				queryClient.invalidateQueries({
					queryKey: ['OrganizationSettingEntityUsers'],
				})
			} else {
				queryClient.invalidateQueries({
					queryKey: ['OrganizationSettingUsers'],
				})
			}
			setShowTrainingImagesDrawer(false)
			closeDrawer()
		},
	})

	const isTrained = watch('trained')

	useEffect(() => {
		if (!selectedUser) return
		setValue('trained', true)
		setValue('trainingImages', selectedUser?.trainingImageUrls || [])
		setValue('profileImage.id', selectedUser?.profileImageUrl?.id || '')
		setValue('profileImage.url', selectedUser?.profileImageUrl?.url || '')
		setValue('aadhaarNumber', selectedUser?.aadhaarNumber || '')
		setValue('aadhaarImage', selectedUser?.aadhaarImageUrl?.id || '')
	}, [setValue, selectedUser])

	const onSubmit = () => {
		demoteNetworkAdminToOperator.mutate()
	}

	return (
		<StyleContainer>
			<Stack className='header'>
				<Typography variant='h4'>Add Training Images</Typography>
				<IconButton onClick={() => setShowTrainingImagesDrawer(false)}>
					<Close />
				</IconButton>
			</Stack>
			<Divider />

			<Stack className='detail'>
				<Stack className='user-details'>
					<CustomProfileElement
						value={watch('profileImage')}
						setValue={(id, url) => {
							setValue('profileImage', { id, url })
						}}
						errorMessage={errors?.profileImage?.id?.message}
						clearErrors={() => clearErrors('profileImage')}
					/>

					<Box className='user-info'>
						<Typography variant='h5'>{selectedUser?.name}</Typography>
						<Stack>
							<Box className='user-contact-info'>
								<MailOutline className='custom-icon' />
								<Typography
									variant='body2'
									className='custom-contact-typography'>
									{selectedUser?.email}
								</Typography>
							</Box>
							<Box className='user-contact-info'>
								<Call className='custom-icon' />
								<Typography
									variant='body2'
									className='custom-contact-typography'>
									{selectedUser?.number}
								</Typography>
							</Box>
						</Stack>
					</Box>
				</Stack>
			</Stack>

			<Stack gap={2} p={3}>
				<FormControl fullWidth>
					<CustomTextField
						schema={demoteManagerSchema}
						select
						{...register('trained')}
						label='Trained'
						id='select-type'
						disabled
						value={watch('trained') ? 'true' : 'false'}
						onChange={(event) => {
							setValue('trained', event.target.value === 'true')
							clearErrors('trained')
						}}
						error={!!errors.trained?.message}
						helperText={errors.trained?.message}>
						<MenuItem value='true'>Yes</MenuItem>
						<MenuItem value='false'>No</MenuItem>
					</CustomTextField>
				</FormControl>

				{isTrained && (
					<>
						<MultipleFileUploader
							data={(watch('trainingImages') ?? []).map((i) => ({
								...i,
								fileName: i?.path,
							}))}
							heading='Upload or Drag the Training Images'
							sx={{
								height: { xs: 100, md: 166 },
								width: '100%',
							}}
							imageHeight={100}
							setUploadData={(data) => {
								setValue('trainingImages', data)
								clearErrors('trainingImages')
							}}
						/>

						<FormHelperText error={Boolean(errors.trainingImages)}>
							{errors?.trainingImages?.message}
						</FormHelperText>
					</>
				)}

				<Stack width='100%' flexDirection={'row-reverse'}>
					<Button onClick={() => setAddIdentificationNumber((p) => !p)}>
						{addIdentificationNumber ? <Remove /> : <Add />}
						<Typography sx={{ marginTop: 1 }}>
							Add Identification Number
						</Typography>
					</Button>
				</Stack>

				{addIdentificationNumber && (
					<FormControl fullWidth>
						<Stack>
							<CustomTextField
								label='Identification Number'
								value={watch('aadhaarNumber')}
								fullWidth
								onChange={(event) => {
									setValue('aadhaarNumber', event.target.value)
									clearErrors('aadhaarNumber')
								}}
								error={!!errors.aadhaarNumber?.message}
								helperText={errors.aadhaarNumber?.message}
							/>
						</Stack>
						<Stack marginTop={3}>
							<CustomFileUploader
								heading='Upload Identification No.'
								sx={{
									height: { xs: 100, md: 150 },
									width: '100%',
								}}
								mediaType='image'
								setUploadData={(data) => {
									setValue('aadhaarImage', data?.id)
									clearErrors('aadhaarImage')
								}}
							/>

							<FormHelperText error={Boolean(errors.aadhaarImage)}>
								{errors?.aadhaarImage?.message}
							</FormHelperText>
						</Stack>
					</FormControl>
				)}

				<Stack direction='row' gap={2} mt={2}>
					<Button
						variant='outlined'
						fullWidth
						onClick={() => setShowTrainingImagesDrawer(false)}>
						Cancel
					</Button>
					<Button
						variant='contained'
						fullWidth
						onClick={handleSubmit(onSubmit)}>
						Save
					</Button>
				</Stack>
			</Stack>
		</StyleContainer>
	)
}

const StyleContainer = styled(Stack)(({ theme }) => ({
	width: '100%',
	'.header': {
		flexDirection: 'row',
		justifyContent: 'space-between',
		alignItems: 'center',
		padding: theme.spacing(2.5),
	},
	'.detail': {
		padding: theme.spacing(2.5),
	},
	'.user-details': {
		display: 'flex',
		flexDirection: 'row',
		gap: theme.spacing(4),
		alignItems: 'center',
	},
	'.user-avatar': {
		width: theme.spacing(11),
		height: theme.spacing(11),
	},
	'.user-info': {
		display: ' flex',
		flexDirection: 'column',
		gap: theme.spacing(0.5),
	},
	'.user-contact-info': {
		display: 'flex',
		alignItems: 'center',
		gap: theme.spacing(1),
		marginTop: theme.spacing(1),
		fontWeight: 100,
		color: theme.palette.text.secondary,
	},
	'.custom-icon': {
		fontSize: theme.spacing(2.25),
	},
	'.custom-contact-typography': {
		fontSize: theme.spacing(1.75),
		fontWeight: 500,
		marginTop: theme.spacing(0.35),
		lineHeight: 0,
	},
}))
