import {
	ActionInformation<PERSON>rawer,
	ImageGrid,
	TrainingProofRenderer,
} from '@/components'
import { SelectApplicationTypeForLogOut } from '@/components/SelectApplicationTypeForLogOut'
import CertificateDialog from '@/components/ViewUserDetails/CertificateDialog'
import { authAxios, useAuthContext } from '@/contexts'
import { IArtisanPro, IFarmForUser, User } from '@/interfaces'
import {
	dateFormats,
	userRoles,
	userRolesNameOrganizationSettings,
} from '@/utils/constant'
import { getFormattedDate, showAxiosErrorToast } from '@/utils/helper'
import {
	Call,
	Close,
	Delete,
	Edit,
	FmdGoodOutlined,
	InsertDriveFile,
	Logout,
	MailOutline,
	NorthEast,
	Replay,
	SouthWest,
} from '@mui/icons-material'
import {
	Avatar,
	Box,
	Button,
	Chip,
	Grid,
	IconButton,
	Stack,
	styled,
	Typography,
} from '@mui/material'
import { useMutation, useQuery, useQueryClient } from '@tanstack/react-query'
import { AxiosError } from 'axios'
import { Dispatch, FC, SetStateAction, useMemo, useState } from 'react'
import { toast } from 'react-toastify'
import GenerateCertificateDrawer from '../../Settings/components/GenerateCertificateDrawer'
import { AddTrainingImages } from './AddTrainingImages'
import { useSearchParams } from 'react-router-dom'
import { theme } from '@/lib/theme/theme'

type UserDetailViewProps = {
	closeDrawer: () => void
	selectedUser: User | null
	setShowEditUser: Dispatch<SetStateAction<User | null>>
	setShowConfirmationDialog: (show: boolean) => void
}
type CompanyInfo = { name?: string; typeName?: string }
enum EentityTypeName {
	company = 'Company',
	cSinkNetwork = 'CSinK Network',
	artisanPro = 'Artisan Pro',
}

type ButtonVariant = 'text' | 'outlined' | 'contained'

const resetCountFn = async (userId: string) => {
	if (!userId) throw new Error('No user selected')
	const { data } = await authAxios.patch(
		`/user/${userId}/pending-uploads-count`
	)
	return data
}

const promoteUserFn = async (selectedUser: User | null) => {
	if (!selectedUser) throw new Error('No user selected')
	const payload = { email: selectedUser?.email || null }

	const apiUrl =
		selectedUser?.accountType === userRoles.artisanProOperator
			? `/artisan-pro-network/${selectedUser?.artisanProNetworkId}/artisian-pro/${selectedUser?.artisanProId}/operator/${selectedUser?.id}/promote`
			: `/cs-network/${selectedUser?.csinkNetworkId}/operator/${selectedUser?.id}/promote-manager`

	const { data } = await authAxios.post(apiUrl, payload)
	return data
}

export const UserDetailView = ({
	closeDrawer,
	selectedUser,
	setShowEditUser,
	setShowConfirmationDialog,
}: UserDetailViewProps) => {
	const queryClient = useQueryClient()
	const { userDetails } = useAuthContext()

	const [showSelectApplicationTypeLogOut, setShowSelectApplicationTypeLogOut] =
		useState<boolean>(false)
	const [showCertificateDrawer, setShowCertificateDrawer] =
		useState<boolean>(false)
	const [showTrainingImagesDrawer, setShowTrainingImagesDrawer] =
		useState<boolean>(false)
	const [selectedEntitieTypeParams] = useSearchParams()
	const entityId = selectedEntitieTypeParams.get('entityId') || null

	const resetCountMutation = useMutation({
		mutationKey: ['resetCount'],
		mutationFn: () => resetCountFn(selectedUser?.id ?? ''),
		onError: (error: AxiosError) => {
			showAxiosErrorToast(error)
		},
		onSuccess: ({ data }) => {
			toast(data?.message || 'Counts decreased successfully')
			queryClient.invalidateQueries({ queryKey: ['OrganizationSettingUsers'] })
			closeDrawer()
		},
	})

	const promoteMutation = useMutation({
		mutationKey: ['promoteToCsinkNetworkManager'],
		mutationFn: () => promoteUserFn(selectedUser),
		onSuccess: (data) => {
			toast(data?.message)
			closeDrawer()
			if (entityId) {
				queryClient.invalidateQueries({
					queryKey: ['OrganizationSettingEntityUsers'],
				})
			} else {
				queryClient.invalidateQueries({
					queryKey: ['OrganizationSettingUsers'],
				})
			}
		},
		onError: (error: AxiosError) => {
			showAxiosErrorToast(error)
		},
	})

	const handleResetCount = () => {
		resetCountMutation.mutate()
	}

	const handlePromote = () => {
		promoteMutation.mutate()
	}

	const handleEdit = () => {
		setShowEditUser(selectedUser)
		closeDrawer()
	}
	const getFarmsDetails = useQuery({
		queryKey: ['farmList',selectedUser?.id],
		queryFn: async () =>
			authAxios.get<IFarmForUser[]>(`/new/farmers/${selectedUser?.id}/farms`),
		enabled: selectedUser?.accountType === 'farmer',
	})
	const userActions = [
		{
			label: 'Edit User',
			icon: <Edit />,
			className: 'button-gray',
			variant: 'outlined',
			onClick: handleEdit,
			show:
				([
					userRoles.CsinkManager,
					userRoles.cSinkNetwork,
					userRoles.ArtisanPro,
					userRoles.Manager,

					userRoles.artisanProOperator,
					userRoles.csinkNetworkOperator,
					userRoles.csink_operator_farmer,
				].includes(selectedUser?.accountType as userRoles) &&
					[userRoles.Admin, userRoles.CsinkManager].includes(
						userDetails?.accountType as userRoles
					)) ||
				[userRoles.csinkNetworkOperator, userRoles.artisanProOperator].includes(
					selectedUser?.accountType as userRoles
				),
		},
		{
			label: 'Delete User',
			icon: <Delete />,
			className: 'button-red',
			variant: 'outlined',
			onClick: () => setShowConfirmationDialog(true),
			show:
				[
					userRoles.CsinkManager,
					userRoles.cSinkNetwork,
					userRoles.ArtisanPro,
					userRoles.Manager,

					userRoles.artisanProOperator,
					userRoles.csinkNetworkOperator,
				].includes(selectedUser?.accountType as userRoles) &&
				[userRoles.Admin, userRoles.CsinkManager].includes(
					userDetails?.accountType as userRoles
				),
		},
		{
			label: 'Demote',
			icon: <SouthWest />,
			className: 'button-red',
			variant: 'outlined',
			onClick: () => setShowTrainingImagesDrawer(true),
			show:
				[
					userRoles.cSinkNetwork,
					userRoles.ArtisanPro,
					userRoles.Manager,
				].includes(selectedUser?.accountType as userRoles) &&
				[userRoles.Admin, userRoles.CsinkManager].includes(
					userDetails?.accountType as userRoles
				),
		},
		{
			label: 'Promote',
			icon: <NorthEast />,
			className: 'button-green',
			variant: 'outlined',
			onClick: handlePromote,
			show:
				[userRoles.artisanProOperator, userRoles.csinkNetworkOperator].includes(
					selectedUser?.accountType as userRoles
				) &&
				[userRoles.Admin, userRoles.CsinkManager].includes(
					userDetails?.accountType as userRoles
				),
		},
		{
			label: `Reset Count (${selectedUser?.pendingUploadsCount})`,
			icon: <Replay />,
			className: 'button-gray',
			variant: 'outlined',
			onClick: handleResetCount,
			show:
				[
					userRoles.CsinkManager,
					userRoles.cSinkNetwork,
					userRoles.ArtisanPro,
					userRoles.Manager,

					userRoles.artisanProOperator,
					userRoles.csinkNetworkOperator,
					userRoles.csink_operator_farmer,
				].includes(selectedUser?.accountType as userRoles) &&
				[userRoles.Admin].includes(userDetails?.accountType as userRoles),
		},
		{
			label: 'Generate Cert',
			icon: <InsertDriveFile />,
			className: 'button-gray',
			variant: 'outlined',
			onClick: () => setShowCertificateDrawer(true),
			show:
				([
					userRoles.cSinkNetwork,
					userRoles.ArtisanPro,
					userRoles.Manager,

					userRoles.artisanProOperator,
					userRoles.csinkNetworkOperator,
					userRoles.csink_operator_farmer,
				].includes(selectedUser?.accountType as userRoles) &&
					[userRoles.Admin, userRoles.CsinkManager].includes(
						userDetails?.accountType as userRoles
					)) ||
				[userRoles.csinkNetworkOperator, userRoles.artisanProOperator].includes(
					selectedUser?.accountType as userRoles
				),
		},
		{
			label: 'Logout from App',
			icon: <Logout />,
			className: 'button-red-filled',
			variant: 'contained',
			onClick: () => setShowSelectApplicationTypeLogOut(true),
			show:
				[
					userRoles.CsinkManager,
					userRoles.cSinkNetwork,
					userRoles.ArtisanPro,
					userRoles.Manager,

					userRoles.artisanProOperator,
					userRoles.csinkNetworkOperator,
					userRoles.csink_operator_farmer,
				].includes(selectedUser?.accountType as userRoles) &&
				[userRoles.Admin].includes(userDetails?.accountType as userRoles),
		},
	]

	const showTraningImages = useMemo(() => {
		return Array.isArray(selectedUser?.trainingImageUrls)
	}, [selectedUser?.trainingImageUrls])

	const showIdentificationProof = useMemo(() => {
		return !!selectedUser?.aadhaarImageUrl
	}, [selectedUser?.aadhaarImageUrl])

	const showCertificates = useMemo(() => {
		return !!selectedUser?.certificateDetails
	}, [selectedUser?.certificateDetails])
	const farmDetails = useMemo(() => {
		return selectedUser?.accountType === 'farmer'
	}, [selectedUser?.accountType])
	const isOnlyEmail = useMemo(() => {
		return [
			userRoles.ceres_auditor,
			userRoles.Admin,
			userRoles.compliance_manager,
			userRoles.CirconomyEmployee,
		].includes(selectedUser?.accountType as userRoles)
	}, [selectedUser?.accountType])

	const showIdentificationNumber = useMemo(() => {
		return [
			userRoles.csinkNetworkOperator,
			userRoles.artisanProOperator,
		].includes(selectedUser?.accountType as userRoles)
	}, [selectedUser?.accountType])

	const companyInfo: CompanyInfo[] = useMemo(() => {
		const accountType = selectedUser?.accountType
		switch (accountType) {
			case userRoles.cSinkNetwork:
			case userRoles.csinkNetworkOperator:
				return [
					...(selectedUser?.csinkNetworks?.map((cn: IArtisanPro) => ({
						name: cn?.name,
						typeName: EentityTypeName.cSinkNetwork,
					})) || []),
				]

			case userRoles.ArtisanPro:
			case userRoles.artisanProOperator:
				return [
					...(selectedUser?.artisanPros?.map((ap: IArtisanPro) => ({
						name: ap?.name,
						typeName: EentityTypeName.artisanPro,
					})) || []),
				]

			case userRoles.CsinkManager:
			case userRoles.role_awaiting:
				return [
					{
						name: selectedUser?.csinkManagerName,
						typeName: EentityTypeName.company,
					},
				]

			case userRoles.Manager:
				return [
					...(selectedUser?.artisanPros?.map((ap: IArtisanPro) => ({
						name: ap?.name,
						typeName: EentityTypeName.artisanPro,
					})) || []),
					...(selectedUser?.csinkNetworks?.map((cn: IArtisanPro) => ({
						name: cn?.name,
						typeName: EentityTypeName.cSinkNetwork,
					})) || []),
				]

			case userRoles.farmer:
				return [{ name: selectedUser?.artisanProName, typeName: '' }]

			case userRoles.csink_operator_farmer:
				return [{ name: selectedUser?.csinkNetworkName, typeName: '' }]

			default:
				return []
		}
	}, [selectedUser])

	return (
		<>
			<ActionInformationDrawer
				open={showTrainingImagesDrawer}
				onClose={() => setShowTrainingImagesDrawer(false)}
				anchor='right'
				component={
					<AddTrainingImages
						setShowTrainingImagesDrawer={setShowTrainingImagesDrawer}
						selectedUser={selectedUser}
						closeDrawer={closeDrawer}
					/>
				}
			/>

			<ActionInformationDrawer
				open={showCertificateDrawer}
				onClose={() => setShowCertificateDrawer(false)}
				anchor='right'
				component={
					selectedUser ? (
						<GenerateCertificateDrawer
							onClose={() => {
								setShowCertificateDrawer(false)
								closeDrawer()
							}}
							userData={selectedUser}
						/>
					) : null
				}
			/>

			<SelectApplicationTypeForLogOut
				userId={selectedUser?.id || ''}
				open={showSelectApplicationTypeLogOut}
				onClose={() => setShowSelectApplicationTypeLogOut(false)}
				cb={() => {
					queryClient.invalidateQueries({ queryKey: ['users'] })
					closeDrawer()
				}}
			/>

			<StyleContainer>
				<Stack className='header'>
					<IconButton onClick={closeDrawer}>
						<Close />
					</IconButton>
				</Stack>

				<Stack className='detail'>
					<Stack className='user-details'>
						<Avatar
							alt={selectedUser?.name || 'User Avatar'}
							className='user-avatar'
							src={selectedUser?.profileImageUrl?.url || ''}
						/>

						<Box className='user-info'>
							<Box className='user-info-edit'>
								<Typography variant='h5'>{selectedUser?.name}</Typography>
								<Chip
									label={
										userRolesNameOrganizationSettings[
											selectedUser?.accountType as keyof typeof userRolesNameOrganizationSettings
										]
									}
									size='small'
									sx={{ alignSelf: 'flex-start' }}
								/>
							</Box>
							{selectedUser?.accountType === userRoles.ceres_auditor && (
								<Button
									className='button-gray'
									variant='outlined'
									startIcon={<Edit />}
									onClick={handleEdit}>
									Edit User
								</Button>
							)}
						</Box>
					</Stack>

					{isOnlyEmail ? (
						<Box className='user-contact-info'>
							<MailOutline className='custom-icon' />
							<Typography variant='body2' className='custom-contact-typography'>
								{selectedUser?.email}
							</Typography>
						</Box>
					) : (
						<Stack marginTop={3}>
							{selectedUser?.email && (
								<Box className='user-contact-info'>
									<MailOutline className='custom-icon' />
									<Typography
										variant='body2'
										className='custom-contact-typography'>
										{selectedUser?.email}
									</Typography>
								</Box>
							)}
							{selectedUser?.number && (
								<Box className='user-contact-info'>
									<Call className='custom-icon' />
									<Typography
										variant='body2'
										className='custom-contact-typography'>
										{selectedUser?.number}
									</Typography>
								</Box>
							)}
							{companyInfo.length > 0 && (
								<Stack spacing={0.5} mt={1.5}>
									<Typography variant='body2'>Networks:</Typography>
									{companyInfo.map((info, i) => (
										<Typography
											key={i}
											sx={{
												fontSize: theme.spacing(1.75),
												paddingLeft: theme.spacing(1),
											}}>
											{info.name}
											{info.typeName ? ` (${info.typeName})` : ''}
										</Typography>
									))}
								</Stack>
							)}
						</Stack>
					)}

					{showIdentificationNumber && (
						<Stack marginTop={2}>
							<Typography variant='body1' sx={{ fontWeight: 600 }}>
								Identification No. : {selectedUser?.aadhaarNumber}
							</Typography>
						</Stack>
					)}

					{showTraningImages && (
						<Stack marginTop={2.5}>
							<Typography variant='body2' sx={{ fontWeight: 600 }}>
								Training Images :
							</Typography>
							<TrainingProofRenderer
								media={(selectedUser?.trainingImageUrls ?? [])?.map((i) => ({
									...i,
									fileName: i?.path || i?.fileName,
									path: i?.path ? i.path : '',
								}))}
								viewMode='table'
								hideTitle
								showInRow
								componentSize={40}
								ShowDeleteOption={false}
							/>
						</Stack>
					)}

					{showIdentificationProof && (
						<Stack marginTop={3}>
							<Typography variant='body2' sx={{ fontWeight: 600 }}>
								Identification Proof :
							</Typography>
							<TrainingProofRenderer
								media={
									selectedUser?.aadhaarImageUrl
										? [
												{
													...selectedUser.aadhaarImageUrl,
													fileName: selectedUser.aadhaarImageUrl.path,
													path: selectedUser.aadhaarImageUrl.path || '',
												},
										  ]
										: []
								}
								viewMode='table'
								hideTitle
								showInRow
								componentSize={40}
								ShowDeleteOption={false}
							/>
						</Stack>
					)}

					{showCertificates && (
						<Stack marginTop={3}>
							<Typography variant='body2' sx={{ fontWeight: 600 }}>
								Certificates :
							</Typography>
							<CertificateDialog
								certificateData={selectedUser?.certificateDetails || {}}
							/>
						</Stack>
					)}
					{farmDetails && (
						<Stack className='farm_detail_section'>
							<Typography variant='body2'>Farm details:</Typography>
							<Stack className='farm_list'>
								{(getFarmsDetails?.data?.data ?? [])?.map((farm) => (
									<FarmRenderer farmDetail={farm} key={farm.id} />
								))}
							</Stack>
						</Stack>
					)}
					<Grid container spacing={2} marginTop={4}>
						{userActions
							.filter((action) => action.show)
							.map((action, index) => (
								<Grid item xs={12} sm={6} key={index}>
									<Button
										fullWidth
										variant={action.variant as ButtonVariant}
										startIcon={action.icon}
										onClick={action.onClick}
										className={action.className}>
										{action.label}
									</Button>
								</Grid>
							))}
					</Grid>
				</Stack>
			</StyleContainer>
		</>
	)
}
const FarmRenderer: FC<{ farmDetail: IFarmForUser }> = ({ farmDetail }) => {
	const handleOpenMap = (
		e: React.MouseEvent,
		coordinateX: number,
		coordinateY: number
	) => {
		e.stopPropagation()
		if (coordinateX && coordinateY) {
			const mapUrl = `https://www.google.com/maps?q=${coordinateX},${coordinateY}`
			window.open(mapUrl, '_blank', 'noopener,noreferrer')
		}
	}
	return (
		<Stack className='farm_details'>
			<Stack direction='row' alignItems='center' columnGap={1}>
				<Typography className='font_size_14 font_weight_600'>
					Farm Address:
				</Typography>
				<Stack
					flexDirection={'row'}
					alignItems='center'
					gap={theme.spacing(0.5)}>
					<FmdGoodOutlined color='error' sx={{ width: theme.spacing(2) }} />
					<Typography
						className='font_size_14 first_letter_capitalize'
						sx={{
							cursor: 'pointer',
							'&:hover': {
								textDecoration: 'underline',
							},
						}}
						onClick={(e) =>
							handleOpenMap(
								e,
								farmDetail?.farmLocation?.x,
								farmDetail?.farmLocation?.y
							)
						}>
						{farmDetail?.landmark} ({farmDetail?.fieldSize}{' '}
						{farmDetail?.fieldSizeUnit})
					</Typography>
				</Stack>
			</Stack>
			<ImageGrid imageList={farmDetail?.farmImages ?? []} componentSize={40} />
			<Stack>
				{farmDetail?.farmCrops?.map((crop) => (
					<Stack key={crop?.id} className='farm_crops'>
						<TagComponent
							label='Farm Crop'
							value={`${crop?.cropName} (${getFormattedDate(
								crop?.createdAt,
								dateFormats.dd_MM_yyyy
							)})`}
						/>
						<TagComponent
							label='Stage'
							value={crop.cropStage.replace(/[-_]/g, ' ')}
						/>
					</Stack>
				))}
			</Stack>
		</Stack>
	)
}
const TagComponent: FC<{
	label: string
	value: string | number
}> = ({ label, value }) => {
	return (
		<Stack className='tag_component'>
			<Typography className='font_size_14 font_weight_600'>{label}:</Typography>
			<Typography className='font_size_14 first_letter_capitalize'>
				{value}
			</Typography>
		</Stack>
	)
}
const StyleContainer = styled(Stack)(({ theme }) => ({
	width: '100%',
	'.header': {
		width: '100%',
		flexDirection: 'row-reverse',
		borderBottom: `1px solid ${theme.palette.divider}`,
		padding: theme.spacing(1.5),
	},
	'.detail': {
		padding: theme.spacing(2.5),
	},
	'.user-details': {
		display: 'flex',
		flexDirection: 'row',
		gap: theme.spacing(2),
		alignItems: 'center',
	},
	'.user-avatar': {
		width: theme.spacing(11),
		height: theme.spacing(11),
	},
	'.user-info': {
		width: '100%',
		display: ' flex',
		flexDirection: 'row',
		alignItems: 'center',
		justifyContent: 'space-between',
		gap: theme.spacing(0.5),
	},
	'.user-contact-info': {
		display: 'flex',
		alignItems: 'center',
		gap: theme.spacing(1),
		marginTop: theme.spacing(1),
		fontWeight: 100,
		color: theme.palette.text.secondary,
	},
	'.farm_detail_section': {
		paddingTop: theme.spacing(3),
		gap: theme.spacing(2),
		'.farm_list': {
			rowGap: theme.spacing(3),
			paddingLeft: theme.spacing(3.25),
		},
		'.farm_details': {
			gap: theme.spacing(2),
			'.farm_crops': {
				gap: theme.spacing(1),
				paddingLeft: theme.spacing(3.25),
			},
		},
	},
	'.font_size_14': {
		fontSize: theme.typography.subtitle2.fontSize,
	},
	'.font_weight_600': {
		fontWeight: theme.typography.caption.fontWeight,
	},
	'.tag_component': {
		flexDirection: 'row',
		columnGap: theme.spacing(1),
		alignItems: 'center',
	},
	'.custom-icon': {
		fontSize: theme.spacing(2.25),
	},
	'.custom-contact-typography': {
		fontSize: theme.spacing(1.75),
		fontWeight: 500,
		marginTop: theme.spacing(0.35),
		lineHeight: 0,
	},
	'.button-gray': {
		textTransform: 'capitalize',
		borderColor: theme.palette.custom.grey[300],
		color: theme.palette.text.primary,
	},
	'.button-red': {
		textTransform: 'capitalize',
		borderColor: theme.palette.custom.grey[300],
		color: theme.palette.custom.red[700],
	},
	'.button-green': {
		textTransform: 'capitalize',
		borderColor: theme.palette.custom.grey[300],
		color: theme.palette.custom.green[900],
	},
	'button-red-filled': {
		textTransform: 'capitalize',
	},
}))
