import { authAxios } from '@/contexts'
import { Button, Stack, styled, useTheme } from '@mui/material'
import { useMutation, useQueryClient } from '@tanstack/react-query'
import { FieldError, useForm } from 'react-hook-form'
import { addNoEntityUserSchema, TAddNoEntityUser } from './schema'
import { yupResolver } from '@hookform/resolvers/yup'
import { FC, useCallback } from 'react'
import { toast } from 'react-toastify'
import { LoadingButton } from '@mui/lab'
import { CustomProfileElement } from '@/components/CustomProfileElement'
import { AxiosError } from 'axios'
import { CustomTextField } from '@/utils/components'
import { showAxiosErrorToast } from '@/utils/helper'
import { OrganizationUser } from '@/utils/constant'

const initialValues = {
	name: '',
	email: '',
	profileImage: null,
}

type TProps = {
	setIsActionInfoDrawer: React.Dispatch<React.SetStateAction<boolean>>
	type: string
}

const getApiUrl = (type: string) => {
	switch (type) {
		case OrganizationUser.admin:
			return ''
		case OrganizationUser.circonomy_employee:
			return '/circonomy-employee'
		case OrganizationUser.compliance_manager:
			return '/compliance-manager'
		case OrganizationUser.ceres_auditor:
			return '/ceres-auditor'
		default:
			throw new Error('Invalid user type')
	}
}

export const AddNoEntityUser: FC<TProps> = ({
	setIsActionInfoDrawer,
	type,
}) => {
	const theme = useTheme()
	const queryClient = useQueryClient()
	const {
		handleSubmit,
		watch,
		setValue,
		register,
		formState: { errors },
		clearErrors,
	} = useForm<TAddNoEntityUser>({
		defaultValues: initialValues,
		mode: 'all',
		resolver: yupResolver<TAddNoEntityUser>(addNoEntityUserSchema),
	})

	const apiUrl = getApiUrl(type)

	const addNoEntityUserMutation = useMutation({
		mutationKey: ['addNoEntityUser'],
		mutationFn: async (payload: TAddNoEntityUser) => {
			const { profileImage, ...rest } = payload
			const body = {
				...rest,

				profileImageId: profileImage?.id ?? null,
			}
			return await authAxios.post(apiUrl, body)
		},
		onSuccess: ({ data }) => {
			toast(data?.message)
			setIsActionInfoDrawer(false)
			queryClient.invalidateQueries({
				queryKey: ['OrganizationSettingUsers'],
			})
		},
		onError: (error: AxiosError) => {
			showAxiosErrorToast(error)
		},
	})

	const handleAddNoEntityUser = useCallback(
		async (values: TAddNoEntityUser) => {
			addNoEntityUserMutation.mutate(values)
		},
		[addNoEntityUserMutation]
	)

	return (
		<StyledStack>
			<CustomProfileElement
				value={watch('profileImage')}
				errorMessage={errors?.profileImage?.id?.message}
				setValue={(id, url) =>
					setValue('profileImage', {
						id,
						url,
					})
				}
				clearErrors={() => clearErrors('profileImage.id')}
			/>
			<CustomTextField
				schema={addNoEntityUserSchema}
				fullWidth
				id='name'
				type='text'
				label='Enter Name '
				variant='outlined'
				error={!!errors.name?.message}
				helperText={(errors?.name as FieldError)?.message}
				{...register('name')}
			/>

			<CustomTextField
				schema={addNoEntityUserSchema}
				id='email'
				label='Enter Email'
				autoComplete='off'
				variant='outlined'
				type='email'
				error={!!errors.email?.message}
				helperText={(errors?.email as FieldError)?.message}
				fullWidth
				inputProps={{
					form: {
						autocomplete: 'off',
					},
				}}
				{...register('email')}
			/>

			<Stack
				direction='row'
				justifyContent='space-between'
				className='buttonContainer'>
				<Button
					onClick={() => setIsActionInfoDrawer(false)}
					sx={{ border: `1px solid ${theme.palette.primary.main}` }}>
					Cancel
				</Button>
				<LoadingButton
					loading={addNoEntityUserMutation.isPending}
					disabled={addNoEntityUserMutation.isPending}
					onClick={handleSubmit(handleAddNoEntityUser)}
					variant='contained'>
					Add
				</LoadingButton>
			</Stack>
		</StyledStack>
	)
}

const StyledStack = styled(Stack)(({ theme }) => ({
	gap: theme.spacing(4),
}))
