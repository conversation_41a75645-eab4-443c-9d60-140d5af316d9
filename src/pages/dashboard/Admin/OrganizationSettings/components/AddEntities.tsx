import { IconButton, Stack, styled, Typography } from '@mui/material'
import { AddEntityForm } from './AddEntityForm'
import { EntityEnum } from '@/utils/constant'
import { AddCSinkManager } from '@/components'
import { Close } from '@mui/icons-material'

type AddEntityProps = {
	setIsActionInfoDrawer: React.Dispatch<React.SetStateAction<boolean>>
	modalType: string
}

const showTitle: { [key: string]: string } = {
	company: 'Add Company',
	artisanPro: 'Add Artisan Pro',
	cSinkNetwork: 'Add Csink Network',
}

export const AddEntities = ({
	setIsActionInfoDrawer,
	modalType,
}: AddEntityProps) => {
	return (
		<StyleContainer>
			<Stack className='header'>
				<Stack
					direction='row'
					spacing={1}
					alignItems='center'
					width='100%'
					justifyContent='space-between'>
					<Typography variant='h4'>{showTitle[modalType]}</Typography>
					<IconButton onClick={() => setIsActionInfoDrawer(false)}>
						<Close />
					</IconButton>
				</Stack>
			</Stack>
			<Stack className='container'>
				{modalType == EntityEnum.company ? (
					<AddCSinkManager
						handleCloseDrawer={() => setIsActionInfoDrawer(false)}
						type={modalType}
						refetchEntities={true}
					/>
				) : (
					<AddEntityForm
						handleCloseDrawer={() => setIsActionInfoDrawer(false)}
						formType={modalType as EntityEnum}
					/>
				)}
			</Stack>
		</StyleContainer>
	)
}

const StyleContainer = styled(Stack)(({ theme }) => ({
	width: '100%',
	'.header': {
		width: '100%',
		flexDirection: 'column',
		justifyContent: 'space-between',
		alignItems: 'center',
		padding: theme.spacing(2.5, 2),
		borderBottom: `${theme.spacing(0.125)} solid ${
			theme.palette.neutral['100']
		}`,
	},
	'.container': {
		width: '100%',
		textAlign: 'center',
		padding: theme.spacing(3),
		gap: theme.spacing(4),
		'.formcontrol': {
			gap: theme.spacing(0.6),

			'.label': {
				color: theme.palette.neutral[500],
			},
		},
		'.buttonContainer': {
			gap: theme.spacing(2),
			button: {
				width: theme.spacing(30),
				height: theme.spacing(4.5),
				padding: theme.spacing(1, 2.5),
			},
		},
	},
}))
