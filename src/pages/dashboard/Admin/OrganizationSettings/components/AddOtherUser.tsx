import { authAxios, useAuthContext } from '@/contexts'
import { OrganizationUser, userRoles } from '@/utils/constant'
import {
	Autocomplete,
	Box,
	Button,
	FormControl,
	FormHelperText,
	MenuItem,
	Stack,
	styled,
	Typography,
	useTheme,
} from '@mui/material'
import { useMutation, useQuery, useQueryClient } from '@tanstack/react-query'
import {
	FieldError,
	FieldErrors,
	useForm,
	UseFormClearErrors,
	UseFormRegister,
	UseFormSetValue,
	UseFormWatch,
} from 'react-hook-form'
import { addOtherUser, TAddOtherUser } from './schema'
import { yupResolver } from '@hookform/resolvers/yup'
import { PhoneInputComponent } from '@/components/PhoneInput'
import { FC, useCallback, useEffect, useState } from 'react'
import { toast } from 'react-toastify'
import { LoadingButton } from '@mui/lab'
import { CustomProfileElement } from '@/components/CustomProfileElement'
import { CustomTextField } from '@/utils/components'
import { handleImageUpload, showAxiosErrorToast } from '@/utils/helper'
import { AxiosError } from 'axios'
import { ICsink, INetwork, TArtisanProsResponse } from '@/interfaces'
import { MultipleFileUploader } from '@/components/MultipleFileUploader'
import { TwoColumnLayout } from '@/components'
import { Add, CreateOutlined } from '@mui/icons-material'
import { IUserContext } from '@/contexts/Auth/type'

const initialValues = {
	id: '',
	name: '',
	email: '',
	phone: null,
	countryCode: null,
	profileImage: {},
	trained: true,
	trainingImages: [],
	aadhaarNumber: '',
	aadhaarImage: '',
}
type Option = {
	label: string
	value: string
}
interface FetchInputFieldProps {
	userDetails: IUserContext | undefined
	errors: FieldErrors<TAddOtherUser>
	watch: UseFormWatch<TAddOtherUser>
	setValue: UseFormSetValue<TAddOtherUser>
	clearErrors: UseFormClearErrors<TAddOtherUser>
	fetchCsinkManager: { data?: Option[] }
	fetchArtisanPro: { data?: Option[] }
	fetchCsinkNetworks: { data?: Option[] }
}

type TProps = {
	setIsActionInfoDrawer: React.Dispatch<React.SetStateAction<boolean>>
	type?: string
}
const findSelectedOption = (
	options: Array<{ label: string; value: string }>,
	id: string
) => {
	return options.find((option) => option.value === id) || null
}

const fetchArtisanProFunction = () => {
	const queryParams = new URLSearchParams({
		limit: '1000',
		page: '0',
	})
	return authAxios.get<TArtisanProsResponse>(
		`/new/artisan-pros?${queryParams.toString()}`
	)
}

const fetchCsinkManagerFunction = () => {
	const queryParams = new URLSearchParams({
		limit: '1000',
		page: '0',
	})
	return authAxios.get<{
		csinkManagers: { id: string; name: string; shortName: string }[]
	}>(`/csink-manager?${queryParams.toString()}`)
}

const fetchCsinkNetworksFunction = () => {
	const queryParams = new URLSearchParams({
		limit: '1000',
		page: '0',
	})
	return authAxios.get<ICsink>(`/new/csink-network?${queryParams.toString()}`)
}

const addCSinkNetworkFn = async (payload: TAddOtherUser) => {
	const { id, name, email, phone, countryCode, profileImage } = payload
	const body = {
		name,
		email,
		phoneNumber: phone || null,
		countryCode: phone ? countryCode : null,
		profileImageId: profileImage?.id ?? null,
	}
	return await authAxios.post(`csink-manager/${id}/unassigned-user`, body)
}

const addArtisanProAdminFn = async (values: TAddOtherUser) => {
	const { name, email, phone, countryCode, profileImage, id } = values
	const body = {
		name,
		email,
		phoneNo: phone || null,
		countryCode: phone ? countryCode : null,
		artisianProId: id,
		profileImageId: profileImage?.id ?? null,
	}
	return await authAxios.post(`/new/artisan-pro/manager`, body)
}

const addArtisanProOperatorFn = async (payload: TAddOtherUser) => {
	const {
		id,
		profileImage,
		trainingImages,
		phone,
		countryCode,
		aadhaarImage,
		aadhaarNumber,
		...rest
	} = payload

	const body = {
		...rest,
		phoneNo: phone || null,
		countryCode: phone ? countryCode : null,
		profileImageId: profileImage?.id ?? null,
		aadhaarImageID: aadhaarImage || null,
		aadhaarNumber: aadhaarNumber || null,
		trainingImages: trainingImages?.map(({ id }) => id) ?? [],
	}

	const { status } = await authAxios.post(
		`/artisian-pro/${id}/artisian-pro-operator/create`,
		body
	)
	return { status }
}

const addCsinkNetworkAdminFn = async (values: TAddOtherUser) => {
	const { name, email, phone, countryCode, profileImage, id } = values
	const body = {
		name,
		email,
		phoneNo: phone || null,
		countryCode: phone ? countryCode : null,
		profileImageId: profileImage?.id ?? null,
		networkId: id,
	}
	return await authAxios.post(`/cs-network-manager`, body)
}

const addCsinkNetworkOperatorFn = async (payload: TAddOtherUser) => {
	const {
		id,
		profileImage,
		trainingImages,
		phone,
		countryCode,
		aadhaarImage,
		aadhaarNumber,
		...rest
	} = payload

	const body = {
		...rest,
		number: phone || null,
		countryCode: phone ? countryCode : null,
		profileImageId: profileImage?.id ?? null,
		aadhaarImageID: aadhaarImage || null,
		aadhaarNumber: aadhaarNumber || null,
		trainingImageIds: trainingImages?.map(({ id }) => id) ?? [],
	}

	const { status } = await authAxios.post(`/cs-network/${id}/operator`, body)
	return { status }
}

const FetchInputField = ({
	userDetails,
	errors,
	watch,
	setValue,
	clearErrors,
	fetchCsinkManager,
	fetchArtisanPro,
	fetchCsinkNetworks,
}: FetchInputFieldProps) => {
	if (!userDetails) return null

	const autocompleteConfigMap: Record<
		string,
		{ data?: Option[]; label: string }
	> = {
		[userRoles.Admin]: {
			data: fetchCsinkManager.data,
			label: 'Select Company',
		},
		[userRoles.ArtisanPro]: {
			data: fetchArtisanPro.data,
			label: 'Select Artisan Pro',
		},
		[userRoles.cSinkNetwork]: {
			data: fetchCsinkNetworks.data,
			label: 'Select CSink Network',
		},
		// to implement manager funcitonality
	}

	const config = autocompleteConfigMap[userDetails.accountType]

	if (config) {
		return (
			<Autocomplete
				options={config.data || []}
				value={findSelectedOption(config.data || [], watch('id'))}
				onChange={(_, newValue) => {
					setValue('id', newValue?.value || '')
					clearErrors('id')
				}}
				renderInput={(params) => (
					<CustomTextField
						{...params}
						schema={addOtherUser}
						label={config.label}
						name='id'
						error={!!errors.id?.message}
						helperText={errors?.id?.message}
					/>
				)}
			/>
		)
	}

	if (userDetails.accountType === userRoles.CsinkManager) {
		return (
			<CustomTextField
				label='Select Company'
				name='id'
				schema={addOtherUser}
				disabled
				value={userDetails.csinkManagerName}
				error={!!errors.id?.message}
				helperText={errors?.id?.message}
			/>
		)
	}

	return null
}

export const AddOtherUser: FC<TProps> = ({ setIsActionInfoDrawer, type }) => {
	const theme = useTheme()
	const { userDetails } = useAuthContext()
	const [showIdentificationProof, setShowIdentificationProof] =
		useState<boolean>(false)
	const [urls, setUrls] = useState({
		aadhaarImage: '',
	})

	const queryClient = useQueryClient()
	const {
		handleSubmit,
		watch,
		setValue,
		register,
		formState: { errors },
		clearErrors,
	} = useForm<TAddOtherUser>({
		defaultValues: initialValues,
		mode: 'all',
		context: {
			isSupervisor: type === OrganizationUser.supervisor,
		},

		resolver: yupResolver(addOtherUser, {}),
	})
	const uploadAadhaarImage = useCallback(
		async (event: React.ChangeEvent<HTMLInputElement>) => {
			const file = event.target.files?.[0]
			if (!file) return

			try {
				const data = await handleImageUpload(file)
				setValue('aadhaarImage', data.id)
				setUrls((prev) => ({ ...prev, aadhaarImage: data.url }))
				clearErrors('aadhaarImage')
			} catch (err) {
				toast('Aadhaar image upload failed')
			}
		},
		[setValue, clearErrors]
	)

	const fetchArtisanPro = useQuery({
		queryKey: ['artisanProForAddUser'],
		queryFn: fetchArtisanProFunction,
		select: ({ data }) =>
			data?.artisanPros?.map((item) => ({
				label: `${item.name} (${item.shortCode})`,
				value: item.id,
			})),
		enabled:
			!!userDetails?.accountType &&
			[userRoles.ArtisanPro, userRoles.Manager].includes(
				userDetails?.accountType as userRoles
			),
	})

	const fetchCsinkManager = useQuery({
		queryKey: ['allCsinkManager'],
		queryFn: fetchCsinkManagerFunction,
		select: ({ data }) =>
			data?.csinkManagers?.map((item) => ({
				label: `${item.name} (${item.shortName})`,
				value: item.id,
			})),
		enabled:
			!!userDetails?.accountType &&
			userDetails?.accountType === userRoles.Admin,
	})
	const fetchCsinkNetworks = useQuery({
		queryKey: ['cSinkNetworkForAddUser'],
		queryFn: fetchCsinkNetworksFunction,
		select: ({ data }) =>
			data?.network?.map(
				(item: INetwork): { label: string; value: string } => ({
					label: `${item.name} (${item.shortName})`,
					value: item.id,
				})
			),
		enabled:
			!!userDetails?.accountType &&
			[userRoles.cSinkNetwork, userRoles.Manager].includes(
				userDetails?.accountType as userRoles
			),
	})
	const handleOnChange = (value: string, dialCode: string) => {
		setValue('countryCode', `+${dialCode}`)
		setValue('phone', value)
	}

	const addCSinkNetworkMutate = useMutation({
		mutationKey: ['addOtherUser'],
		mutationFn: addCSinkNetworkFn,
		onSuccess: ({ data }) => {
			toast(data?.message)
			setIsActionInfoDrawer(false)
			queryClient.invalidateQueries({ queryKey: ['OrganizationSettingUsers'] })
		},
		onError: (error: AxiosError) => showAxiosErrorToast(error),
	})

	const addArtisanProAdminMutation = useMutation({
		mutationKey: ['addArtisanProAdmin'],
		mutationFn: addArtisanProAdminFn,
		onSuccess: ({ data }) => {
			toast(data?.message)
			setIsActionInfoDrawer(false)
			queryClient.invalidateQueries({ queryKey: ['OrganizationSettingUsers'] })
		},
		onError: (error: AxiosError) => showAxiosErrorToast(error),
	})

	const addArtisanProOperatorMutation = useMutation({
		mutationKey: ['addArtisanProOperator'],
		mutationFn: addArtisanProOperatorFn,
		onSuccess: () => {
			toast('Operator added successfully')
			setIsActionInfoDrawer(false)
			queryClient.invalidateQueries({ queryKey: ['OrganizationSettingUsers'] })
		},
		onError: (error: AxiosError) => showAxiosErrorToast(error),
	})

	const addCsinkNetworkAdminMutation = useMutation({
		mutationKey: ['addCsinkNetworkAdmin'],
		mutationFn: addCsinkNetworkAdminFn,
		onSuccess: ({ data }) => {
			toast(data?.message)
			setIsActionInfoDrawer(false)
			queryClient.invalidateQueries({ queryKey: ['OrganizationSettingUsers'] })
		},
		onError: (error: AxiosError) => showAxiosErrorToast(error),
	})

	const addCsinkNetworkOperatorMutation = useMutation({
		mutationKey: ['addCsinkNetworkOperator'],
		mutationFn: addCsinkNetworkOperatorFn,
		onSuccess: () => {
			toast('Operator added successfully')
			setIsActionInfoDrawer(false)
			queryClient.invalidateQueries({ queryKey: ['OrganizationSettingUsers'] })
		},
		onError: (error: AxiosError) => showAxiosErrorToast(error),
	})

	const handleAddOtherUser = useCallback(
		async (values: TAddOtherUser) => {
			if (userDetails?.accountType === userRoles.ArtisanPro) {
				if (type === 'supervisor') {
					addArtisanProOperatorMutation.mutate(values)
				} else {
					addArtisanProAdminMutation.mutate(values)
				}
			} else if (userDetails?.accountType === userRoles.cSinkNetwork) {
				if (type === 'supervisor') {
					addCsinkNetworkOperatorMutation.mutate(values)
				} else {
					addCsinkNetworkAdminMutation.mutate(values)
				}
			} else {
				addCSinkNetworkMutate.mutate(values)
			}
		},
		[
			userDetails?.accountType,
			type,
			addArtisanProAdminMutation,
			addArtisanProOperatorMutation,
			addCSinkNetworkMutate,
			addCsinkNetworkOperatorMutation,
			addCsinkNetworkAdminMutation,
		]
	)

	const isSubmitting =
		addCSinkNetworkMutate.isPending ||
		addArtisanProAdminMutation.isPending ||
		addArtisanProOperatorMutation.isPending ||
		addCsinkNetworkAdminMutation.isPending ||
		addCsinkNetworkOperatorMutation.isPending

	useEffect(() => {
		if (userDetails?.accountType !== userRoles.CsinkManager) return
		setValue('id', userDetails?.csinkManagerId)
	}, [setValue, userDetails?.accountType, userDetails?.csinkManagerId])

	return (
		<StyledStack>
			<CustomProfileElement
				value={watch('profileImage')}
				errorMessage={errors?.profileImage?.id?.message}
				setValue={(id, url) =>
					setValue('profileImage', {
						id,
						url,
					})
				}
				clearErrors={() => clearErrors('profileImage')}
			/>

			<FetchInputField
				userDetails={userDetails}
				errors={errors}
				watch={watch}
				setValue={setValue}
				clearErrors={clearErrors}
				fetchCsinkManager={fetchCsinkManager}
				fetchArtisanPro={fetchArtisanPro}
				fetchCsinkNetworks={fetchCsinkNetworks}
			/>

			<CustomTextField
				schema={addOtherUser}
				fullWidth
				id='name'
				type='text'
				label='Enter Name '
				variant='outlined'
				error={!!errors.name?.message}
				helperText={(errors?.name as FieldError)?.message}
				{...register('name')}
			/>

			<CustomTextField
				schema={addOtherUser}
				id='email'
				label='Enter Email'
				autoComplete='off'
				variant='outlined'
				type='email'
				error={!!errors.email?.message}
				helperText={(errors?.email as FieldError)?.message}
				fullWidth
				inputProps={{
					form: {
						autocomplete: 'off',
					},
				}}
				{...register('email')}
			/>
			<FormControl>
				<PhoneInputComponent
					value={watch('phone') ?? ''}
					handleOnChange={handleOnChange}
					dialCode={watch('countryCode')}
					getSelectedCountryDialCode={(dialCode) =>
						setValue('countryCode', dialCode)
					}
				/>
				<FormHelperText error={Boolean(errors?.phone)}>
					{errors?.phone && (
						<Typography color='error' variant='caption'>
							{(errors?.phone as FieldError)?.message}
						</Typography>
					)}
				</FormHelperText>
			</FormControl>
			{type === 'supervisor' && (
				<>
					<FormControl fullWidth>
						<CustomTextField
							select
							label='Trained'
							id='select-type'
							value={true}
							disabled>
							<MenuItem value='true'>Yes</MenuItem>
						</CustomTextField>
					</FormControl>

					<Stack>
						<Stack rowGap={2} width='100%'>
							<MultipleFileUploader
								heading='Upload or Drag the Training Document'
								sx={{
									height: { xs: 100, md: 166 },
									width: '100%',
								}}
								imageHeight={100}
								setUploadData={(data) => {
									setValue('trainingImages', data)
									clearErrors('trainingImages')
								}}
							/>
						</Stack>
						<FormHelperText error={Boolean(errors.trainingImages)}>
							{errors?.trainingImages?.message}
						</FormHelperText>
					</Stack>
					<IdentificationProofSection
						show={showIdentificationProof}
						setShow={setShowIdentificationProof}
						aadhaarNumberError={errors.aadhaarNumber}
						aadhaarImageError={errors.aadhaarImage}
						url={urls?.aadhaarImage}
						onUpload={uploadAadhaarImage}
						register={register}
					/>
				</>
			)}

			<Stack
				direction='row'
				justifyContent='space-between'
				className='buttonContainer'>
				<Button
					onClick={() => setIsActionInfoDrawer(false)}
					sx={{ border: ` 1px solid ${theme.palette.primary.main}` }}>
					Cancel
				</Button>{' '}
				<LoadingButton
					loading={isSubmitting}
					disabled={isSubmitting}
					onClick={handleSubmit(handleAddOtherUser)}
					variant='contained'>
					Add
				</LoadingButton>
			</Stack>
		</StyledStack>
	)
}

interface IdentificationProofSectionProps {
	show: boolean
	setShow: (show: boolean) => void
	aadhaarNumberError?: FieldError
	aadhaarImageError?: FieldError
	url?: string
	onUpload: (e: React.ChangeEvent<HTMLInputElement>) => void
	register: UseFormRegister<TAddOtherUser>
}

export const IdentificationProofSection: React.FC<
	IdentificationProofSectionProps
> = ({
	show,
	setShow,
	aadhaarNumberError,
	aadhaarImageError,
	url,
	onUpload,
	register,
}) => {
	if (!show) {
		return (
			<TwoColumnLayout
				gridBreakpoints={[0, 6]}
				left={
					<Button
						variant='text'
						className='show-subcolumns-btn'
						startIcon={<Add color='primary' />}
						onClick={() => setShow(true)}>
						Add Identification Proof
					</Button>
				}
				right={<></>}
			/>
		)
	}

	return (
		<>
			<CustomTextField
				schema={addOtherUser}
				id='identification'
				label='Identification No.'
				type='number'
				placeholder='Enter Your Identification Number'
				variant='outlined'
				fullWidth
				{...register('aadhaarNumber')}
				error={!!aadhaarNumberError}
				helperText={aadhaarNumberError?.message}
			/>
			<Stack>
				<Typography fontWeight='bold'> Upload ID Proof</Typography>
				<label htmlFor='inputAadhar'>
					<StyledBox>
						<CreateOutlined
							fontSize='medium'
							color='inherit'
							sx={{ position: 'absolute' }}
						/>
						{url && (
							<Box
								component='img'
								src={url}
								height='90%'
								alt='Upload ID Proof'
							/>
						)}
						<input
							id='inputAadhar'
							type='file'
							accept='.jpg, .jpeg, .png, .heic, .webp'
							style={{ display: 'none' }}
							onChange={onUpload}
						/>
					</StyledBox>
				</label>
				<FormHelperText error={!!aadhaarImageError}>
					{aadhaarImageError?.message && (
						<Typography color='error'>{aadhaarImageError.message}</Typography>
					)}
				</FormHelperText>
			</Stack>
		</>
	)
}

const StyledStack = styled(Stack)(({ theme }) => ({
	gap: theme.spacing(4),
}))
const StyledBox = styled(Box)(({ theme }) => ({
	position: 'relative',
	display: 'flex',
	justifyContent: 'center',
	alignItems: 'center',
	borderRadius: theme.spacing(1),
	height: theme.spacing(15.5),
	border: '1px dashed grey',
	marginTop: '16px',
}))
