import {
	ActionInformationDrawer,
	AddEntity,
	AddUser,
	CustomCard,
	CustomHeader,
	QueryInput,
} from '@/components'
import FmdGoodOutlinedIcon from '@mui/icons-material/FmdGoodOutlined'
import { AddRounded, Close, EmailOutlined, Search } from '@mui/icons-material'
import LocalPhoneOutlinedIcon from '@mui/icons-material/LocalPhoneOutlined'
import { useCallback, useMemo, useRef, useState } from 'react'
import {
	Avatar,
	Box,
	Button,
	Chip,
	ClickAwayListener,
	Dialog,
	DialogActions,
	DialogContent,
	DialogTitle,
	IconButton,
	MenuItem,
	MenuList,
	Paper,
	Popper,
	Stack,
	styled,
	Tooltip,
	Typography,
} from '@mui/material'
import { useAuthContext } from '@/contexts'
import { entitiesRoles } from '@/utils/constant'
import { theme } from '@/lib/theme/theme'
import { EntityEnum, userRoles } from '@/utils/constant'
import { AddEntities, UsersList } from './components'
import EntitiesList from './components/EntitiesList'
import { EntityUsersList } from './components/EntitieUsersList'
import { useOrganizationSettings } from './useOrganizationSettings'
import { UnassignedUser, User } from '@/interfaces'

import { AddOrganisationUser } from './components/AddOrganisationUser'
import { UserDetailView } from './components/UserDetailView'
import { getGoogleMapLink } from '@/utils/helper'
import { Confirmation } from '@/components/Confirmation'
import { AddBiomassReference } from '@/components/AddBiomassReference'
import { useNavigate, useSearchParams } from 'react-router-dom'

const formatAccountName = (accountType: string): string => {
	return accountType
		.split('_')
		.map((word) => word.charAt(0).toUpperCase() + word.slice(1))
		.join(' ')
}

export const OrganizationSettings = () => {
	const {
		usersList,
		entityUsers,
		totalUsersCount,
		showAddUser,
		entityUsersCount,
		setShowAddUser,
		entitiesList,
		totalEntitiesCount,
		cSinkNetworkCount,
		showAddEntity,
		setShowAddEntity,
		selectedEntity,
		setSelectedEntity,
		showAssignUserDialog,
		setShowAssignUserDialog,
		unassignedUsersList,
		selectedUserForAssign,
		setSelectedUserForAssign,
		handleAssignUser,
		userFiltersOption,
		isEntitiesLoading,
		isUsersLoading,
		isEntityUsersLoading,
		showAllUsersList,
		handleSuspendEntity,
		showEditEntities,
		setShowEditEntities,
		editEntityId,
		fetchEntities,
		canEditOrDeleteUser,
		showEditUser,
		setShowEditUser,
		handleRemoveUser,
		isUserDetailInfoDrawer,
		setIsUserDetailInfoDrawer,
		showConfirmationDialog,
		setShowConfirmationDialog,
		showBiomassReferenceDialog,
		setShowBiomassReferenceDialog,
		showAddFarmer,
		setShowAddFarmer,
	} = useOrganizationSettings()

	const [selectedUser, setSelectedUser] = useState<User | null>(null)
	const handleOpenDetails = (user: User) => {
		setSelectedUser(user)
		setIsUserDetailInfoDrawer(true)
	}
	const handleDeleteUser = (user: User | null) => {
		if (user) {
			setSelectedUser(user)
			setShowConfirmationDialog(true)
		}
	}
	return (
		<>
			{showEditEntities && (
				<ActionInformationDrawer
					open={!!showEditEntities}
					onClose={() => setShowEditEntities(null)}
					anchor='right'
					component={
						<AddEntity
							handleCloseDrawer={() => {
								setShowEditEntities(null)
								fetchEntities.refetch()
							}}
							editMode
							cSinkId={editEntityId}
							apsId={editEntityId}
							cSinkNetworkId={editEntityId}
							type={showEditEntities as entitiesRoles}
						/>
					}
				/>
			)}
			{showEditUser && (
				<ActionInformationDrawer
					open={!!showEditUser}
					onClose={() => setShowEditUser(null)}
					anchor='right'
					component={
						<AddUser
							setIsActionInfoDrawer={() => setShowEditUser(null)}
							userData={showEditUser}
							modalType={'edit'}
						/>
					}
				/>
			)}
			<Confirmation
				confirmationText={
					<Typography>
						Are you sure you want to remove this{' '}
						{formatAccountName(selectedUser?.accountType ?? '')}?
					</Typography>
				}
				open={showConfirmationDialog}
				handleClose={() => setShowConfirmationDialog(false)}
				handleNoClick={() => setShowConfirmationDialog(false)}
				handleYesClick={() => handleRemoveUser(selectedUser)}
			/>

			<ActionInformationDrawer
				open={isUserDetailInfoDrawer}
				onClose={() => setIsUserDetailInfoDrawer(false)}
				anchor='right'
				component={
					<UserDetailView
						closeDrawer={() => setIsUserDetailInfoDrawer(false)}
						selectedUser={selectedUser}
						setShowEditUser={setShowEditUser}
						setShowConfirmationDialog={setShowConfirmationDialog}
					/>
				}
			/>
			<ActionInformationDrawer
				open={showAddUser}
				onClose={() => setShowAddUser(false)}
				anchor='right'
				component={
					<AddOrganisationUser
						setIsActionInfoDrawer={() => {
							setShowAddUser(false)
						}}
						isCsinkNetworkFarmer={showAddFarmer}
						// modalType={'add'}
						// addingTrainingImages
					/>
				}
			/>
			<ActionInformationDrawer
				open={showAddEntity}
				onClose={() => setShowAddEntity(false)}
				anchor='right'
				component={
					<AddEntities
						setIsActionInfoDrawer={setShowAddEntity}
						modalType={selectedEntity}
					/>
				}
			/>
			{showBiomassReferenceDialog && (
				<ActionInformationDrawer
					open={!!showBiomassReferenceDialog}
					onClose={() => setShowBiomassReferenceDialog(null)}
					anchor='right'
					component={
						<AddBiomassReference
							id={showBiomassReferenceDialog?.id ?? ''}
							networkType={showBiomassReferenceDialog.type}
							onClose={() => setShowBiomassReferenceDialog(null)}
							showAddBiomassReferenceButton={false}
						/>
					}
				/>
			)}
			{showAssignUserDialog ? (
				<AssignUserDialog
					open={showAssignUserDialog}
					onClose={() => setShowAssignUserDialog(false)}
					unassignedUsersList={unassignedUsersList}
					selectedUserForAssign={selectedUserForAssign}
					setSelectedUserForAssign={setSelectedUserForAssign}
					handleAssignUser={handleAssignUser}
				/>
			) : null}
			<StyledContainer>
				<Box className='header'>
					<CustomHeader
						showBottomBorder={true}
						headingComponent={
							<Typography variant='h3' sx={{ fontSize: theme.spacing(3) }}>
								My Networks
							</Typography>
						}
						showButton={false}
					/>
				</Box>
				<Box className='user-role-details-header'>
					<CustomCard
						headerComponent={
							<HeaderComponent
								setShowAddUser={setShowAddUser}
								setShowAddFarmer={setShowAddFarmer}
								setShowAddEntity={setShowAddEntity}
								setSelectedEntity={setSelectedEntity}
								cSinkNetworkCount={cSinkNetworkCount}
							/>
						}
					/>
				</Box>

				<Box
					className='entities-user-box'
					height={'75vh'} // According to the required Design
					display={'flex'}
					flexDirection={'row'}>
					<Box className='entities-box-left'>
						<EntitiesList
							setShowEditEntities={setShowEditEntities}
							handleSuspendEntity={handleSuspendEntity}
							isEntitiesLoading={isEntitiesLoading}
							entitiesList={entitiesList}
							totalEntitiesCount={totalEntitiesCount}
						/>
					</Box>
					<Box className='user-box-right'>
						{showAllUsersList ? (
							<UsersList
								isUsersLoading={isUsersLoading}
								userFiltersOption={userFiltersOption}
								usersList={usersList}
								totalUsersCount={totalUsersCount}
								canEditOrDeleteUser={canEditOrDeleteUser}
								setShowEditUser={setShowEditUser}
								handleOpenDetails={handleOpenDetails}
								handleDeleteUser={handleDeleteUser}
							/>
						) : (
							<EntityUsersList
								handleOpenDetails={handleOpenDetails}
								isEntityUsersLoading={isEntityUsersLoading}
								setShowAssignUserDialog={setShowAssignUserDialog}
								entityUsers={entityUsers}
								totalEntityUsersCount={entityUsersCount}
								canEditOrDeleteUser={canEditOrDeleteUser}
								setShowEditUser={setShowEditUser}
								handleDeleteUser={handleDeleteUser}
								setShowBiomassReferenceDialog={setShowBiomassReferenceDialog}
							/>
						)}
					</Box>
				</Box>
			</StyledContainer>
		</>
	)
}

type HeaderComponentType = {
	setShowAddUser: React.Dispatch<React.SetStateAction<boolean>>
	setShowAddFarmer: React.Dispatch<React.SetStateAction<boolean>>
	setShowAddEntity: React.Dispatch<React.SetStateAction<boolean>>
	setSelectedEntity: React.Dispatch<React.SetStateAction<string>>
	cSinkNetworkCount: number
}

const HeaderComponent = ({
	setShowAddUser,
	setShowAddFarmer,
	setShowAddEntity,
	setSelectedEntity,
	cSinkNetworkCount,
}: HeaderComponentType) => {
	const { userDetails } = useAuthContext()
	const [open, setOpen] = useState(false)
	const anchorRef = useRef<HTMLButtonElement | null>(null)

	const parseCoordinates = useCallback(
		(locationString: string): [string, string] => {
			if (locationString?.startsWith('(') && locationString?.endsWith(')')) {
				const coordinates = locationString?.slice(1, -1).split(',')
				if (coordinates?.length === 2) {
					return [coordinates[0]?.trim(), coordinates[1]?.trim()]
				}
			}
			return ['0', '0']
		},
		[]
	)

	const handleToggle = () => {
		setOpen((prev) => !prev)
	}

	const handleClose = (event: Event | React.SyntheticEvent) => {
		if (
			anchorRef.current &&
			anchorRef.current.contains(event.target as HTMLElement)
		) {
			return
		}
		setOpen(false)
	}

	const handleMenuItemClick = useCallback(
		(option: string) => {
			setShowAddEntity(true)
			setSelectedEntity(option)
			setOpen(false)
		},
		[setSelectedEntity, setShowAddEntity]
	)

	const HeaderDetailsBaseOnUserRole = () => {
		const content = useMemo(() => {
			switch (userDetails?.accountType) {
				case userRoles.Admin:
					return (
						<Stack direction='column' columnGap={1}>
							<Typography variant='body1' className='user-role-name'>
								Super Admin ({userDetails.name})
							</Typography>
						</Stack>
					)
				case userRoles.CsinkManager:
				case userRoles.Manager:
				case userRoles.ArtisanPro:
				case userRoles.cSinkNetwork: {
					const location: string[] = parseCoordinates(
						userDetails?.csinkManagerDetails?.location ?? '(0,0)'
					)
					const biomassValues =
						userDetails?.csinkManagerDetails?.biomassReference ?? []
					const hasBiomassValues = biomassValues.length > 0
					const firstBiomass = biomassValues[0]
					const additionalBiomassCount = biomassValues.length - 1
					return (
						<Stack direction='column' gap={theme.spacing(1)}>
							<Typography variant='body1' className='user-role-name'>
								{userDetails?.csinkManagerDetails?.name}
							</Typography>
							<Button
								component='a'
								href={getGoogleMapLink(location[0], location[1])}
								target='_blank'
								variant='text'
								startIcon={
									<FmdGoodOutlinedIcon sx={{ width: theme.spacing(2.5) }} />
								}
								sx={{
									p: 0,
									width: 'fit-content',
									color: 'black',
								}}>
								<Typography
									color={theme.palette.custom.grey[200]}
									pt={theme.spacing(0.1)}>
									{userDetails?.csinkManagerDetails?.locationName}
								</Typography>
							</Button>

							{hasBiomassValues && (
								<Stack
									direction='row'
									alignItems='center'
									gap={theme.spacing(1)}>
									<CustomCard
										sx={{
											width: 'fit-content',
											padding: theme.spacing(1, 2),
											borderRadius: theme.spacing(1.5),
										}}
										headerComponent={
											<Typography>
												{firstBiomass?.biomassName} ({firstBiomass?.fieldSize}{' '}
												{firstBiomass?.fieldSizeUnit} ={' '}
												{firstBiomass?.biomassQuantity} kgs)
											</Typography>
										}
									/>

									{additionalBiomassCount > 0 && (
										<Tooltip
											title={
												<Stack direction='column'>
													{biomassValues.slice(1).map((item) => (
														<Typography key={item?.id} variant='caption'>
															{item?.biomassName}
														</Typography>
													))}
												</Stack>
											}
											placement='bottom-start'
											arrow>
											<Box sx={{ cursor: 'pointer' }}>
												<Typography>
													+{additionalBiomassCount} values
												</Typography>
											</Box>
										</Tooltip>
									)}
								</Stack>
							)}
						</Stack>
					)
				}
				default:
					return <></>
			}
		}, [])

		return content
	}
	const canAddFarmer =
		(userDetails?.accountType === userRoles.Admin ||
			userDetails?.accountType === userRoles.CsinkManager) &&
		(cSinkNetworkCount ?? 0) > 0
	const menuItems = useMemo(
		() => [
			{
				label: 'Add Company',
				value: EntityEnum.company,
				show: userDetails?.accountType === userRoles.Admin,
				onClick: () => handleMenuItemClick(EntityEnum.company),
			},
			{
				label: 'Add Artisan Pro',
				value: EntityEnum.artisanPro,
				show:
					userDetails?.accountType === userRoles.Admin ||
					userDetails?.accountType === userRoles.CsinkManager,
				onClick: () => handleMenuItemClick(EntityEnum.artisanPro),
			},
			{
				label: 'Add CSink Network',
				value: EntityEnum.cSinkNetwork,
				show:
					userDetails?.accountType === userRoles.Admin ||
					userDetails?.accountType === userRoles.CsinkManager,
				onClick: () => handleMenuItemClick(EntityEnum.cSinkNetwork),
			},
			{
				label: 'Add Users',
				value: 'add_user',
				show: true,
				onClick: () => setShowAddUser(true),
			},
			{
				label: 'Add CSink Network Farmer',
				value: 'add_cSinkNetwork_farmer',
				show: canAddFarmer,
				onClick: () => {
					setShowAddFarmer(true)
					setShowAddUser(true)
				},
			},
		],
		[
			userDetails?.accountType,
			handleMenuItemClick,
			setShowAddUser,
			setShowAddFarmer,
			canAddFarmer,
		]
	)

	return (
		<>
			<Stack className='user-role-content'>
				<HeaderDetailsBaseOnUserRole />
				<Stack className='filters-buttons-list'>
					<Button
						ref={anchorRef}
						onClick={handleToggle}
						sx={{ p: 0, borderRadius: 4.5 }}>
						<Chip
							icon={<AddRounded />}
							label='Add'
							clickable
							color='primary'
							size='small'
							sx={{
								padding: theme.spacing(2),
								borderRadius: 4.5,
								fontWeight: 500,
							}}
						/>
					</Button>
				</Stack>
			</Stack>
			<Popper
				open={open}
				anchorEl={anchorRef.current}
				placement='bottom-start'
				style={{ zIndex: 1000, paddingTop: theme.spacing(1) }}>
				<ClickAwayListener onClickAway={handleClose}>
					<Paper elevation={3}>
						<MenuList>
							{menuItems
								?.filter((item) => item?.show)
								?.map((item) => (
									<MenuItem key={item?.label} onClick={item?.onClick}>
										{item?.label}
									</MenuItem>
								))}
						</MenuList>
					</Paper>
				</ClickAwayListener>
			</Popper>
		</>
	)
}

interface AssignUserDialogProps {
	open: boolean
	onClose: () => void
	unassignedUsersList: UnassignedUser[]
	selectedUserForAssign: string | null
	setSelectedUserForAssign: (id: string) => void
	handleAssignUser: () => void
}

const AssignUserDialog: React.FC<AssignUserDialogProps> = ({
	open,
	onClose,
	unassignedUsersList,
	selectedUserForAssign,
	setSelectedUserForAssign,
	handleAssignUser,
}) => {
	const [selectedSearchParams] = useSearchParams()
	const navigate = useNavigate()
	const handleClose = () => {
		selectedSearchParams.delete('userSearch')
		navigate(`?${selectedSearchParams.toString()}`, { replace: true })
		onClose()
	}

	return (
		<Dialog open={open} onClose={handleClose} fullWidth maxWidth='sm'>
			<DialogTitle textAlign='center'>
				<Stack direction='row' alignItems='center' sx={{ width: '100%' }}>
					<Box flex={1} />
					<Typography
						align='center'
						fontWeight={theme.typography.caption.fontWeight}
						fontSize={theme.typography.h6.fontSize}
						color={theme.palette.primary.main}>
						Assign User
					</Typography>
					<Box flex={1} display='flex' justifyContent='flex-end'>
						<IconButton onClick={handleClose}>
							<Close />
						</IconButton>
					</Box>
				</Stack>
				<Stack paddingTop={theme.spacing(2)}>
					<QueryInput
						queryKey='userSearch'
						name='search'
						placeholder='Search by number or name'
						className='search-textFiled'
						setPageOnSearch
						InputProps={{
							startAdornment: <Search fontSize='small' />,
						}}
					/>
				</Stack>
			</DialogTitle>

			<DialogContent>
				{unassignedUsersList?.length > 0 ? (
					<Stack
						direction='column'
						gap={theme.spacing(2)}
						padding={theme.spacing(1)}
						maxHeight={theme.spacing(50)}
						overflow='auto'>
						{unassignedUsersList.map((item) => (
							<StyledItemContainer
								selected={selectedUserForAssign === item.id}
								key={item.id}
								onClick={() => setSelectedUserForAssign(item.id)}>
								<Avatar
									sx={{
										width: theme.spacing(5),
										height: theme.spacing(5),
									}}
									src={item?.profileImage?.url}
								/>
								<Stack gap={theme.spacing(0.5)} width='100%'>
									<Typography
										variant='body1'
										fontWeight={theme.typography.caption.fontWeight}>
										{item.name}
									</Typography>
									<Stack
										direction='row'
										alignItems='center'
										gap={theme.spacing(2)}>
										{item.email && (
											<Stack
												direction='row'
												alignItems='center'
												gap={theme.spacing(0.25)}>
												<EmailOutlined
													sx={{
														width: theme.spacing(2),
														color: theme.palette.custom.grey[200],
													}}
												/>
												<Typography
													variant='subtitle1'
													color={theme.palette.custom.grey[200]}>
													{item.email}
												</Typography>
											</Stack>
										)}
										{item.phoneNumber && (
											<Stack
												direction='row'
												alignItems='center'
												gap={theme.spacing(0.25)}>
												<LocalPhoneOutlinedIcon
													sx={{
														width: theme.spacing(2),
														color: theme.palette.custom.grey[200],
													}}
												/>
												<Typography
													variant='subtitle1'
													color={theme.palette.custom.grey[200]}>
													{item.countryCode ? `(${item.countryCode}) ` : ''}
													{item.phoneNumber}
												</Typography>
											</Stack>
										)}
									</Stack>
								</Stack>
							</StyledItemContainer>
						))}
					</Stack>
				) : (
					<Typography textAlign={'center'}>No Users Found</Typography>
				)}
			</DialogContent>

			<DialogActions sx={{ px: theme.spacing(4), py: theme.spacing(2) }}>
				<Button variant='contained' fullWidth onClick={handleAssignUser}>
					Assign
				</Button>
			</DialogActions>
		</Dialog>
	)
}

const StyledContainer = styled(Stack)(({ theme }) => ({
	gap: theme.spacing(0.5),
	'.header': {
		padding: theme.spacing(1.5, 0),
		borderBottom: `${theme.spacing(0.125)} solid ${
			theme.palette.neutral['100']
		}`,
	},
	'.user-role-details-header': {
		padding: theme.spacing(1),
		'.user-role-content': {
			display: 'flex',
			flexDirection: 'row',
			justifyContent: 'space-between',
			'.user-role-name': {
				fontWeight: theme.typography.caption.fontWeight,
			},
			'.filters-buttons-list': {
				display: 'flex',
				flexDirection: 'row',
				gap: theme.spacing(2),
				height: theme.spacing(5),
			},
		},
	},
	'.entities-heading': {
		fontWeight: theme.typography.body2.fontWeight,
	},
	'.entities-user-box': {
		'.entities-box-left': {
			flex: 1,
			'.entities-box': {
				...scrollbarStyles,
				height: '80%',
			},
		},
		'.user-box-right': {
			flex: 1,
			'.user-box-entity-list': { ...scrollbarStyles, height: '90%' },
			'.user-box': { ...scrollbarStyles, height: '80%' },
		},
	},
	'.edit-button': {
		display: 'flex',
		flexDirection: 'row',
		alignItems: 'center',
		padding: theme.spacing(0.5, 2),
		gap: theme.spacing(2),
	},
	'.Suspend-delete-button': {
		display: 'flex',
		flexDirection: 'row',
		alignItems: 'center',
		color: theme.palette.primary.main,
		padding: theme.spacing(0.5, 2),
		borderRadius: theme.spacing(1),
		border: `${theme.spacing(0.1)} solid ${theme.palette.neutral['200']}`,
		gap: theme.spacing(1),
		backgroundColor: theme.palette.common.white,
	},
	'.Active-button': {
		display: 'flex',
		flexDirection: 'row',
		alignItems: 'center',
		color: theme.palette.custom.green[900],
		padding: theme.spacing(0.5, 2),
		borderRadius: theme.spacing(1),
		border: `${theme.spacing(0.1)} solid ${theme.palette.neutral['200']}`,
		gap: theme.spacing(1),
		backgroundColor: theme.palette.common.white,
	},
	'.entity-icon': {
		width: theme.spacing(4),
		height: theme.spacing(4),
		backgroundColor: theme.palette.custom.red[300],
		padding: theme.spacing(0.5),
		borderRadius: theme.spacing(1),
	},
	'.assign-user-icon': {
		display: 'flex',
		flexDirection: 'row',
		alignItems: 'center',
		color: theme.palette.primary.main,
		padding: theme.spacing(0.5, 2),
		borderRadius: theme.spacing(1),
		border: `${theme.spacing(0.1)} solid ${theme.palette.primary.main}`,
		gap: theme.spacing(1),
	},
	'.sticky-header': {
		display: 'flex',
		flexDirection: 'row',
		padding: `${theme.spacing(0, 1, 0, 1)}`,
		position: 'sticky',
		top: 0,
		zIndex: 1,
		backgroundColor: theme.palette.common.white,
	},
}))

const scrollbarStyles = {
	overflow: 'auto',
	// height: '80%',

	// WebKit browsers
	'&::-webkit-scrollbar': {
		width: '2px',
	},
	'&::-webkit-scrollbar-thumb': {
		backgroundColor: 'rgba(0, 0, 0, 0.4)',
		borderRadius: '2px',
	},
	'&::-webkit-scrollbar-track': {
		background: 'transparent',
	},

	// Firefox
	scrollbarWidth: 'thin',
	scrollbarColor: 'rgba(0, 0, 0, 0.4) transparent',

	// IE / Edge
	'-ms-overflow-style': 'auto',
}

interface StyledItemProps {
	selected: boolean
}

const StyledItemContainer = styled(Stack, {
	shouldForwardProp: (prop) => prop !== 'selected',
})<StyledItemProps>(({ theme, selected }) => ({
	flexDirection: 'row',
	alignItems: 'center',
	padding: theme.spacing(1),
	gap: theme.spacing(2),
	backgroundColor: selected ? theme.palette.action.selected : 'transparent',
	'&:hover': {
		backgroundColor: selected
			? theme.palette.action.selected
			: theme.palette.action.hover,
	},
}))
