import { authAxios, useAuthContext } from '@/contexts'
import {
	ActionData,
	BioCharTabsType,
	GetPendingTaskResponse,
	TBiocharActionData,
} from '@/interfaces'
import { Nullable, SitesResponse } from '@/types'
import {
	ActionTypes,
	AppType,
	defaultPage,
} from '@/utils/constant'
import { GridEventListener } from '@mui/x-data-grid'
import { useMutation, useQuery } from '@tanstack/react-query'
import { useCallback, useReducer, useState } from 'react'
import { useSearchParams } from 'react-router-dom'
import { toast } from 'react-toastify'

const simpleReducer = (
	prevValue: TBiocharActionData,
	nextValue: Partial<TBiocharActionData>
) => ({
	...prevValue,
	...nextValue,
})

const initialValues = {
	data: null,
	error: '',
	artisanProId: '',
	siteId: '',
	statusType: '',
	csinkNetworkId: '',
	kilnId: '',
}

export const usePendingTask = () => {
	const [searchParams, setSearchParams] = useSearchParams()
	const paramsLimit = searchParams.get('limit') || 100
	const paramsPage = searchParams.get('page') || defaultPage
	const paramsSiteId = searchParams.get('siteId') || ''
	const paramsKlinId = searchParams.get('klinId') || ''
	const paramsBiocharType =
		searchParams.get('biochar_type') || BioCharTabsType.biocharProduction
	const [dialogBiomass, setDialogBiomass] = useState<boolean>(false)
	const [dialogBiochar, setDialogBiochar] = useState<boolean>(false)

	const [actionData, setActionData] = useState<Nullable<ActionData>>(null)
	const [biocharActionData, setBioCharActionData] = useReducer(
		simpleReducer,
		initialValues
	)
	const [actionId, setActionId] = useState<string>('')
	const [actionType, setActionType] = useState<ActionTypes>(
		ActionTypes.ADD_PACKAGING_CSINK
	)
	const [appType, setAppType] = useState<AppType>(AppType.ArtisanProApp)

	const { userDetails } = useAuthContext()

	const [expanded, setExpanded] = useState<Nullable<number | false>>(null)

	const handleExpand =
		(panel: number) => (_: React.SyntheticEvent, newExpanded: boolean) => {
			setExpanded(newExpanded ? panel : false)
		}

	const actionTypesValue = (type: BioCharTabsType) => {
		switch (type) {
			case BioCharTabsType.biocharProduction:
				return `${ActionTypes.BIOMASS_COLLECTION},${ActionTypes.BATCH_CREATION},${ActionTypes.ADD_TEMPERATURE},${ActionTypes.END_PROCESS},${ActionTypes.ADD_PROCESS_DETAILS},${ActionTypes.UPDATE_PROCESS_BIOMASS_QUANTITY},${ActionTypes.ADD_BIOCHAR},${ActionTypes.ADD_BIOCHAR_CSINK},${ActionTypes.BATCH_CREATION_CSINK},${ActionTypes.BIOMASS_COLLECTION_CSINK},${ActionTypes.ADD_FARMER_FARM_CROP_CSINK},${ActionTypes.ADD_FARMER_FARM_CROP_ARTISAN},${ActionTypes.ADD_FPU},${ActionTypes.ADD_VEHICLE}`
			case BioCharTabsType.biocharApplication:
				return `${ActionTypes.ADD_PACKAGING_ARTISAN},${ActionTypes.ADD_PACKAGING_CSINK},${ActionTypes.ADD_MIXING_ARTISAN},${ActionTypes.ADD_MIXING_CSINK},${ActionTypes.ADD_DISTRIBUTION_FARMER_ARTISAN},${ActionTypes.ADD_DISTRIBUTION_FARMER_CSINK},${ActionTypes.ADD_DISTRIBUTION_OTHER_ARTISAN},${ActionTypes.ADD_DISTRIBUTION_OTHER_CSINK},${ActionTypes.REPLENISH_FARMER_DISTRIBUTION},${ActionTypes.REPLENISH_OTHER_DISTRIBUTION},${ActionTypes.ADD_SITE_APPLICATION_ARTISAN}`
			default:
				return ''
		}
	}

	const getPendingTasks = useQuery({
		queryKey: [
			'pendingTask',
			paramsLimit,
			paramsPage,
			paramsKlinId,
			paramsSiteId,
			paramsBiocharType,
		],
		queryFn: async () => {
			const queryParams = new URLSearchParams({
				limit: String(paramsLimit),
				page: String(paramsPage),
				siteId: paramsSiteId,
				klinId: paramsKlinId,
				actionTypes: actionTypesValue(paramsBiocharType as BioCharTabsType),
				status: `created,error,processed`,
			})
			const { data } = await authAxios.get<GetPendingTaskResponse>(
				`/new/dmrv-lite/actions/app-created?${queryParams.toString()}`
			)
			// console.log(data);
			return data
		},
	})

	const fetchSites = useQuery({
		queryKey: ['fetchSites'],
		queryFn: async () => {
			const { data } = await authAxios.get<SitesResponse>(
				`/new/dmrv-lite/sites`
			)

			return data
		},
		select: (data) => {
			return (data?.sites ?? []).map((item) => ({
				value: item?.id || '',
				label: item.name || '',
			}))
		},
	})

	const handleRowClick: GridEventListener<'rowClick'> = useCallback(
		(params) => {
			setActionId(params?.row?.id)
			setActionType(params?.row?.type)
			setAppType(params?.row?.appType)
			if (paramsBiocharType === BioCharTabsType.biocharProduction) {
				setBioCharActionData({
					data: JSON.parse(params?.row?.data),
					error: params?.row?.error,
					artisanProId: params?.row?.artisanProId,
					siteId: params?.row?.siteId,
					statusType: params?.row?.status,
					kilnId: params?.row?.kilnId,
					csinkNetworkId: params?.row?.csinkNetworkId,
				})
				// TODO: remove in the future
				if (
					[
						ActionTypes.BIOMASS_COLLECTION,
						ActionTypes.BIOMASS_COLLECTION_CSINK,
						ActionTypes.BATCH_CREATION,
						ActionTypes.BATCH_CREATION_CSINK,
						ActionTypes.ADD_TEMPERATURE,
						ActionTypes.ADD_BIOCHAR,
						ActionTypes.ADD_BIOCHAR_CSINK,
						ActionTypes.ADD_VEHICLE,
						ActionTypes.ADD_FPU,
					].includes(params?.row?.type)
				) {
					setDialogBiochar(true)
				}
			} else if (paramsBiocharType === BioCharTabsType.biocharApplication) {
				const parsedData = JSON.parse(params?.row?.data)
				const data = {
					...parsedData,
					packagingBag:
						parsedData?.packagingBag &&
						Object.keys(parsedData.packagingBag).length > 0
							? [parsedData.packagingBag]
							: [],
					error: params?.row?.error,
					csinkNetworkId: params?.row?.csinkNetworkId,
					statusType: params?.row?.status,
					artisanProId: params?.row?.artisanProId,
					...(params?.row?.type === ActionTypes.ADD_DISTRIBUTION_FARMER_CSINK ||
					params?.row?.type === ActionTypes.ADD_DISTRIBUTION_FARMER_ARTISAN
						? {
								openMixQty: parsedData?.openMixPackages?.[0]?.openMixQty,
								packagingTypeId:
									parsedData?.openMixPackages?.[0]?.packagingTypeId,
						  }
						: {}),
				}
				setActionData(data)
				if (
					![
						ActionTypes.REPLENISH_OTHER_DISTRIBUTION,
						ActionTypes.REPLENISH_FARMER_DISTRIBUTION,
					].includes(params?.row?.type)
				) {
					setDialogBiomass(true)
				}
			}
		},
		[paramsBiocharType]
	)

	const handleTabChange = useCallback(
		(value: string) => {
			setSearchParams(
				(urlParams) => {
					if (searchParams.has('page')) searchParams.delete('page')
					if (searchParams.has('limit')) searchParams.delete('limit')
					if (searchParams.has('klinId')) searchParams.delete('klinId')
					if (searchParams.has('siteId')) searchParams.delete('siteId')
					urlParams.set('biochar_type', value)
					return urlParams
				},
				{ replace: true }
			)
		},
		[searchParams, setSearchParams]
	)

	const useApproveAction = () => {
		return useMutation({
			mutationFn: async () => {
				const { data } = await authAxios.put(
					`/new/dmrv-lite/action/${actionId}/sync`
				)
				return data
			},
			onSuccess: (data) => {
				getPendingTasks.refetch() // Refetch pending tasks after success
				toast(data?.message || `Action successfully completed`)
			},
			onError: (error) => {
				toast(`Something went wrong`)
				console.error('Error processing action:', error)
			},
		})
	}

	return {
		getPendingTasks,
		paramsLimit,
		paramsPage,
		handleRowClick,
		actionData,
		setActionData,
		actionId,
		setActionId,
		fetchSites,
		actionType,
		setActionType,
		paramsBiocharType,
		handleTabChange,
		userDetails,
		dialogBiomass,
		setDialogBiomass,
		dialogBiochar,
		setDialogBiochar,
		biocharActionData,
		setBioCharActionData,
		appType,
		setAppType,
		expanded,
		setExpanded,
		handleExpand,
		useApproveAction,
	}
}
