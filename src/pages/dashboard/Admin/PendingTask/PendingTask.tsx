import {
	CustomHeader,
	CustomPagination,
	NoData,
	QueryAutoComplete,
} from '@/components'
import {
	Box,
	Chip,
	CircularProgress,
	Stack,
	styled,
	Typography,
} from '@mui/material'
import { GridColDef, GridValidRowModel } from '@mui/x-data-grid'
import { useMemo } from 'react'
import { usePendingTask } from './usePendingTask'
import { DetailsBiocharCollection, ViewPendingTaskDetails } from './components'
import { BioCharTabsType } from '@/interfaces'
import { ActionTypes } from '@/utils/constant'
import { theme } from '@/lib/theme/theme'
import CustomAccordian from './AccordianComponent/CustomAccordian'

const filterFieldForUser = ['biomass_aggregator', 'c_sink_manager', 'admin']
const chipList = [
	{ label: 'Biochar Production', value: BioCharTabsType.biocharProduction },
	{ label: 'Biochar Application', value: BioCharTabsType.biocharApplication },
]

export const PendingTask = () => {
	const {
		getPendingTasks,
		paramsLimit,
		paramsPage,
		handleRowClick,
		actionData,
		actionId,
		fetchSites,
		actionType,
		paramsBiocharType,
		userDetails,
		handleTabChange,
		dialogBiomass,
		setDialogBiomass,
		dialogBiochar,
		setDialogBiochar,
		biocharActionData,
		appType,
		useApproveAction,
	} = usePendingTask()

	const { mutate: handleDirectApproveButton } = useApproveAction()
	const columns: GridColDef<GridValidRowModel>[] = useMemo(
		() => [
			{
				field: 'id',
				headerName: 'S. No',
				flex: 0.5,
				minWidth: 80,
				renderCell: (params) => (
					<Typography variant='subtitle1'>
						{params.api.getRowIndexRelativeToVisibleRows(params.row.id) + 1}
					</Typography>
				),
			},
			{
				field: 'type',
				headerName: 'Action Type',
				flex: 1,
				renderCell: (params) => (
					<Typography variant='subtitle1'>
						{params?.value ? params?.value?.replace(/_/g, ' ') : '-'}
					</Typography>
				),
			},
			{
				field: 'appType',
				headerName: 'App Type',
				flex: 1,
			},
			{
				field: 'status',
				headerName: 'Status',
				flex: 1,
				renderCell: (params) =>
					params.row?.status ? (
						<Stack direction='row' width={'100%'} alignItems={'center'}>
							<Chip
								label={params?.row?.status}
								color='default'
								sx={{ textTransform: 'capitalize' }}
							/>
							{[
								ActionTypes.REPLENISH_FARMER_DISTRIBUTION,
								ActionTypes.REPLENISH_OTHER_DISTRIBUTION,
							].includes(params.row?.type) && (
								<Box
									className='approveBox'
									onClick={() => handleDirectApproveButton()}>
									<Typography color={theme.palette.success.main}>
										Approve
									</Typography>
								</Box>
							)}
						</Stack>
					) : null,
			},
		],
		[handleDirectApproveButton, paramsLimit, paramsPage]
	)

	const filterInitialValue = useMemo(() => {
		if (filterFieldForUser.includes(userDetails?.accountType ?? '')) {
			return userDetails?.biomassAggregatorId
		}
		return ''
	}, [userDetails])

	const totalData = getPendingTasks.data?.result

	return (
		<>
			{actionData !== null && dialogBiomass ? (
				<ViewPendingTaskDetails
					fetchSites={fetchSites}
					data={actionData}
					open={dialogBiomass}
					onClose={() => setDialogBiomass(false)}
					actionId={actionId}
					getPendingTasks={getPendingTasks}
					actionType={actionType}
				/>
			) : null}
			{biocharActionData?.data !== null && dialogBiochar ? (
				<DetailsBiocharCollection
					data={biocharActionData}
					open={dialogBiochar}
					onClose={() => setDialogBiochar(false)}
					actionId={actionId}
					getPendingTasks={getPendingTasks}
					actionType={actionType}
					appType={appType}
				/>
			) : null}
			<StyledContainer>
				<Box className='header'>
					<CustomHeader
						showBottomBorder={true}
						heading='Pending Task'
						showButton={false}
					/>
				</Box>
				<Stack className='middle-section custom-pad'>
					<Stack className='header-component' gap={2}>
						<Stack className='filter-Chips'>
							{chipList.map(({ label, value }) => {
								return (
									<Chip
										key={value}
										onClick={() => handleTabChange(value)}
										color={paramsBiocharType === value ? 'primary' : 'default'}
										label={label}
									/>
								)
							})}
						</Stack>
						<QueryAutoComplete
							options={fetchSites?.data ?? []}
							queryKey={'siteId'}
							label={'Site Name'}
							isDisable={false}
							initialValue={filterInitialValue ?? ''}
							filtersToReset={['klinId']}
							loading={fetchSites?.isLoading}
						/>
					</Stack>
				</Stack>
				<Box className='table-container custom-pad'>
					{getPendingTasks?.isFetching || getPendingTasks?.isPending ? (
						<Stack className='loading-section'>
							<CircularProgress size={40} />
						</Stack>
					) : getPendingTasks?.data?.result?.length === 0 ||
					  getPendingTasks?.data?.result === null ? (
						<Stack className='no-data-section'>
							<NoData />
						</Stack>
					) : (
						<Box>
							<CustomAccordian
								totalData={totalData}
								getPendingTasks={getPendingTasks}
								columns={columns}
								handleRowClick={handleRowClick}
							/>
						</Box>
					)}
				</Box>
				<Stack direction='row' alignItems='center' justifyContent='center'>
					{!!getPendingTasks?.data?.count && (
						<CustomPagination
							rowCount={getPendingTasks?.data?.count ?? 0}
							customDefaultLimit={100}
						/>
					)}
				</Stack>
			</StyledContainer>
		</>
	)
}

const StyledContainer = styled(Stack)(({ theme }) => ({
	padding: theme.spacing(4, 0),
	gap: theme.spacing(3),
	'.custom-pad': {
		padding: theme.spacing(0, 4),
	},
	'.header': {
		borderBottom: `${theme.spacing(0.125)} solid ${
			theme.palette.neutral['100']
		}`,
	},

	'.table-container': {
		gap: theme.spacing(2),
		padding: theme.spacing(1, 0, 2, 0),
		height: '64vh',
		overflow: 'auto',
		scrollBehavior: 'smooth',
		'::-webkit-scrollbar': {
			width: theme.spacing(1.5),
			display: 'block',
		},
		'::-webkit-scrollbar-thumb': {
			backgroundColor: theme.palette.grey['300'],
			borderRadius: theme.spacing(1),
			borderRight: 'none',
			borderLeft: 'none',
		},

		'::-webkit-scrollbar-track-piece:end': {
			marginBottom: '20px',
			background: 'transparent',
		},
		'::-webkit-scrollbar-track-piece:start': {
			marginTop: '20px',
			background: 'transparent',
		},
		'.loading-section': {
			height: '50vh',
			alignItems: 'center',
			justifyContent: 'center',
			padding: theme.spacing(0, 4),
		},
		'.no-data-section': {
			height: '50vh',
			alignItems: 'center',
			justifyContent: 'center',
		},
	},
	'.middle-section': {
		flexDirection: 'row',
		gap: theme.spacing(2),
		flexWrap: 'wrap',
		'.header-component': {
			flexWrap: 'wrap',
			alignItems: 'center',
			flexDirection: 'row',

			'.filter-Chips': {
				flexDirection: 'row',
				gap: theme.spacing(2),
				flexWrap: 'wrap',
			},
		},
	},
	'.approveBox': {
		flexGrow: 1,
		display: 'flex',
		justifyContent: 'center',
		cursor: 'pointer',
	},
}))
