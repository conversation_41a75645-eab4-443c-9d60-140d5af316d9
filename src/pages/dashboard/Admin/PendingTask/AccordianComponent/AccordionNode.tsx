import { CustomDataGrid } from "@/components";
import { Action, GetPendingTaskResponse } from "@/interfaces";
import { theme } from "@/lib/theme/theme";
import { ExpandMore } from "@mui/icons-material";
import { Accordion, AccordionDetails, AccordionSummary, Stack, Typography } from "@mui/material";
import { GridColDef, GridEventListener, GridValidRowModel } from "@mui/x-data-grid";
import { DefinedQueryObserverResult, QueryObserverLoadingErrorResult } from "@tanstack/react-query";
import { format } from "date-fns";
import { useState } from "react";

const monthNames = ["January", "February", "March", "April", "May", "June",
    "July", "August", "September", "October", "November", "December"];

enum DataType {
    String = 'string',
    Number = 'number',
    TimeStamp = 'timestamp',
    Month = 'month',
    Year = 'year',
    Array = 'array',
    Object = 'object'
}


type NestedDataStructure = {
    [key: string]: Action[] | NestedDataStructure;
};

interface AccordionNodeProps {
    data: Action[] | NestedDataStructure;
    label: string;
    level: number;
    index: number;
    getPendingTasks: DefinedQueryObserverResult<GetPendingTaskResponse, Error> | QueryObserverLoadingErrorResult<GetPendingTaskResponse, Error>;
    columns: GridColDef<GridValidRowModel>[];
    handleRowClick: GridEventListener<"rowClick">;
}

const AccordionNode: React.FC<AccordionNodeProps> = ({
    data,
    label,
    level,
    index,
    getPendingTasks,
    columns,
    handleRowClick
}) => {

    const [expanded, setExpanded] = useState<number | false>(false);

    const handleExpand = (panelIndex: number) => (event: React.SyntheticEvent, isExpanded: boolean) => {
        event.stopPropagation();
        setExpanded(isExpanded ? panelIndex : false);
    };

    const checkType = (value: Action[] | NestedDataStructure): DataType => {
        if (Array.isArray(value)) {
            return DataType.Array;
        }
        return DataType.Object;
    };

    const isAccordionContainsError = (item: Action[] | NestedDataStructure) => {
        if (Array.isArray(item)) {
            return item.some((action: Action) => action.status === 'error') ;
        } else {
            return Object.values(item).some(isAccordionContainsError);
        }
    }

    const currentDateInfo = () => {
        const currentDate = new Date();
        return {
            currentMonth: currentDate.getMonth(),
            currentYear: currentDate.getFullYear()
        };
    };

    const type = checkType(data);
    const { currentYear } = currentDateInfo();

    const getLabel = (label: string): string => {
        if (label.length < 3) {
            return `${monthNames[Number(label)]}, ${currentYear}`;
        } else if (label.length < 5) {
            return label;
        } else {
            return format(new Date(label), 'PPPP');
        }
    };

    return (
        <Accordion
            disableGutters
            elevation={2}
            key={`${label}-${index}-level-${level}`}
            expanded={expanded === index}
            onChange={handleExpand(index)}
        >
            <AccordionSummary
                expandIcon={<ExpandMore />}
                aria-controls='panel1-content'
                id='panel1-header'
            >
                <Stack direction="row" justifyContent="space-between" width="100%">
                    <Typography>{getLabel(label)}</Typography>
                </Stack>
                {isAccordionContainsError(data) && (
                <Typography color={theme.palette.error.main} mr={2}>
                    Error
                </Typography>
                )}
            </AccordionSummary>
            <AccordionDetails>
                {type === DataType.Array ? (
                    <CustomDataGrid
                        showPagination={false}
                        showPaginationDetails={false}
                        rows={data as Action[]}
                        columns={columns}
                        loading={getPendingTasks?.isFetching || getPendingTasks?.isPending}
                        onRowClick={handleRowClick}
                    />
                ) : (
                    Object.entries(data as NestedDataStructure).map(([key, value], idx) => (
                        <AccordionNode
                            key={`${key}-${idx}`}
                            data={value}
                            label={key}
                            level={level + 1}
                            index={idx}
                            getPendingTasks={getPendingTasks}
                            columns={columns}
                            handleRowClick={handleRowClick}
                        />
                    ))
                )}
            </AccordionDetails>
        </Accordion>
    );
};

export default AccordionNode;