import { Action, GetPendingTaskResponse } from "@/interfaces";
import { GridColDef, GridEventListener, GridValidRowModel } from "@mui/x-data-grid";
import { DefinedQueryObserverResult, QueryObserverLoadingErrorResult } from "@tanstack/react-query";
import AccordionNode from "./AccordionNode";


type NestedDataStructure = {
    [key: string]: Action[] | NestedDataStructure;
};

interface CustomAccordionProps {
    totalData: {
        appCreatedAt: Date;
        actions: Action[];
    }[] | undefined;
    getPendingTasks: DefinedQueryObserverResult<GetPendingTaskResponse, Error> |
    QueryObserverLoadingErrorResult<GetPendingTaskResponse, Error>;
    columns: GridColDef<GridValidRowModel>[];
    handleRowClick: GridEventListener<"rowClick">;
}

const CustomAccordion: React.FC<CustomAccordionProps> = ({
    totalData,
    getPendingTasks,
    columns,
    handleRowClick
}) => {
    const currentDateInfo = () => {
        const currentDate = new Date();
        return {
            currentMonth: currentDate.getMonth(),
            currentYear: currentDate.getFullYear()
        };
    };

    const getDateInfoFromString = (dateString: Date) => {
        const date = new Date(dateString);
        return {
            month: date.getMonth(),
            year: date.getFullYear(),
            timeStamp: date.toISOString()
        };
    };


    const modifiedData = totalData?.reduce((acc: NestedDataStructure, item) => {
        const { month, year, timeStamp } = getDateInfoFromString(item.appCreatedAt);
        const strMonth = `${month}`;
        const strYear = `${year}`;
        const strTimeStamp = `${timeStamp}`;
        const { currentMonth, currentYear } = currentDateInfo();

        if (year !== currentYear) {
            if (!acc[strYear]) acc[strYear] = {};
            const yearData = acc[strYear] as NestedDataStructure;
            if (!yearData[strMonth]) yearData[strMonth] = {};
            const monthData = yearData[strMonth] as NestedDataStructure;
            monthData[strTimeStamp] = item.actions;
        } else if (month !== currentMonth) {
            if (!acc[strMonth]) acc[strMonth] = {};
            const monthData = acc[strMonth] as NestedDataStructure;
            monthData[strTimeStamp] = item.actions;
        } else {
            acc[strTimeStamp] = item.actions;
        }

        return acc;
    }, {} as NestedDataStructure) ?? {} as NestedDataStructure;

    return (
        <>
            {Object.entries(modifiedData).map(([key, value], index) => (
                <AccordionNode
                    key={key}
                    data={value}
                    label={key}
                    level={0}
                    index={index}
                    getPendingTasks={getPendingTasks}
                    columns={columns}
                    handleRowClick={handleRowClick}
                />
            ))}
        </>
    );
};


export default CustomAccordion;