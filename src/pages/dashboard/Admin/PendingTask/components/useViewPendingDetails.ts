import { useSearchParams } from 'react-router-dom'
import { authAxios } from '@/contexts'
import {
	ActionData,
	ActionResponse,
	Bag,
	BiocharDataResponse,
	CropData,
	GetBuyerListResponse,
	getFarmerData,
	GetFarmerListResponse,
	GetPackagingTypeResponse,
	GetPendingTaskResponse,
	IBiomassData,
	IVehicle,
	KilnListResponse,
	StatusType,
	TArtisanProsResponse,
	Vehicle,
} from '@/interfaces'
import { ICrop } from '@/types'
import { useMutation, useQuery, UseQueryResult } from '@tanstack/react-query'
import { useCallback, useMemo } from 'react'
import { useForm, useFieldArray } from 'react-hook-form'
import { yupResolver } from '@hookform/resolvers/yup'
import { toast } from 'react-toastify'
import { actionSchema } from '../schema'
import { ActionTypes } from '@/utils/constant'
import { AxiosError } from 'axios'

type ViewPendingTaskDetailsHookProps = {
	data: ActionData
	actionId: string
	fetchSites: UseQueryResult<
		{
			value: string
			label: string
		}[],
		Error
	>
	getPendingTasks: UseQueryResult<GetPendingTaskResponse, Error>
	actionType: ActionTypes
	onClose: () => void
}

export type AutoCompleteOptionsKeys =
	| 'farmerIds'
	| 'packagingTypeId'
	| 'vehicleServerId'
	| 'mixTypeId'
	| 'fpuId'
	| 'portionType'
type Option = {
	value: string | undefined
	label: string | undefined
}

export const useViewPendingDetails = ({
	data,
	actionId,
	fetchSites,
	getPendingTasks,
	actionType,
	onClose,
}: ViewPendingTaskDetailsHookProps) => {
	const fetchBiomass = useQuery({
		queryKey: ['getBiomassTypes'],
		queryFn: () => {
			const queryParams = new URLSearchParams({
				limit: '10000000',
				page: '0',
			})

			return authAxios(`/crops?${queryParams.toString()}`)
		},
		select: ({ data }) => {
			const crops = data.crops
			return { crops }
		},
	})

	const fetchPackagingTypes = useQuery({
		queryKey: ['fetchPackagingTypes'],
		queryFn: async () => {
			const { data } = await authAxios.get<GetPackagingTypeResponse>(
				`/packaging-types`
			)
			return data
		},
		select: (data) => {
			return (data?.types ?? [])?.map((type) => ({
				value: type.id,
				label: type.name,
				type: type.type,
			}))
		},
	})

	const fetchBags = useQuery({
		queryKey: ['fetchBags'],
		queryFn: async () => {
			const { data } = await authAxios.get<{ count: number; bags: Bag[] }>(
				`/bags?limit=100000&page=0&klinId=${paramsKlinId}&siteId=${paramsSiteId}`
			)
			return data
		},
		select: (data) => {
			return (data?.bags ?? [])?.map((bag) => ({
				value: bag.id,
				label: bag.name,
				networkId: bag.artisanProId ? bag.artisanProId : bag.cSinkNetworkId,
				networkName: bag.artisanProName
					? bag.artisanProName
					: bag.cSinkNetworkName,
			}))
		},
	})

	const fetchVehcile = useQuery({
		queryKey: ['fetchVehcile'],
		queryFn: async () => {
			const { data } = await authAxios.get<{
				count: number
				vehicles: Vehicle[]
			}>(`/new/vehicles?limit=10000&page=0`)
			return data
		},
		select: (data) => {
			return (data?.vehicles ?? [])?.map((vehicle) => ({
				value: vehicle.id,
				label: vehicle.name,
			}))
		},
	})

	const fetchBuyers = useQuery({
		queryKey: ['fetchBuyers'],
		queryFn: async () => {
			const { data } = await authAxios.get<GetBuyerListResponse>(
				`/new/buyers?limit=10000&page=0`
			)

			return data
		},
		select: (data) => {
			return (data?.othersBuyers ?? [])?.map((buyer) => ({
				value: buyer.id,
				label: buyer.name,
			}))
		},
	})

	const fetchFarmers = useQuery({
		queryKey: ['fetchFarmers'],
		queryFn: async () => {
			const { data } = await authAxios.get<getFarmerData>(
				`/new/farmers?limit=10000&page=0`
			)
			return data
		},
		select: (data) => {
			return (data?.farmers ?? [])?.map((farmer) => ({
				id: farmer.id,
				label: farmer.name,
			}))
		},
	})

	const fetchCsinkFarmers = useQuery({
		queryKey: ['fetchCsinkFarmers', data?.csinkNetworkId],
		queryFn: async () => {
			const { data: responseData } = await authAxios.get<GetFarmerListResponse>(
				`/cs-network/${data?.csinkNetworkId}/farmers?limit=10000&page=0`
			)
			return responseData
		},
		select: (responseData) => {
			return (responseData?.farmers ?? [])?.map((farmer) => ({
				value: farmer.id,
				label: farmer.name,
			}))
		},
		enabled: !!data?.csinkNetworkId,
	})

	const fetchKilns = useQuery({
		queryKey: ['fetchKilns', data?.csinkNetworkId],
		queryFn: async () => {
			const { data: responseData } = await authAxios.get<KilnListResponse>(
				`/cs-network/${data?.csinkNetworkId}/kilns?&sendAllKilns=true`
			)
			return responseData
		},
		select: (responseData) => {
			return (responseData?.kilns ?? [])?.map((farmer) => ({
				value: farmer.id,
				label: farmer.name,
			}))
		},
		enabled: !!data?.csinkNetworkId,
	})

	const fetchVehicleType = useQuery({
		queryKey: ['fetchFarmers'],
		queryFn: async () => {
			const { data: responseData } = await authAxios.get<{
				count: number
				vehicles: IVehicle[]
			}>(`/cs-network/${data?.csinkNetworkId}/vehicles?limit=10000&page=0`)
			return responseData
		},
		select: (responseData) => {
			return (responseData?.vehicles ?? [])?.map((vehicle) => ({
				value: vehicle.id,
				label: vehicle.name,
			}))
		},
		enabled: !!data?.csinkNetworkId,
	})

	const fetchBiochar = useQuery({
		queryKey: ['fetchBiochar'],
		queryFn: async () => {
			const { data: responseData } = await authAxios.get<BiocharDataResponse>(
				`/site/${data?.siteId}/biochar?limit=10000&page=0`
			)
			return responseData
		},
		enabled: !!data?.siteId,
	})

	const fetchFpus = useQuery({
		queryKey: ['fetchFpus'],
		queryFn: async () => {
			const resp = await authAxios.get<IBiomassData>(
				`/artisian-pro/${data?.artisanProId}/site/${data?.siteId}/fpu?limit=1000&page=0`
			)
			return resp.data
		},
		select: (responseData) => {
			return (responseData?.fpu ?? [])?.map((fpu) => ({
				value: fpu.id,
				label: fpu.name,
			}))
		},
		enabled: Boolean(data?.artisanProId),
	})

	const fetchArtisanPro = useQuery({
		queryKey: ['fetchArtisanPro'],
		queryFn: () => {
			const queryParams = new URLSearchParams({
				limit: '1000',
				page: '0',
			})
			return authAxios.get<TArtisanProsResponse>(
				`/new/artisan-pros?${queryParams.toString()}`
			)
		},
		select: ({ data }) =>
			data?.artisanPros?.map((item) => ({
				value: item.id,
				label: item.name,
			})),
		enabled: Boolean(data?.artisanProId),
	})

	const handleDefaultValues = useCallback(
		({
			actionType,
			data,
		}: {
			actionType: ActionTypes
			data: ActionResponse
		}) => {
			const commonValues = {
				description: data?.description || '',
				imageIds: data?.imageIds || [],
				videoIds: data?.videoIds || [],
				artisanProId: data?.artisanProId || '',
			}
			switch (actionType) {
				case ActionTypes.ADD_MIXING_ARTISAN:
					return {
						biocharQuantity: data?.biocharQuantity || 0,
						otherMixQuantity: data?.otherMixQuantity || 0,
						otherMixQuantityUnit: data?.otherMixQuantityUnit || '',
						waterQuantityAdded: data?.waterQuantityAdded || 0,
						biocharOnlyBags: data?.biocharOnlyBags || [],
						packagingTypeId: data?.packagingTypeId || '',
						cropId: data?.cropId || '',
						...(data?.siteId
							? {
									siteId: data?.siteId || '',
							  }
							: {}),
						...commonValues,
					}
				case ActionTypes.ADD_PACKAGING_ARTISAN:
					return {
						packagingQuantity: data?.packagingQuantity || 0,
						bags: data?.bags || [],
						packagingTypeId: data?.packagingTypeId || '',
						...(data?.siteId
							? {
									siteId: data?.siteId || '',
							  }
							: {}),
						...commonValues,
					}
				case ActionTypes.ADD_DISTRIBUTION_OTHER_ARTISAN:
					return {
						VehicleType: data?.VehicleType || '',
						vehicleId: data?.vehicleId || '',
						fuelValue: data?.fuelValue || 0,
						openMixPackages: data?.openMixPackages || [],
						isOpenDistribution: data?.isOpenDistribution || false,
						packagingBags: data?.packagingBags || [],
						buyerId: data?.buyerId || '',
						...commonValues,
					}
				case ActionTypes.ADD_DISTRIBUTION_FARMER_ARTISAN:
					return {
						VehicleType: data?.VehicleType || '',
						vehicleId: data?.vehicleId || '',
						vehicleServerId: data?.vehicleServerId || '',
						fuelValue: data?.fuelValue || 0,
						openMixPackages: data?.openMixPackages || [],
						openMixQty: data?.openMixPackages[0]?.openMixQty || 0,
						packagingTypeId: data?.openMixPackages?.[0]?.packagingTypeId || '',
						isOpenDistribution: data?.isOpenDistribution || false,
						distributionBags: data?.distributionBags || [],
						buyerId: data?.buyerId || '',
						...(data?.siteId
							? {
									siteId: data?.siteId || '',
							  }
							: {}),
						farmerIds: data?.farmerIds || [],
						...commonValues,
					}
				case ActionTypes.ADD_MIXING_CSINK:
					return {
						biocharQuantity: data?.biocharQuantity || 0,
						otherMixQuantity: data?.otherMixQuantity || 0,
						otherMixQuantityUnit: data?.otherMixQuantityUnit || '',
						waterQuantityAdded: data?.waterQuantityAdded || 0,
						biocharOnlyBags: data?.biocharOnlyBags || [],
						packagingTypeId: data?.packagingTypeId || '',
						cropId: data?.cropId || '',
						kilnId: data?.kilnId || '',
						...commonValues,
					}
				case ActionTypes.ADD_PACKAGING_CSINK:
					return {
						packagingQuantity: data?.packagingQuantity || 0,
						bags: data?.bags || [],
						packagingTypeId: data?.packagingTypeId || '',
						kilnId: data?.kilnId || '',
						...commonValues,
					}
				case ActionTypes.ADD_DISTRIBUTION_FARMER_CSINK:
					return {
						VehicleType: data?.VehicleType || '',
						vehicleId: data?.vehicleId || '',
						vehicleServerId: data?.vehicleServerId || '',
						fuelValue: data?.fuelValue || 0,
						openMixPackages: data?.openMixPackages || [],
						openMixQty: data?.openMixQty,
						packagingTypeId: data?.packagingTypeId,
						isOpenDistribution: data?.isOpenDistribution || false,
						distributionBags: data?.distributionBags || [],
						buyerId: data?.buyerId || '',
						kilnId: data?.kilnId || '',
						farmerIds: data?.farmerIds || [],
						...commonValues,
					}
				case ActionTypes.ADD_SITE_APPLICATION_ARTISAN:
					return {
						fpuId: data?.fpuId || '',
						mixTypeId: data?.mixTypeId || '',
						mixQuantity: data?.mixQuantity || 0,
						packagingBag: data?.packagingBag || [],
						portionType: data?.portionType || '',
						artisanProId: data?.artisanProId || '',
						...(data?.siteId
							? {
									siteId: data?.siteId || '',
							  }
							: {}),
					}
				default:
					return {
						biocharOnlyBags: data?.biocharOnlyBags || [],
						packagingTypeId: data?.packagingTypeId || '',
						cropId: data?.cropId || '',
						...(data?.siteId
							? {
									siteId: data?.siteId || '',
							  }
							: {}),
						...commonValues,
					}
			}
		},
		[]
	)

	const getBagFieldName = (actionType: ActionTypes) => {
		switch (actionType) {
			case ActionTypes.ADD_MIXING_ARTISAN:
				return 'biocharOnlyBags'
			case ActionTypes.ADD_PACKAGING_ARTISAN:
				return 'bags'
			case ActionTypes.ADD_PACKAGING_CSINK:
				return 'bags'
			case ActionTypes.ADD_DISTRIBUTION_OTHER_ARTISAN:
				return 'packagingBags'
			case ActionTypes.ADD_SITE_APPLICATION_ARTISAN:
				return 'packagingBag'
			case ActionTypes.ADD_DISTRIBUTION_FARMER_CSINK:
			case ActionTypes.ADD_DISTRIBUTION_FARMER_ARTISAN:
				return 'distributionBags'
			default:
				return 'biocharOnlyBags'
		}
	}

	const bagFieldName = getBagFieldName(actionType)

	const [searchParams] = useSearchParams()
	const paramsSiteId = searchParams.get('siteId') || ''
	const paramsKlinId = searchParams.get('klinId') || ''

	const { control, handleSubmit, setValue, formState } =
		useForm<ActionResponse>({
			defaultValues: handleDefaultValues({ actionType, data }),
			mode: 'onChange',
			resolver: yupResolver<any>(actionSchema(actionType)),
		})

	const actionTypeHiddenFields = useMemo(() => {
		switch (actionType) {
			case ActionTypes.ADD_DISTRIBUTION_FARMER_CSINK:
				return [
					'imageIds',
					'videoIds',
					'biocharOnlyBags',
					'imageIDs',
					'bags',
					'vehcileId',
					'csinkNetworkId',
					'artisanProId',
					'buyerId',
					'farmerIds',
					'isOpenDistribution',
					'openMixPackages',
					'vehicleLocalId',
					'packagingBags',
					'distributionBags',
					'vehicleServerId',
					...(!data?.isOpenDistribution ? ['openMixQty'] : []),
					'fuelValue',
					'VehicleType',
					'packagingTypeId',
					'statusType',
					'error',
				]
			case ActionTypes.ADD_DISTRIBUTION_FARMER_ARTISAN:
				return [
					'imageIds',
					'videoIds',
					'biocharOnlyBags',
					'imageIDs',
					'bags',
					'vehicleId',
					'csinkNetworkId',
					'artisanProId',
					'buyerId',
					'farmerIds',
					'isOpenDistribution',
					...(!data?.vehicleServerId
						? ['vehicleServerId']
						: ['vehicleLocalId']),
					'packagingBags',
					'fuelValue',
					'VehicleType',
					'openMixPackages',
					'distributionBags',
					'statusType',
					'error',
				]
			case ActionTypes.ADD_SITE_APPLICATION_ARTISAN:
				return [
					'fpuId',
					'localApplicationId',
					'imageIds',
					'videoIds',
					'biocharOnlyBags',
					'isPackagingApplication',
					'imageIDs',
					'portionType',
					'bags',
					'vehcileId',
					'csinkNetworkId',
					'artisanProId',
					'buyerId',
					'farmerIds',
					'isOpenDistribution',
					...(!data?.fpuId ? ['fpuId'] : ['localFpuId']),
					'fuelValue',
					'VehicleType',
					'packagingBag',
					'openMixPackages',
					'distributionBags',
					'mixTypeId',
					'statusType',
					'error',
				]
			default:
				return [
					'imageIds',
					'videoIds',
					'biocharOnlyBags',
					'imageIDs',
					'bags',
					'vehcileId',
					'csinkNetworkId',
					'artisanProId',
					'buyerId',
					'farmerIds',
					'isOpenDistribution',
					'openMixPackages',
					'vehicleLocalId',
					'packagingBags',
					'distributionBags',
					'vehicleServerId',
					'statusType',
					'error',
				]
		}
	}, [actionType, data?.fpuId, data?.isOpenDistribution, data?.vehicleServerId])
	const filteredData = useMemo(
		() =>
			Object.entries(data)?.filter(
				([key]) => !actionTypeHiddenFields?.includes(key)
			),
		[data]
	)
	const autoCompleteDataFields = useMemo(() => {
		switch (actionType) {
			case ActionTypes.ADD_DISTRIBUTION_FARMER_CSINK:
				return Object.entries(data)
					.sort()
					?.filter(([key]) =>
						[
							'artisanProId',
							'farmerIds',
							...(data?.isOpenDistribution ? ['packagingTypeId'] : []),
							'vehicleServerId',
						].includes(key)
					)
			case ActionTypes.ADD_SITE_APPLICATION_ARTISAN:
				return Object.entries(data)
					.sort()
					?.filter(([key]) =>
						['artisanProId', 'mixTypeId', 'fpuId', 'portionType'].includes(key)
					)
			default:
				return Object.entries(data)?.filter(([key]) =>
					['artisanProId'].includes(key)
				)
		}
	}, [data, actionType])

	const informationData = useMemo<{
		totalNotAssessedBiocharQty?: number
		totalAvailableBiocharQty?: number | null
		availableBiocharQty?: CropData[] | null
	}>(() => {
		const infoData = fetchBiochar?.data
		const type =
			fetchPackagingTypes?.data?.find(
				(type) => type.value === data?.packagingTypeId
			)?.type || ''

		if (!infoData) return {}

		if (type === 'liquid') {
			return {
				totalAvailableBiocharQty: infoData.totalAvailableBiocharQty,
				totalNotAssessedBiocharQty: infoData.totalNotAssessedBiocharQty,
				availableBiocharQty: null,
			}
		} else if (type === 'solid') {
			return {
				totalAvailableBiocharQty: null,
				totalNotAssessedBiocharQty: infoData.totalNotAssessedBiocharQty,
				availableBiocharQty: infoData.availableBiocharQty.map(
					({ cropName, biocharQty }) => ({
						cropName,
						biocharQty,
					})
				),
			}
		}
		return {}
	}, [data?.packagingTypeId, fetchBiochar?.data, fetchPackagingTypes?.data])

	const newData = useMemo(
		() =>
			filteredData?.map(([key, value]) => {
				switch (key) {
					case 'cropId':
						return [
							key,
							fetchBiomass?.data?.crops?.find((i: ICrop) => i?.id === value)
								?.name || value,
						]
					case 'siteId':
						return [
							key,
							fetchSites?.data?.find((item) => item.value === value)?.label ||
								value,
						]
					case 'kilnId':
						return [
							key,
							(data?.csinkNetworkId ? fetchKilns : fetchSites)?.data?.find(
								(item) => item.value === value
							)?.label || value,
						]
					case 'packagingTypeId':
						return [
							key,
							fetchPackagingTypes?.data?.find((type) => type.value === value)
								?.label || value,
						]
					case 'vehicleServerId':
						return [
							'Vehicle',
							fetchVehcile?.data?.find((type) => type.value === value)?.label ||
								value,
						]
					case 'buyerId':
						return [
							key,
							fetchBuyers?.data?.find((type) => type.value === value)?.label ||
								value,
						]
					case 'farmers': {
						if (!Array.isArray(value)) {
							return [key, 'no Farmer']
						}
						const farmerIds = [
							...new Set(
								value
									.map((item) => (item as { farmerId?: string })?.farmerId)
									.filter(Boolean)
							),
						]

						return [
							key,
							farmerIds
								.map(
									(farmerId) =>
										fetchFarmers?.data?.find((farmer) => farmer.id == farmerId)
											?.label || ''
								)
								.join(', ') || '',
						]
					}

					default:
						return [key, value]
				}
			}),
		[
			filteredData,
			fetchBiomass?.data?.crops,
			fetchSites,
			data?.csinkNetworkId,
			fetchKilns,
			fetchPackagingTypes?.data,
			fetchVehcile?.data,
			fetchBuyers?.data,
			fetchFarmers?.data,
		]
	)
	const autocompleteData = useMemo(
		() =>
			autoCompleteDataFields?.map(([key, value]) => {
				switch (key) {
					case 'farmerIds':
						return [
							key,
							fetchCsinkFarmers?.data?.find((i) => i?.value === value)?.label ||
								value,
						]
					case 'packagingTypeId':
						return [
							key,
							fetchPackagingTypes?.data?.find((i) => i?.value === value)
								?.label || value,
						]
					case 'mixTypeId':
						return [
							key,
							fetchVehicleType?.data?.find((i) => i?.value === value)?.label ||
								value,
						]
					case 'fpuId':
						return [
							key,
							fetchFpus?.data?.find((i) => i?.value === value)?.label || value,
						]
					case 'vehicleServerId':
						return [
							key,
							fetchVehicleType?.data?.find((i) => i?.value === value)?.label ||
								value,
						]
					case 'artisanProId':
						return [
							key,
							fetchArtisanPro?.data?.find((i) => i?.value === value)?.label ||
								value,
						]
					default:
						return [key, value]
				}
			}),
		[
			autoCompleteDataFields,
			fetchArtisanPro?.data,
			fetchCsinkFarmers?.data,
			fetchFpus?.data,
			fetchPackagingTypes?.data,
			fetchVehicleType?.data,
		]
	)
	const portionTypes = useMemo(
		() => [
			{
				value: 'full',
				label: 'Full',
			},
			{
				value: 'half',
				label: 'Half',
			},
			{
				value: 'quarter',
				label: 'Quarter',
			},
		],
		[]
	)
	const autoCompleteOptions = useMemo(
		() => ({
			farmerIds: fetchCsinkFarmers?.data || [],
			packagingTypeId: fetchPackagingTypes?.data || [],
			vehicleServerId: fetchVehicleType?.data || [],
			mixTypeId: fetchPackagingTypes?.data || [],
			fpuId: fetchFpus?.data || [],
			siteId: fetchSites || [],
			artisanProId: fetchArtisanPro?.data || [],
			portionType: portionTypes,
		}),
		[
			fetchArtisanPro?.data,
			fetchCsinkFarmers?.data,
			fetchFpus?.data,
			fetchPackagingTypes?.data,
			fetchSites,
			fetchVehicleType?.data,
			portionTypes,
		]
	)

	const getOptions = (key: AutoCompleteOptionsKeys): Option[] => {
		return autoCompleteOptions[key]
	}

	const { fields, append, remove } = useFieldArray({
		control,
		name: bagFieldName,
	})
	const showBags = useMemo(() => {
		switch (actionType) {
			case ActionTypes.ADD_DISTRIBUTION_FARMER_CSINK:
				if (data?.isOpenDistribution) return false
				return true
			case ActionTypes.ADD_DISTRIBUTION_FARMER_ARTISAN:
				return true
			case ActionTypes.ADD_PACKAGING_ARTISAN:
				return true
			case ActionTypes.ADD_SITE_APPLICATION_ARTISAN:
				if (data?.packagingBag?.length || 0 > 0) return true
				return false
			default:
				true
		}
	}, [actionType, data?.isOpenDistribution, data?.packagingBag])

	const biocharOnlyBags = useMemo(
		() =>
			data?.biocharOnlyBags?.map((bag) => ({
				id:
					fetchBags?.data?.find((item) => item.value === bag.bagId)?.label ||
					bag.bagId,
				count: bag.count,
			})),
		[data?.biocharOnlyBags, fetchBags?.data]
	)

	// Form submission handler

	const generatePayload = useCallback(
		(formData: ActionData) => {
			switch (actionType) {
				case ActionTypes.ADD_DISTRIBUTION_FARMER_CSINK: {
					const { openMixQty, packagingTypeId, ...rest } = formData
					const payload = {
						...rest,
						openMixPackages: [
							{
								openMixQty: Number(openMixQty),
								packagingTypeId,
							},
						],
					}
					return payload
				}

				case ActionTypes.ADD_DISTRIBUTION_FARMER_ARTISAN: {
					const { openMixQty, packagingTypeId, ...rest } = formData
					const payload = {
						...rest,
						openMixPackages: [
							{
								openMixQty: Number(openMixQty),
								packagingTypeId,
							},
						],
					}
					return payload
				}

				case ActionTypes.ADD_SITE_APPLICATION_ARTISAN: {
					const { packagingBag, mixQuantity, portionType, ...rest } = formData
					const payload = {
						...rest,
						portionType:
							(mixQuantity || 0) > 0 || portionType == '' ? null : portionType,
						mixQuantity: (mixQuantity || 0) > 0 ? Number(mixQuantity) : null,
						packagingBag:
							(packagingBag?.length || 0) > 0 ? packagingBag?.[0] : {},
					}
					return payload
				}

				default:
					return formData
			}
		},
		[actionType]
	)

	const getLabelFromKey = (key: string): string => {
		const labelMap: Record<string, string> = {
			fpuId: 'Application Site',
			mixQuantity: 'Mixed Qty',
			mixTypeId: 'Mix Type Name',
			siteId: 'Site Name',
			portionType: 'Portion Type',
			artisanProId: 'Artisan pro name',
		}

		return labelMap[key] || key
	}

	const performAction = async ({
		formData,
		actionId,
		statusType,
		isDirty,
	}: {
		formData: ActionData
		actionId: string
		statusType: string | undefined
		isDirty: boolean
	}) => {
		const payload = generatePayload(formData)
		let apiUrl = ''
		let apiData = {}

		if (statusType === 'error' && !isDirty) {
			apiUrl = `/new/dmrv-lite/action/${actionId}/retry-sync`
		} else if (statusType === 'created' && !isDirty) {
			apiUrl = `/new/dmrv-lite/action/${actionId}/sync`
		} else {
			apiUrl = `/new/dmrv-lite/action/${actionId}`
			apiData = { data: JSON.stringify(payload), isApproved: false }
		}

		const { data: response } = await authAxios.put(apiUrl, apiData)
		return response
	}

	const mutation = useMutation({
		mutationFn: performAction,
		onSuccess: (response: any) => {
			onClose()
			toast(response?.message || 'Action successfully completed')
			getPendingTasks.refetch()
		},
		onError: (error: AxiosError<{ error: string }>) => {
			onClose()
			getPendingTasks.refetch()
			toast(error.response?.data?.error || 'Something went wrong')
		},
	})

	const onSubmit = (formData: ActionData) => {
		mutation.mutate({
			formData,
			actionId,
			statusType: data?.statusType,
			isDirty: formState.isDirty,
		})
	}

	const getButtonLabel = () => {
		if (data?.statusType === StatusType.PROCESSED) return StatusType.PROCESSED
		if (data?.statusType === StatusType.ERROR && !formState.isDirty)
			return StatusType.ERROR
		if (data?.statusType === StatusType.CREATED && !formState.isDirty)
			return StatusType.CREATED
		return StatusType.PENDING
	}

	return {
		control,
		handleSubmit,
		setValue,
		formState,
		onSubmit,
		biocharOnlyBags,
		fields,
		append,
		remove,
		newData,
		searchParams,
		fetchBags,
		autocompleteData,
		autoCompleteOptions,
		showBags,
		getOptions,
		bagFieldName,
		getButtonLabel,
		informationData,
		getLabelFromKey,
	}
}
