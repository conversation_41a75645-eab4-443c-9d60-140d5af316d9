import {
	ActionData,
	bagList,
	GetPendingTaskResponse,
	StatusType,
} from '@/interfaces'
import { TModal } from '@/types'
import { Add, RemoveCircleOutline } from '@mui/icons-material'
import {
	Dialog,
	DialogContent,
	DialogTitle,
	IconButton,
	Stack,
	TextField,
	Button,
	FormHelperText,
	Autocomplete,
	DialogActions,
	Typography,
	MenuItem,
	Box,
} from '@mui/material'
import { UseQueryResult } from '@tanstack/react-query'
import React from 'react'
import { Controller } from 'react-hook-form'
import { ActionTypes } from '@/utils/constant'
import {
	AutoCompleteOptionsKeys,
	useViewPendingDetails,
} from './useViewPendingDetails'
import BiocharSummary from './BiocharSummary'

type ViewPendingTaskDetailsProps = TModal & {
	data: ActionData
	actionId: string
	fetchSites: UseQueryResult<
		{
			value: string
			label: string
		}[],
		Error
	>
	getPendingTasks: UseQueryResult<GetPendingTaskResponse, Error>
	actionType: ActionTypes
}

export const ViewPendingTaskDetails: React.FC<ViewPendingTaskDetailsProps> = ({
	open,
	onClose,
	data,
	actionId,
	fetchSites,
	getPendingTasks,
	actionType,
}) => {
	const {
		control,
		handleSubmit,
		setValue,
		onSubmit,
		fields,
		append,
		remove,
		newData,
		fetchBags,
		showBags,
		autocompleteData,
		bagFieldName,
		getOptions,
		getButtonLabel,
		informationData,
		getLabelFromKey,
	} = useViewPendingDetails({
		data,
		actionId,
		fetchSites,
		getPendingTasks,
		actionType,
		onClose,
	})

	const statusBody = (status: string) => {
		switch (status) {
			case 'error':
				return {
					label: 'Error',
					color: 'error',
					name: 'Retry-sync',
				}
			case 'created':
				return {
					label: 'Created',
					color: 'green',
					name: 'Approve',
				}
			case 'processed':
				return {
					label: 'Processed',
					color: 'blue',
				}
			default:
				return {
					label: '',
					color: 'black',
					name: 'Save',
				}
		}
	}

	return (
		<Dialog open={open} onClose={onClose} fullWidth maxWidth='sm'>
			<DialogTitle variant='h6' textAlign='center'>
				<Stack
					flexDirection='row'
					justifyContent={'center'}
					alignItems={'center'}
					p={1}>
					<Typography variant='body2'>
						{actionType?.split('_').join(' ')}
					</Typography>
					<Stack
						flexDirection={'row'}
						gap={1}
						sx={{
							position: 'absolute',
							right: 20,
							top: 20,
						}}>
						<Typography variant='body1'>Status: </Typography>
						<Typography color={statusBody(data?.statusType)?.color}>
							{statusBody(data?.statusType)?.label}
						</Typography>
					</Stack>
				</Stack>
				{actionType === ActionTypes.ADD_MIXING_ARTISAN && (
					<BiocharSummary
						notAssessedQty={informationData.totalNotAssessedBiocharQty}
						approvedQty={informationData.totalAvailableBiocharQty}
						cropData={informationData.availableBiocharQty}
					/>
				)}
			</DialogTitle>
			<DialogContent>
				<form onSubmit={handleSubmit(onSubmit)}>
					<Stack pt={2} rowGap={3}>
						{autocompleteData?.map(([key, value]) => (
							<Controller
								key={key as string}
								name={key as any}
								control={control}
								render={({ field, fieldState }) => (
									<Stack spacing={1}>
										<TextField
											disabled={
												getButtonLabel() === StatusType.PROCESSED ||
												key === 'artisanProId'
											}
											{...field}
											select
											label={getLabelFromKey(key as string)}
											defaultValue={value}
											sx={{ textTransform: 'capitalize' }}
											fullWidth
											error={!!fieldState.error}
											helperText={fieldState.error?.message || ''}>
											{getOptions(key as AutoCompleteOptionsKeys)?.map(
												(option, index) => (
													<MenuItem
														key={`${key}-${index}`}
														value={option?.value}>
														{option?.label}
													</MenuItem>
												)
											)}
										</TextField>
									</Stack>
								)}
							/>
						))}
						{newData.map(([key, value]) => {
							const isReadOnlyField = [
								'cropId',
								'siteId',
								'kilnId',
								'siteId',
								'packagingTypeId',
								'vehicleId',
								'Vehicle',
								'farmers',
							].includes(key)

							return isReadOnlyField ? (
								<TextField
									key={key}
									label={getLabelFromKey(key)}
									value={value}
									sx={{ textTransform: 'capitalize' }}
									disabled={getButtonLabel() === StatusType.PROCESSED}
									inputProps={{ readOnly: true }}
									fullWidth
								/>
							) : (
								<Controller
									key={key}
									name={key}
									control={control}
									render={({ field, fieldState }) => {
										return (
											<Stack spacing={1}>
												<TextField
													{...field}
													disabled={getButtonLabel() === StatusType.PROCESSED}
													label={getLabelFromKey(key)}
													defaultValue={value}
													sx={{ textTransform: 'capitalize' }}
													fullWidth
													error={!!fieldState.error}
												/>
												{fieldState.error && (
													<FormHelperText error>
														{fieldState.error.message}
													</FormHelperText>
												)}
											</Stack>
										)
									}}
								/>
							)
						})}
						{/* Render bags using useFieldArray */}
						{showBags &&
							fields.map((item, index) => (
								<Stack
									key={item.id + index}
									direction='row'
									columnGap={1}
									justifyContent={'space-between'}
									alignItems='center'>
									<Controller
										name={`${bagFieldName as bagList}.${index}.bagId` as const}
										control={control}
										render={({ field, fieldState }) => (
											<Stack spacing={1} width={'50%'}>
												<Autocomplete
													{...field}
													disabled={getButtonLabel() === StatusType.PROCESSED}
													value={
														fetchBags?.data?.find(
															(option) => option.value === field.value
														) ?? null
													}
													fullWidth
													onChange={(_, newValue) => {
														setValue(
															`${bagFieldName as bagList}.${index}.bagId`,
															newValue?.value ?? ''
														)
														setValue(
															`${bagFieldName as bagList}.${index}.bagName`,
															newValue?.label ?? ''
														)
													}}
													options={fetchBags?.data || []}
													renderOption={(props, option) => {
														return (
															<li {...props}>
																<Stack>
																	<Typography variant='body1'>
																		{option.label}
																	</Typography>
																	{option.networkName ? (
																		<Typography variant='subtitle1'>
																			({option.networkName})
																		</Typography>
																	) : null}
																</Stack>
															</li>
														)
													}}
													renderInput={(params) => (
														<TextField {...params} label='Bag Name' fullWidth />
													)}
												/>
												{fieldState.error && (
													<FormHelperText error>
														{fieldState.error.message}
													</FormHelperText>
												)}
											</Stack>
										)}
									/>
									<Controller
										name={`${bagFieldName as bagList}.${index}.count` as const}
										control={control}
										render={({ field, fieldState }) => (
											<Stack spacing={1}>
												<TextField
													disabled={getButtonLabel() === StatusType.PROCESSED}
													{...field}
													label='Count'
													fullWidth
													type='number'
													error={!!fieldState.error}
												/>
												{fieldState.error && (
													<FormHelperText error>
														{fieldState.error.message}
													</FormHelperText>
												)}
											</Stack>
										)}
									/>
									{getButtonLabel() !== 'processed' &&
										bagFieldName !== 'packagingBag' && (
											<IconButton onClick={() => remove(index)}>
												<RemoveCircleOutline />
											</IconButton>
										)}
								</Stack>
							))}
						{showBags &&
						bagFieldName !== 'packagingBag' &&
						getButtonLabel() !== 'processed' ? (
							<Stack flexDirection={'row-reverse'}>
								<Button
									type='button'
									startIcon={<Add />}
									sx={{ width: 'fit-content' }}
									variant='outlined'
									onClick={() => append({ bagId: '', count: 0 })}>
									Add Bag
								</Button>
							</Stack>
						) : null}
						{data?.error ? (
							<Stack pl={2}>
								<Typography variant='body2'>Errors: </Typography>
								<Box component='ul' sx={{ listStyleType: 'disc', pl: 2 }}>
									{Array.isArray(data?.error) ? (
										data.error.map((errorMessage: string) => (
											<Box component='li' key={errorMessage}>
												{errorMessage}
											</Box>
										))
									) : (
										<Box component='li'>{data.error}</Box>
									)}
								</Box>
							</Stack>
						) : null}
						{getButtonLabel() !== StatusType.PROCESSED && (
							<DialogActions sx={{ justifyContent: 'space-between' }}>
								<Button
									variant='outlined'
									color='primary'
									fullWidth
									onClick={onClose}>
									Cancel
								</Button>
								<Button
									type='submit'
									fullWidth
									variant='contained'
									color='primary'>
									{statusBody(getButtonLabel())?.name}
								</Button>
							</DialogActions>
						)}
					</Stack>
				</form>
			</DialogContent>
		</Dialog>
	)
}
