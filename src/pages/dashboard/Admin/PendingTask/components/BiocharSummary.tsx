import React, { useState } from 'react'
import {
	Grid,
	Typography,
	IconButton,
	Popover,
	Stack,
	Box,
} from '@mui/material'
import { ExpandMore, ExpandLess } from '@mui/icons-material'
import { BiocharSummaryProps, CropData } from '@/interfaces'
import { theme } from '@/lib/theme/theme'

const CropPopup: React.FC<{
	anchorEl: HTMLElement | null
	onClose: () => void
	cropData?: CropData[] | null
}> = ({ anchorEl, onClose, cropData }) => {
	const open = Boolean(anchorEl)

	return (
		<Popover
			open={open}
			anchorEl={anchorEl}
			onClose={onClose}
			anchorOrigin={{ vertical: 'bottom', horizontal: 'right' }}
			transformOrigin={{ vertical: 'top', horizontal: 'right' }}
			PaperProps={{
				sx: {
					width: 'auto',
					maxWidth: 'sm',
				},
			}}>
			<Stack gap={theme.spacing(1)} padding={theme.spacing(2)}>
				{cropData?.map((crop, index) => (
					<Box key={index}>
						<Typography variant='body1'>
							{crop.cropName} ({crop.biocharQty} ltr)
						</Typography>
					</Box>
				))}
			</Stack>
		</Popover>
	)
}

const BiocharSummary: React.FC<BiocharSummaryProps> = ({
	notAssessedQty,
	approvedQty,
	cropData,
}) => {
	const [anchorEl, setAnchorEl] = useState<null | HTMLElement>(null)
	const open = Boolean(anchorEl)

	const handleClick = (event: React.MouseEvent<HTMLElement>) => {
		setAnchorEl(open ? null : event.currentTarget)
	}

	return (
		<>
			<Grid container spacing={2} alignItems='center'>
				{notAssessedQty !== undefined && (
					<Grid item xs={6}>
						<Typography variant='body1' textAlign='left'>
							Not Assessed Qty: {notAssessedQty} ltr
						</Typography>
					</Grid>
				)}
				{approvedQty !== null && (
					<Grid item xs={6}>
						<Typography variant='body1' textAlign='right'>
							Available Biochar Qty: {approvedQty} ltr
						</Typography>
					</Grid>
				)}

				{cropData !== null &&
					(cropData?.length === 1 ? (
						<Grid item xs={6}>
							<Typography variant='body1' textAlign='right'>
								{cropData?.[0]?.cropName} ({cropData?.[0]?.biocharQty} ltr)
							</Typography>
						</Grid>
					) : (
						<Grid
							item
							xs={6}
							display='flex'
							alignItems='center'
							justifyContent='flex-end'>
							<Typography variant='body1'>Approved Qty</Typography>
							<IconButton onClick={handleClick}>
								{open ? <ExpandLess /> : <ExpandMore />}
							</IconButton>
						</Grid>
					))}
			</Grid>
			<CropPopup
				anchorEl={anchorEl}
				onClose={() => setAnchorEl(null)}
				cropData={cropData}
			/>
		</>
	)
}

export default BiocharSummary
