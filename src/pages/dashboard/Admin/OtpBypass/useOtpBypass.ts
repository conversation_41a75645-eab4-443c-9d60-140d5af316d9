import { authAxios } from '@/contexts'
import { defaultLimit, defaultPage } from '@/utils/constant'
import { useMutation, useQuery, useQueryClient } from '@tanstack/react-query'
import { useState } from 'react'
import { useSearchParams } from 'react-router-dom'
import { toast } from 'react-toastify'

export const useOtpBypass = () => {
	const [searchParams] = useSearchParams()

	const search = searchParams.get('search') ?? ''

	const [deleteNumber, setDeleteNumber] = useState<string | null>(null)

	const queryClient = useQueryClient()
	const paramsLimit = searchParams.get('limit') || defaultLimit
	const paramsPage = searchParams.get('page') || defaultPage
	const bypassNumbersQuery = useQuery({
		queryKey: ['getOtpBypassList', paramsLimit, paramsPage, search],
		queryFn: async () => {
			const { data } = await authAxios(
				`/by-pass?limit=${paramsLimit}&page=${paramsPage}&search=${search}`
			)
			return data
		},
		enabled: true,
	})

	const deleteBypassNumber = useMutation({
		mutationKey: ['deleteBypassNumber'],
		mutationFn: (bypassId: string) => authAxios.delete(`/by-pass/${bypassId}`),
		onSuccess: (data) => {
			toast(data?.data?.message)
			queryClient.refetchQueries({
				queryKey: ['getOtpBypassList'],
				// exact: true,
				// refetchType:'active'
			})
			setDeleteNumber(null)
		},
		onError: (error: Error) => {
			toast(error?.message)
		},
	})

	return {
		byPassList: bypassNumbersQuery.data?.byPassNumbers,
		byPassListCount: bypassNumbersQuery.data?.count ?? 0,
		loading: bypassNumbersQuery.isLoading,
		deleteBypassNumber,
		deleteNumber,
		setDeleteNumber,
	}
}
