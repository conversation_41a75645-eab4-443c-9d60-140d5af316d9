import { <PERSON>, But<PERSON>, Stack, styled, Typography } from '@mui/material'
import { GridColDef, GridDeleteIcon, GridValidRowModel } from '@mui/x-data-grid'
import { ActionInformationDrawer, CustomHeader } from '../../../../components'
import { CustomTable } from '@/components/CustomTable'
import { useMemo, useState } from 'react'
import { getFormattedDate } from '@/utils/helper/getFormattedDate'
import { dateFormats, userRoles, userRolesName } from '@/utils/constant'
import { useOtpBypass } from './useOtpBypass'
import { AddCircle } from '@mui/icons-material'
import { Confirmation } from '@/components/Confirmation'
import { AddBypass } from '@/components/AddBypass'

export const OtpBypass = () => {
	const {
		byPassList,
		deleteBypassNumber,
		deleteNumber,
		setDeleteNumber,
		byPassListCount,
		loading,
	} = useOtpBypass()
	const [openOtpBypassDialog, setOpenOtpbBypassDialog] = useState(false)

	const columns: GridColDef<GridValidRowModel>[] = useMemo(
		() => [
			{
				field: 'id',
				headerName: 'S. No',
				flex: 0.5,
				minWidth: 80,
			},
			{
				field: 'countryCode',
				headerName: 'Country Code',
				flex: 1,
				minWidth: 150,
			},
			{
				field: 'number',
				headerName: 'OTP Bypass',
				flex: 1,
				minWidth: 150,
			},
			{
				field: 'name',
				headerName: 'Name',
				flex: 1,
				minWidth: 150,
			},
			{
				field: 'role',
				headerName: 'Role',
				flex: 1,
				minWidth: 150,
			},
			{
				field: 'approverName',
				headerName: 'Approved By',
				flex: 1,
				minWidth: 150,
				renderCell: (params) => (
					<Stack direction='row'>
						<Typography>
							{params.row?.approverName}{' '}
							{params.row?.approverRole
								? `(${userRolesName[params.row?.approverRole as userRoles]})`
								: ''}
						</Typography>
					</Stack>
				),
			},
			{
				field: 'createdAt',
				headerName: 'Created At',
				flex: 1,
				minWidth: 250,
				renderCell: (params) => (
					<Typography>
						{params?.value
							? `${getFormattedDate(params?.value, dateFormats.dd_MMM_yyyy)}`
							: '-'}
					</Typography>
				),
			},
			{
				field: 'action',
				headerName: 'Action',
				flex: 1,
				minWidth: 150,
				renderCell: (params) => (
					<Stack flexDirection='row' columnGap={2}>
						<Button
							onClick={() => setDeleteNumber(params?.row?.id)}
							startIcon={<GridDeleteIcon />}>
							Delete
						</Button>
					</Stack>
				),
			},
		],
		[setDeleteNumber]
	)

	const GridHeaderComponents = () => {
		return (
			<Stack className='grid-header-component' columnGap={2}>
				<Button
					startIcon={<AddCircle />}
					onClick={() => setOpenOtpbBypassDialog(true)}
					variant='contained'>
					Add Bypass Number
				</Button>
			</Stack>
		)
	}

	return (
		<StyledContainer>
			<Box className='header'>
				<CustomHeader
					showBottomBorder={true}
					heading='SMS OTP Bypass'
					showButton={false}
					// endComponent={<HeaderEndButtons />}
				/>
			</Box>

			<ActionInformationDrawer
				open={openOtpBypassDialog}
				onClose={() => setOpenOtpbBypassDialog(false)}
				anchor='right'
				component={<AddBypass close={() => setOpenOtpbBypassDialog(false)} />}
			/>
			<Stack className='container'>
				<CustomTable
					columns={columns}
					count={byPassListCount}
					rows={byPassList ?? []}
					showPagination
					showPaginationDetails
					headerComponent={<GridHeaderComponents />}
					isLoading={loading}
				/>
			</Stack>
			{deleteNumber ? (
				<Confirmation
					open={!!deleteNumber}
					confirmationText='Are you sure you want to remove Otp-Bypass '
					handleClose={() => setDeleteNumber(null)}
					handleNoClick={() => setDeleteNumber(null)}
					handleYesClick={() => deleteBypassNumber.mutate(deleteNumber)}
				/>
			) : null}
		</StyledContainer>
	)
}

const StyledContainer = styled(Stack)(({ theme }) => ({
	gap: theme.spacing(2),
	'.header': {
		padding: theme.spacing(4, 0),
		borderBottom: `${theme.spacing(0.125)} solid ${
			theme.palette.neutral['100']
		}`,
	},
	'.container': {
		padding: theme.spacing(4, 3),
		gap: theme.spacing(2),
		'.grid-header-component': {
			flexDirection: 'row',
			alignItems: 'center',
		},
	},
}))

// function HeaderEndButtons() {
// 	return (
// 		<Stack direction='row' spacing={2}>
// 			<IconButton
// 				sx={{
// 					border: `${theme.spacing(0.125)} solid ${
// 						theme.palette.neutral['100']
// 					}`,
// 					borderRadius: '10px',
// 				}}>
// 				<Box component='img' src={DownloadIcon} height={20} width={20} />
// 			</IconButton>
// 		</Stack>
// 	)
// }
