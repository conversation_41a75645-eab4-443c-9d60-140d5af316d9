import { NoData } from '@/components'
import { User } from '@/interfaces'
import { userRolesName, userRolesNameUserManagement } from '@/utils/constant'
import { proxyImage } from '@/utils/helper'
import {
	Stack,
	CircularProgress,
	Grid,
	Avatar,
	Chip,
	IconButton,
	Typography,
} from '@mui/material'
import { FC } from 'react'

interface IProps {
	dataViewType: 'list' | 'grid'
	userList: User[]
	isLoading?: boolean
	handleOpenDetails: (user: User) => void
}

export const UserManagementGridView = ({
	handleOpenDetails,
	userList,
	isLoading,
}: IProps) => {
	if (!userList?.length)
		return (
			<Stack
				sx={{
					height: 'calc(100vh - 375px)',
				}}
				justifyContent='center'
				alignItems='center'
				width='100%'
				p={5}>
				{isLoading ? <CircularProgress color='primary' /> : <NoData />}
			</Stack>
		)
	return (
		<Grid container className='user_list'>
			{userList?.map((user) => (
				<Grid item display='flex' height='100%' key={user.id}>
					<UserCard
						user={user}
						handleOpenDetails={() => handleOpenDetails(user)}
					/>
				</Grid>
			))}
		</Grid>
	)
}

const UserCard: FC<{ user: User; handleOpenDetails: () => void }> = ({
	user,
	handleOpenDetails,
}) => {
	const accountType =
		userRolesNameUserManagement[user?.accountType as keyof typeof userRolesName]
	return (
		<Stack
			onClick={handleOpenDetails}
			className='user_card'
			component={IconButton}
			sx={{ borderRadius: 2 }}>
			<Avatar
				alt={user.name}
				src={
					user?.profileImageUrl?.id
						? proxyImage(user?.profileImageUrl?.path)
						: ''
				}
				className='user_image'
			/>
			<Typography
				variant='body2'
				sx={{ wordBreak: 'break-word', textAlign: 'center' }}>
				{user.name?.length > 20 ? `${user.name.slice(0, 20)}...` : user.name}
			</Typography>
			<Typography className='user_id'>
				{user.email ? user.email : `${user.countryCode} ${user.number}`}
			</Typography>
			<Chip className='user_role' label={accountType} />
		</Stack>
	)
}
