import { CustomDataGrid } from '@/components'
import { User } from '@/interfaces'
import { theme } from '@/lib/theme/theme'
import { Avatar, Stack, Typography } from '@mui/material'
import {
	GridCellParams,
	GridColDef,
	GridEventListener,
	GridValidRowModel,
} from '@mui/x-data-grid'
import { useCallback, useMemo } from 'react'
import { tabEnum } from './useUserManagement'
import { userRolesName, userRolesNameUserManagement } from '@/utils/constant'

interface IProps {
	dataViewType: 'list' | 'grid'
	userList: User[]
	paramsTab: tabEnum
	isLoading?: boolean
	handleOpenDetails: (user: User) => void
}

export const UserManagementListView = ({
	handleOpenDetails,
	userList,
	paramsTab,
	isLoading,
}: IProps) => {
	const usersColumn: GridColDef<GridValidRowModel>[] = useMemo(
		() => [
			{
				field: 'name',
				headerName: 'Name',
				minWidth: 120,
				flex: 1,
				renderCell: (params) => (
					<Stack flexDirection='row' alignItems='center' gap={theme.spacing(1)}>
						<Avatar src={params?.row?.profileImageUrl?.url} />
						<Typography variant='subtitle1'>{params?.value}</Typography>
					</Stack>
				),
			},

			...(![
				tabEnum.admin,
				tabEnum.circonomy_employee,
				tabEnum.ba_admins,
				tabEnum.csink_managers,
				tabEnum.compliance_manager,
				tabEnum.ceres_auditors,
			]?.includes(paramsTab)
				? [
						{
							field: 'networkName',
							headerName: 'Network Name',
							minWidth: 120,
							flex: 1,
							renderCell: (params: GridCellParams) => (
								<Typography variant='subtitle1'>
									{params?.row?.artisanProName ??
										params?.row?.csinkNetworkName ??
										params?.row?.artisanProNetworkName ??
										'-'}
								</Typography>
							),
						},
				  ]
				: []),
			...([tabEnum.csink_managers, tabEnum.ba_admins]?.includes(paramsTab)
				? [
						{
							field: 'csinkManagerName',
							headerName: 'CSM Name',
							minWidth: 120,
							flex: 1,
							renderCell: (params: GridCellParams) => (
								<Typography variant='subtitle1'>
									{params?.row?.csinkManagerName ?? '-'}
								</Typography>
							),
						},
				  ]
				: []),
			...([tabEnum.ba_admins]?.includes(paramsTab)
				? [
						{
							field: 'biomassAggregatorName',
							headerName: 'BA Name',
							minWidth: 120,
							flex: 1,
							renderCell: (params: GridCellParams) => (
								<Typography variant='subtitle1'>
									{params?.row?.biomassAggregatorName ?? '-'}
								</Typography>
							),
						},
				  ]
				: []),

			{
				field: 'email',
				headerName: 'Email',
				flex: 1,
				minWidth: 150,
			},
			...([
				tabEnum.csink_managers,
				tabEnum.ba_admins,
				tabEnum.network_admins,
				tabEnum.farmers,
			]?.includes(paramsTab)
				? [
						{
							field: 'number',
							headerName: 'Phone number',
							minWidth: 120,
							flex: 1,
							renderCell: (params: GridCellParams) => (
								<Typography variant='subtitle1'>
									{params?.row?.number
										? `${params?.row?.countryCode}-${params?.row?.number}`
										: '-'}
								</Typography>
							),
						},
				  ]
				: []),
			...(![tabEnum.csink_managers, tabEnum.ba_admins]?.includes(paramsTab)
				? [
						{
							field: 'accountType',
							headerName: 'Role',
							flex: 1,
							minWidth: 150,
							renderCell: (params: GridCellParams) =>
								params?.value ? (
									<Typography textTransform='capitalize' variant='subtitle1'>
										{
											userRolesNameUserManagement[
												params?.row?.accountType as keyof typeof userRolesName
											]
										}{' '}
									</Typography>
								) : (
									<Typography>-</Typography>
								),
						},
				  ]
				: []),
		],
		[paramsTab]
	)
	const handleRowClick: GridEventListener<'rowClick'> = useCallback(
		(params) => {
			handleOpenDetails(params?.row)
		},
		[handleOpenDetails]
	)

	return (
		<Stack padding={theme.spacing(2)}>
			<CustomDataGrid
				columns={usersColumn}
				showRowsPerPage={false}
				loading={isLoading}
				rows={userList}
				onRowClick={handleRowClick}
				showPagination={false}
			/>
		</Stack>
	)
}
