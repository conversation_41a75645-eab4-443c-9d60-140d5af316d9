import { User } from '@/interfaces'
import { UserManagementGridView } from './UserManagementGridView'
import { UserManagementListView } from './UserManagerListView'
import { tabEnum } from './useUserManagement'

interface IProps {
	dataViewType: 'list' | 'grid'
	userList: User[]
	isLoading?: boolean
	paramsTab:tabEnum
	handleOpenDetails: (user: User) => void
}
export const RenderUsers = ({
	dataViewType,paramsTab,
	userList,
	handleOpenDetails,
	isLoading,
}: IProps) => {
	if (dataViewType === 'grid')
		return (
			<UserManagementGridView
				dataViewType={dataViewType}
				handleOpenDetails={handleOpenDetails}
				userList={userList}
				isLoading={isLoading}
			/>
		)
	else
		return (
			<UserManagementListView
				dataViewType={dataViewType}
				paramsTab={paramsTab}
				handleOpenDetails={handleOpenDetails}
				userList={userList}
				isLoading={isLoading}
			/>
		)
}
