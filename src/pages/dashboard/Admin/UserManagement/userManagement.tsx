import {
	Autocomplete,
	Box,
	Button,
	IconButton,
	Stack,
	styled,
	Tab,
	TextField,
	useTheme,
} from '@mui/material'
import {
	ActionInformationDrawer,
	AddUser,
	CustomHeader,
	CustomPagination,
	QueryInput,
	ViewUserDetails,
} from '../../../../components'
import { TabContext, TabList } from '@mui/lab'
import { FC, useCallback, useMemo, useState } from 'react'
import { AddRounded, Search, TableRows } from '@mui/icons-material'
import { useAuthContext } from '@/contexts'
import { userRoles } from '@/utils/constant'
import { tabEnum, useUserManagement } from './useUserManagement'
import { TModalTypeForUserManagement, User } from '@/interfaces'
import { RenderUsers } from './RenderUsers'

const tabs = [
	{
		label: 'All Users',
		value: tabEnum.all,
	},
	{
		label: 'Admins',
		value: tabEnum.admin,
	},
	{
		label: 'Compliance Manager',
		value: tabEnum.compliance_manager,
	},
	{
		label: 'CERES Auditor',
		value: tabEnum.ceres_auditors,
	},
	{
		label: 'Circonomy Employee',
		value: tabEnum.circonomy_employee,
	},
	{
		label: 'CSM Admins',
		value: tabEnum.csink_managers,
	},
	{
		label: 'BA Admin',
		value: tabEnum.ba_admins,
	},
	{
		label: 'Network Admins',
		value: tabEnum.network_admins,
	},
	{
		label: 'Operators',
		value: tabEnum.operators,
	},
	{
		label: 'Farmers',
		value: tabEnum.farmers,
	},
]

const buttons = ['list', 'grid']

export const UserManagement = () => {
	const theme = useTheme()
	const {
		setSearchParams,
		paramsTab,
		totalUserCount,
		userList,
		searchParams,
		networkList,
		paramsDataViewType,
		isLoading,
		userTabCounnt,
		handleChangeDataViewType,
	} = useUserManagement()
	const [isActionInfoDrawer, setIsActionInfoDrawer] = useState<boolean>(false)
	const [modalType, setModalType] = useState<TModalTypeForUserManagement>('add')
	const [selectedUser, setSelectedUser] = useState<User | null>(null)
	const { userDetails } = useAuthContext()

	const showTabs = useMemo(() => {
		switch (userDetails?.accountType) {
			case userRoles.Admin:
				return [
					tabEnum.all,
					tabEnum.network_admins,
					tabEnum.ba_admins,
					tabEnum.farmers,
					tabEnum.operators,
					tabEnum.admin,
					tabEnum.circonomy_employee,
					tabEnum.csink_managers,
					tabEnum.compliance_manager,
					tabEnum.ceres_auditors,
				]
			case userRoles.CsinkManager:
				return [
					tabEnum.all,
					tabEnum.network_admins,
					tabEnum.ba_admins,
					tabEnum.farmers,
					tabEnum.operators,
				]
			case userRoles.BiomassAggregator:
				return [
					tabEnum.all,
					tabEnum.network_admins,
					tabEnum.farmers,
					tabEnum.operators,
				]
			case userRoles.artisanProNetworkManager:
				return [
					tabEnum.all,
					tabEnum.network_admins,
					tabEnum.farmers,
					tabEnum.operators,
				]
			case userRoles.ArtisanPro:
			case userRoles.cSinkNetwork:
			case userRoles.Manager:
				return [tabEnum.all, tabEnum.farmers, tabEnum.operators]
			default:
				return []
		}
	}, [userDetails?.accountType])

	const hideTabsWithNoUsers = useCallback(
		(tab: tabEnum) => {
			switch (tab) {
				case tabEnum.ba_admins:
					return !userTabCounnt?.biomassAggregators
				case tabEnum.farmers:
					return !userTabCounnt?.farmers
				case tabEnum.operators:
					return !userTabCounnt?.operators
				case tabEnum.network_admins:
					return !userTabCounnt?.networkAdmins
				case tabEnum.all:
				case tabEnum.circonomy_employee:
				case tabEnum.admin:
				default:
					return false
			}
		},
		[userTabCounnt]
	)

	const handleChange = useCallback(
		(_: unknown, newValue: tabEnum) => {
			setSearchParams((prev) => {
				prev.set('tab', newValue)
				prev.set('page', '0')
				prev.set('limit', '10')
				return prev
			})
		},
		[setSearchParams]
	)

	const Filters = () => {
		return (
			<Stack direction='row' columnGap={2}>
				{/* {userDetails?.accountType === userRoles.Admin && (
					<Autocomplete
						value={
							baList.find(
								(option) => option.value === searchParams.get('baId')
							) ?? null
						}
						options={baList}
						renderInput={(params) => <TextField {...params} label='BA' />}
						onChange={(_: unknown, selectedOption) => {
							const nsp = new URLSearchParams(searchParams)
							nsp.delete('networkId')
							if (selectedOption) {
								nsp.set('baId', selectedOption.value)
							} else {
								nsp.delete('baId')
							}
							const param = nsp
							setSearchParams(param, {
								replace: true,
							})
						}}
						sx={{
							width: 200,
							'.MuiOutlinedInput-root': {
								height: 38,
								'.MuiInputBase-input': {
									paddingTop: '2.5px',
									fontSize: theme.typography.subtitle1,
								},
							},
						}}
					/>
				)} */}
				<Autocomplete
					value={
						networkList.find(
							(option) => option.value === searchParams.get('networkId')
						) ?? null
					}
					options={networkList}
					renderInput={(params) => <TextField {...params} label='Networks' />}
					onChange={(_: unknown, selectedOption) => {
						const nsp = new URLSearchParams(searchParams)
						if (selectedOption) {
							nsp.set('networkId', selectedOption.value)
						} else {
							nsp.delete('networkId')
						}
						const param = nsp
						setSearchParams(param, {
							replace: true,
						})
					}}
					sx={{
						width: 200,
						'.MuiOutlinedInput-root': {
							height: 38,
							'.MuiInputBase-input': {
								paddingTop: '2.5px',
								fontSize: theme.typography.subtitle1,
							},
						},
					}}
				/>
			</Stack>
		)
	}

	const handleOpenDetails = useCallback((user: User) => {
		setIsActionInfoDrawer(true)
		setModalType('view')
		setSelectedUser(user)
	}, [])

	const handleModalTypeAndOpenDrawer = useCallback(
		(modalType: TModalTypeForUserManagement) => {
			if (modalType == 'add') {
				setSelectedUser(null)
			}
			setIsActionInfoDrawer(true)
			setModalType(modalType)
		},
		[]
	)

	return (
		<>
			<ActionInformationDrawer
				open={isActionInfoDrawer}
				onClose={() => setIsActionInfoDrawer(false)}
				anchor='right'
				component={
					<RenderModal
						type={modalType}
						setIsActionInfoDrawer={setIsActionInfoDrawer}
						user={selectedUser as User}
						userList={userList}
						handleModalTypeAndOpenDrawer={handleModalTypeAndOpenDrawer}
					/>
				}
			/>
			<StyledContainer>
				<Box className='header'>
					<CustomHeader
						showBottomBorder={true}
						heading='User Management'
						showButton={false}
						endComponent={
							<Stack direction='row' className='grid-header-component'>
								<Button
									onClick={() => handleModalTypeAndOpenDrawer('add')}
									variant='contained'
									startIcon={<AddRounded />}
									size='small'>
									Add User
								</Button>
								<QueryInput
									className='search-textFiled'
									queryKey='search'
									placeholder='Search'
									setPageOnSearch
									InputProps={{
										startAdornment: <Search fontSize='small' />,
									}}
								/>
								{[
									userRoles.Admin,
									userRoles.CsinkManager,
									userRoles.BiomassAggregator,
								]?.includes(userDetails?.accountType as userRoles) ? (
									<Filters />
								) : null}
							</Stack>
						}
					/>
				</Box>
				<Stack className='container'>
					<TabContext value={paramsTab}>
						<Stack className='tab-container'>
							<TabList className='tabList' onChange={handleChange}>
								{tabs.map(({ label, value }) =>
									showTabs.includes(value) && !hideTabsWithNoUsers(value) ? (
										<Tab key={value} label={label} value={value} />
									) : null
								)}
							</TabList>
							<Stack className='button-container'>
								{buttons.map((label) => (
									<IconButton
										key={label}
										className={`tab-button ${
											paramsDataViewType === label ? 'selected-button' : ''
										}`}
										onClick={() => handleChangeDataViewType(label)}>
										{label === 'list' ? (
											<TableRows className='button-icon' />
										) : (
											<Box component='img' src='/images/nav-icons.svg' />
										)}
									</IconButton>
								))}
							</Stack>
						</Stack>
						<RenderUsers
							dataViewType={paramsDataViewType as 'grid' | 'list'}
							handleOpenDetails={handleOpenDetails}
							paramsTab={paramsTab as tabEnum}
							userList={userList}
							isLoading={isLoading}
						/>
						<Stack
							direction='row'
							alignItems='center'
							justifyContent='center'
							mt={4}>
							<CustomPagination rowCount={totalUserCount} />
						</Stack>
					</TabContext>
				</Stack>
			</StyledContainer>
		</>
	)
}

const RenderModal: FC<{
	type: TModalTypeForUserManagement
	setIsActionInfoDrawer: React.Dispatch<React.SetStateAction<boolean>>
	user: User
	handleModalTypeAndOpenDrawer: (id: TModalTypeForUserManagement) => void
	userList: User[]
}> = ({
	type,
	setIsActionInfoDrawer,
	user,
	handleModalTypeAndOpenDrawer,
	userList,
}) => {
	const { entityTabsCount } = useUserManagement()
	switch (type) {
		case 'view':
			return (
				<ViewUserDetails
					userList={userList}
					setIsActionInfoDrawer={setIsActionInfoDrawer}
					userData={user}
					handleModalTypeAndOpenDrawer={handleModalTypeAndOpenDrawer}
				/>
			)
		case 'promote':
		case 'edit':
		case 'add':
		case 'demote':
			return (
				<AddUser
					setIsActionInfoDrawer={setIsActionInfoDrawer}
					userData={user}
					modalType={type}
					entityTabsCount={entityTabsCount}
				/>
			)
		default:
			return null
	}
}

const StyledContainer = styled(Stack)(({ theme }) => ({
	gap: theme.spacing(2),
	'.header': {
		padding: theme.spacing(4, 0),
	},
	'.grid-header-component': {
		flexDirection: 'row',
		alignItems: 'center',
		gap: theme.spacing(2),
		flexWrap: 'wrap',
		justifyContent: 'end',
		'.search-textFiled': {
			width: 200,
			'.MuiInputBase-root': {
				borderRadius: theme.spacing(1.25),
				height: theme.spacing(4.75),
			},
		},
	},
	'.container': {
		paddingBottom: theme.spacing(10),

		'.tab-container': {
			alignItems: 'center',
			flexDirection: 'row',
			justifyContent: 'space-between',
			padding: theme.spacing(0, 2.5),
			borderBottom: `${theme.spacing(0.125)} solid ${
				theme.palette.neutral['100']
			}`,
		},
		'.button-container': {
			flexDirection: 'row',
			alignItems: 'center',
			background: theme.palette.neutral['100'],
			padding: theme.spacing(0.5),
			borderRadius: theme.spacing(0.75),
			'.tab-button': {
				background: 'transparent',
				color: theme.palette.common.black,
				textTransform: 'none',
				height: 32,
				width: 40,
				fontWeight: 400,
				borderRadius: theme.spacing(0.75),
			},
			'.selected-button': {
				background: theme.palette.common.white,
			},
			'.button-icon': {
				width: theme.spacing(3),
				height: theme.spacing(3),
				color: theme.palette.neutral[300],
			},
		},
		'.user_list': {
			flexDirection: 'row',
			flexWrap: 'wrap',
			gap: theme.spacing(1.25),
			padding: theme.spacing(2.5, 1.25),
			paddingTop: theme.spacing(5),
			height: 'calc(100vh - 375px)',
			overflow: 'auto',
			rowGap: theme.spacing(4),
			display: 'grid',
			gridTemplateColumns: 'repeat(auto-fill, minmax(300px, 1fr))',
		},
		'.user_card': {
			height: '100%',
			minWidth: 208,
			alignItems: 'center',
			rowGap: theme.spacing(1),
			flexGrow: 1,
			'.user_image': {
				width: theme.spacing(12.5),
				height: theme.spacing(12.5),
			},
			'.user_id': {
				color: theme.palette.neutral[300],
				textTransform: 'none',
				fontSize: theme.typography.overline.fontSize,
				wordBreak: 'break-word',
				textAlign: 'center',
			},
			'.user_role': {
				backgroundColor: theme.palette.neutral[100],
				color: theme.palette.neutral[500],
				textTransform: 'capitalize',
			},
		},
	},
}))
