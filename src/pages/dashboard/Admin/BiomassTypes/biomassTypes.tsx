import {
	ActionInformationDrawer,
	AddBiomass,
	AddOrEditBiocharContentDetail,
	CustomDataGrid,
	CustomHeader,
	ImageCarouselDialog,
	QueryInput,
} from '@/components'
import { BiocharContentDetails } from '@/components/CustomTable'
import { Add, MoreVert, Search } from '@mui/icons-material'
import EditRoundedIcon from '@mui/icons-material/EditRounded'
import ArrowUpwardIcon from '@mui/icons-material/ArrowUpward'

import {
	Box,
	Button,
	Divider,
	IconButton,
	Menu,
	MenuItem,
	Stack,
	styled,
	Tooltip,
	Typography,
} from '@mui/material'
import {
	GridColDef,
	GridRenderCellParams,
	GridTreeNodeWithRender,
	GridValidRowModel,
} from '@mui/x-data-grid'
import { useMemo, useState } from 'react'
import { useBiomassTypes } from './useBiomassTypes'
import { getSerialNumber, proxyImage } from '@/utils/helper'
import { ICrop } from '@/types'
import { defaultLimit, userRoles } from '@/utils/constant'
import { useAuthContext } from '@/contexts'
import { AssignBiomass } from '@/components/AssignBiomass'
import { IImage } from '@/interfaces'
import { useSearchParams } from 'react-router-dom'

export const BiomassTypes = () => {
	const { biomassList, totalBiomass, loading, userDetails } = useBiomassTypes()
	const [isActionInfoDrawer, setIsActionInfoDrawer] = useState<boolean>(false)
	const [showBiocharDetailsDrawer, setShowBiocharDetailsDrawer] = useState<
		string | null
	>(null)
	const [drawerState, setDrawerState] = useState<string>('')
	const [openAddBiocharDrawer, setOpenAddBiocharDrawer] = useState<
		string | null
	>(null)
	const [selectedBiomass, setSelectedBiomass] = useState<{
		biomass: ICrop | null
		editMode: boolean
	}>({
		biomass: null,
		editMode: false,
	})
	const [searchParams] = useSearchParams()
	const limit = searchParams.get('limit') ?? defaultLimit

	const [imageList, setImageList] = useState<IImage[]>([])

	const columns: GridColDef<GridValidRowModel>[] = useMemo(
		() => [
			{
				field: 'id',
				headerName: 'S. No',
				flex: 0.5,
				minWidth: 80,
				renderCell: (params) => {
					return (
						<Typography variant='subtitle1'>
							{getSerialNumber(params, Number(limit))}
						</Typography>
					)
				},
			},
			{
				field: 'name',
				headerName: 'Crop/Biomass Name',
				flex: 1,
				minWidth: 150,
			},
			{
				field: 'season',
				headerName: 'Season Name',
				flex: 1,
				minWidth: 150,
				renderCell: (params) => (
					<Typography textTransform='capitalize'>{params.value}</Typography>
				),
			},
			{
				field: 'type',
				headerName: 'Type',
				flex: 1,
				minWidth: 150,
				renderCell: (params) => (
					<Typography textTransform='capitalize'>{params.value}</Typography>
				),
			},
			{
				field: 'description',
				headerName: 'Description',
				flex: 1,
				minWidth: 150,
				maxWidth: 200,
				renderCell: (params) =>
					params?.value ? (
						<Tooltip
							title={<Typography variant='body1'>{params.value}</Typography>}
							placement='top'
							arrow>
							<Typography className='first_letter_capitalize'>
								{params.value?.length < 70
									? params?.value
									: `${params?.value?.slice(0, 70)}...`}
							</Typography>
						</Tooltip>
					) : (
						<Typography>--</Typography>
					),
			},
			...(userDetails?.accountType === userRoles.Admin
				? [
						{
							field: 'createdByName',
							headerName: 'Created By',
							flex: 1,
							minWidth: 150,
						},
				  ]
				: []),
			{
				field: 'image',
				headerName: 'Image',
				flex: 1,
				minWidth: 150,
				renderCell: (params) =>
					params?.row?.imagePathUrl ? (
						<Box
							component='img'
							src={proxyImage(params?.row?.imagePathUrl?.path)}
							width={40}
							height={40}
							onClick={(e) => {
								e.stopPropagation()
								setImageList([
									{
										fileName: params?.row?.imagePathUrl?.path,
										...params?.row?.imagePathUrl,
									},
								])
							}}
						/>
					) : (
						<Typography>--</Typography>
					),
			},
			{
				field: 'action',
				headerName: 'Action',
				flex: 1,
				renderCell: (params) => (
					<RenderMenu
						params={params}
						setOpenAddBiocharDrawer={setOpenAddBiocharDrawer}
						setDrawerState={setDrawerState}
						setIsActionInfoDrawer={setIsActionInfoDrawer}
						setSelectedBiomass={setSelectedBiomass}
					/>
				),
			},
		],
		[userDetails?.accountType, limit]
	)

	const GridHeaderStartComponents = () => {
		return (
			<Stack className='grid-header-component' columnGap={2}>
				<Typography variant='h5'>Crop/Biomass</Typography>
			</Stack>
		)
	}

	return (
		<>
			<ActionInformationDrawer
				open={isActionInfoDrawer}
				onClose={() => {
					setIsActionInfoDrawer(false)
					setDrawerState('')
					setSelectedBiomass({
						biomass: null,
						editMode: false,
					})
				}}
				anchor='right'
				component={
					drawerState === 'Assign' ? (
						<AssignBiomass
							handleClose={() => {
								setIsActionInfoDrawer(false)
								setSelectedBiomass({
									biomass: null,
									editMode: false,
								})
							}}
							biomassDetails={selectedBiomass?.biomass}
						/>
					) : (
						<AddBiomass
							handleClose={() => {
								setIsActionInfoDrawer(false)
								setSelectedBiomass({
									biomass: null,
									editMode: false,
								})
							}}
							editMode={selectedBiomass.editMode}
							biomassDetails={selectedBiomass?.biomass}
						/>
					)
				}
			/>
			<ActionInformationDrawer
				open={!!openAddBiocharDrawer}
				onClose={() => setOpenAddBiocharDrawer(null)}
				anchor='right'
				component={
					<AddOrEditBiocharContentDetail
						handleClose={() => {
							setOpenAddBiocharDrawer(null)
						}}
						type='add'
						biomassId={openAddBiocharDrawer ?? ''}
					/>
				}
			/>
			<ActionInformationDrawer
				open={!!showBiocharDetailsDrawer}
				onClose={() => setShowBiocharDetailsDrawer(null)}
				anchor='right'
				component={
					<BiocharContentDetails
						id={showBiocharDetailsDrawer || ''}
						onClose={() => setShowBiocharDetailsDrawer(null)}
					/>
				}
			/>

			<StyledContainer>
				<Box className='header'>
					<CustomHeader
						showBottomBorder={true}
						heading='Biomass'
						showButton={false}
					/>
				</Box>
				<Stack className='container'>
					<CustomDataGrid
						columns={columns}
						rowCount={totalBiomass}
						rows={biomassList}
						showPagination
						loading={loading}
						showPaginationDetails
						onRowClick={(params) =>
							setShowBiocharDetailsDrawer(params?.row?.id)
						}
						headerEndComponent={
							<Stack className='grid-header-component' columnGap={2}>
								<QueryInput
									className='search-textFiled'
									queryKey='search'
									placeholder='Search'
									setPageOnSearch
									InputProps={{
										startAdornment: <Search fontSize='small' />,
									}}
								/>
								<Button
									onClick={() => setIsActionInfoDrawer(true)}
									className='btn'
									startIcon={<Add />}
									variant='contained'>
									Add Crop/Biomass
								</Button>
							</Stack>
						}
						headerComponent={<GridHeaderStartComponents />}
					/>
				</Stack>
			</StyledContainer>
			{imageList?.length ? (
				<ImageCarouselDialog
					open={!!imageList?.length}
					close={() => {
						setImageList([])
					}}
					ImagesList={imageList ?? []}
					showDownload={false}
				/>
			) : null}
		</>
	)
}

const RenderMenu = ({
	setSelectedBiomass,
	setIsActionInfoDrawer,
	setDrawerState,
	setOpenAddBiocharDrawer,
	params,
}: {
	setSelectedBiomass: (
		value: React.SetStateAction<{
			biomass: ICrop | null
			editMode: boolean
		}>
	) => void
	setOpenAddBiocharDrawer: (value: React.SetStateAction<string | null>) => void
	setIsActionInfoDrawer: (value: React.SetStateAction<boolean>) => void
	setDrawerState: (value: React.SetStateAction<string>) => void
	params: GridRenderCellParams<
		GridValidRowModel,
		any,
		any,
		GridTreeNodeWithRender
	>
}) => {
	const [anchorEl, setAnchorEl] = useState<null | HTMLElement>(null)

	const open = Boolean(anchorEl)
	const handleClose = () => {
		setAnchorEl(null)
	}
	const { userDetails } = useAuthContext()

	const handleActionButton = (event: React.MouseEvent<HTMLButtonElement>) => {
		setAnchorEl(event.currentTarget)
	}
	return (
		<Stack>
			<IconButton
				onClick={(e) => {
					e.stopPropagation()
					e.preventDefault()
					handleActionButton(e)
				}}>
				<MoreVert />
			</IconButton>
			<Menu
				id='basic-menu'
				anchorEl={anchorEl}
				open={open}
				onClose={handleClose}
				anchorOrigin={{ vertical: 'bottom', horizontal: 'right' }}
				transformOrigin={{ vertical: 'top', horizontal: 'right' }}
				MenuListProps={{
					'aria-labelledby': 'basic-button',
				}}>
				<Stack>
					<MenuItem
						onClick={() => {
							setIsActionInfoDrawer(true)
							setDrawerState('AddEdit')
							setSelectedBiomass({
								editMode: true,
								biomass: params?.row as ICrop,
							})
							handleClose()
						}}>
						<Box sx={{ height: '25px', width: '30px' }}>
							<EditRoundedIcon />
						</Box>
						Edit Biomass/Crop
					</MenuItem>
					<Divider />
					{userDetails?.accountType === userRoles.Admin && (
						<MenuItem
							onClick={() => {
								setIsActionInfoDrawer(true)
								setDrawerState('Assign')
								setSelectedBiomass({
									editMode: true,
									biomass: params?.row as ICrop,
								})
								handleClose()
							}}>
							<Box sx={{ height: '25px', width: '30px' }}>
								<ArrowUpwardIcon />
							</Box>
							Assign Biomass
						</MenuItem>
					)}
					<Divider />
					<MenuItem
						onClick={(e) => {
							e.stopPropagation()
							setOpenAddBiocharDrawer(params?.row?.id)
							handleClose()
						}}>
						<Box sx={{ height: '25px', width: '30px' }}>
							<Add />
						</Box>
						Add Biochar Details
					</MenuItem>
				</Stack>
			</Menu>
		</Stack>
	)
}

const StyledContainer = styled(Stack)(({ theme }) => ({
	gap: theme.spacing(2),
	'.header': {
		padding: theme.spacing(4, 0),
		borderBottom: `${theme.spacing(0.125)} solid ${
			theme.palette.neutral['100']
		}`,
	},
	'.container': {
		padding: theme.spacing(4, 3),
		gap: theme.spacing(2),
		'.grid-header-component': {
			flexDirection: 'row',
			alignItems: 'center',
			'.search-textFiled': {
				minWidth: 334,
				width: '100%',
				'.MuiInputBase-root': {
					height: theme.spacing(4.5),
					borderRadius: theme.spacing(1.25),
				},
			},
			'.btn': {
				width: '100%',
			},
		},
	},
}))
