import { authAxios, useAuthContext } from '@/contexts'
import { ICrop } from '@/types'
import { defaultLimit, defaultPage } from '@/utils/constant'
import { useQuery } from '@tanstack/react-query'
import { useSearchParams } from 'react-router-dom'

export const useBiomassTypes = () => {
	const [searchParams] = useSearchParams()

	const search = searchParams.get('search') ?? ''

	const paramsLimit = searchParams.get('limit') || defaultLimit.toString()
	const paramsPage = searchParams.get('page') || defaultPage.toString()

	const { userDetails } = useAuthContext()
	const fetchBiomass = useQuery({
		queryKey: ['getBiomassTypes', paramsLimit, paramsPage, search],
		queryFn: () => {
			const queryParams = new URLSearchParams({
				limit: paramsLimit,
				page: paramsPage,
				search,
			})

			return authAxios(`/crops?${queryParams.toString()}`)
		},
		select: ({ data }) => {
			const crops = data.crops?.reduce(
				(acc: ICrop[], curr: ICrop) => [
					...acc,
					{
						...curr,
						season: curr.season.split('_').join(' '),
						type: curr.type.split('_').join(' '),
						seasonValue: curr.season,
						biomassType: curr.type,
					},
				],
				[]
			)
			return { crops, count: data?.count }
		},
		enabled: true,
	})

	return {
		biomassList: fetchBiomass?.data?.crops ?? [],
		totalBiomass: fetchBiomass?.data?.count ?? 0,
		loading: fetchBiomass?.isLoading,
		userDetails,
	}
}
