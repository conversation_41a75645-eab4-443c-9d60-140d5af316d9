import {
  Box,
  Typography,
  Button,
  Paper,
  styled,
} from '@mui/material'
import { Dashboard } from '@mui/icons-material'
import ErrorOutlineIcon from '@mui/icons-material/ErrorOutline'
import { isRouteErrorResponse, Link, useRouteError } from 'react-router-dom'
import { useEffect } from 'react';
import { toast } from 'react-toastify'

const StyledErrorPageWrapper = styled(Box)(({ theme }) => ({
  display: 'flex',
  justifyContent: 'center',
  alignItems: 'center',
  height: '100vh',
  backgroundColor: theme.palette.background.default,

  '.error-card': {
    padding: theme.spacing(4),
    maxWidth: 400,
    textAlign: 'center',
    borderRadius: theme.spacing(2),
    backgroundColor: theme.palette.background.paper,
  },

  '.icon': {
    fontSize: 40,
    marginBottom: theme.spacing(1),
  },

  '.message': {
    marginBottom: theme.spacing(2),
  },

  '.error-id-box': {
    backgroundColor: theme.palette.grey[100],
    padding: theme.spacing(1),
    borderRadius: theme.spacing(1),
    fontFamily: 'monospace',
    fontSize: '0.875rem',
    color: theme.palette.text.primary,
    marginBottom: theme.spacing(2),
  },

  '.go-back-button': {
    fontWeight: 'bold',
    textTransform: 'none',
  },
}))

export default function ErrorPage() {
  const error = useRouteError()

  let errorId = '404'
  let errorMsg = 'Page Not Found'

  if (isRouteErrorResponse(error)) {
    errorId = `${error.status}`
    errorMsg = `${error.statusText}`
  } else if (error instanceof Error) {
    errorId = `500`
    errorMsg = `${error.message}`
  }

  useEffect(() => {
    if (errorMsg && typeof errorMsg === 'string') {
      toast.error(errorMsg, { toastId: 'error-toast' })
    }
  }, [errorMsg])

  return (
    <StyledErrorPageWrapper>
      <Paper className="error-card" elevation={3}>
        <ErrorOutlineIcon color="error" className="icon" />

        <Typography
          variant="h6"
          fontWeight="bold"
          color="error"
          textAlign="center"
          gutterBottom
        >
          Something went wrong!
        </Typography>

        <Typography
          color="text.secondary"
          textAlign="center"
          className="message"
        >
          {errorId === '404'
            ? "Oops! We couldn't find the page you're looking for."
            : "An unexpected error occurred while loading this page."}
        </Typography>

        <Box className="error-id-box">
          Error ID: {errorId}
        </Box>

        <Button
          variant="outlined"
          color="error"
          fullWidth
          startIcon={<Dashboard />}
          component={Link}
          to="/login"
          className="go-back-button"
        >
          Go Back
        </Button>
      </Paper>
    </StyledErrorPageWrapper>
  )
}