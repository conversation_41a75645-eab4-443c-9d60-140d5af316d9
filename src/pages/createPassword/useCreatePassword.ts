import { yupResolver } from '@hookform/resolvers/yup'
import { useForm } from 'react-hook-form'
import { useLocation, useNavigate, useSearchParams } from 'react-router-dom'
import { publicAxios } from '../../contexts/Api/authAxios'
import { useAuthStore } from '../../contexts/Auth/useAuthStore'
import { toast } from 'react-toastify'
import { createPasswordSchema, TCreatePassword } from './schema'
import { useMutation, useQuery } from '@tanstack/react-query'
import { AxiosError } from 'axios'
import { useAuthContext } from '@/contexts'

const initialValues: TCreatePassword = {
	password: '',
	email: '',
	confirmPassword: '',
}
interface IInvitationResponse {
	id: string
	email: string
	accountType: string
}
interface ISetpasswordResponse {
	isValid: boolean
	token: string
	refreshToken: string
}

export const useCreatePassword = () => {
	const [searchParams] = useSearchParams()
	const navigate = useNavigate()
	const { getUserDetails } = useAuthContext()
	const { pathname } = useLocation()
	const invitationId = searchParams.get('invitationId') ?? ''
	const isCreateNewPassword = pathname === '/invitation'
	const {
		register,
		watch,
		handleSubmit,
		setValue,
		formState: { errors },
	} = useForm<TCreatePassword>({
		defaultValues: initialValues,
		resolver: yupResolver<TCreatePassword>(createPasswordSchema),
	})

	const getInvitationProfileQuery = useQuery({
		queryKey: ['getInvitationQuery', invitationId],
		queryFn: async () => {
			try {
				const { data } = await publicAxios.get<IInvitationResponse>(
					`/invitation/details?invitationId=${invitationId}`
				)
				setValue('email', data?.email)
				return data
			} catch (error: any) {
				toast(error?.response?.data?.messageToUser)
			}
		},
		enabled: !!invitationId,
	})

	const createPasswordMutation = useMutation({
		mutationKey: ['createPasswordMutation'],
		mutationFn: async (password: string) => {
			const { data } = await publicAxios.put<ISetpasswordResponse>(
				`/set-password`,
				{
					invitationId,
					password,
				}
			)
			return data
		},
		onSuccess: (data) => {
			getUserDetails()
			useAuthStore.setState({
				token: data?.token,
				refreshToken: data?.refreshToken,
				isLoggedIn: true,
			})
			navigate('/dashboard/home')
		},
		onError: (error: AxiosError) => {
			toast((error?.response?.data as { messageToUser: string })?.messageToUser)
		},
	})

	const handleCreatePassword = (values: TCreatePassword) => {
		createPasswordMutation.mutate(values.password)
	}
	return {
		register,
		handleSubmit,
		watch,
		getInvitationProfileQuery,
		handleCreatePassword,
		errors,
		invitationId,
		isCreateNewPassword,
	}
}
