import backgroundBanner from '@/assets/icons/backgroud-banner.png'
import logo from '@/assets/icons/logoText.svg'
import VisibilityIcon from '@mui/icons-material/Visibility'
import VisibilityOffIcon from '@mui/icons-material/VisibilityOff'
import {
	alpha,
	Box,
	Button,
	Card,
	FormControl,
	IconButton,
	InputAdornment,
	Stack,
	styled,
	TextField,
	Typography,
} from '@mui/material'
import { FC, PropsWithChildren, useState } from 'react'
import { theme } from '@/lib/theme/theme'
import { useCreatePassword } from './useCreatePassword'

interface IVisibleButtonState {
	password: boolean
	confirmPassword: boolean
}

const initialState: IVisibleButtonState = {
	password: false,
	confirmPassword: false,
}

export const CreatePassword: FC<PropsWithChildren> = () => {
	const [isPasswordVisible, setPasswordVisible] =
		useState<IVisibleButtonState>(initialState)
	const {
		errors,
		register,
		handleSubmit,
		handleCreatePassword,
		isCreateNewPassword,
	} = useCreatePassword()

	return (
		<StyledContainer>
			<Card component={Stack} elevation={1} className='container' spacing={3}>
				<Box component={'img'} src={logo} height={52} width={212} />
				<Typography variant='h3' fontFamily={'overlock'}>
					{isCreateNewPassword ? 'Create Password' : 'Reset Password'}
				</Typography>
				{isCreateNewPassword ? (
					<Typography
						variant='subtitle1'
						textAlign='center'
						color={theme.palette.neutral[700]}>
						You can now join the team. Set your password and get started with
						the Circonomy Account
					</Typography>
				) : null}

				<Stack
					component='form'
					className='form-container'
					onSubmit={handleSubmit(handleCreatePassword)}>
					<StyledFormController fullWidth variant='standard'>
						<Typography className='label' variant='body1'>
							E-mail
						</Typography>
						<TextField
							fullWidth
							disabled
							autoFocus
							placeholder='Enter your email'
							type='email'
							error={!!errors.email?.message}
							helperText={errors.email?.message}
							{...register('email')}
						/>
					</StyledFormController>
					<StyledFormController fullWidth variant='standard'>
						<Typography className='label' variant='body1'>
							Enter your password
						</Typography>
						<TextField
							fullWidth
							placeholder='Enter your password'
							type={isPasswordVisible.password ? 'text' : 'password'}
							error={!!errors.password?.message}
							helperText={errors.password?.message}
							InputProps={{
								endAdornment: (
									<InputAdornment position='end'>
										<IconButton
											onClick={() =>
												setPasswordVisible((prev) => ({
													...prev,
													password: !prev.password,
												}))
											}>
											{isPasswordVisible.password ? (
												<VisibilityOffIcon className='icon' />
											) : (
												<VisibilityIcon className='icon' />
											)}
										</IconButton>
									</InputAdornment>
								),
							}}
							{...register('password')}
						/>
					</StyledFormController>
					<StyledFormController fullWidth variant='standard'>
						<Typography className='label' variant='body1'>
							Confirm your password
						</Typography>
						<TextField
							fullWidth
							placeholder='Confirm your password'
							type={isPasswordVisible.confirmPassword ? 'text' : 'password'}
							error={!!errors.confirmPassword?.message}
							helperText={errors.confirmPassword?.message}
							InputProps={{
								endAdornment: (
									<InputAdornment position='end'>
										<IconButton
											onClick={() =>
												setPasswordVisible((prev) => ({
													...prev,
													confirmPassword: !prev.password,
												}))
											}>
											{isPasswordVisible.confirmPassword ? (
												<VisibilityOffIcon className='icon' />
											) : (
												<VisibilityIcon className='icon' />
											)}
										</IconButton>
									</InputAdornment>
								),
							}}
							{...register('confirmPassword')}
						/>
					</StyledFormController>

					<Button
						fullWidth
						variant='contained'
						className='confirm-button'
						type='submit'>
						Confirm
					</Button>
				</Stack>
			</Card>
		</StyledContainer>
	)
}

const StyledContainer = styled(Stack)(({ theme }) => ({
	height: '100vh',
	width: '100%',
	background: `url(${backgroundBanner}) center/cover no-repeat`,
	justifyContent: 'center',
	alignItems: 'flex-start',
	paddingLeft: theme.spacing(14),
	'.container': {
		width: theme.spacing(60.75),
		padding: theme.spacing(4, 5),
		justifyContent: 'center',
		alignItems: 'center',
		flexDirection: 'column',
		borderRadius: theme.spacing(2.5),
		'.form-container': {
			width: '100%',
			gap: theme.spacing(2),
		},
		'.confirm-button': {
			padding: theme.spacing(1.5),
		},
	},
}))

const StyledFormController = styled(FormControl)(({ theme }) => ({
	gap: theme.spacing(0.5),
	'.icon': {
		color: alpha(theme.palette.neutral['500'], 0.6),
	},
	'.label': {
		color: theme.palette.neutral['500'],
	},
	'.MuiInputBase-root': {
		borderRadius: theme.spacing(0.75),
		color: theme.palette.neutral['500'],
	},
}))
