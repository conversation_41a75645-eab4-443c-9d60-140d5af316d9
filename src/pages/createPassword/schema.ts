import * as Yup from 'yup'
import { emailValidator } from '../login/schema'

export const createPasswordSchema=Yup.object({
    email: emailValidator,
	password: Yup.string().required('Please enter password'),
	confirmPassword: Yup.string()
		.required('Confirm Password is a required field')
		.oneOf([Yup.ref('password')], 'Confirm password does not match'),
})

export type TCreatePassword=Yup.InferType <typeof createPasswordSchema>