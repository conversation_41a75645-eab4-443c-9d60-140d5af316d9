import { useState } from 'react'
import { CompanyRegistrationApproved } from './components/companyRegistration/CompanyRegistrationApproved'
import { HeaderComponent } from './components/HeaderComponent'
import ContractDocuments from './components/contractDocuments/ContractDocuments'
import { DmrvCustomization } from './DmrvCustomization/DmrvCustomization'
import { CompleteRegistration } from './components'
import { CompanyRegistrationSteps } from './stage'

type ActiveComponentProps = {
	activeStep: number
	setActiveStep: React.Dispatch<React.SetStateAction<number>>
}

export const CompanyValidation = () => {
	const [activeStep, setActiveStep] = useState<CompanyRegistrationSteps>(CompanyRegistrationSteps.REGISTRATION_APPROVED)
	const [lastStartedStage, setLastStartedStage] = useState<CompanyRegistrationSteps>(CompanyRegistrationSteps.REGISTRATION_APPROVED)

	return (
		<>
			<HeaderComponent
				setLastStartedStage={setLastStartedStage}
				activeStep={activeStep}
				setActiveStep={setActiveStep}
				lastStartedStage={lastStartedStage}
				publicRoute={false}
			/>
			<ActiveComponent activeStep={activeStep} setActiveStep={setActiveStep} />
		</>
	)
}

const ActiveComponent = ({
	activeStep,
	setActiveStep,
}: ActiveComponentProps) => {
	switch (activeStep) {
		case 0:
			return <CompanyRegistrationApproved setActiveStep={setActiveStep} />
		case 1:
			return <ContractDocuments setActiveStep={setActiveStep} />
		case 2:
			return <DmrvCustomization setActiveStep={setActiveStep} />
		case 3:
			return <CompleteRegistration />
		default:
			return <></>
	}
}
