import { Stack, Typography, styled, Switch, Button, CircularProgress } from '@mui/material'
import { IDmrvCustomizationPayload, useDmrvCustomization } from './useDmrvCustomization'
import HelpOutlineIcon from '@mui/icons-material/HelpOutline'
import { CompanyRegistrationSteps } from '../stage'


type Feature = {
  key: string
  title: string
  description: string
}
const initialData: Feature[] = [
  {
    key: 'isTEAEnabled',
    title: 'TEA (Technology, Engineering and Architecture)',
    description:
      'Enable this feature to incorporate advanced technology and architectural insights into your project assessment.',
  },
  {
    key: 'isLCAEnabled',
    title: 'LCA (Lifecycle Assessment)',
    description:
      'Select this option to perform a comprehensive lifecycle assessment, evaluating environmental impacts at each stage of your project lifecycle.',
  },
  {
    key: 'isFinanceToolEnabled',
    title: 'Finance Tool',
    description:
      'Activate the finance analysis and forecasting tools tailored to your project needs.',
  },
]


export const DmrvCustomization = ({ setActiveStep }: { setActiveStep: React.Dispatch<React.SetStateAction<CompanyRegistrationSteps>> }) => {

  const {
    isLoading,
    isEditable,
    enableToggle,
    handleToggle,
    handleSubmit,
    isSubmitting,
  } = useDmrvCustomization({ setActiveStep })

  if (isLoading) {
    return (
      <StyledContainer>
        <CircularProgress />
      </StyledContainer>
    )
  }

  return (
    <StyledContainer>
      <Stack className='container'>
        {initialData.map(({ title, description, key }) => (
          <Stack key={key} className='card-content'>
            <Stack gap={1}>
              <Typography variant='h6'>{title}</Typography>
              <Typography color='text.secondary'>{description}</Typography>
            </Stack>
            <Switch
              checked={enableToggle[key as keyof IDmrvCustomizationPayload] ?? false}
              onChange={() => handleToggle(key as keyof IDmrvCustomizationPayload)}
              disabled={!isEditable}
            />
          </Stack>
        ))}
      </Stack>

      <Stack
        width='50%'
        mt={10}
        sx={{ borderBottom: (theme) => `1px solid ${theme.palette.divider}` }}
      />

      <Stack className='footer-content'>
        <Stack direction='row' gap={1}>
          <HelpOutlineIcon color='action' fontSize='inherit' sx={{ fontSize: 18 }} />
          <Typography variant='body1' color='text.secondary'>
            Need help? Contact us at{' '}
            <span style={{ color: '#D06C5E' }}><EMAIL></span>
          </Typography>
        </Stack>

        {isEditable && (
          <Button
            variant='contained'
            endIcon={<span style={{ display: 'flex', alignItems: 'center' }}>{'>'}</span>}
            onClick={handleSubmit}
            disabled={isSubmitting}
          >
            {isSubmitting ? 'Saving...' : 'Proceed to Next Step'}
          </Button>
        )}
      </Stack>
    </StyledContainer>
  )
}

const StyledContainer = styled(Stack)(({ theme }) => ({
  padding: theme.spacing(4),
  width: '100%',
  display: 'flex',
  flexDirection: 'column',
  alignItems: 'center',
  justifyContent: 'center',
  marginBottom: theme.spacing(3),
  gap: theme.spacing(3),

  '.container': {
    width: '50%',
    gap: theme.spacing(5),
    border: `1px solid ${theme.palette.divider}`,
    padding: theme.spacing(4),
    borderRadius: theme.spacing(2),
  },
  '.card-content': {
    flexDirection: 'row',
    justifyContent: 'space-between',
    alignItems: 'flex-start',
    border: `1px solid ${theme.palette.divider}`,
    padding: theme.spacing(3),
    borderRadius: theme.spacing(1),
    boxShadow: '0 1px 4px rgba(0,0,0,0.07)',
  },
  '.footer-content': {
    display: 'flex',
    flexDirection: 'row',
    justifyContent: 'space-between',
    alignItems: 'center',
    width: '50%',
    marginTop: theme.spacing(1),
  },

}))