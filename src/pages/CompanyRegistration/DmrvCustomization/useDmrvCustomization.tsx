import { useQuery, useMutation, useQueryClient } from '@tanstack/react-query'
import { authAxios, useAuthContext } from '@/contexts'
import { toast } from 'react-toastify'
import { AxiosError } from 'axios'
import { useEffect, useState } from 'react'
import { ICompanyDetails, ICompanyStage } from '@/utils/constant'
import { getCompanyDetails } from '../components/useHeaderComponent'
import { CompanyRegistrationSteps } from '../stage'

export interface IDmrvCustomizationPayload {
	isTEAEnabled: boolean
	isLCAEnabled: boolean
	isFinanceToolEnabled: boolean
}

const updateDmrvData = async (
	payload: IDmrvCustomizationPayload,
	companyID?: string
) => {
	if (!companyID) return
	const response = await authAxios.put(
		`/company/${companyID}/toggle-dmrv-customization`,
		payload
	)
	return response.data
}

export const useDmrvCustomization = ({
	setActiveStep,
}: {
	setActiveStep: React.Dispatch<React.SetStateAction<CompanyRegistrationSteps>>
}) => {
	const queryClient = useQueryClient()

	const { userDetails } = useAuthContext()

	const query = useQuery({
		queryKey: ['companyDetails', userDetails?.companyId],
		queryFn: getCompanyDetails,
		initialData: () =>
			queryClient.getQueryData<ICompanyDetails>([
				'companyDetails',
				userDetails?.companyId,
			]),
		enabled: false,
	})

	const companyID = userDetails?.companyId

	const [enableToggle, setEnableToggle] = useState<IDmrvCustomizationPayload>({
		isTEAEnabled: false,
		isLCAEnabled: false,
		isFinanceToolEnabled: false,
	})
	const [isEditable, setIsEditable] = useState(true)

	const mutation = useMutation({
		mutationFn: (payload: IDmrvCustomizationPayload) =>
			updateDmrvData(payload, companyID),

		onSuccess: () => {
			queryClient.invalidateQueries({
				queryKey: ['dmrvCustomization', companyID],
			})
			toast.success('dMRV customization data updated successfully')
			query.refetch()
			setActiveStep(CompanyRegistrationSteps.COMPLETE_REGISTRATION)
		},
		onError: (error: AxiosError) => {
			const errorMsg =
				error?.response?.data &&
				typeof error.response.data === 'object' &&
				'messageToUser' in error.response.data
					? (error.response.data as { messageToUser: string }).messageToUser
					: 'An error occurred while updating details.'
			toast.error(errorMsg)
		},
	})

	useEffect(() => {
		if (query.data) {
			const dmrvStage = query.data?.stages?.find(
				(s: ICompanyStage) => s.stage === 'dmrv_customization'
			)

			setIsEditable(dmrvStage?.status === 'not_started')

			const newEnablData: IDmrvCustomizationPayload = {
				isTEAEnabled: query.data.isTEAEnabled ?? false,
				isLCAEnabled: query.data.isLCAEnabled ?? false,
				isFinanceToolEnabled: query.data.isFinanceToolEnabled ?? false,
			}
			setEnableToggle(newEnablData)
		}
	}, [query.data])

	const handleToggle = (key: keyof IDmrvCustomizationPayload) => {
		if (!isEditable) return
		setEnableToggle((prev) => ({
			...prev,
			[key]: !prev[key],
		}))
	}

	const handleSubmit = () => {
		mutation.mutate(enableToggle)
	}

	return {
		isLoading: query.isLoading,
		isEditable,
		enableToggle,
		handleToggle,
		handleSubmit,
		isSubmitting: mutation.isPending,
	}
}
