import { CompanyRegistrationForm } from './components/companyRegistration/CompanyRegistrationForm'
import { HeaderComponent } from './components/HeaderComponent'
import { useState } from "react"
import { CompanyRegistrationSteps } from './stage'


export const CompanyRegistration = () => {
	const [activeStep, setActiveStep] = useState<CompanyRegistrationSteps>(CompanyRegistrationSteps.REGISTRATION_APPROVED)

	return (
		<>
			<HeaderComponent
				activeStep={activeStep}
				setActiveStep={setActiveStep}
				lastStartedStage={0}
				publicRoute
			/>
			<CompanyRegistrationForm />
		</>
	)
}
