import { useFieldArray, useForm } from 'react-hook-form'
import { yupResolver } from '@hookform/resolvers/yup'
import {
	QueryFunctionContext,
	useMutation,
	useQuery,
	useQueryClient,
} from '@tanstack/react-query'
import { toast } from 'react-toastify'
import { publicAxios } from '@/contexts'
import { useEffect, useState } from 'react'
import { CompanyRegistrationSchema, TCompanyRegistrationSchema } from './schema'
import { AxiosError } from 'axios'
import { useSearchParams } from 'react-router-dom'

const initialValues = () => ({
	name: '',
	email: '',
	countryCode: '',
	country: '',
	phoneNumber: '',
	pinCode: '',
	state: '',
	address: '',
	coiDocumentId: '',
	companyLogoId: '',
	serviceType: '',
	registry: '',
	admins: [
		{
			name: '',
			email: '',
			countryCode: '',
			phoneNumber: '',
			profileImageId: '',
			managerLogoUrl: '',
		},
	],
})

type companyIntialValueResponse = {
	companyName: string
	managerName: string
	managerEmail: string
	isValid?: boolean
}

const getCompanyDetails = async ({ queryKey }: QueryFunctionContext) => {
	const invitationId = queryKey[1]
	const { data } = await publicAxios.get<companyIntialValueResponse>(
		`/company/public/invitation/${invitationId}`
	)
	return data
}

export const useCompanyRegistrationForm = () => {
	const [searchParams] = useSearchParams()

	const invitationId = searchParams.get('invitationId')

	const submitFunction = async (data: TCompanyRegistrationSchema) => {
		const response = await publicAxios.post('/company/public/request', {
			...data,
			invitationId: invitationId,
		})
		return response.data
	}

	const companyDetailsQuery = useQuery({
		queryKey: ['companyInitialData', invitationId],
		queryFn: getCompanyDetails,
		enabled: !!invitationId,
	})

	const queryClient = useQueryClient()
	const [openRegistrationPopup, setOpenRegistrationPopup] =
		useState<boolean>(false)

	const methods = useForm<TCompanyRegistrationSchema>({
		mode: 'onChange',
		resolver: yupResolver<TCompanyRegistrationSchema>(
			CompanyRegistrationSchema
		),
		defaultValues: initialValues(),
	})

	const { control, setValue } = methods
	const { fields, append, remove } = useFieldArray({
		control,
		name: 'admins',
	})

	useEffect(() => {
		if (companyDetailsQuery.data) {
			setValue('name', companyDetailsQuery?.data?.companyName ?? '')
			setValue('admins.0.name', companyDetailsQuery?.data?.managerName ?? '')
			setValue('admins.0.email', companyDetailsQuery?.data?.managerEmail ?? '')
		}
	}, [companyDetailsQuery?.data, setValue])

	const { mutate: onSubmit } = useMutation({
		mutationFn: submitFunction,
		onSuccess: (data) => {
			queryClient.refetchQueries({
				queryKey: ['useCompanyDetails'],
			})
			toast(`${data?.message || 'Company registration successful.'}`)
			setOpenRegistrationPopup(true)
		},
		onError: (error: AxiosError) => {
			const errorMessage =
				error?.response?.data &&
				typeof error.response.data === 'object' &&
				'messageToUser' in error.response.data
					? (error.response.data as { messageToUser: string }).messageToUser
					: 'An error occurred while during submission.'
			toast.error(errorMessage)
		},
	})


	return {
		...methods,
		fields,
		append,
		remove,
		onSubmit,
		openRegistrationPopup,
		setOpenRegistrationPopup,
		companyDetailsQuery,
	}
}
