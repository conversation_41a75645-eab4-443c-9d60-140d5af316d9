import {
	Country<PERSON><PERSON>s,
	CustomFileUploader,
	PhoneInputComponent,
} from '@/components'
import {
	Button,
	FormHelperText,
	MenuItem,
	Stack,
	styled,
	Typography,
} from '@mui/material'
import { useCompanyRegistrationForm } from './useCompanyRegistrationForm'
import { useCallback } from 'react'
import BusinessIcon from '@mui/icons-material/Business'
import PeopleIcon from '@mui/icons-material/People'
import AddIcon from '@mui/icons-material/Add'
import { ImageFileTypes } from '@/pages/dashboard'
import { theme } from '@/lib/theme/theme'
import { FormProvider } from 'react-hook-form'
import { CompanyManagerDetails } from './CompanyManagerDetails'
import {
	RegistryTypeEnum,
	RegistryTypeNames,
	ServiceTypeEnum,
	ServiceTypeNames,
} from '@/utils/constant'
import { CustomTextField } from '@/utils/components'
import { CompanyRegistrationSchema } from './schema'
import RegistrationPendingDialog from './RegistrationPendingDialog'
import InvalidLinkDialog from './subComponents/InvalidLinkDialog'

const ServiceData = [
	{ label: ServiceTypeNames.industrial, value: ServiceTypeEnum.industrial },
	{ label: ServiceTypeNames.artisanal, value: ServiceTypeEnum.artisanal },
	{
		label: ServiceTypeNames.iot_artisanal,
		value: ServiceTypeEnum.iot_artisanal,
	},
]

const RegistryData = [
	{ label: RegistryTypeNames.csi, value: RegistryTypeEnum.CSI },
	{ label: RegistryTypeNames.puro, value: RegistryTypeEnum.Puro },
	{ label: RegistryTypeNames.isometric, value: RegistryTypeEnum.Isometric },
]

export const smallInputStyle = {
	'& .MuiInputBase-root': {
		height: theme.spacing(6),
	},
}

export const CompanyRegistrationForm = () => {
	const {
		handleSubmit,
		fields,
		append,
		remove,
		onSubmit,
		openRegistrationPopup,
		setOpenRegistrationPopup,
		companyDetailsQuery,
		...methods
	} = useCompanyRegistrationForm()

	const {
		formState: { errors },
		setValue,
		watch,
		setError,
		clearErrors,
		register,
		getValues,
	} = methods

	const handleOnChange = useCallback(
		(value: string, dialCode: string) => {
			setValue('countryCode', `+${dialCode}`, {
				shouldDirty: true,
			})

			setValue('phoneNumber', value, {
				shouldDirty: true,
			})
			if (value.toString().length === 0) {
				setError('phoneNumber', { message: 'Please enter phone number' })
				return
			}
			clearErrors('phoneNumber')
		},
		[clearErrors, setError, setValue]
	)

	return (
		<>
			<FormProvider handleSubmit={handleSubmit} {...methods}>
				<form onSubmit={handleSubmit((data) => onSubmit(data))}>
					<StyledContainer className='container'>
						<Stack className='card-container'>
							<Stack className='card'>
								<Stack className='titleContainer'>
									<BusinessIcon color='primary' />
									<Typography className='title'>Company Details</Typography>
								</Stack>
								<Stack direction={'row'}>
									<Stack className='formContainer' gap={3}>
										<Stack className='threeRows'>
											<CustomTextField
												watch={watch}
												sx={smallInputStyle}
												schema={CompanyRegistrationSchema}
												id='name'
												label='Company Name'
												disabled
												variant='outlined'
												placeholder='Enter Company Name'
												fullWidth
												{...register('name')}
												error={!!errors?.name?.message}
												helperText={errors?.name?.message}
											/>
											<Stack>
												<PhoneInputComponent
													smallSize
													fullWidth
													dialCode={watch('countryCode')}
													value={String(watch('phoneNumber'))}
													handleOnChange={handleOnChange}
													getSelectedCountryDialCode={(dialCode) =>
														setValue('countryCode', dialCode)
													}
												/>
												<FormHelperText error>
													{errors?.phoneNumber?.message}
													{errors?.countryCode?.message}
												</FormHelperText>
											</Stack>
											<CustomTextField
												watch={watch}
												sx={smallInputStyle}
												schema={CompanyRegistrationSchema}
												id='email'
												label='Email'
												variant='outlined'
												placeholder='Enter Email'
												fullWidth
												{...register('email')}
												error={!!errors?.email?.message}
												helperText={errors?.email?.message}
											/>
											<CustomTextField
												watch={watch}
												sx={smallInputStyle}
												schema={CompanyRegistrationSchema}
												select
												fullWidth
												value={watch('country')}
												label='Select the Country'
												{...register('country')}
												id='country'
												error={!!errors?.country?.message}
												helperText={errors?.country?.message}>
												{CountryNames.map((country) => (
													<MenuItem key={country.name} value={country.name}>
														{country.name}
													</MenuItem>
												))}
											</CustomTextField>
											<CustomTextField
												watch={watch}
												sx={smallInputStyle}
												schema={CompanyRegistrationSchema}
												id='state'
												label='State'
												variant='outlined'
												placeholder='Enter State'
												fullWidth
												{...register('state')}
												error={!!errors?.state?.message}
												helperText={errors?.state?.message}
											/>
											<CustomTextField
												watch={watch}
												sx={smallInputStyle}
												schema={CompanyRegistrationSchema}
												id='pinCode'
												label='Pincode'
												variant='outlined'
												placeholder='Enter Pin Code'
												fullWidth
												{...register('pinCode')}
												error={!!errors?.pinCode?.message}
												helperText={errors?.pinCode?.message}
											/>
										</Stack>
										<CustomTextField
											watch={watch}
											sx={smallInputStyle}
											schema={CompanyRegistrationSchema}
											id='address'
											label='Company Address'
											variant='outlined'
											placeholder='Enter Company Address'
											fullWidth
											{...register('address')}
											error={!!errors?.address?.message}
											helperText={errors?.address?.message}
										/>
										<Stack className='threeRows'>
											<CustomTextField
												watch={watch}
												sx={smallInputStyle}
												schema={CompanyRegistrationSchema}
												select
												fullWidth
												value={watch('serviceType')}
												label='Service Type'
												{...register('serviceType')}
												id='serviceType'
												error={!!errors?.serviceType?.message}
												helperText={errors?.serviceType?.message}>
												{ServiceData.map((service) => (
													<MenuItem key={service.value} value={service.value}>
														{service.label}
													</MenuItem>
												))}
											</CustomTextField>
											<CustomTextField
												watch={watch}
												sx={smallInputStyle}
												schema={CompanyRegistrationSchema}
												select
												fullWidth
												value={watch('registry')}
												label='Registry Type'
												{...register('registry')}
												id='registry'
												error={!!errors?.registry?.message}
												helperText={errors?.registry?.message}>
												{RegistryData.map((registry) => (
													<MenuItem key={registry.value} value={registry.value}>
														{registry.label}
													</MenuItem>
												))}
											</CustomTextField>
										</Stack>
									</Stack>
									<Stack
										width='30%'
										marginLeft={theme.spacing(3)}
										marginBottom={theme.spacing(10)}
										justifyContent='center'
										alignItems={'center'}>
										<CustomFileUploader
											directionColumnReverse
											imageUrl={watch('companyLogoUrl') ?? ''}
											acceptFileTypes={ImageFileTypes}
											heading='Upload Company Logo'
											sx={{
												height: theme.spacing(20),
												width: theme.spacing(20),
												borderRadius: 30,
											}}
											imageHeight={100}
											setUploadData={({ id }) => {
												setValue('companyLogoId', id, {
													shouldDirty: true,
													shouldTouch: true,
													shouldValidate: true,
												})
											}}
											type='company_logo'
											publicRoute
											mediaType='image'
										/>
										<FormHelperText error={true}>
											{errors?.companyLogoId?.message}
										</FormHelperText>
									</Stack>
								</Stack>
								<Stack>
									<Stack>
										<CustomFileUploader
											directionColumnReverse
											imageUrl={watch('coiDocumentId') ?? ''}
											acceptFileTypes={['pdf']}
											heading='Upload COI Document'
											sx={{
												height: theme.spacing(20),
												width: theme.spacing(20),
												borderRadius: 3,
											}}
											imageHeight={100}
											setUploadData={({ id }) => {
												setValue('coiDocumentId', id, {
													shouldDirty: true,
													shouldTouch: true,
													shouldValidate: true,
												})
											}}
											type='coi_document'
											publicRoute
										/>
										<FormHelperText error={true}>
											{errors?.coiDocumentId?.message}
										</FormHelperText>
									</Stack>
								</Stack>
							</Stack>
							<Stack className='card'>
								<Stack className='titleContainer'>
									<PeopleIcon color='primary' />
									<Typography className='title'>Admin Details</Typography>
								</Stack>
								{fields.map((item, index) => (
									<CompanyManagerDetails
										key={item.id}
										item={item}
										index={index}
										remove={remove}
									/>
								))}
								{watch('admins')?.length < 2 ? (
									<Stack className='addButtonContainer'>
										<Button
											startIcon={<AddIcon />}
											onClick={() => {
												if ((watch('admins') ?? []).length >= 2) {
													return
												}
												append({
													name: '',
													email: '',
													countryCode: '',
													phoneNumber: '',
													profileImageId: '',
												})
											}}
											className='addBtn'>
											Add Another Admin
										</Button>
									</Stack>
								) : null}
							</Stack>
							<Stack className='submitContainer'>
								<Button
									className='submitButton'
									type='submit'
									variant='contained'
									disabled={false}>
									Submit for Approval
								</Button>
							</Stack>
						</Stack>
					</StyledContainer>
				</form>
			</FormProvider>

			<RegistrationPendingDialog
				companyName={getValues('name')}
				open={openRegistrationPopup}
				setOpen={setOpenRegistrationPopup}
			/>
			<InvalidLinkDialog open={!companyDetailsQuery?.isPending && !companyDetailsQuery?.data?.isValid && !openRegistrationPopup} />
		</>
	)
}

const StyledContainer = styled(Stack)(({ theme }) => ({
	padding: theme.spacing(4),
	width: '100%',
	display: 'flex',
	flexDirection: 'column',
	alignItems: 'center',
	justifyContent: 'center',
	marginBottom: theme.spacing(3),
	gap: theme.spacing(3),
	'.threeRows': {
		display: 'grid',
		gridTemplateColumns: 'repeat(3, 1fr)',
		gap: theme.spacing(3),
		width: '100%',
	},
	'.submitContainer': {
		width: '100%',
		display: 'flex',
		flexDirection: 'row',
		justifyContent: 'flex-end',
		'.submitButton': {
			width: theme.spacing(30),
			height: theme.spacing(4.5),
			padding: theme.spacing(1, 2.5),
		},
	},
	'.addButtonContainer': {
		width: '100%',
		display: 'flex',
		flexDirection: 'row',
		justifyContent: 'flex-start',
		'.addBtn': {
			// color: theme.palette.primary.main,
			// backgroundColor: 'transparent',
			borderColor: theme.palette.primary.main,
			alignItems: 'center',
			'&:hover': {
				backgroundColor: 'transparent',
				color: theme.palette.primary.dark,
			},
		},
	},

	'.titleContainer': {
		display: 'flex',
		flexDirection: 'row',
		alignItems: 'center',
		// justifyContent: 'center',
		width: '100%',
		gap: theme.spacing(1),
	},
	'.title': {
		fontSize: theme.spacing(2.4),
		fontWeight: 500,
		// color: theme.palette.primary.main,
	},
	'.card': {
		border: '1px solid grey',
		borderColor: theme.palette.neutral[200],
		padding: theme.spacing(4),
		display: 'flex',
		flexDirection: 'column',
		gap: theme.spacing(3),
		borderRadius: theme.spacing(2),
	},
	'.managerCard': {
		border: '1px solid grey',
		borderColor: theme.palette.neutral[200],
		padding: theme.spacing(4),
		display: 'flex',
		flexDirection: 'row',
		alignItems: 'center',

		gap: theme.spacing(3),
		borderRadius: theme.spacing(2),
	},
	'.card-container': {
		width: '65%',
		display: 'flex',
		flexDirection: 'column',
		gap: theme.spacing(3),
	},
	'.removeBtn': {
		position: 'absolute',
		top: 0,
		right: 0,
		transform: 'translate(50%, -50%)',
		padding: theme.spacing(0),
		backgroundColor: theme.palette.primary.dark,
		border: '1px solid #ccc',
		boxShadow: 1,
		'&:hover': {
			backgroundColor: theme.palette.primary.light,
		},
		'.removeIcon': {
			color: theme.palette.common.white,
			width: theme.spacing(3),
			height: theme.spacing(3),
		},
	},
}))
