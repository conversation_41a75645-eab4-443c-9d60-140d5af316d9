import * as React from 'react';
import { styled } from '@mui/material/styles';
import Dialog from '@mui/material/Dialog';
import pendingRegistration from '@/assets/icons/pending_registration.svg'
import Typography from '@mui/material/Typography';
import { Box, Card } from '@mui/material';
import { ImgFileSuccess, ImgMail, ImgClock } from '@/assets/icons';
import { theme } from '@/lib/theme/theme';

type RegistrationPendingDialogProps = {
    companyName: string,
    open: boolean,
    setOpen: React.Dispatch<React.SetStateAction<boolean>>
}



export default function RegistrationPendingDialog({ companyName, open = true, setOpen }: RegistrationPendingDialogProps) {
    const getCurrentDate = () => {
        const currentDate = new Date()
        const formatted = currentDate.toLocaleDateString('en-US', {
            year: 'numeric',
            month: 'long',
            day: 'numeric',
        });
        return formatted;
    }

    const submissionDate = getCurrentDate();

    const handleClose = () => {
        setOpen(false);
    };


    return (
        <React.Fragment>
            <BootstrapDialog
                onClose={handleClose}
                aria-labelledby="customized-dialog-title"
                open={open}
                PaperProps={{
                    sx: {
                        width: theme.spacing(80),
                        maxWidth: '90%',
                        backgroundColor: theme.palette.grey[100],
                        paddingX: theme.spacing(3),
                        paddingBottom: theme.spacing(3)
                    },
                }}
            >
                <DialogHeader id="customized-dialog-title">
                    <Box
                        component={'img'}
                        src={pendingRegistration}
                        height={68}
                        width={68}
                    />

                    <Typography variant='h3'>
                        Registration Sent for Approval
                    </Typography>

                    <Typography
                        color={theme.palette.custom.grey[800]}
                        variant="body1"
                        textAlign={'center'}
                    >
                        Your company details have been submitted to Circonomy.
                        We're currently reviewing your request.
                    </Typography>
                </DialogHeader>

                <Box display={'flex'} flexDirection={'column'} gap={2}>
                    <CustomCardComponent>
                        <CardInfoWrapper>
                            <Box display={'flex'} alignItems={'center'} gap={1}>
                                <Box component={'img'} src={ImgFileSuccess} width={20} height={20} />
                                <Typography variant='body2'>
                                    Request Summary
                                </Typography>
                            </Box>
                            <Box display={'flex'}>
                                <Box width={'50%'} display={'flex'} flexDirection={'column'} gap={1}>
                                    <Box>
                                        <Typography variant='caption'>Company Name</Typography>
                                        <Typography variant='body1'>{companyName}</Typography>
                                    </Box>
                                    <Box>
                                        <Typography variant='caption'>Status</Typography>
                                        <Box
                                            paddingX={1}
                                            display={'flex'}
                                            justifyContent={'center'}
                                            alignItems={'center'}
                                            gap={1}
                                            width={'50%'}
                                            sx={{
                                                backgroundColor: theme.palette.custom.red[600],
                                                borderRadius: '30px'
                                            }}
                                        >
                                            <Box component={'img'} src={ImgClock} width={12} height={12} />
                                            <Typography
                                                color={theme.palette.custom.red[700]}
                                                variant='caption'
                                            >
                                                Pending Review
                                            </Typography>
                                        </Box>
                                    </Box>
                                </Box>
                                <Box width={'50%'} display={'flex'} flexDirection={'column'} gap={1}>
                                    <Box>
                                        <Typography variant='caption'>Submitted Date</Typography>
                                        <Typography variant='body1'>{submissionDate}</Typography>
                                    </Box>
                                    <Box>
                                        <Typography variant='caption'>Estimated Review Time</Typography>
                                        <Typography variant='body1'>1–3 business days</Typography>
                                    </Box>
                                </Box>
                            </Box>
                        </CardInfoWrapper>
                    </CustomCardComponent>

                    <CustomCardComponent>
                        <CardMailWrapper>
                            <Box>
                                <Box component={'img'} src={ImgMail} />
                            </Box>
                            <Box display={'flex'} flexDirection={'column'} gap={1}>
                                <Typography variant='body2'>What happens next?</Typography>
                                <Typography variant='subtitle1'>
                                    We'll notify you <NAME_EMAIL> once the review is complete.
                                </Typography>
                            </Box>
                        </CardMailWrapper>
                    </CustomCardComponent>
                </Box>

            </BootstrapDialog>
        </React.Fragment>
    );
}


const BootstrapDialog = styled(Dialog)(({ theme }) => ({
    '& .MuiDialogContent-root': {
        padding: theme.spacing(1),
    },
    '& .MuiDialogActions-root': {
        padding: theme.spacing(1),
    },
    '&. MuiPaperProps-root': {
        width: '640px',
        maxWidth: '90%',
        paddingBottom: theme.spacing(3),
    },
    '& .MuiBackdrop-root': {
        backgroundColor: 'rgba(255, 255, 255, 0.6)',
        backdropFilter: 'blur(6px)',
    },
}));

const DialogHeader = styled(Box)(({ theme }) => ({
    padding: theme.spacing(3),
    display: "flex",
    gap: theme.spacing(2),
    alignItems: "center",
    justifyContent: "center",
    flexDirection: "column"
}));

const CustomCardComponent = styled(Card)(({ theme }) => ({
    display: "flex",
    padding: theme.spacing(2)
}));

const CardInfoWrapper = styled(Box)(({ theme }) => ({
    width: '100%',
    display: 'flex',
    flexDirection: 'column',
    gap: theme.spacing(2),
    padding: theme.spacing(2),
}));

const CardMailWrapper = styled(Box)(({ theme }) => ({
    width: '100%',
    display: 'flex',
    gap: theme.spacing(2),
    padding: theme.spacing(2)
}));