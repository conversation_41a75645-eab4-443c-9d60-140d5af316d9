import { Box, CircularProgress, Stack, styled } from "@mui/material"
import { useQuery, useQueryClient } from "@tanstack/react-query"
import { useAuthContext } from "@/contexts"
import { CompanyDetails } from "./subComponents/CompanyDetails";
import { UploadedDocuments } from "./subComponents/UploadedDocuments";
import { ManagerDetails } from "./subComponents/ManagerDetails";
import { NextStep } from "./subComponents/NextStep";
import { ICompanyDetails } from "@/utils/constant";


export const CompanyRegistrationApproved = ({
    setActiveStep,
}: {
    setActiveStep: React.Dispatch<React.SetStateAction<number>>
}) => {

    const queryClient = useQueryClient()
    const { userDetails } = useAuthContext()

    const companyDetails = useQuery({
        queryKey: ["companyDetails", userDetails?.companyId],
        initialData: () => queryClient.getQueryData<ICompanyDetails>(['companyDetails', userDetails?.companyId]),
        enabled: false
    })

    if (companyDetails.isPending) {
        return (
            <StyledBox>
                <CircularProgress />
            </StyledBox>
        );
    }

    return (
        <StyledContainer>
            <Stack className="container">
                <CompanyDetails companyDetails={companyDetails?.data} />
                <UploadedDocuments filename={companyDetails?.data?.coiDocument?.fileName} url={companyDetails?.data?.coiDocument?.url} createdAt={companyDetails?.data?.coiDocument?.createdAt} />
            </Stack>
            <Stack className="container">
                <ManagerDetails adminDetails={companyDetails?.data?.admins} />
                <NextStep setActiveStep={setActiveStep} />
            </Stack>
        </StyledContainer>
    )
}

const StyledBox = styled(Box)(() => ({
    display: 'flex',
    justifyContent: 'center',
    alignItems: 'center',
    height: '50vh',
    width: '100%',
}));

const StyledContainer = styled(Stack)(({ theme }) => ({
    padding: theme.spacing(2.5),
    width: '100%',
    display: 'flex',
    flexDirection: 'row',
    // alignItems: 'center',
    justifyContent: 'center',
    marginBottom: theme.spacing(4),
    gap: theme.spacing(2.5),
    '.container': {
        gap: theme.spacing(2.5),
    },
}))
