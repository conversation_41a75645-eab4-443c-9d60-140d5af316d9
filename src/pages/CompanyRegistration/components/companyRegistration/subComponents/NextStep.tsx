import { Stack, Typography, Button } from "@mui/material";
import { StyledCard } from "./StyledCard";
import ArrowForwardIosOutlinedIcon from '@mui/icons-material/ArrowForwardIosOutlined';


export const NextStep = ({ setActiveStep }: { setActiveStep: React.Dispatch<React.SetStateAction<number>> }) => {
    return (
        <StyledCard className="card">
            <Stack className="nextContainer">
                <Stack className="logoContainer">
                    <ArrowForwardIosOutlinedIcon color="primary" />
                </Stack>
                <Stack className="subheadingContainer">
                    <Typography className="title">Ready for the next step</Typography>
                    <Typography>Proceed to upload contract documents</Typography>
                </Stack>
                <Button variant="contained" color="primary" onClick={() => setActiveStep(1)}>Continue to Contract Documents</Button>
            </Stack>
        </StyledCard>
    );
}