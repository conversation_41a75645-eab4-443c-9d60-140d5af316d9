import { Stack, Typography, Box, Divider } from "@mui/material";
import { StyledCard } from "./StyledCard";
import PlaceOutlinedIcon from '@mui/icons-material/PlaceOutlined';
import PhoneOutlinedIcon from '@mui/icons-material/PhoneOutlined';
import EmailOutlinedIcon from '@mui/icons-material/EmailOutlined';
import BusinessIcon from '@mui/icons-material/Business';
import { ICompanyDetails, RegistryTypeNames, ServiceTypeNames } from "@/utils/constant";

const CompanyDetailsData = [
    { icon: PlaceOutlinedIcon, title: "Address", fieldName: "address", },
    { icon: PhoneOutlinedIcon, title: "Phone Number", fieldName: "phoneNumber", },
    { icon: null, title: "Pin Code", fieldName: "pinCode", },
    { icon: null, title: "State", fieldName: "state", },
    { icon: EmailOutlinedIcon, title: "Email Address", fieldName: "email", },
    { icon: null, title: "Country", fieldName: "country", },
]

export const CompanyDetails = ({ companyDetails }: { companyDetails?: ICompanyDetails }) => {
    return (
        <StyledCard>
            <Stack className="leftPanel">
                <Stack className="titleContainer">
                    <BusinessIcon color="primary" className="titleIcon" />
                    <Typography className="title">Company Details</Typography>
                </Stack>
                <Stack direction="row" alignItems="center" gap={2}>
                    <Box
                        component='img'
                        height={40}
                        width={40}
                        className='logo'
                        src={companyDetails?.companyLogo?.url ?? ""}
                    />
                    <Stack className="companyHeader">
                        <Typography className="comTitle">{companyDetails?.name}</Typography>
                        <Typography className="comSubHeading">Registered Company</Typography>
                    </Stack>
                </Stack>
                <Divider className="divider" />
                <Stack className="threeCol">
                    {CompanyDetailsData.map((item, index) => (
                        <Stack key={index} className={`companyDetails ${item.fieldName === 'address' ? 'twoColChild' : ''}`}>
                            <Stack className="detailsHeader">
                                {item.icon && <item.icon className="icon" />}
                                <Typography className="detailsTitle">{item.title}</Typography>
                            </Stack>
                            <Typography className="details">
                                {(() => {
                                    const value = companyDetails?.[item.fieldName as keyof ICompanyDetails];
                                    if (typeof value === 'string' || typeof value === 'number') {
                                        return value;
                                    }
                                    return '-';
                                })()}
                            </Typography>
                        </Stack>
                    ))}
                    <Stack key="appType" className={`companyDetails`}>
                        <Stack className="detailsHeader">
                            <Typography className="detailsTitle">App Type</Typography>
                        </Stack>
                        <Typography className="details">
                            {ServiceTypeNames[companyDetails?.serviceType as keyof typeof ServiceTypeNames] || companyDetails?.serviceType}
                        </Typography>
                    </Stack>
                    <Stack key="registryType" className={`companyDetails`}>
                        <Stack className="detailsHeader">
                            <Typography className="detailsTitle">Registry Type</Typography>
                        </Stack>
                        <Typography className="details"> {RegistryTypeNames[companyDetails?.registry as keyof typeof RegistryTypeNames]}</Typography>
                    </Stack>
                </Stack>
            </Stack>
        </StyledCard>
    );
}