import { Stack, Typography, Box } from "@mui/material";
import { StyledCard } from "./StyledCard";
import PersonOutlineOutlinedIcon from '@mui/icons-material/PersonOutlineOutlined';
import EmailOutlinedIcon from '@mui/icons-material/EmailOutlined';
import PhoneOutlinedIcon from '@mui/icons-material/PhoneOutlined';
import { IAdmin } from "@/utils/constant";



export const ManagerDetails = ({ adminDetails }: { adminDetails?: IAdmin[] }) => {
    return (
        <StyledCard className="card fw">
            <Stack className="titleContainer">
                <PersonOutlineOutlinedIcon color="primary" className="titleIcon" />
                <Typography className="title">Manager Details</Typography>
            </Stack>
            <Stack className="fc">
                {adminDetails?.map((manager, index) => (
                    <Stack className="cardRow" key={index}>
                        <Stack className="managerTitle">
                            <Box
                                component='img'
                                className='logo'
                                src={manager?.profileImage?.url ?? ""}
                            />
                            <Typography className="managerName">{manager.name}</Typography>
                        </Stack>
                        <Stack className="managerRow">
                            <EmailOutlinedIcon className="icon" />
                            <Typography className="managerDetail">{manager.email}</Typography>
                        </Stack>
                        <Stack className="managerRow">
                            <PhoneOutlinedIcon className="icon" />
                            <Typography className="managerDetail">{manager.countryCode} {manager.phoneNumber}</Typography>
                        </Stack>
                    </Stack>
                ))}
            </Stack>
        </StyledCard>
    );
}
