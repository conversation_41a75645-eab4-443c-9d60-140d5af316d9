import { Stack, Typo<PERSON>, But<PERSON> } from "@mui/material";
import { StyledCard } from "./StyledCard";
import DescriptionOutlinedIcon from '@mui/icons-material/DescriptionOutlined';
import CalendarTodayOutlinedIcon from '@mui/icons-material/CalendarTodayOutlined';
import RemoveRedEyeOutlinedIcon from '@mui/icons-material/RemoveRedEyeOutlined';
import FileDownloadOutlinedIcon from '@mui/icons-material/FileDownloadOutlined';
import { toast } from "react-toastify";
import { publicAxios } from "@/contexts";
import { useState } from "react";
import { dateFormats } from "@/utils/constant";
import { getFormattedDate } from "@/utils/helper";
import { Nullable } from "@/types";
import { PdfPreviewDialog } from "@/components/TrainingProofRenderer/PdfPreviewDialog";


export const UploadedDocuments = ({ filename, url, createdAt }: { filename?: Nullable<string>, url?: Nullable<string>, createdAt?: Nullable<string> }) => {

    const [showDialog, setShowDialog] = useState(false)

    const handleDownload = async () => {
        if (!url) return
        try {
            const response = await publicAxios.get(url, {
                responseType: 'blob',
            })
            const blobUrl = URL.createObjectURL(response.data)
            const link = document.createElement('a')
            link.href = blobUrl
            link.download = 'Coi_document.pdf'
            document.body.appendChild(link)
            link.click()
            document.body.removeChild(link)
            URL.revokeObjectURL(blobUrl)
        } catch (error) {
            toast('Error downloading Coi Document.')
        }
    }

    return (
        <StyledCard >
            <Stack className="fw">
                <Stack className="titleContainer">
                    <DescriptionOutlinedIcon color="primary" className="titleIcon" />
                    <Typography className="title">Uploaded Documents</Typography>
                </Stack>
                <Stack className="cardRow">
                    <Stack className="logoContainer">
                        <DescriptionOutlinedIcon className="logo" />
                    </Stack>

                    <Stack className="fileDetails">
                        <Typography className="filename">{filename}</Typography>
                        <Stack className="fileDateContainer">
                            <CalendarTodayOutlinedIcon className="icon" />
                            <Typography className="fileDate">{getFormattedDate(
                                createdAt ?? "",
                                dateFormats.MMM_dd_yyyy
                            )}</Typography>
                        </Stack>
                    </Stack>
                    <Stack className="buttonContainer">
                        <Button
                            startIcon={<RemoveRedEyeOutlinedIcon />}
                            className="btn"
                            onClick={() => setShowDialog(true)}>
                            View
                        </Button>
                        <Button
                            startIcon={<FileDownloadOutlinedIcon />}
                            className="btn"
                            onClick={handleDownload}>
                            Download
                        </Button>
                    </Stack>
                </Stack>
            </Stack>
            <PdfPreviewDialog pdfUrl={url ?? ""} open={showDialog} close={() => setShowDialog(false)} />
        </StyledCard>
    );
}