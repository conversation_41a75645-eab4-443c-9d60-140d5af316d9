import { Stack, styled } from '@mui/material'

export const StyledCard = styled(Stack)(({ theme }) => ({
	padding: theme.spacing(3),
	display: 'flex',
	flexDirection: 'column',
	gap: theme.spacing(1),
	border: '1px solid grey',
	borderColor: theme.palette.neutral[200],
	borderRadius: theme.spacing(1.5),
	boxShadow: '0px 2px 4px rgba(0, 0, 0, 0.1)',
	'.leftPanel': {
		width: theme.spacing(100),
		display: 'flex',
		flexDirection: 'column',
		gap: theme.spacing(2),
		'.companyHeader': {
			display: 'flex',
			flexDirection: 'column',
			gap: theme.spacing(0.5),
			'.comTitle': {
				fontSize: theme.spacing(3),
				fontWeight: 600,
			},
			'.comSubHeading': {
				fontSize: theme.spacing(2),
				color: theme.palette.neutral[500],
			},
		},
		'.companyDetails': {
			display: 'flex',
			flexDirection: 'column',
			gap: theme.spacing(0.5),
			padding: theme.spacing(1),
			borderRadius: theme.spacing(1),
			'.detailsHeader': {
				display: 'flex',
				flexDirection: 'row',
				alignItems: 'center',
				gap: theme.spacing(0.7),
				fontSize: theme.spacing(2.5),
				fontWeight: 500,
				'.icon': {
					width: theme.spacing(2),
					color: theme.palette.neutral[500],
					paddingBottom: theme.spacing(0.5),
				},
				'.detailsTitle': {
					fontSize: theme.spacing(1.75),
					color: theme.palette.neutral[500],
				},
			},
			'.details': {
				fontSize: theme.spacing(2.0),
			},
			'&.twoColChild': {
				gridColumn: 'span 2',
			},
		},
	},
	'.titleContainer': {
		display: 'flex',
		flexDirection: 'row',
		alignItems: 'center',
		width: '100%',
		gap: theme.spacing(1),
		'.titleIcon': {
			marginBottom: theme.spacing(1),
		},
	},
	'.title': {
		fontSize: theme.spacing(3),
		fontWeight: 600,
	},
	'.logo': {
		width: theme.spacing(10),
		height: theme.spacing(10),
		borderColor: 'none',
		borderRadius: theme.spacing(1),
		objectFit: 'contain',
	},
	'.fw': {
		width: '100%',
		'.cardRow': {
			width: '100%',
			display: 'flex',
			alignItems: 'center',
			flexDirection: 'row',
			border: '1px solid grey',
			borderColor: theme.palette.neutral[200],
			borderRadius: theme.spacing(1.5),
			marginTop: theme.spacing(1.5),
			padding: theme.spacing(2),
			gap: theme.spacing(2),
			'.buttonContainer': {
				marginLeft: 'auto',
				display: 'flex',
				flexDirection: 'row',
				gap: theme.spacing(1),

				'.btn': {
					border: `1px solid ${theme.palette.neutral[200]}`,
					color: theme.palette.neutral[700],
					'&:hover': {
						backgroundColor: theme.palette.neutral[100],
						borderColor: theme.palette.neutral[400],
					},
				},
			},
			'.logoContainer': {
				display: 'flex',
				alignItems: 'center',
				justifyContent: 'center',
				width: theme.spacing(7),
				height: theme.spacing(7),
				borderRadius: theme.spacing(1),
				backgroundColor: theme.palette.error.light,
				'.logo': {
					width: theme.spacing(3),
					height: theme.spacing(3),
					color: theme.palette.primary.main,
				},
			},
			'.fileDetails': {
				display: 'flex',
				flexDirection: 'column',
				justifyContent: 'center',
				gap: theme.spacing(0),
				'.filename': {
					paddingTop: theme.spacing(1),
					fontSize: theme.spacing(2),
					fontWeight: 500,
				},
				'.fileDateContainer': {
					height: theme.spacing(1),
					display: 'flex',
					flexDirection: 'row',
					alignItems: 'center',
					gap: theme.spacing(1),
					'.fileDate': {
						padding: theme.spacing(0),
						display: 'flex',
						alignItems: 'center',
						justifyContent: 'center',
						fontSize: theme.spacing(1.6),
						color: theme.palette.neutral[500],
					},
					'.icon': {
						width: theme.spacing(1.7),
						paddingBottom: theme.spacing(0.5),
						color: theme.palette.neutral[500],
					},
				},
			},
		},
	},
	'.fc': {
		display: 'flex',
		gap: theme.spacing(0.5),
		width: theme.spacing(40),
		'.cardRow': {
			width: '100%',
			display: 'flex',
			flexDirection: 'column',
			border: '1px solid grey',
			borderColor: theme.palette.neutral[200],
			borderRadius: theme.spacing(1.5),
			marginTop: theme.spacing(1.5),
			padding: theme.spacing(2),
			gap: theme.spacing(0.5),
			'.managerTitle': {
				display: 'flex',
				flexDirection: 'row',
				alignItems: 'center',
				gap: theme.spacing(2),
				paddingBottom: theme.spacing(1),
				'.logo': {
					width: theme.spacing(6),
					height: theme.spacing(6),
					borderRadius: theme.spacing(50),
					backgroundColor: theme.palette.neutral[200],
				},
				'.managerName': {
					fontSize: theme.spacing(2.3),
					fontWeight: 600,
				},
			},
			'.managerRow': {
				display: 'flex',
				flexDirection: 'row',
				alignItems: 'center',
				gap: theme.spacing(1),

				'.icon': {
					width: theme.spacing(1.8),
					color: theme.palette.neutral[500],
					paddingBottom: theme.spacing(0),
				},
				'.managerDetail': {
					fontSize: theme.spacing(1.9),
					color: theme.palette.neutral[500],
				},
			},
		},
	},
	'.threeCol': {
		display: 'grid',
		gridTemplateColumns: 'repeat(3, 1fr)',
		gap: theme.spacing(0),
		width: '80%',
	},
	'.divider': {
		borderColor: theme.palette.neutral[200],
	},
	'.nextContainer': {
		display: 'flex',
		flexDirection: 'column',
		alignItems: 'center',
		gap: theme.spacing(2),
		'.logoContainer': {
			display: 'flex',
			alignItems: 'center',
			justifyContent: 'center',
			width: theme.spacing(7),
			height: theme.spacing(7),
			borderRadius: theme.spacing(10),
			backgroundColor: theme.palette.error.light,
			'.logo': {
				width: theme.spacing(3),
				height: theme.spacing(3),
				color: theme.palette.primary.main,
			},
		},
		'.title': {
			fontSize: theme.spacing(2.4),
			fontWeight: 600,
		},
		'.subheadingContainer': {
			display: 'flex',
			flexDirection: 'column',
			alignItems: 'center',
			gap: theme.spacing(0.5),
			'.title': {
				fontSize: theme.spacing(2.2),
				fontWeight: 500,
			},
		},
	},
}))
