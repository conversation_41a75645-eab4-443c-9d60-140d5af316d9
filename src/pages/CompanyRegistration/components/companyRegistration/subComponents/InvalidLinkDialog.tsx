import * as React from 'react';
import { styled } from '@mui/material/styles';
import Dialog from '@mui/material/Dialog';
import Typography from '@mui/material/Typography';
import { Box, Card, Link } from '@mui/material';
import { ImgMail } from '@/assets/icons';
import { theme } from '@/lib/theme/theme';
import ErrorIcon from '@mui/icons-material/Error';

type InvalidLinkDialog = {
    open: boolean,
}

export default function RegistrationPendingDialog({ open }: InvalidLinkDialog) {


    return (
        <React.Fragment>
            <BootstrapDialog
                aria-labelledby="customized-dialog-title"
                open={open}
                PaperProps={{
                    sx: {
                        width: theme.spacing(80),
                        maxWidth: '90%',
                        backgroundColor: theme.palette.grey[100],
                        paddingX: theme.spacing(3),
                        paddingBottom: theme.spacing(3)
                    },
                }}
            >
                <DialogHeader id="customized-dialog-title">
                    {/* <Box
                        component={'img'}
                        src={ErrorIcon}
                        height={68}
                        width={68}
                    /> */}
                    <ErrorIcon className='headerIcon' />

                    <Typography variant='h3'>
                        This invitation link is invalid
                    </Typography>

                    <Typography
                        color={theme.palette.custom.grey[800]}
                        variant="body1"
                        textAlign={'center'}
                    >
                        Your company details have already been submitted to Circonomy.
                    </Typography>
                </DialogHeader>

                <Box display={'flex'} flexDirection={'column'} gap={2} alignItems={"center"}>
                    <CustomCardComponent>
                        <CardMailWrapper>
                            <Box>
                                <Box component={'img'} src={ImgMail} />
                            </Box>
                            <Box display={'flex'} flexDirection={'row'} gap={0.5}>
                                <Typography variant='subtitle1'>
                                    If you have any query contact
                                </Typography>
                                <Typography variant="subtitle1" className="boldEmail">
                                    <Link href="mailto:<EMAIL>" underline="hover">
                                        <EMAIL>
                                    </Link>
                                </Typography>
                            </Box>
                        </CardMailWrapper>
                    </CustomCardComponent>
                </Box>

            </BootstrapDialog>
        </React.Fragment>
    );
}


const BootstrapDialog = styled(Dialog)(({ theme }) => ({
    '& .MuiDialogContent-root': {
        padding: theme.spacing(1),
    },
    '& .MuiDialogActions-root': {
        padding: theme.spacing(1),
    },
    '& .MuiPaper-root': {
        width: '640px',
        maxWidth: '90%',
        paddingBottom: theme.spacing(3)
    },
    '& .MuiBackdrop-root': {
        backgroundColor: 'rgba(255, 255, 255, 0.7)',
        backdropFilter: 'blur(6px)',
    },
}));

const DialogHeader = styled(Box)(({ theme }) => ({
    padding: theme.spacing(3),
    display: "flex",
    gap: theme.spacing(2),
    alignItems: "center",
    justifyContent: "center",
    flexDirection: "column",
    '.headerIcon': {
        color: theme.palette.primary.main,
        width: theme.spacing(8),
        height: theme.spacing(8),
    },
}));

const CustomCardComponent = styled(Card)(({ theme }) => ({
    display: "flex",
    padding: theme.spacing(2),
    backgroundColor: "white",
    boxShadow: "none",
    borderRadius: theme.spacing(2),

}));

const CardMailWrapper = styled(Box)(({ theme }) => ({
    width: '100%',
    display: 'flex',
    flexDirection: 'row',
    alignItems: 'center',
    boxShadow: "none",
    gap: theme.spacing(2),
    padding: theme.spacing(2),
    '.boldEmail': {
        fontWeight: 500
    }
}));