import { FormControl, FormHelperText, IconButton, Stack, useTheme } from "@mui/material"
import { CompanyRegistrationSchema, TCompanyRegistrationSchema } from "./schema"
import { useFormContext } from "react-hook-form"
import { CustomFileUploader, PhoneInputComponent } from "@/components"
import { ImageFileTypes } from "@/pages/dashboard"
import { useCallback } from "react"
import RemoveIcon from '@mui/icons-material/Remove'
import { CustomTextField } from "@/utils/components"
import { smallInputStyle } from "./CompanyRegistrationForm"


export const CompanyManagerDetails = ({
    index,
    remove,
}: {
    index: number
    item: { id: string }
    remove: (index: number) => void
}) => {

    const theme = useTheme()


    const {
        setValue,
        formState: { errors },
        watch,
        register,
        clearErrors,
        setError,
    } = useFormContext<TCompanyRegistrationSchema>()

    const handleOnChange = useCallback(
        (value: string, dialCode: string) => {
            setValue(`admins.${index}.countryCode`, `+${dialCode}`, {
                shouldDirty: true,
            })

            setValue(`admins.${index}.phoneNumber`, value, {
                shouldDirty: true,
            })
            if (value.toString().length === 0) {
                setError(`admins.${index}.phoneNumber`, { message: 'Please enter phone number' })
                return
            }
            clearErrors(`admins.${index}.phoneNumber`)
        },
        [clearErrors, index, setError, setValue])

    return (
        <Stack className="managerCard" position='relative'>
            <Stack>
                <CustomFileUploader
                    directionColumnReverse
                    imageUrl={watch(`admins.${index}.managerLogoUrl`) ?? ''}
                    acceptFileTypes={ImageFileTypes}
                    heading='Upload'
                    sx={{
                        height: theme.spacing(10),
                        width: theme.spacing(10),
                        borderRadius: 10,
                    }}
                    imageHeight={100}
                    setUploadData={({ id }) => {
                        setValue(`admins.${index}.profileImageId`, id, {
                            shouldDirty: true,
                            shouldTouch: true,
                            shouldValidate: true,
                        })
                    }}
                    type='company_logo'
                    publicRoute
                    mediaType='image'
                />
                <FormHelperText error={true}>
                    {errors?.admins?.[index]?.profileImageId?.message}
                </FormHelperText>
            </Stack>
            <Stack direction={"row"} spacing={2} width='80%'>
                <CustomTextField
                    watch={watch}
                    sx={smallInputStyle}
                    schema={CompanyRegistrationSchema}
                    id={`admins.${index}.managerName`}
                    disabled={index == 0}
                    label='Manager Name'
                    variant='outlined'
                    placeholder='Enter manager name'
                    fullWidth
                    {...register(`admins.${index}.name`)}
                    error={!!errors?.admins?.[index]?.name}
                    helperText={errors?.admins?.[index]?.name?.message}
                />
                <CustomTextField
                    watch={watch}
                    sx={smallInputStyle}
                    schema={CompanyRegistrationSchema}
                    id={`admins.${index}.email`}
                    disabled={index == 0}
                    label='Manager Email'
                    variant='outlined'
                    placeholder='Enter manager email'
                    fullWidth
                    {...register(`admins.${index}.email`)}
                    error={!!errors?.admins?.[index]?.email}
                    helperText={errors?.admins?.[index]?.email?.message}
                />
                <FormControl fullWidth>
                    <PhoneInputComponent
                        smallSize
                        dialCode={watch(`admins.${index}.countryCode`)}
                        value={String(watch(`admins.${index}.phoneNumber`))}
                        handleOnChange={handleOnChange}
                        getSelectedCountryDialCode={(dialCode) =>
                            setValue(`admins.${index}.countryCode`, dialCode)
                        }
                    />
                    <FormHelperText error>
                        {errors?.admins?.[index]?.phoneNumber?.message}
                        {errors?.admins?.[index]?.countryCode?.message}
                    </FormHelperText>
                </FormControl>
            </Stack>

            {watch('admins')?.length !== 1 && index !== 0 ? (
                <IconButton onClick={() => remove(index)} className='removeBtn'>
                    <RemoveIcon className='removeIcon' />
                </IconButton>)
                : <></>}
        </Stack>
    )

}