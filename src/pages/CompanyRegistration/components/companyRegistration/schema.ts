import * as Yup from 'yup'

export const CompanyRegistrationSchema = Yup.object().shape({
	name: Yup.string().required('Company name is required'),
	countryCode: Yup.string().required(' '),
	phoneNumber: Yup.string()
		.matches(/^\d+$/, 'Phone number is mandatory')
		.required('Phone number is mandatory'),
	email: Yup.string()
		.email('Invalid email')
		.required('Company email is required'),
	country: Yup.string().required('Country is required'),
	state: Yup.string().required('Country is required'),
	pinCode: Yup.string().required('Pincode is required'),
	companyLogoId: Yup.string().required('Company logo is required.'),
	companyLogoUrl: Yup.string().optional(),
	coiDocumentId: Yup.string().required('Company COI document is required.'),
	address: Yup.string().required('Company Address is required.'),
	serviceType: Yup.string().required('Company service type is required.'),
	registry: Yup.string().required('Company registry is required.'),
	admins: Yup.array()
		.of(
			Yup.object().shape({
				profileImageId: Yup.string().required('Manager logo is required.'),
				managerLogoUrl: Yup.string().optional().nullable(),
				name: Yup.string().required('Name is required'),
				email: Yup.string()
					.email('Invalid email')
					.required('Manager email is required'),
				countryCode: Yup.string().required(' '),
				phoneNumber: Yup.string()
					.matches(/^\d+$/, 'Phone number is mandatory')
					.required('Phone number is mandatory'),
			})
		)
		.min(1, 'At least 1 manager is required')
		.required(),
})

export type TCompanyRegistrationSchema = Yup.InferType<
	typeof CompanyRegistrationSchema
>
