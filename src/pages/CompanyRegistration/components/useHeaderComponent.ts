import { authAxios, useAuthContext } from '@/contexts'
import { ICompanyDetails } from '@/utils/constant'
import { QueryFunctionContext, useQuery } from '@tanstack/react-query'

export const getCompanyDetails = async ({ queryKey }: QueryFunctionContext) => {
	const companyId = queryKey[1]
	const { data } = await authAxios.get<ICompanyDetails>(`/company/${companyId}`)
	return data
}

export const useHeaderComponent = () => {
	const { userDetails } = useAuthContext()
	const companyDetailsQuery = useQuery({
		queryKey: ['companyDetails', userDetails?.companyId],
		queryFn: getCompanyDetails,
		enabled: !!userDetails?.companyId,
	})

	return { companyDetailsQuery }
}
