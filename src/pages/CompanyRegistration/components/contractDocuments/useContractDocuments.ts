import { authAxios, useAuthContext } from '@/contexts'
import { ICompanyDetails } from '@/utils/constant'
import { useMutation, useQuery, useQueryClient } from '@tanstack/react-query'
import { AxiosError } from 'axios'
import { useState } from 'react'
import { toast } from 'react-toastify'
import { getCompanyDetails } from '../useHeaderComponent'

type FormValues = {
	ndaFile: {
		id: string | null
		name: string | null
		url: string | null
		uploadAt: Date | null
	}

	caFile: {
		id: string | null
		name: string | null
		url: string | null
		uploadAt: Date | null
	}
}

export const useContractDocuments = () => {
	const [documentReviewMessage, setDocumentReviewMessage] =
		useState<boolean>(false)

	const queryClient = useQueryClient()
	const { userDetails } = useAuthContext()

	const getDocumentsInfo = useQuery({
		queryKey: ['companyDetails', userDetails?.companyId],
		queryFn: getCompanyDetails,
		initialData: () =>
			queryClient.getQueryData<ICompanyDetails>([
				'companyDetails',
				userDetails?.companyId,
			]),
		enabled: false,
	})

	const uploadDocumentsMutation = useMutation({
		mutationKey: ['contractDocuments'],
		mutationFn: async (data: FormValues) => {
			const companyId = userDetails?.companyId
			const payload = {
				commercialDocumentId: data.caFile.id,
				ndaDocumentId: data.ndaFile.id,
			}

			return await authAxios.put(
				`company/${companyId}/contract-documents`,
				payload
			)
		},
		onError: (error: AxiosError) => {
			const errorMessage =
				error?.response?.data &&
					typeof error.response.data === 'object' &&
					'messageToUser' in error.response.data
					? (error.response.data as { messageToUser: string }).messageToUser
					: 'An error occurred while uploading documents'
			toast.error(errorMessage)
		},
		onSuccess: ({ data }) => {
			toast(data?.message)
			setDocumentReviewMessage(true)
			getDocumentsInfo.refetch()
		},
	})

	return {
		documentReviewMessage,
		setDocumentReviewMessage,
		uploadDocumentsMutation,
		getDocumentsInfo: getDocumentsInfo.data,
	}
}
