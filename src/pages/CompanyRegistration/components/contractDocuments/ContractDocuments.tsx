import { useEffect, useState } from 'react';
import {
    Box,
    Typography,
    Button,
    Paper,
    Stack,
    styled,
} from '@mui/material';
import VisibilityIcon from '@mui/icons-material/Visibility';
import DownloadIcon from '@mui/icons-material/Download';
import ErrorOutlineIcon from '@mui/icons-material/ErrorOutline';

import { useForm, Controller } from 'react-hook-form';
import { FileUploader } from 'react-drag-drop-files';

import { theme } from '@/lib/theme/theme';
import {
    ImgFile
} from '@/assets/icons';
import { ChatBubbleOutlineRounded, CheckCircleOutline, FileDownloadOutlined, FileUploadOutlined, } from '@mui/icons-material';
import { handleImageUpload } from '@/utils/helper';
import { useContractDocuments } from './useContractDocuments';
import { PdfPreviewDialog } from '@/components/TrainingProofRenderer/PdfPreviewDialog';
import originalNdaFile from '@/assets/files/Original_NDA.pdf'
import { CompanyStages, ICompanyStage, StageStatus } from '@/utils/constant';
import { publicAxios } from '@/contexts';
import { toast } from 'react-toastify';
import { CompanyRegistrationSteps } from '../../stage';

const ORIGINAL_NDA_FILE_NAME = 'Original_NDA.pdf';


type FormValues = {
    ndaFile: {
        id: string | null;
        name: string | null;
        url: string | null;
        uploadAt: Date | null;
    };

    caFile: {
        id: string | null;
        name: string | null;
        url: string | null;
        uploadAt: Date | null;
    }
}

const defaultFormValue = {
    ndaFile: {
        id: null,
        name: null,
        url: null,
        uploadAt: null
    },
    caFile: {
        id: null,
        name: null,
        url: null,
        uploadAt: null
    }
}

const ContractDocuments = ({ setActiveStep }: { setActiveStep: React.Dispatch<React.SetStateAction<number>> }) => {
    const [viewOrignalNdaFile, setViewOriginalNdaFile] = useState(false);
    const [showReplaceFileIcon, setShowReplaceFileIcon] = useState(false);

    const {
        control,
        setValue,
        getValues,
        reset,
        watch
    } = useForm<FormValues>({
        mode: "all",
        defaultValues: defaultFormValue
    });

    const {
        documentReviewMessage,
        uploadDocumentsMutation,
        getDocumentsInfo
    } = useContractDocuments()


    useEffect(() => {
        if (getDocumentsInfo) {
            const contractDoc = getDocumentsInfo?.stages?.find(
                (item: ICompanyStage) => item?.stage === CompanyStages.contract
            );

            if (contractDoc?.status === StageStatus.notStarted || contractDoc?.status === StageStatus.pending) {
                setShowReplaceFileIcon(true);
            }

            reset({
                ndaFile: {
                    id: getDocumentsInfo?.ndaDocument?.id,
                    name: getDocumentsInfo?.ndaDocument?.fileName,
                    url: getDocumentsInfo?.ndaDocument?.url,
                    uploadAt: new Date(getDocumentsInfo?.ndaDocument?.createdAt ?? ""),
                },
                caFile: {
                    id: getDocumentsInfo?.commercialDocument?.id,
                    name: getDocumentsInfo?.commercialDocument?.fileName,
                    url: getDocumentsInfo?.commercialDocument?.url,
                    uploadAt: new Date(getDocumentsInfo?.commercialDocument?.createdAt ?? ""),
                }
            });
        }
    }, [getDocumentsInfo, reset]);


    const formatDate = (date: Date | null) =>
        date?.toLocaleDateString('en-US', {
            year: 'numeric',
            month: 'long',
            day: 'numeric',
        }) +
        ', ' +
        date?.toLocaleTimeString('en-US', { hour: 'numeric', minute: '2-digit' });


    const handleFileChange = async (field: 'ndaFile' | 'caFile', file: File) => {
        const data = await handleImageUpload(file);

        if (field === 'ndaFile') {
            setValue("ndaFile", {
                id: data?.id,
                name: data?.fileName,
                url: data?.url,
                uploadAt: new Date(),
            })
        } else {
            setValue("caFile", {
                id: data?.id,
                name: data?.fileName,
                url: data?.url,
                uploadAt: new Date()
            })
        }
        uploadDocumentsMutation.mutate(getValues());
    };

    const handleOrginalNdaDownload = (fileUrl: string) => {
        try {
            const url = fileUrl;
            const a = document.createElement('a');
            a.setAttribute('download', ORIGINAL_NDA_FILE_NAME)
            a.href = url;
            document.body.appendChild(a);
            a.click();
            document.body.removeChild(a);
        } catch (error) {
            toast("Error Downloading Original_NDA File");
        }
    }


    const handleDownload = async (fileType: string, url: string) => {
        if (!url) return
        try {
            const response = await publicAxios.get(url, {
                responseType: 'blob',
            })

            const blobUrl = URL.createObjectURL(response.data)
            const link = document.createElement('a')
            link.href = blobUrl
            if (fileType === 'ndaFile') {
                link.download = getValues('ndaFile.name') ?? ""
            } else {
                link.download = getValues('caFile.name') ?? ""
            }
            document.body.appendChild(link)
            link.click()
            document.body.removeChild(link)
            URL.revokeObjectURL(blobUrl)
        } catch (error) {
            toast(`Error Downloading File`)
        }
    }

    return (
        <>
            <PdfPreviewDialog pdfUrl={originalNdaFile} open={viewOrignalNdaFile} close={() => setViewOriginalNdaFile(false)} />
            <form>
                <StyledContainer>
                    <Box width="65%"                                                                                                                                                                                                                                                                        >

                        {documentReviewMessage && (
                            <Box
                                className="documentReviewCard"
                            >
                                <Box
                                    className="documentReviewCardIcon"
                                >
                                    <ChatBubbleOutlineRounded sx={{ fontSize: theme.spacing(2.5), color: theme.palette.custom.lightest.primary }} />
                                </Box>
                                <Box display={'flex'} flexDirection={'column'} gap={0.5}>
                                    <Typography variant='body2' color={theme.palette.custom.red[400]}>Documents Under Review</Typography>
                                    <Typography variant='subtitle1' color={theme.palette.custom.red[400]}>Your documents have been successfully submitted and are pending Circonomy's approval. You can view or download your submitted files below.</Typography>
                                </Box>
                            </Box>
                        )}

                        {/* NDA Section */}
                        <Paper variant="outlined" className='card'>
                            <Stack direction="row" alignItems="center" spacing={1} mb={3}>
                                <Box component="img" src={ImgFile} height={26} width={26} />
                                <Typography variant="h4" lineHeight={1}>NDA Agreement</Typography>
                            </Stack>

                            {/* Original File Display */}
                            <Box
                                className="originalNdaFileCard"
                            >
                                <Box display="flex" alignItems="center" gap={1}>
                                    <Box component="img" src={ImgFile} />
                                    <Box display={'flex'} flexDirection={'column'} gap={0.5}>
                                        <Typography fontWeight={600}>Original_NDA.pdf</Typography>
                                        <Typography variant="caption" color={theme.palette.neutral[300]}>
                                            Standard NDA template
                                        </Typography>
                                    </Box>
                                </Box>
                                <Stack direction="row" spacing={1}>
                                    <Button
                                        variant="outlined"
                                        size="small"
                                        startIcon={<VisibilityIcon />}
                                        className='actionButton'
                                        onClick={() => setViewOriginalNdaFile(true)}
                                    >
                                        View
                                    </Button>
                                    <Button
                                        variant="outlined"
                                        size="small"
                                        startIcon={<DownloadIcon />}
                                        className='actionButton'
                                        onClick={() => handleOrginalNdaDownload(originalNdaFile)}
                                    >
                                        Download
                                    </Button>
                                </Stack>
                            </Box>

                            <Box display="flex" justifyContent="space-between" alignItems="center" padding={1}>
                                <Typography variant="caption" fontWeight={500}>
                                    Upload Signed NDA
                                </Typography>


                                {
                                    !watch('ndaFile.id') && (
                                        <Stack direction="row" alignItems="center" spacing={1}>
                                            <ErrorOutlineIcon fontSize="small" color="error" />
                                            <Typography variant="caption" color="error">
                                                Please upload NDA to proceed
                                            </Typography>
                                        </Stack>
                                    )
                                }
                            </Box>

                            <Controller
                                name="ndaFile.id"
                                control={control}
                                render={({ field }) => (
                                    <>
                                        {field.value ? (
                                            <>
                                                <Box display={'flex'} alignItems={'center'} gap={1} mb={1}>
                                                    <CheckCircleOutline color="success" fontSize='small' sx={{ color: theme.palette.custom.green[900] }} />
                                                    <Typography variant="subtitle1" color={theme.palette.custom.green[900]}>NDA is uploaded successfully</Typography>
                                                </Box>
                                                <Box
                                                    className="uploadedFileCard"
                                                >
                                                    <Box display="flex" alignItems="center" gap={1.5}>
                                                        <Box
                                                            className="uploadedFileCardIcon"
                                                        >
                                                            <CheckCircleOutline fontSize='small' sx={{ color: theme.palette.custom.green[900] }} />
                                                        </Box>
                                                        <Box>
                                                            <Typography fontWeight={600}>{getValues('ndaFile.name')}</Typography>
                                                            <Typography variant="caption" color="text.secondary">
                                                                {getValues("ndaFile.uploadAt") && formatDate(getValues("ndaFile.uploadAt"))}
                                                            </Typography>
                                                        </Box>
                                                    </Box>
                                                    <Stack direction="row" spacing={1}>
                                                        <Button
                                                            variant="outlined"
                                                            size="small"
                                                            onClick={() => handleDownload("ndaFile", getValues('ndaFile.url') ?? "")}
                                                            startIcon={<FileDownloadOutlined />}
                                                            className='actionButtonGreen'
                                                        >
                                                            Download
                                                        </Button>
                                                        {showReplaceFileIcon &&
                                                            <FileUploader
                                                                handleChange={(file: File) => handleFileChange("ndaFile", file)}
                                                                name="file"
                                                                types={['PDF', 'DOC', 'DOCX']}
                                                            >
                                                                <Button
                                                                    variant="outlined"
                                                                    size="small"
                                                                    startIcon={<FileUploadOutlined />}
                                                                    className='actionButtonGreen'
                                                                >
                                                                    Replace
                                                                </Button>
                                                            </FileUploader>
                                                        }
                                                    </Stack>
                                                </Box>
                                            </>
                                        ) : (
                                            <FileUploader

                                                handleChange={(file: File) => handleFileChange("ndaFile", file)}
                                                name="file"
                                                types={['PDF', 'DOC', 'DOCX']}
                                            >
                                                <Box
                                                    className="fileUploaderCard"
                                                >
                                                    <FileUploadOutlined fontSize='large' />
                                                    <Typography variant="body1" mt={1}>
                                                        Drag and drop your signed NDA here, or
                                                    </Typography>
                                                    <Button
                                                        variant="outlined"
                                                        size="small"
                                                        startIcon={<FileUploadOutlined />}
                                                        className='uploadFileButton'
                                                    >
                                                        Choose File
                                                    </Button>
                                                    <Typography variant="caption" color={theme.palette.neutral[300]} mt={1}>
                                                        PDF, DOC, DOCX up to 10MB
                                                    </Typography>
                                                </Box>
                                            </FileUploader>
                                        )}
                                    </>
                                )}
                            />
                        </Paper>

                        {/* Commercial Agreement Section */}
                        <Paper variant="outlined" className='card'>
                            <Stack direction="row" alignItems="center" spacing={1} mb={2}>
                                <Box component="img" src={ImgFile} height={26} width={26} />
                                <Typography variant="h5">Commercial Agreement</Typography>
                            </Stack>

                            <Box display="flex" justifyContent="space-between" alignItems="center" padding={1}>
                                <Typography variant="caption" fontWeight={500}>
                                    Upload Commercial Agreement
                                </Typography>

                                {
                                    !watch('caFile.id') && (
                                        <Stack direction="row" alignItems="center" spacing={1}>
                                            <ErrorOutlineIcon fontSize="small" color="error" />
                                            <Typography variant="caption" color="error">
                                                Please upload Commercial Agreement to proceed
                                            </Typography>
                                        </Stack>
                                    )
                                }


                            </Box>

                            <Controller
                                name="caFile.id"
                                control={control}
                                render={({ field }) => (
                                    <>
                                        {field.value ? (
                                            <>
                                                <Box display={'flex'} alignItems={'center'} gap={1} mb={1}>
                                                    <CheckCircleOutline color="success" fontSize='small' sx={{ color: theme.palette.custom.green[900] }} />
                                                    <Typography variant="subtitle1" color={theme.palette.custom.green[900]}>Commercial Agreement is uploaded successfully</Typography>
                                                </Box>
                                                <Box
                                                    className="uploadedFileCard"
                                                >
                                                    <Box display="flex" alignItems="center" gap={1.5}>
                                                        <Box
                                                            className="uploadedFileCardIcon"
                                                        >
                                                            <CheckCircleOutline fontSize='small' sx={{ color: theme.palette.custom.green[900] }} />
                                                        </Box>
                                                        <Box>
                                                            <Typography fontWeight={600}>{getValues('caFile.name')}</Typography>
                                                            <Typography variant="caption" color="text.secondary">
                                                                {getValues("caFile.uploadAt") && formatDate(getValues("caFile.uploadAt"))}
                                                            </Typography>
                                                        </Box>
                                                    </Box>
                                                    <Stack direction="row" spacing={1}>
                                                        <Button
                                                            variant="outlined"
                                                            size="small"
                                                            onClick={() => handleDownload("caFile", getValues('caFile.url') ?? "")}
                                                            startIcon={<FileDownloadOutlined />}
                                                            className='actionButtonGreen'
                                                        >
                                                            Download
                                                        </Button>
                                                        {showReplaceFileIcon &&
                                                            <FileUploader
                                                                handleChange={(file: File) => handleFileChange("caFile", file)}
                                                                name="file"
                                                                types={['PDF', 'DOC', 'DOCX']}
                                                            >
                                                                <Button
                                                                    variant="outlined"
                                                                    size="small"
                                                                    startIcon={<FileUploadOutlined />}
                                                                    className='actionButtonGreen'
                                                                >
                                                                    Replace
                                                                </Button>
                                                            </FileUploader>
                                                        }
                                                    </Stack>
                                                </Box>
                                            </>
                                        ) : (
                                            <FileUploader
                                                handleChange={(file: File) => handleFileChange("caFile", file)}
                                                name="file"
                                                types={['PDF', 'DOC', 'DOCX']}
                                            >
                                                <Box
                                                    className="fileUploaderCard"
                                                >
                                                    <FileUploadOutlined fontSize='large' />
                                                    <Typography variant="body1" mt={1}>
                                                        Drag and drop your signed commercial agreement here, or
                                                    </Typography>
                                                    <Button
                                                        className='uploadFileButton'
                                                        variant="outlined"
                                                        size="small"
                                                        startIcon={<FileUploadOutlined />}
                                                    >
                                                        Choose File
                                                    </Button>
                                                    <Typography variant="caption" color={theme.palette.neutral[300]} mt={1}>
                                                        PDF, DOC, DOCX up to 10MB
                                                    </Typography>
                                                </Box>
                                            </FileUploader>
                                        )}
                                    </>
                                )}
                            />
                        </Paper>

                        <Box mt={3} textAlign="right">
                            <Button
                                type="button"
                                variant="contained"
                                color="primary"
                                disabled={!(watch('ndaFile.id') && watch('caFile.id'))}
                                onClick={() => setActiveStep(CompanyRegistrationSteps.DMRV_CUSTOMIZATION)}

                            >
                                Proceed to Next Step
                            </Button>
                        </Box>
                    </Box>
                </StyledContainer>
            </form>
        </>
    );
};

export default ContractDocuments;


const StyledContainer = styled(Stack)(({ theme }) => {
    const actionButtonBase = {
        textTransform: 'capitalize',
        backgroundColor: theme.palette.background.paper,
        fontWeight: 300,
        borderColor: theme.palette.custom.grey[300],
        color: theme.palette.text.primary,
    };

    return {
        display: "flex",
        padding: theme.spacing(2),
        justifyContent: "center",
        alignItems: "center",
        width: "100%",
        '.card': {
            padding: theme.spacing(3),
            marginBottom: theme.spacing(3),
            borderRadius: theme.spacing(1.5),
            borderColor: theme.palette.neutral[200]
        },
        '.actionButton': actionButtonBase,
        '.actionButtonGreen': {
            ...actionButtonBase,
            '&:hover': {
                borderColor: theme.palette.custom.green[400],
                backgroundColor: theme.palette.custom.green[600]
            }
        },
        '.uploadFileButton': {
            textTransform: 'capitalize',
            color: theme.palette.text.primary,
            borderColor: theme.palette.neutral[200],
            paddingX: theme.spacing(2),
            paddingY: theme.spacing(1),
            '&:hover': {
                backgroundColor: theme.palette.neutral[100],
                borderColor: theme.palette.neutral[300],
                color: theme.palette.primary.main
            },
        },
        '.uploadedFileCard': {
            border: `1px solid ${theme.palette.custom.green[400]}`,
            backgroundColor: `${theme.palette.custom.green[300]}`,
            borderRadius: theme.spacing(1),
            padding: theme.spacing(2.5),
            display: 'flex',
            justifyContent: 'space-between',
            alignItems: 'center',
        },
        ".uploadedFileCardIcon": {
            padding: theme.spacing(1.25),
            display: 'flex',
            alignItems: 'center',
            justifyContent: 'center',
            backgroundColor: theme.palette.custom.green[600],
            borderRadius: theme.spacing(1.5),
        },
        '.originalNdaFileCard': {
            border: `1px solid ${theme.palette.custom.red[400]}`,
            borderRadius: theme.spacing(1),
            padding: theme.spacing(2.5),
            display: 'flex',
            justifyContent: 'space-between',
            alignItems: 'center',
            marginBottom: theme.spacing(2),
            backgroundColor: theme.palette.custom.red[300],
        },
        '.fileUploaderCard': {
            display: "flex",
            flexDirection: "column",
            justifyContent: "center",
            gap: theme.spacing(1),
            alignItems: "center",
            border: '2px dashed #ccc',
            borderRadius: theme.spacing(1),
            textAlign: 'center',
            padding: theme.spacing(3),
            cursor: 'pointer',
        },
        '.documentReviewCard': {
            display: 'flex',
            alignItems: 'flex-start',
            gap: theme.spacing(2),
            padding: theme.spacing(4),
            backgroundColor: theme.palette.custom.red[300],
            marginBottom: theme.spacing(3),
            borderRadius: theme.spacing(3),
            borderLeft: `5px solid ${theme.palette.custom.red[400]}`
        },
        '.documentReviewCardIcon': {
            display: 'flex',
            alignItems: 'center',
            justifyContent: 'center',
            padding: theme.spacing(1.25),
            backgroundColor: theme.palette.custom.red[400],
            borderRadius: theme.spacing(2.25)
        }
    };
});


