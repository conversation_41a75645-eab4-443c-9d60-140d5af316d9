import { Box, Stack, styled, Typography } from '@mui/material'
import { CustomizedSteppers } from './Stage'
import logo from '@/assets/icons/logo.svg'
import { CustomChip } from '@/components/CustomeChip'
import CheckOutlinedIcon from '@mui/icons-material/CheckOutlined'
import { useHeaderComponent } from './useHeaderComponent'
import {
	CompanyStages,
	dateFormats,
	StageStatus,
	StageStatusName,
} from '@/utils/constant'
import QueryBuilderRoundedIcon from '@mui/icons-material/QueryBuilderRounded'
import { useEffect, useMemo } from 'react'
import { getFormattedDate } from '@/utils/helper'
import { CompanyRegistrationSteps } from '../stage'

export const HeaderComponent = ({
	activeStep,
	lastStartedStage,
	setActiveStep,
	setLastStartedStage,
	publicRoute,
}: {
	activeStep: CompanyRegistrationSteps
	lastStartedStage: CompanyRegistrationSteps
	setActiveStep: React.Dispatch<React.SetStateAction<CompanyRegistrationSteps>>
	setLastStartedStage?: React.Dispatch<
		React.SetStateAction<CompanyRegistrationSteps>
	>
	publicRoute: boolean
}) => {
	const { companyDetailsQuery } = useHeaderComponent()
	const { data: companyDetails } = companyDetailsQuery

	const currentStageStatus = useMemo(() => {
		if (activeStep === CompanyRegistrationSteps.COMPLETE_REGISTRATION) {
			return companyDetails?.status === StageStatus.approved
				? StageStatus.approved
				: StageStatus.pending
		} else {
			return companyDetails?.stages?.[activeStep].status ?? ''
		}
	}, [activeStep, companyDetails?.stages, companyDetails?.status])

	useEffect(() => {
		if (!companyDetails) return

		const determineStageIndex = () => {
			if (companyDetails?.stage === CompanyStages.registration) {
				return CompanyRegistrationSteps.CONTRACT_DOCUMENTS
			}
			if (companyDetails?.stage === CompanyStages.contract) {
				// If stage is contract, check if isCompleted for stage 1 (contract) is true then return 2, else return 1
				return companyDetails?.stages[1]?.isCompleted
					? CompanyRegistrationSteps.DMRV_CUSTOMIZATION
					: CompanyRegistrationSteps.CONTRACT_DOCUMENTS
			}
			if (companyDetails?.stage === CompanyStages.dmrvCustomization) {
				// If stage is dmrvCustomization, check if isCompleted for stage 2 (dmrvCustomization) is true then return 4 (used for showing stage 4 icon), else return 3
				// return companyDetails?.stages[2]?.isCompleted ? 4 : CompanyRegistrationSteps.COMPLETE_REGISTRATION
				return CompanyRegistrationSteps.COMPLETE_REGISTRATION
			}
			return CompanyRegistrationSteps.REGISTRATION_APPROVED
		}
		const stageIndex = determineStageIndex()
		setLastStartedStage?.(stageIndex)
	}, [companyDetails, setLastStartedStage])

	const HeaderDetails = useMemo(() => {
		switch (activeStep) {
			case 0:
				return {
					heading: 'Company Registration',
					subHeading: companyDetailsQuery?.data?.stages?.[0]?.assessedAt
						? `Approved on ${getFormattedDate(
								companyDetailsQuery?.data?.stages?.[0]?.assessedAt ?? '',
								dateFormats.MMMM_dd_yyyy
						  )}`
						: ``,
				}

			case 1:
				return {
					heading: 'Contract Documents',
					subHeading: 'Upload the required contract documents to proceed.',
				}
			case 2:
				return {
					heading: 'dMRV Customization',
					subHeading: 'Configure and customize your dMRV tools.',
				}
			case 3:
				return {
					heading: 'Complete',
					subHeading:
						'Your form has been submitted and in review from circonomy team.',
				}
		}
	}, [activeStep, companyDetailsQuery?.data?.stages])

	return (
		<Box>
			<StyledContainer>
				{activeStep == CompanyRegistrationSteps.REGISTRATION_APPROVED &&
				publicRoute ? (
					<>
						<Stack className='titleContainer' gap={3}>
							<Box
								component='img'
								src={logo}
								height={40}
								width={40}
								className='arrow-icon'
							/>
							<Typography className='Title'>Welcome to Circonomy!</Typography>
						</Stack>
						<Stack className='desc'>
							<Typography>Lets get started with your onboarding.</Typography>
							<Typography>
								To begin, please complete the steps below. Each step unlocks the
								next once approved.
							</Typography>
						</Stack>
					</>
				) : (
					<Stack className='stateHeader'>
						<Stack className='headingContainer'>
							<Typography className='heading'>
								{HeaderDetails?.heading}
							</Typography>
							<Typography className='subHeading'>
								{HeaderDetails?.subHeading}
							</Typography>
						</Stack>
						<Stack className='chipContainer'>
							<CustomChip
								appliedClass={currentStageStatus}
								icon={
									currentStageStatus === StageStatus.approved ? (
										<CheckOutlinedIcon color='success' fontSize='small' />
									) : (
										<QueryBuilderRoundedIcon color='warning' fontSize='small' />
									)
								}
								label={StageStatusName[currentStageStatus as StageStatus]}
							/>
						</Stack>
					</Stack>
				)}
				<CustomizedSteppers
					companyDetails={companyDetails}
					activeStep={activeStep}
					lastStartedStage={lastStartedStage}
					setActiveStep={setActiveStep}
				/>
			</StyledContainer>
		</Box>
	)
}

const StyledContainer = styled(Stack)(({ theme }) => ({
	padding: theme.spacing(4),
	paddingTop: theme.spacing(6),
	fontSize: theme.typography.pxToRem(30),
	width: '100%',
	alignItems: 'center',
	justifyContent: 'center',
	gap: theme.spacing(2),
	backgroundColor: '#FBF7F6',
	borderBottom: `1px solid #E9E8E8`,
	'.stateHeader': {
		display: 'flex',
		flexDirection: 'row',
		width: '70%',
		marginBottom: theme.spacing(4),
		'.headingContainer': {
			display: 'flex',
			gap: theme.spacing(1),
			flexDirection: 'column',
			'.heading': {
				fontSize: theme.spacing(4),
				fontWeight: 600,
			},
			subHeading: {},
		},
		'.chipContainer': {
			marginLeft: 'auto',
		},
	},
	'.titleContainer': {
		display: 'flex',
		flexDirection: 'row',
		width: '100%',
		alignItems: 'center',
		justifyContent: 'center',
		gap: theme.spacing(2),
	},
	'.Title': {
		fontSize: theme.spacing(4),
		fontWeight: 600,
		// color: theme.palette.primary.main,
	},
	'.desc': {
		display: 'flex',
		flexDirection: 'column',
		alignItems: 'center',
		justifyContent: 'center',
		gap: theme.spacing(1),
		fontSize: theme.typography.pxToRem(16),
		color: '#888',
		paddingBottom: theme.spacing(2),
	},
}))
