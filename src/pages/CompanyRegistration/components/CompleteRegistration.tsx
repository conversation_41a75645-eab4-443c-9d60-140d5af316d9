import { Box, Chip, Stack, styled, Typography } from '@mui/material'
import {
	PendingIcon,
	DocIconWithRightClick,
	MessageIcon,
	PendingClockIcon,
} from '@/assets/icons'
import { useQuery, useQueryClient } from '@tanstack/react-query'
import { useAuthContext } from '@/contexts'
import { dateFormats, ICompanyDetails } from '@/utils/constant'
import { getFormattedDate } from '@/utils/helper'

const StyledContainer = styled(Stack)(({ theme }) => ({
	width: '100%',
	padding: theme.spacing(4),
	backgroundColor: '#FBF7F6',

	'.card': {
		display: 'flex',
		justifyContent: 'center',
		alignItems: 'center',
		gap: theme.spacing(2),
		border: `1px solid ${theme.palette.neutral?.[200]}`,
		alignSelf: 'center',
		padding: theme.spacing(4),
		borderRadius: theme.spacing(2),
		boxShadow: '0px 2px 4px rgba(0, 0, 0, 0.1)',
	},

	'.centered': {
		width: '100%',
		alignItems: 'center',
		display: 'flex',
		flexDirection: 'column',
	},

	'.pendingIcon': {
		width: 68,
		height: 68,
	},

	'.subtitle': {
		color: '#626262',
		textAlign: 'center',
		marginTop: theme.spacing(2),
	},

	'.summaryCard': {
		width: '100%',
		border: `1px solid ${theme.palette.neutral?.[200]}`,
		padding: theme.spacing(3),
		boxShadow: '0px 2px 4px rgba(0, 0, 0, 0.1)',
		borderRadius: theme.spacing(2),
		display: 'flex',
		flexDirection: 'column',
		gap: theme.spacing(2),
	},

	'.row': {
		width: '100%',
		display: 'flex',
		flexDirection: 'row',
		gap: theme.spacing(2),
	},

	'.halfColumn': {
		width: '100%',
		display: 'flex',
		flexDirection: 'column',
		gap: theme.spacing(1),
	},

	'.chipWrapper': {
		display: 'flex',
		flexDirection: 'row',
	},

	'.chip': {
		backgroundColor: '#FFEDD5',
		color: '#9A3412',
	},

	'.infoCard': {
		width: '100%',
		border: `1px solid ${theme.palette.neutral?.[200]}`,
		padding: theme.spacing(3),
		display: 'flex',
		flexDirection: 'row',
		boxShadow: '0px 2px 4px rgba(0, 0, 0, 0.1)',
		borderRadius: theme.spacing(2),
		gap: theme.spacing(2),
	},

	'.messageIconWrapper': {
		display: 'flex',
		gap: theme.spacing(2),
	},
}))

export const CompleteRegistration = () => {

	const queryClient = useQueryClient()

	const { userDetails } = useAuthContext()

	const companyDetails = useQuery({
		queryKey: ["companyDetails", userDetails?.companyId],
		initialData: () => queryClient.getQueryData<ICompanyDetails>(['companyDetails', userDetails?.companyId]),
		enabled: false
	})


	return (
		<StyledContainer>
			<Stack className='card'>
				<Stack className='centered' gap={2}>
					<Box component='img' src={PendingIcon} className='pendingIcon' />
					<Typography variant='h5'>
						Onboarding Request Sent for Approval
					</Typography>
					<Typography variant='subtitle1' className='subtitle'>
						Your company details have been successfully submitted to Circonomy.
						<br /> We're currently reviewing your request.
					</Typography>
				</Stack>

				<Stack className='summaryCard'>
					<Stack className='row'>
						<Box component='img' src={DocIconWithRightClick} />
						<Typography variant='h5' color='textSecondary'>
							Request Summary
						</Typography>
					</Stack>

					<Stack className='row' mt={2}>
						<Stack className='halfColumn'>
							<Typography variant='subtitle1' color='textSecondary'>
								Company Name
							</Typography>
							<Typography variant='subtitle2' color='textSecondary'>
								{companyDetails?.data?.name}
							</Typography>
							<Typography variant='subtitle1' color='textSecondary'>
								Status
							</Typography>
							<Stack className='chipWrapper'>
								<Chip
									icon={
										<Box
											component='img'
											src={PendingClockIcon}
											sx={{ width: 12, height: 12 }}
										/>
									}
									label='Pending Review'
									className='chip'
								/>
							</Stack>
						</Stack>

						<Stack className='halfColumn'>
							{[
								'Submitted Date',
								`${getFormattedDate(
									companyDetails?.data?.stages?.[2]?.createdAt ?? "",
									dateFormats.MMMM_dd_yyyy
								)}`,
								'Estimated Review Time',
								'1-2 Business Days',
							].map((text) => (
								<Typography
									key={text}
									variant='subtitle1'
									color='textSecondary'>
									{text}
								</Typography>
							))}
						</Stack>
					</Stack>
				</Stack>

				<Stack className='infoCard'>
					<Stack className='messageIconWrapper'>
						<Box component='img' src={MessageIcon} />
					</Stack>
					<Stack className='halfColumn'>
						<Typography variant='h5' color='textSecondary'>
							What Happens Next?
						</Typography>
						<Typography variant='subtitle1' color='textSecondary'>
							We'll notify you via email <b><EMAIL></b> once the
							review is complete.
						</Typography>
					</Stack>
				</Stack>
			</Stack>
		</StyledContainer>
	)
}
