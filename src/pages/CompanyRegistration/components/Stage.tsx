import {
	<PERSON><PERSON>,
	<PERSON><PERSON>,
	<PERSON>,
	Step<PERSON>abel,
	StepIconProps,
	Typography,
	StepButton,
} from '@mui/material'
import StepConnector, {
	stepConnectorClasses,
} from '@mui/material/StepConnector'
import { styled } from '@mui/material/styles'

import LockIcon from '@mui/icons-material/Lock'
import BusinessRoundedIcon from '@mui/icons-material/BusinessRounded'
import { useCallback, useMemo } from 'react'
import CheckRoundedIcon from '@mui/icons-material/CheckRounded'
import DescriptionOutlinedIcon from '@mui/icons-material/DescriptionOutlined'
import ConstructionRoundedIcon from '@mui/icons-material/ConstructionRounded'
import { ICompanyDetails, StageStatus } from '@/utils/constant'
import { theme } from '@/lib/theme/theme'
import { CompanyRegistrationSteps } from '../stage'

const ColorlibConnector = styled(StepConnector)(({ theme }) => ({
	[`&.${stepConnectorClasses.alternativeLabel}`]: {
		top: 22,
		left: 'calc(-50% + 25px)',
		right: 'calc(50% + 25px)',
	},
	[`&.${stepConnectorClasses.active}`]: {
		[`& .${stepConnectorClasses.line}`]: {
			backgroundColor: theme.palette.grey[800],
			border: 'none',
			height: 2,
		},
	},
	[`&.${stepConnectorClasses.completed}`]: {
		[`& .${stepConnectorClasses.line}`]: {
			backgroundColor: theme.palette.grey[800],
			border: 'none',
			height: 2,
		},
	},
	[`& .${stepConnectorClasses.line}`]: {
		height: 1,
		borderTop: `1px dotted ${theme.palette.grey[800]}`,
		width: '100%',
		borderRadius: 1,
		...theme.applyStyles('dark', {
			backgroundColor: theme.palette.grey[800],
		}),
	},
}))

export const CustomizedSteppers = ({
	lastStartedStage,
	setActiveStep,
	companyDetails,
}: {
	companyDetails?: ICompanyDetails
	activeStep: number
	lastStartedStage: number
	setActiveStep: React.Dispatch<React.SetStateAction<number>>
}) => {
	const stageData = useMemo(() => {
		const CurrentIconForStage = (index: number) => {
			if (
				lastStartedStage === CompanyRegistrationSteps.REGISTRATION_APPROVED &&
				index === 0
			) {
				return BusinessRoundedIcon
			} else if (
				companyDetails?.stages[index]?.status === StageStatus.approved ||
				companyDetails?.stages[index]?.status === StageStatus.pending
			) {
				return CheckRoundedIcon
			} else if (
				companyDetails?.stages?.[index]?.status === StageStatus.notStarted &&
				companyDetails?.stages[index - 1]?.isCompleted === true
			) {
				if (index === 0) {
					return BusinessRoundedIcon
				} else if (index === 1) {
					return DescriptionOutlinedIcon
				} else if (index === 2) {
					return ConstructionRoundedIcon
				}
			}
			return LockIcon
		}
		return [
			{
				title: 'Registration',
				detail: 'Provide Company details and documentation',
				icon: CurrentIconForStage(0),
				status: companyDetails?.stages?.[0]?.status || StageStatus.approved,
			},
			{
				title: 'Contract Documents',
				detail: 'Provide Company documents',
				icon: CurrentIconForStage(1),
				status: companyDetails?.stages?.[1]?.status || StageStatus.notStarted,
			},
			{
				title: 'dMRV Customization',
				detail: 'Setup what you want to setup for your dMRV',
				icon: CurrentIconForStage(2),
				status: companyDetails?.stages?.[2]?.status || StageStatus.notStarted,
			},
			{
				title: 'Complete',
				detail: 'Finish setup',
				icon:
					companyDetails?.stages?.[2]?.isCompleted === true
						? CheckRoundedIcon
						: LockIcon,
				status: companyDetails?.stages[3]?.status || StageStatus.notStarted,
			},
		]
	}, [companyDetails?.stages, lastStartedStage])

	const getStageStatus = (stepIndex: number) =>
		companyDetails?.stages?.[stepIndex]?.status

	const getBorder = useCallback(
		(stepIndex: number) => {
			if (stepIndex > lastStartedStage) {
				return `1px solid ${theme.palette.grey[800]}`
			}
			return 'none'
		},
		[lastStartedStage]
	)

	const getBackgroundColor = useCallback(
		(status: StageStatus, stepIndex: number) => {
			if (
				stepIndex === CompanyRegistrationSteps.REGISTRATION_APPROVED &&
				lastStartedStage === CompanyRegistrationSteps.REGISTRATION_APPROVED
			) {
				return theme.palette.primary.main
			} else if (stepIndex === CompanyRegistrationSteps.COMPLETE_REGISTRATION) {
				if (companyDetails?.stages?.[2]?.isCompleted === true) {
					if (companyDetails?.status === StageStatus.approved) {
						return theme.palette.custom.green[800]
					} else {
						return theme.palette.custom.yellow[300]
					}
				} else return 'transparent'
			}
			switch (status) {
				case StageStatus.approved:
					return theme.palette.custom.green[800]
				case StageStatus.pending:
					return theme.palette.custom.yellow[300]
				case StageStatus.notStarted:
					if (companyDetails?.stages[stepIndex - 1]?.isCompleted === true)
						return theme.palette.primary.main
					return 'transparent'
				default:
					return 'transparent'
			}
		},
		[companyDetails?.stages, companyDetails?.status, lastStartedStage]
	)

	const getColor = useCallback(
		(status: StageStatus, stepIndex: number) => {
			if (
				stepIndex !== CompanyRegistrationSteps.REGISTRATION_APPROVED &&
				lastStartedStage === CompanyRegistrationSteps.REGISTRATION_APPROVED
			) {
				return theme.palette.common.black
			} else if (stepIndex === CompanyRegistrationSteps.COMPLETE_REGISTRATION) {
				if (companyDetails?.stages?.[2]?.isCompleted === true) {
					if (companyDetails?.status === StageStatus.approved) {
						return theme.palette.common.white
					} else {
						return theme.palette.primary.main
					}
				} else return theme.palette.common.black
			}
			switch (status) {
				case StageStatus.pending:
					return theme.palette.primary.main
				case StageStatus.approved:
					return theme.palette.common.white
				case StageStatus.notStarted:
					if (companyDetails?.stages[stepIndex - 1]?.isCompleted === false)
						return theme.palette.common.black
					return theme.palette.common.white
				default:
					return theme.palette.common.black
			}
		},
		[companyDetails?.stages, companyDetails?.status, lastStartedStage]
	)

	const ColorlibStepIconRoot = styled('div')<{
		ownerState: { completed?: boolean; active?: boolean; stepIndex: number }
	}>(({ ownerState }) => {
		const status =
			getStageStatus(ownerState.stepIndex) ?? StageStatus.notStarted

		return {
			zIndex: 1,
			width: 40,
			height: 40,
			display: 'flex',
			borderRadius: '35%',
			justifyContent: 'center',
			alignItems: 'center',
			border: getBorder(ownerState.stepIndex),
			backgroundColor: getBackgroundColor(status, ownerState.stepIndex),
			color: getColor(status, ownerState.stepIndex),
		}
	})

	function ColorlibStepIcon(props: StepIconProps) {
		const { active, completed, icon, className } = props
		const stepIndex = Number(icon) - 1
		const IconComponent = stageData[stepIndex]?.icon

		return (
			<ColorlibStepIconRoot
				ownerState={{ completed, active, stepIndex }}
				className={className}>
				{IconComponent && <IconComponent sx={{ height: 24 }} />}
			</ColorlibStepIconRoot>
		)
	}

	return (
		<Stack sx={{ width: '50%' }} spacing={4}>
			<Stepper
				alternativeLabel
				activeStep={lastStartedStage}
				connector={<ColorlibConnector />}>
				{stageData.map((item, index) => (
					<Step key={item.title}>
						<StepButton onClick={() => setActiveStep(index)}>
							<StepLabel StepIconComponent={ColorlibStepIcon}>
								<Stack direction='column' spacing={0.5} alignItems='center'>
									<Typography
										style={{
											fontSize: '1rem',
											fontWeight: 'bold',
											alignItems: 'center',
										}}>
										{item.title}
									</Typography>
									<Typography
										style={{
											fontSize: '0.8rem',
											color: '#888',
											textAlign: 'center',
										}}>
										{item.detail}
									</Typography>
								</Stack>
							</StepLabel>
						</StepButton>
					</Step>
				))}
			</Stepper>
		</Stack>
	)
}
