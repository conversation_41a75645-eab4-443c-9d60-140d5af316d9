import { ArrowLeftRounded, AttachFile } from '@mui/icons-material'
import { Box, Button, Stack, styled, Typography, useTheme } from '@mui/material'
import { FC, useCallback, useMemo, useState } from 'react'
import { format } from 'date-fns'
import Stocks from '../../../assets/icons/firepot.svg'
import Sinks from '../../../assets/icons/hexgon.svg'
import { GridColDef, GridValidRowModel } from '@mui/x-data-grid'
import {
	ActionInformationDrawer,
	CustomChip,
	CustomDataGrid,
} from '@/components'
import { ISink, IStock, TCertificateDetails } from '@/interfaces'
import { CustomTable } from '@/components/CustomTable'
import { ViewOrAddCertificates } from '@/pages/dashboard'
import { useCreditsProjectDetails } from './useCreditsProjectDetails'

const chipStatusClass: { [key: string]: string } = {
	Approved: 'approved',
	Rejected: 'rejected',
	Pending: 'pending',
}

type TCertificationDrawerProps = {
	mode: 'view' | 'upload'
	open: boolean
	certificates: TCertificateDetails[]
	for: 'project' | 'stock'
	stockId?: string
}

const initialValue: TCertificationDrawerProps = {
	mode: 'view',
	open: false,
	certificates: [],
	for: 'project',
}

export const CreditProjectDetails = () => {
	const theme = useTheme()
	const {
		internalProjectDetails,
		handleBack,
		paramsTab,
		handleTabChange,
		getSinkList,
		rowCount,
		getStocksList,
		createStockMutation,
	} = useCreditsProjectDetails()
	const [certificationDrawerDetail, setCertificationDrawerDetail] =
		useState<TCertificationDrawerProps>(initialValue)

	const projectDetails = useMemo(
		() => [
			{
				label: 'Project Name',
				value: internalProjectDetails?.name,
			},
			{
				label: 'Project ID',
				value: internalProjectDetails?.projectID,
			},
			{
				label: 'Comment',
				value: internalProjectDetails?.comments,
			},
		],
		[
			internalProjectDetails?.comments,
			internalProjectDetails?.name,
			internalProjectDetails?.projectID,
		]
	)

	const tabs = [
		{
			label: 'Stocks',
			value: 'stocks',
			imageSrc: Stocks,
		},
		{
			label: 'Sinks',
			value: 'sinks',
			imageSrc: Sinks,
		},
	]

	const sinkColumns: GridColDef<GridValidRowModel>[] = useMemo(
		() => [
			{
				field: 'id',
				headerName: 'Sink ID',
				minWidth: 100,
				flex: 1,
				renderCell: (params) => (
					<Typography variant='subtitle1'>{params?.value}</Typography>
				),
			},
			{
				field: 'stockId',
				headerName: 'Stock ID',
				minWidth: 100,
				flex: 1,
				renderCell: (params) => (
					<Typography variant='subtitle1'>{params?.value}</Typography>
				),
			},
			{
				field: 'biocharQuantity',
				headerName: 'Quantity (tonnes)',
				minWidth: 150,
				flex: 1,
				renderCell: (params) => (
					<Typography variant='subtitle1'>{params?.value}</Typography>
				),
			},

			{
				field: 'blendingMatrixId',
				headerName: 'Matrix ID',
				minWidth: 150,
				flex: 1,
				renderCell: (params) =>
					(params?.value ?? [])?.length > 0 ? (
						<Stack direction='column'>
							{params?.value?.map((i: string) => (
								<Typography variant='subtitle1' key={i}>
									{i}
								</Typography>
							))}
						</Stack>
					) : (
						<Typography variant='subtitle1'>-</Typography>
					),
			},
			{
				field: 'sinkStatus',
				headerName: 'Status',
				minWidth: 100,
				flex: 1,
				renderCell: (params) => (
					<CustomChip
						appliedClass={chipStatusClass[params?.value]}
						label={params?.value}
					/>
				),
			},
		],
		[]
	)

	const stockColumns: GridColDef<GridValidRowModel>[] = useMemo(
		() => [
			{
				field: 'stockId',
				headerName: 'Stock ID',
				minWidth: 100,
				flex: 1,
				renderCell: (params) => (
					<Typography variant='subtitle1'>{params?.value}</Typography>
				),
			},

			{
				field: 'bioCharQuantity',
				headerName: 'Quantity (tonnes)',
				minWidth: 100,
				flex: 1,
				renderCell: (params) => (
					<Typography variant='subtitle1'>{params?.value}</Typography>
				),
			},

			{
				field: 'carbonCredits',
				headerName: 'C-Qty (tCO2)',
				minWidth: 150,
				flex: 1,
				renderCell: (params) => (
					<Typography variant='subtitle1'>{params?.value}</Typography>
				),
			},
			{
				field: 'carbonPercentage',
				headerName: 'C-Content (%)',
				minWidth: 150,
				flex: 1,
				renderCell: (params) => (
					<Typography variant='subtitle1'>{params?.value}</Typography>
				),
			},
			{
				field: 'cropName',
				headerName: 'Biomass Type',
				minWidth: 150,
				flex: 1,
				renderCell: (params) => (
					<Typography variant='subtitle1'>{params?.value}</Typography>
				),
			},

			{
				field: 'productionDate',
				headerName: 'Date of Production',
				minWidth: 150,
				flex: 1,
				renderCell: (params) => (
					<Typography variant='subtitle1'>
						{format(new Date(params?.value), 'yyyy-MM-dd')}
					</Typography>
				),
			},
			{
				field: 'status',
				headerName: 'Status',
				minWidth: 150,
				flex: 1,
				renderCell: (params) => (
					<CustomChip
						appliedClass={chipStatusClass[params?.value]}
						label={params?.value}
					/>
				),
			},
		],
		[]
	)

	const handleViewOrAttachProjectCertificate = useCallback(() => {
		setCertificationDrawerDetail({
			open: true,
			certificates: internalProjectDetails?.certificates ?? [],
			mode: 'view',
			for: 'project',
			stockId: '',
		})
	}, [internalProjectDetails])

	const certificateMemo = useMemo(() => {
		if (internalProjectDetails?.certificate !== null) {
			return (
				<Stack className='info-container'>
					<Typography className='label'>Certificate</Typography>
					<Button
						className='btn'
						variant='text'
						onClick={handleViewOrAttachProjectCertificate}
						startIcon={
							<AttachFile sx={{ color: theme.palette.neutral[500] }} />
						}>
						{internalProjectDetails?.certificationBodyName || 'Attach'}
					</Button>
				</Stack>
			)
		}
		return null
	}, [
		internalProjectDetails,
		theme.palette.neutral,
		handleViewOrAttachProjectCertificate,
	])

	return (
		<>
			<ActionInformationDrawer
				open={certificationDrawerDetail.open}
				onClose={() => setCertificationDrawerDetail(initialValue)}
				anchor='right'
				component={
					<ViewOrAddCertificates
						mode={certificationDrawerDetail.mode}
						modal={certificationDrawerDetail.for}
						onClose={() =>
							setCertificationDrawerDetail((prev) => ({
								...initialValue,
								mode: prev.mode,
								for: prev.for,
								stockId: '',
							}))
						}
						certificates={certificationDrawerDetail.certificates ?? []}
						stockId={certificationDrawerDetail.stockId}
					/>
				}
			/>

			<StyledContained>
				<Stack className='header-navigation'>
					<Button
						onClick={handleBack}
						className='batch-button'
						variant='text'
						startIcon={<ArrowLeftRounded fontSize='small' />}>
						Projects
					</Button>
					<Typography variant='body1' color={theme.palette.neutral['500']}>
						&nbsp;/ {internalProjectDetails?.name}
					</Typography>
				</Stack>
				<Stack className='container'>
					<Box className='project-details'>
						{projectDetails.map((detail) => (
							<Stack key={detail.label} className='info-container'>
								<Typography className='label'>{detail.label}</Typography>
								<Typography className='value'>{detail.value || '-'}</Typography>
							</Stack>
						))}
						{certificateMemo}
					</Box>
					<Stack>
						<Typography className='label'>Artisan Pro Name:</Typography>
						{(internalProjectDetails?.artisanProDetails ?? [])?.length > 0
							? internalProjectDetails?.artisanProDetails?.map((text, idx) => (
									<Typography key={idx} variant='subtitle2'>
										{text?.name} {text?.address ? `(${text?.address})` : ''}
									</Typography>
							  ))
							: '-'}
					</Stack>
					<Stack className='tab-container'>
						<Stack className='tab-list'>
							{tabs.map((tab) => (
								<Stack
									key={tab.value}
									onClick={() => handleTabChange(tab.value)}
									component={Button}
									id={tab.value}
									className={`tab ${
										paramsTab === tab.value ? 'active-tab' : ''
									}`}>
									<Box component='img' src={tab.imageSrc} className='tab-img' />
									<Typography className='tab-label'>{tab.label}</Typography>
								</Stack>
							))}
						</Stack>
						<Stack className='tab-panel'>
							<RenderTabPanel
								panelValue={paramsTab}
								sink={{
									rows: getSinkList?.data ?? [],
									columns: sinkColumns,
									loading: getSinkList?.isLoading,
								}}
								stock={{
									rows: getStocksList?.data ?? [],
									columns: stockColumns,
									isLoading:
										getStocksList?.isLoading || getStocksList?.isFetching,
								}}
								rowCount={rowCount}
								isCreateStockApiLoading={createStockMutation.isPending ?? false}
							/>
						</Stack>
					</Stack>
				</Stack>
			</StyledContained>
		</>
	)
}

type TTabCommonProps<T> = {
	rows: T[]
	loading: boolean
	columns: GridColDef<GridValidRowModel>[]
}

type TRenderTabPanel = {
	panelValue: string
	sink: TTabCommonProps<ISink>
	stock: Omit<TTabCommonProps<IStock>, 'loading'> & { isLoading: boolean }
	rowCount: number
	isCreateStockApiLoading: boolean
}

const RenderTabPanel: FC<TRenderTabPanel> = ({
	panelValue,
	sink,
	rowCount,
	stock,
}) => {
	const handleRowClick = useCallback((params: any) => {
		params.setOpen(!params?.open)
		const collapseDetails = {
			kilnProcess: params?.row?.kilnProcess,
			stockId: params?.row?.stockId,
		}
		params.setHookData(collapseDetails)
	}, [])

	switch (panelValue) {
		case 'stocks':
			return (
				<CustomTable
					{...stock}
					count={rowCount}
					showPagination
					showPaginationDetails
					isComponent
					component='stock_process_details'
					handleRowClick={handleRowClick}
				/>
			)
		case 'sinks':
			return <CustomDataGrid {...sink} showPagination rowCount={rowCount} />
		default:
			return null
	}
}

const StyledContained = styled(Stack)(({ theme }) => ({
	gap: theme.spacing(2),
	'.header-navigation': {
		padding: theme.spacing(4, 1, 2),
		borderBottom: `${theme.spacing(0.125)} solid ${
			theme.palette.neutral['100']
		}`,
		alignItems: 'center',
		flexDirection: 'row',
		'.batch-button': {
			color: theme.palette.neutral['500'],
			...theme.typography.body1,
			'.arrow-icon': {
				color: theme.palette.neutral['500'],
			},
		},
	},
	'.container': {
		padding: theme.spacing(1, 2.2),
		rowGap: theme.spacing(2),
		'.label': {
			fontSize: theme.typography.caption.fontSize,
			fontWeight: theme.typography.h1.fontWeight,
			color: theme.palette.neutral[300],
		},
		'.project-details': {
			display: 'grid',
			gridTemplateColumns: 'repeat(auto-fill, minmax(320px, 1fr))',
			rowGap: theme.spacing(2),
			paddingBottom: theme.spacing(2.375),
			'.info-container': {
				'.label': {
					fontSize: theme.typography.caption.fontSize,
					fontWeight: theme.typography.h1.fontWeight,
					color: theme.palette.neutral[300],
				},
				'.value': {
					...theme.typography.body2,
					fontWeight: theme.typography.body1.fontWeight,
				},
				'.btn': {
					width: 'fit-content',
					...theme.typography.body2,
				},
			},
		},
		'.tab-container': {
			gap: theme.spacing(3.5),
			'.tab-list': {
				flexDirection: 'row',
				columnGap: theme.spacing(1),
				'.tab': {
					flexDirection: 'row',
					alignItems: 'center',
					justifyContent: 'flex-start',
					gap: theme.spacing(1.25),
					width: theme.spacing(25),
					border: `1px solid #74747445`,
					borderRadius: theme.spacing(1.5),
					padding: theme.spacing(1.25),
					background: '#B9B9B945',
					'.tab-img': {
						width: theme.spacing(2.5),
						height: theme.spacing(2.5),
						objectFit: 'contain',
						filter: 'grayscale(1)',
					},
					'.tab-label': {
						...theme.typography.caption,
						fontWeight: theme.typography.body2.fontWeight,
						color: theme.palette.neutral[500],
					},
					'&.active-tab': {
						border: `1px solid ${theme.palette.neutral[100]}`,
						background: theme.palette.success.light,
						'.tab-img': {
							filter: 'grayScale(0)',
						},
						'.tab-label': {
							color: theme.palette.success.main,
						},
					},
					'& .MuiTouchRipple-root': {
						color: theme.palette.success.main,
					},
				},
			},
			'.tab-panel': {
				'.grid-header-component': {
					flexDirection: 'row',
					alignItems: 'center',
					flexWrap: 'wrap',
					rowGap: theme.spacing(1),
					'.form-controller': {
						margin: theme.spacing(0.125),
						minWidth: theme.spacing(14),
						'.MuiOutlinedInput-notchedOutline': {
							borderRadius: theme.spacing(1.25),
						},
						'.date-picker': {
							height: theme.spacing(4.5),
							input: {
								width: theme.spacing(10),
								padding: theme.spacing(1.15, 1, 1.15, 2),
								fontSize: theme.typography.caption.fontSize,
							},
							'& .MuiInputAdornment-root': {
								'& .MuiIconButton-root': {
									svg: {
										width: theme.spacing(2.5),
										height: theme.spacing(2.5),
									},
								},
							},
						},
					},
				},
			},
		},
	},
}))
