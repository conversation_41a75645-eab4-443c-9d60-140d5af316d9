import { publicAxios } from '@/contexts'
import {
	IInternalProjectDetails,
	ISinkResponse,
	IStockListResponse,
} from '@/interfaces'
import { Nullable } from '@/types'
import { defaultLimit, defaultPage } from '@/utils/constant'
import { useMutation, useQuery } from '@tanstack/react-query'
import { useCallback, useState } from 'react'
import { useNavigate, useParams, useSearchParams } from 'react-router-dom'
import { toast } from 'react-toastify'

export const useCreditsProjectDetails = () => {
	const { projectId } = useParams()
	const [searchParams, setSearchParams] = useSearchParams()
	const navigate = useNavigate()
	const paramsTab = searchParams.get('tab') || 'stocks'
	const paramsPage = searchParams.get('page') || defaultPage
	const paramsLimit = searchParams.get('limit') || defaultLimit
	const [selectedRowParams, setSelectedRowParams] = useState<any>()
	const [rowCount, setRowCount] = useState<number>(0)
	const [selectedIdsForProcessTab, setSelectedIdsForProcessTab] = useState<
		string[]
	>([])

	const getInternalProjectDetails = useQuery({
		queryKey: ['internalProjectDetails', projectId],
		queryFn: async () =>
			publicAxios<Nullable<IInternalProjectDetails>>(
				`/public/internal-project/${projectId}`
			),
	})

	const getRowParams = (params: any) => setSelectedRowParams(params)

	const getSinkList = useQuery({
		queryKey: ['sinkList'],
		queryFn: async () => {
			try {
				const { data } = await publicAxios.get<ISinkResponse>(
					`/public/internal-project/${projectId}/sinks?limit=${paramsLimit}&page=${paramsPage}`
				)
				setRowCount(data?.count || 0)
				return data?.sinks
			} catch (error: any) {
				toast(error?.response?.data?.messageToUser)
				return null
			}
		},
		enabled: paramsTab === 'sinks',
	})

	const getStocksList = useQuery({
		queryKey: ['stockList', projectId, paramsLimit, paramsPage],
		queryFn: async () => {
			const { data } = await publicAxios<IStockListResponse>(
				`/public/internal-project/${projectId}/stocks?limit=${paramsLimit}&page=${paramsPage}`
			)
			setRowCount(data?.count ?? 0)
			return data?.stocks
		},
		select: (data) => {
			return (data ?? [])?.map((stock, idx) => ({
				...stock,
				id: idx,
			}))
		},
		enabled: paramsTab === 'stocks',
	})

	const createStockMutation = useMutation({
		mutationKey: ['createSelectedStock', selectedIdsForProcessTab, projectId],
		mutationFn: async () => {
			const { data } = await publicAxios.post(
				`/public/global-csink/process/stock`,
				{
					processIds: selectedIdsForProcessTab,
					internalProjectId: projectId,
				}
			)
			return data
		},
		onSuccess: ({ data }) => {
			toast(data?.message)
			setSelectedIdsForProcessTab([])
		},
		onError: (error: any) => {
			toast(error?.response?.data?.messageToUser)
		},
	})

	const handleCreateMutation = useCallback(() => {
		createStockMutation.mutate()
	}, [createStockMutation])

	const handleTabChange = useCallback(
		(value: string) => {
			setSearchParams(
				(prev) => {
					prev.set('tab', value)
					return prev
				},
				{ replace: true }
			)
			setRowCount(0)
		},
		[setSearchParams]
	)
	const handleBack = () => {
		navigate(-1)
	}

	const handleSelectProcess = useCallback(
		(id: string) => {
			if (selectedIdsForProcessTab?.includes(id)) {
				setSelectedIdsForProcessTab((prev) => prev.filter((x) => x !== id))
				return
			}
			setSelectedIdsForProcessTab((prev) => [...prev, id])
		},
		[selectedIdsForProcessTab]
	)

	return {
		internalProjectDetails: getInternalProjectDetails?.data?.data,
		navigate,
		paramsTab,
		handleBack,
		handleTabChange,
		paramsLimit,
		paramsPage,
		getSinkList,
		rowCount,
		getStocksList,
		handleSelectProcess,
		selectedIdsForProcessTab,
		handleCreateMutation,
		createStockMutation,
		getRowParams,
		selectedRowParams,
	}
}
