import { Nullable } from '@/types'
import { ActionTypes } from '@/utils/constant'

export interface GetPendingTaskResponse {
	count: number
	result: { appCreatedAt: Date; actions: Action[] }[]
}

export interface Action {
	id: string
	appType: string
	data: string
	type: ActionTypes
	appCreatedAt: Date
	siteAction: boolean
	siteName: string
	siteShortCode: string
	siteId: string
	kilnId: null
	kilnName: null
	kilnShortName: null
	error: string
	status: string
}

export interface BiocharBag {
	bagId: string
	count: number
	bagName?: string
}
export interface openMixPackage {
	openMixQty: number
	packagingTypeId: string
}

export type ActionData = {
	statusType: string
	error: string | string[]
	biocharQuantity: number
	cropId?: string
	description: string
	imageIds: string[]
	otherMixQuantity: number
	otherMixQuantityUnit: string
	packagingTypeId: string
	siteId?: { value: string; label: string }
	kilnId?: string
	waterQuantityAdded: number
	biocharOnlyBags?: BiocharBag[]
	bags?: BiocharBag[]
	packagingBags?: BiocharBag[]
	distributionBags?: BiocharBag[]
	videoIds: string[]
	VehicleType: string
	vehicleId: string
	fuelValue: number
	openMixPackages: openMixPackage[]
	buyerId: string
	vehicleServerId: string
	vehicleLocalId: string
	farmerIds: string[]
	isOpenDistribution: boolean
	csinkNetworkId?: string
	artisanProId?: string
	openMixQty?: number
	packagingQuantity: number
	fpuId?: string
	localFpuId?: string
	packagingBag?: BiocharBag[]
	mixTypeId?: string
	mixQuantity?: number
	portionType?: string
}

export type bagList =
	| 'bags'
	| 'biocharOnlyBags'
	| 'packagingBags'
	| 'distributionBags'

// TOOD: make it partial in future
export type ActionResponse = ActionData

export interface GetPackagingTypeResponse {
	types: PackagingType[]
}

export interface PackagingType {
	id: string
	name: string
	biocharOnly: boolean
	otherMixName: null | string
	type: string
	fixedRatio: boolean
	density: number | null
}

export enum BioCharTabsType {
	biocharProduction = 'biochar_production',
	biocharApplication = 'biochar_application',
}

export type TContianer = {
	count: number
	measuringContainerId: string
	localMeasuringContainerId: Nullable<string>
	volume: number
	height: number
	isFullyFilled: boolean
	isHeightInMM: boolean
}

export type TBiocharProductionResponse = {
	address?: string
	artisanProId?: string
	latitude?: number
	longitude?: number
	_biomassTypeId?: Nullable<string>
	biomassTypeId: string
	_biomassType: { _value: string; _label: string }
	biomassQuantityUnit: string
	biomassQuantity: string
	siteId: string
	farmerId: string
	name?: string
	localId?: string
	number?: number
	categoryId?: string
	_vehicle: Nullable<any>
	description: string
	imageIds?: string[]
	ImageIds?: string[]
	videoIds: string[]
	fuelType?: string
	biomassTransportationVehicleType?: string
	kilnId: string
	_isFarmer?: string
	fpuId?: string
	_kilnId?: string
	moistureContentValue: number[]
	temperatureUnit: string
	temperature: number
	measuringContainer: TContianer[]
	samplingContainerId: string
}

export type TBiocharProductionForm = {
	biomassTypeId: { value: string | null; label: string }
	biomassQuantityUnit: string
	biomassQuantity: string
	latitude?: number
	longitude?: number
	siteId: { value: string; label: string }
	artisanProId: { value: string; label: string }
	farmerId: { value: string; label: string }
	_vehicle: Nullable<any>
	description: string
	imageIds?: string[]
	ImageIds?: string[]
	videoIds: string[]
	localId?: string
	categoryId: { value: string; label: string }
	fuelType?: { value: string; label: string }
	biomassTransportationVehicleType?: string
	kilnId: { value: string; label: string }
	_isFarmer?: string
	fpuId?: { value: string; label: string }
	moistureContentValue: number[]
	temperatureUnit: string
	temperature: number
	measuringContainer: TContianer[]
	samplingContainerId: string
}

export type TBiocharActionData = {
	data: Nullable<TBiocharProductionResponse>
	error: string | string[]
	artisanProId: string
	siteId: string
	kilnId: string
	statusType: string
	csinkNetworkId: string
}

export type AutocompleteItems =
	| 'siteId'
	| 'kilnId'
	| 'farmerId'
	| 'vehicleId'
	| 'biomassTypeId'
	| 'fpuId'

export enum StatusType {
	PROCESSED = 'processed',
	ERROR = 'error',
	CREATED = 'created',
	PENDING = 'pending',
}

export type BiocharDataResponse = {
	availableBiocharQty: CropData[]
	totalAvailableBiocharQty: number
	totalNotAssessedBiocharQty: number
}

export interface CropData {
	cropName: string
	cropId?: string
	biocharQty: number
}

export interface BiocharSummaryProps {
	notAssessedQty?: number
	approvedQty?: number | null
	cropData?: CropData[] | null
}
