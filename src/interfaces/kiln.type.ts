import { Nullable } from '@/types'
import { IMediaWithDeleteParams } from './batch.type'
import { IKiln } from './site.type'

export interface IKilnWithDimensionImages extends IKiln {
	upper_surface_images: Nullable<IMediaWithDeleteParams[]>
	lower_surface_images: Nullable<IMediaWithDeleteParams[]>
	depth_images: Nullable<IMediaWithDeleteParams[]>
	lower_side_images: Nullable<IMediaWithDeleteParams[]>
	upper_side_images: Nullable<IMediaWithDeleteParams[]>
	diameter_images: Nullable<IMediaWithDeleteParams[]>
	frustum_length_images: Nullable<IMediaWithDeleteParams[]>
	short_base_images: Nullable<IMediaWithDeleteParams[]>
	long_base_images: Nullable<IMediaWithDeleteParams[]>
}
