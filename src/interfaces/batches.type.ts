export interface IKilnProcess {
	id: string
	shortCode: string
	startTime: string
	endTime: string | null
	status: string
	biomassQuantity: number
	biocharQuantity: number
	carbonPercentage: number | null
	carbonCredits: number | null
	density: number | null
	c02Emission: number | null
	kilnID: string
	kilnName: string
	kilnShortName: string
	siteId: string
	siteName: string
	siteShortName: string
	networkId: string | null
	networkName: string | null
	networkShortName: string | null
	artisanProId: string
	artisanProName: string
	artisanProShortName: string
	cropName: string
	biomassAggregatorId: string
	biomassAggregatorName: string
	biomassType: string
}

export interface ICrops {
	cropName:string,
	id:string
}
export interface IBatch {
	kilnProcesses: IKilnProcess[]
	count: number
	crops:ICrops[]
}
