export interface IProjects {
	id: string
	name: string
	projectTime: string
	capacity: number
	ImagePaths: string[]
	address: string
	available: number
	rate: number
	method: string
	description: string
	contactsIds: string[]
	projectStatus: string
	methodology: string
	boughtByUsers: number
	createdAt: string
}
export interface IProjectDetails {
	id: string
	name: string
	rate: number
	creditsIssued: number
	available: number
	aboutProjectImageProxyUrl?: string
	aboutProjectImageUrl?: string
	address?: string
	annualCarbonRemovalCapacity?: string
	application?: string
	biocharDescription?: string
	biocharImageProxyUrl?: string
	biocharImageUrl?: string
	permanence?: string
	boughtByUsers?: number
	state?: string
	calculationMethods?: string
	endorsingAgentDescription?: string
	endorsingAgentName: string
	endorsingAgentUrl?: string
	contacts: IContactPeople[]
	createdAt?: Date
	lat?: number
	long?: number
	durabilityImageId?: string
	biocharImageId?: string
	aboutProjectImageId?: string
	creditingEndDate?: string
	creditingStartDate: string
	currentVerifier: string
	description: string
	documents: IDocuments[]
	contactsIds: string[]
	durabilityDescription: string
	durabilityImageProxyUrl: string
	durabilityImageUrl: string
	headerImageProxyUrls?: string[]
	headerImageUrls?: string[]
	linkToRegistry?: string
	methaneMitigation: string
	applicationDetails: string
	methaneMitigationStrategyDetails: string
	projectStatus: string
	registrationDate: string
	registryDescription?: string
	operatorId: string
	headerImages?: ImgInfo[]
	registryUrl: string
	shortDescription: string
	standardDescription?: string
	standardName: string
	standardUrl: string
	technology: string
	totalBoughtCredits?: number
	projectMapLocationURL?: string
	unGoalIds: number[]
	unGoals: IUnGoals[]
	nextAvailableDate: string | null
	certificationDetails: string
	monitoring: string
	seoNameUrl: string
}
export interface IProjectDocuments {
	circonomyPDD?: IDocuments | null
	ceresCertificate?: IDocuments | null
	officialPDD?: IDocuments | null
	dmrvEndorsement?: IDocuments | null
}
export interface IContactPeople {
	id: string
	imagePath: string
	imageURL: string
	name: string
	email: string
	description: string
	designation: string
	phone: string
	linkedinLink: string
	imageId: string
}

export interface IUnGoals {
	description?: string
	iconFilename: string
	id: number
	name: string
	topic?: string
}
export interface IDocuments {
	imageId: string
	name: string
	imagePath: string
	imageProxyURL: string
	imageURL: string
}
export interface ImgInfo {
	id: string
	url: string
	path: string
	fileName?: string
	createdTime?: string
}

export interface IProjectsResponse {
	count: number
	projects: IProjects[]
}

export enum ProjectAccordion {
	projectIntroduction = 'projectIntroduction',
	projectDescription = 'projectDescription',
	projectAbout = 'projectAbout',
	projectCertification = 'projectCertification',
	projectMonitoring = 'projectMonitoring',
	projectDetails = 'projectDetails',
	projectTechnology = 'projectTechnology',
	contactDetails = 'contactDetails',
	documentDetails = 'documentDetails',
	projectImg = 'projectImg',
}

export interface IDevelopmentGoalsList {
	id?: number
	name?: string
	iconFilename?: string
	description?: string
	topic?: string
}

export interface IContactDetails {
	contactName: string
	id?: string
	contactEmail: string
	contactPhoto: string
	contactUrl: string
	linkedinLink: string
}
