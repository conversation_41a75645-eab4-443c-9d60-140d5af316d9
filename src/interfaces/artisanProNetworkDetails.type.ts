import { Nullable } from '@/types'
import { ImageURL, IMeasuringContainer } from './batch.type'
import {
	Bag,
	ICsinkApplicationType,
	ICsinkMixingType,
	IOtherBuyers,
	IVehicle,
	Kiln,
	KilnOperator,
	TBiomassProcessingDetails,
	TMethaneCompensateStrategies,
	Vehicle,
} from './csink.type'
import { IImage } from './image.type'
import { Coordinate } from './BiomassAggregator.type'
import { BiomassReference } from './Entity'
import { ProfileImageURL } from './userManagement'

export interface ManagerDetail {
	//
	managerCountryCode?: string
	managerId: string
	managerName: string
	managerPhone: string
	managerEmail: string
	trained: boolean
	trainingImages: IImage[]
	countryCode: string
	language: string
	certificateUrl: null
	certificateStatus: string
	isCsinkManager: boolean
	profileImageUrl: IImage
	managerAddress: string
	accountType: string
	csinkManagerId: string
	pendingUploadsCount: number
}
export interface IArtisanProNetworkDetails {
	address: string
	biomassAggregatorID: string
	biomassAggregatorName: string
	biomassAggregatorShortName: string
	artisianProNetworkName: string
	id: string
	isCsinkManager: boolean
	name: string
	shortCode: string
	totalBiocharProduced: number | null
	artisanProCount: number
	totalBiomassDropped?: number
	managerDetails?: ManagerDetail[]
	siteCount?: number
}
export interface ISiteList {
	id: string
	name: string
	address: string
	coordinate: string
	biocharQuantity: number
	mixedQuantity: number
	packedQuantity: number
	createdAt: Date
	shortCode: string
	networkId: string
	networkName: string
	artisianProName: string
	baName: string
	isCsinkManager: boolean
	approvedBiocharQuantity: number
	notAssessedBiocharQuantity: number
	kilnCount: number
	samplingContainerCount: number
	operators?: IOperator[]
	details: Details
}

export interface Details {
	vehicles: Vehicle[]
	measuringContainers: MeasuringContainer[]
	samplingContainers: SamplingContainer[]
	fpu: FPU[]
	kilns: Kiln[]
	carbonCredits: CarbonCredit[]
	biochar: Biochar[]
	biomass: Biomass[]
}

export interface Biochar {
	cropId: string
	cropName: string
	biocharProduced: number
	biocharProducedInTonne: number
}

export interface CarbonCredit {
	cropId: string
	cropName: string
	carbonCredits: number
}

export interface FPU {
	id: string
	name: string
	address: string
	coordinate: string
	createdAt: Date
	fpuArea: FPUArea[] | null
}

export interface BiocharDetails {
	biocharQuantity: number
	distributedBiocharQuantity: number
	mixedBiocharQuantity: number
	approvedBiocharQuantity: number
	notAssessedBiocharQuantity: number
	totalBiocharProduced: number
	totalBiocharProducedInTonne: number
}

export interface MeasuringContainer {
	id: string
	ShortName: string
	diameter: null
	diameterUnit: string
	height: number
	heightUnit: string
	imageURL: ImageURL
	imageURLs: ImageURL[]
	createdAt: Date
	volume: number
	length: number
	lengthUnit: string
	breadth: number
	breadthUnit: string
	shape: string
	name: string
	inUse: boolean
	isPartialFilled: boolean
	measuringContainerHeight: null
	upperSurfaceDiameter: null
	upperSurfaceDiameterUnit: string
	lowerSurfaceDiameter: null
	lowerSurfaceDiameterUnit: string
}

export interface SamplingContainer {
	id: string
	diameter: null
	height: number
	shortCode: string
	imageId: null
	imageURLs: ImageURL[]
	imagePath: null
	imageURL: string
	filled: boolean
	createdAt: Date
	kilnProcesses: null
	name: string
	length: null
	breadth: null
	shape: string
	volume: number
	inUse: boolean
	volumeFilled: number
	upperSurfaceDiameter: number
	upperSurfaceDiameterUnit: string
	lowerSurfaceDiameter: number
	lowerSurfaceDiameterUnit: string
}
export interface IFootprint {
	id: string
	operatedBy: string
	process: string
	fuelType: string
	time: number
	startCoordinate: string
	endCoordinate: string
	distance: number
	siteId: string
	addedTime: string
	fuelValue: number | null
	carbonEmissionInGram: number
}

export interface IArtisanProNetworkList {
	id: string
	name: string
	shortCode: string
	address: string
	artisianProNetworkID: string
	artisianProNetworkName: string
	bighaInHectare: number
	managerNames: string[]
	details: IDetails
	totalBiocharProduced: number | null
	methaneCompensationStrategy: string
	managerDetails: ManagerDetail[]
	totalBiocharProducedInTonne: number | null
	siteCount: number
	biomassAggregatorName: string
	operatorDetails?: KilnOperator[]
	totalBiomassDropped: number | null
	totalCarbonEmissionInGram?: number | null
	documents: Nullable<IImage[]>
	methaneCompensateType: Nullable<string>
	compensationType: string
	biomassPreprocessingDetails: TBiomassProcessingDetails
	location: Nullable<{ x: number; y: number }>
	methaneCompensateStrategies: TMethaneCompensateStrategies[]
}

export interface IDetails {
	preferredCrops: IPreferredCrops[]
	vehicles: IVehicle[]
	measuringContainer: IMeasuringContainer[]
	otherBuyers: IOtherBuyers[]
	footprints?: IFootprint[]
	bags: Bag[]
	carbonCredits: CarbonCredit[]
	biochar: Biochar[]
	biomass: Biomass[]
	totalBiomass: number
}

export interface Biomass {
	cropId: string
	cropName: string
	biomassAvailable: number
	biomassProduced: number
}
export interface IPreferredCrops {
	cropId: string
	name: string
	createdAt: Date
	image: ImageURL
}

export interface Biochar {
	cropId: string
	cropName: string
	biocharProduced: number
	biocharProducedInTonne: number
}

export interface CarbonCredit {
	cropId: string
	cropName: string
	carbonCredits: number
}

export interface ISiteData {
	count: number
	siteList: ISiteList[]
}

export interface ISiteDetails {
	id: string
	name: string
	address: string
	isArtisan: boolean
	shortCode: string
}
export interface ISiteDropDown {
	siteDetails: ISiteDetails[]
}

export interface ISiteList {
	id: string
	name: string
	address: string
	coordinate: string
	artisianProID: string
	artisianProName: string
	biocharQuantity: number
	mixedQuantity: number
	packedQuantity: number
	shortCode: string
	artisianProNetworkID: string
	artisianProNetworkName: string
	biomassAggregatorID: string
	biomassAggregatorName: string
	createdAt: Date
	biocharDetails: BiocharDetails
	biomassDetails: BiomassDetail[]
	operators?: IOperator[]
}

export interface BiocharDetails {
	biocharQuantity: number
	approvedBiocharQuantity: number
	notAssessedBiocharQuantity: number
	totalBiocharProduced: number
	totalBiocharProducedInTonne: number
}

export interface BiomassDetail {
	siteID: string
	biomassTypeId: string
	biomassTypeName: string
	currentBiomassQuantity: number
}

export interface IBiomassData {
	count: number
	fpu: FPU[]
}

export interface FPU {
	id: string
	name: string
	address: string
	coordinate: string
	createdAt: Date
	fpuArea: FPUArea[] | null
}

export interface FPUArea {
	x: number
	y: number
}

export interface IArtisanProDetails {
	id: string
	name: string
	shortCode: string
	address: string
	managerDetails: ManagerDetail[]
	artisianProNetworkId: string
	artisianProNetworkName: string
	biomassAggregatorId: string
	biomassAggregatorName: string
	isCsinkManager: boolean
	methaneCompensationStrategy: string
	trained: boolean
	csinkManagerId: string
	fpuCount: number
	siteCount: number
	operatorCount: number
	buyerCount?: number
	measuringContainerCount: number
	measuringBagsCount: number
	hasMaxBiocharGenerationLimitReached: boolean
	farmersCount: number
	bighaInHectare: number
	acreInHectare: number
	biomassAggregatorShortName: string
	operatorDetails: IOperator[]
	documents: Nullable<IImage[]>
	methaneCompensateType: Nullable<string>
	compensationType: string
	biomassPreprocessingDetails: TBiomassProcessingDetails
	methaneCompensateStrategies: TMethaneCompensateStrategies[]
	isCsinkManagerSuspended?: boolean
	isBiomassAggregatorSuspended?: boolean
	isArtisanProNetworkSuspended?: boolean
	isArtisanProSuspended?: boolean
	kmlCoordinates: Coordinate[]
	biomassReference: BiomassReference[]
	mixingTypes: ICsinkMixingType[]
	applicationTypes: ICsinkApplicationType[]
	assignedBiomass: AssignBiomass[]
}

export interface AssignBiomass {
	id: string
	cropName: string
}

export interface IOperator {
	id: string
	name: string
	email: null
	phoneNo: string
	countryCode: string
	imageURLs: Nullable<ImageURL>
	language: string
	certificateStatus: string
	certificateUrl: null
	profileImageUrl: IImage
	aadhaarNumber: null
	aadhaarImageUrl: null
	accountType: string
}
export interface IEditOperator {
	id: string
	name: string
	email: string | null
	phoneNo: string
	countryCode: string
	imageURLs: ProfileImageURL[] | null
	language: string
	certificateStatus: string
	certificateUrl: null
	profileImageUrl: Nullable<IImage> | undefined
	aadhaarNumber: string | null
	aadhaarImageUrl: ProfileImageURL | null
	accountType: string
}
export type TArtisanProInfo = Pick<
	IArtisanProDetails,
	| 'id'
	| 'name'
	| 'address'
	| 'siteCount'
	| 'shortCode'
	| 'artisianProNetworkId'
	| 'artisianProNetworkName'
	| 'methaneCompensationStrategy'
	| 'managerDetails'
	| 'biomassAggregatorName'
> & {
	totalBiocharProduced: number
	totalBiocharProducedInTonnes: number
	totalBiomassDropped: number
	totalBiomassDroppedInTonnes: number
	carbonCredits: number
}

export type TArtisanProListResponse = {
	count: number
	page: number
	limit: number
	artisanProBiomassList: TArtisanProInfo[]
	artisanProBiocharList: TArtisanProInfo[]
}

export type TArtisanProsResponse = {
	count: number
	artisanPros: TArtisanProInfo[]
}
export interface IAssignOperator extends IEditOperator {
	siteId: string
}