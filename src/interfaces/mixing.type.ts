import { Nullable } from '@/types'

export type TMixingListResponse = {
	count: number
	result: TMixingType[]
}
export type TMixingType = {
	id: string
	mixType: string
	totalBiocharQuantity: number
	availableBiocharQuantity: number
	totalMixedQuantity: number
	availableMixedQuantity: number
	otherMaterialQuantity: number
	isArtisan: boolean
	images: Nullable<IFileData[]>
	videos: Nullable<IFileData[]>
	artisanProId: Nullable<string>
	artisanProName: Nullable<string>
	artisanProShortName: Nullable<string>
	csinkNetworkId: Nullable<string>
	csinkNetworkName: Nullable<string>
	csinkNetworkShortName: Nullable<string>
	usedBags: Nullable<TBagsInfo[]>
	createdBags: Nullable<TBagsInfo[]>
	createdAt: Nullable<Date>
}

export type TBagsInfo = {
	id: string
	name: string
	images: Nullable<IFileData[]>
	inventoryCount: number
}

export type IFileData = {
	id: Nullable<string>
	url: Nullable<string>
	path: Nullable<string>
	deletionStatus: Nullable<string>
	deletionReason: Nullable<string>
	thumbnailURL?: Nullable<string>
	fileType?: Nullable<string>
	fileName?: Nullable<string>
	createdAt?: Nullable<string>
}

export enum EnumSubNetwork {
	all = 'all',
	cSinkNetwork = 'network',
	artisanPro = 'artisanPro',
}

export type TResponseMixType = {
	types: IMixType[]
}

export type IMixType = {
	id: string
	name: string
	biocharOnly: boolean
	otherMixName: Nullable<string>
	type: string
	fixedRatio: boolean
	density: Nullable<string>
}
