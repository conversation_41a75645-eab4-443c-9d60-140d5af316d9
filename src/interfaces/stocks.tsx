import { Location } from './csink.type'

export interface Process {
	id: string
	shortCode: string
	biocharQuantity: number
	carbonCredits: number
	startDate: string
	endDate: string
	isSiteProcess: boolean
	cropId: string
	cropName: string
	siteId: string | null
	siteName: string | null
	kilnId: string | null
	kilnName: string | null
}

export interface IProjectDetail {
	ProjectId: string
	ProjectName: string
	registryProjectName: string
	StockId: string
	biocharQuantityInLitres: number
	biocharQuantity: number
	totalCarbonCreditsInTonnes: number
	totalSPCFraction: number
	csinkManagerName: string
	biomassName: string
	networks: Network[]
	sites: Site[]
	labReportDocuments: ILabReportDocument[]
}

export interface ILabReportDocument {
	id: string
	url: string
	path: string
	deletionStatus: string
	deletionReason: string
	fileType: string
	createdAt: string
}

export interface IProcessData {
	count: number
	processes: Process[]
	projectDetail: IProjectDetail
	monitoringReport: ILabReportDocument
	annexReport: ILabReportDocument[]
	methaneCompensateStrategyDocuments: ILabReportDocument[]
}

export interface IStockDistribution {
	id: string
	name: string
	biocharQuantity: number
	location: Location
}

export interface ILocationDetails {
	id: string
	name: string
	address?: string
	biocharQuantity: number
	biocharQuantityInTonnes?: number
	isProductionLocation?: boolean
	siteLocation?: Location // Location for production and mixing/packaging sites
	networkLocation?: Location // Currently unused, reserved for future network-level location data
	location?: Location // Location for application sites
}

export interface IStockLocation {
	production: ILocationDetails[]
	mixingPackaging: ILocationDetails[]
	application: ILocationDetails[]
}


export interface IMixing {
	id: string
	name: string
	matrixId: string[]
	biocharQuantity: number
	locations: Location[]
}

export type Network = {
	id: string
	name: string
	shortName: string
	isArtisan: boolean
}

export type Site = {
	id: string
	name: string
	shortName: string
	coordinate: {
		x: number
		y: number
	}
	address: string
	isArtisan: boolean
}
