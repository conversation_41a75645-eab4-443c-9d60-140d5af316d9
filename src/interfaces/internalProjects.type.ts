import { IMediaWithDeleteParams } from './batch.type'
import { IImage } from './image.type'

export interface IInternalProjectList {
	count: number
	limit: number
	page: number
	internalProjectList: IInternalProject[]
}

export interface IInternalProject {
	id: string
	name: string
	startTime: string
	endTime: string
	comments: null | string
	projectId: string
	projectLocation: string
	projectSummary: string

	certificateFile: Omit<IImage, 'fileName'>
	certificationBodyName: string
	certificationDate: string | null
	certificateId: string
	createdAt: string
	certificate: Omit<IImage, 'fileName'>
}

export interface IInternalProjectDetails
	extends Omit<
		IInternalProject,
		'projectId' | 'certificateFile' | 'certificate'
	> {
	id: string
	projectID: string
	registryProjectName: string
	code: string
	name: string
	createdTime: string
	startTime: string
	endTime: string
	comments: string
	certificates: any[]
	csinkManagerId: string
	producerId?: string
	producerNumber?: string
	certificateId: string
	certificationBodyName: string
	certificationDate: string
	csinkManagerName?: string
	additionalFootprint: AdditionalFootprint[]
	totalTransportationDistance: number
	projectImage: ProjectImage
	projectLocation: string
	projectSummary: string
	artisanProDetails: { id: string; name: string; address: string }[]
	csinkNetworkDetails?: { id: string; name: string; address: string }[]
	certificate?:
	| Omit<IImage, 'fileName'>
	| { type?: string; path: string; url: string; fileName?: string }
}

export interface ProjectImage {
	id: string
	url: string
	path: string
	deletionStatus: boolean | null
	deletionReason: string | null
	fileType: string
}

export interface AdditionalFootprint {
	process: string
	carbonEmissionInGram: number
}

export interface INetworkForInternalProjectResponse {
	count: number
	networks: INetworkForInternalProject[]
}

export interface IGlobalProducers {
	operator_name: string
	operator_id: string
	operator_number: string
}

export interface INetworkForInternalProject {
	id: string
	name: string
	shortName: string
	isArtisan: boolean
}

export interface ISinkResponse {
	sinks: ISink[]
	count: number
}
export interface ISinkFile {
	id: string
	identificationCode?: string
	description?: string
	file?: IMediaWithDeleteParams
	createdAt?: Date | null
}

export interface ISink {
	id: string
	stockId: string
	biocharQuantity: number
	stockStatus: string
	sinkStatus: string
	createdAt: string
	blendingMatrixId: string[]
	sinkId: string
	files: ISinkFile[]
}

export interface IProcessForInternalProject {
	count: number
	process: IProcess[]
}

export interface IProcess {
	id: string
	shortName: string
	kilnId: string
	kilnName: string
	siteId: null | string
	siteName: null | string
	artisanProId: null | string
	artisanProName: null | string
	artisanProNetworkId: null | string
	artisanProNetworkName: null | string
	csinkNetworkId: null | string
	csinkNetworkName: null | string
	biomassAggregatorId: string
	biomassAggregatorName: string
	samplingContainerId: null | string
	samplingContainerShortName: null | string
	filledDate: Date
	kilnProcessCreatedAt: Date
	kilnProcessStatus: string
	stockId: null
	isStockCreated: boolean
	stockStatus: null
	cropName: string
	bioCharQuantity: number
	totalSinkQuantity: number
}

export interface IStockListResponse {
	count: number
	stocks: IStock[]
}

export interface IStock {
	stockId: string
	stockCode: string
	bioCharQuantity: number
	carbonCredits: number
	carbonPercentage: number
	createdAt: Date
	productionDate: Date
	kilnProcess: KilnProcess[]
	status: string
	canSinkStock: boolean
	isSinkCreated: boolean
	sinks: SinkDetails[]
	certificates: TCertificateDetailForStock[]
	cropName: string
}

export interface KilnProcess {
	id: string
	shortCode: string
	bioCharQuantity: number
	CarbonPercentage: number
	carbonCredits: number
	startDate: Date
	endDate: Date
	isSiteProcess: boolean
	bioCharSinkQuantity: BioCharSinkQuantity
	siteName: string
}

export interface KilnProcessList {
	process: KilnProcess[]
}

export interface BioCharSinkQuantity {
	isBioCharSink: boolean
	packedDistributedQty: number
	packedDistributedOtherQty: number
	notPackedDistributedQty: number
	notPackedDistributedOtherQty: number
	packedDistributedQtyBioChar: number
	packedDistributedOtherQtyBioChar: number
	notPackedDistributedQtyBioChar: number
	notPackedDistributedOtherQtyBioChar: number
	packedDistributedQtyMix: number
	packedDistributedOtherQtyMix: number
	notPackedDistributedQtyMix: number
	notPackedDistributedOtherQtyMix: number
	solidMixQty: number
}

export interface SinkDetails
	extends Pick<
		ISink,
		'createdAt' | 'blendingMatrixId' | 'sinkId' | 'biocharQuantity'
	> {
	packingId: string
}

export type TCertificateDetails = Pick<
	IInternalProjectDetails,
	| 'certificate'
	| 'certificateId'
	| 'certificationBodyName'
	| 'certificationDate'
>

export type TCertificateDetailForStock = Omit<
	TCertificateDetails,
	'certificate'
> &
	Pick<IInternalProject, 'certificateFile'> & {
		type?: string
	}
export interface IAssignedCertificate {
	id: string
	type: string
	bodyName: string
	issueDate: string
	expiryDate: string | null
	certificateId: string
	file: IMediaWithDeleteParams
}
