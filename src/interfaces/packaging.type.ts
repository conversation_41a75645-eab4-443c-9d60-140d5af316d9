import { Nullable } from '@/types'
import { IFileData, TBagsInfo } from './mixing.type'

export type TPackagingListResponse = {
	count: number
	result: TPackagingType[]
}
export type TPackagingType = {
	id: string
	mixType: string
	totalBiocharQuantity: number
	availableBiocharQuantity: number
	totalMixedQuantity: number
	availableMixedQuantity: number
	otherMaterialQuantity: number
	isArtisan: boolean
	images: Nullable<IFileData[]>
	videos: Nullable<IFileData[]>
	artisanProId: Nullable<string>
	artisanProName: Nullable<string>
	artisanProShortName: Nullable<string>
	csinkNetworkId: Nullable<string>
	csinkNetworkName: Nullable<string>
	csinkNetworkShortName: Nullable<string>
	usedBags: Nullable<TBagsInfo[]>
	createdBags: Nullable<TBagsInfo[]>
	createdAt: Nullable<Date>
}
