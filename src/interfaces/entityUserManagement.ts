import { INetworkManager } from '@/components/AddBypass'
import {
	ICsinkApplicationType,
	ICsinkMixingType,
	TBiomassProcessingDetails,
	TMethaneCompensateStrategies,
} from './csink.type'
import { BiomassReference } from './Entity'
import { Coordinate } from './BiomassAggregator.type'

export interface BiomassReferenceResponse {
	id: string
	address: string
	name: string
	managerDetails: CSNetworkManager[]
	mixingTypes: ICsinkMixingType[]
	applicationTypes: ICsinkApplicationType[]
	biomassReference: BiomassReference[]
	assignedBiomass: AssignedBiomass[]
	methaneCompensateStrategies: TMethaneCompensateStrategies[]
	biomassPreprocessingDetails: TBiomassProcessingDetails
	kmlCoordinates: Coordinate[]
}

export interface AssignedBiomass {
	id: string
	cropName: string
}

export interface CSNetworkManager extends INetworkManager {
	isCsinkManager: boolean
}
