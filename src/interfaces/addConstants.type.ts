import { Nullable } from '@/types'
import { UseQueryResult } from '@tanstack/react-query'
import {
	FormState,
	UseFormClearErrors,
	UseFormHandleSubmit,
	UseFormRegister,
	UseFormReset,
	UseFormSetValue,
	UseFormWatch,
} from 'react-hook-form'
import { SetURLSearchParams } from 'react-router-dom'

export type TAddConstant = {
	csinkManagerId: string
	// Moisture
	csinkNetworkMoistureRequired?: Nullable<boolean>
	csinkNetworkMaximumMoisture?: Nullable<number>
	artisanProMaximumMoisture?: Nullable<number>

	// Temperature
	csinkNetworkTemperatureRequired?: Nullable<boolean>
	csinkNetworkMinimumTemperatureImages?: Nullable<number>
	csinkNetworkMinimumTemperature?: Nullable<number>
	artisanProMinimumTemperatureImages?: Nullable<number>
	artisanProMinimumTemperature?: Nullable<number>

	// Quenching Image
	csinkNetworkSeparateQuenchingImage?: Nullable<boolean>
	artisanProSeparateQuenchingImage?: Nullable<boolean>

	// Firing Video, Chimney Image, and others
	csinkNetworkFiringVideosRequired?: Nullable<boolean>
	csinkNetworkSeparateChimneyImage?: Nullable<boolean>
	artisanProSeparateChimneyImage?: Nullable<boolean>
	artisanProMaximumVideoLengthInSeconds?: Nullable<number>
	csinkNetworkMaximumVideoLengthInSeconds?: Nullable<number>

	csinkNetworkMinimumFiringVideos?: Nullable<number>
	csinkNetworkMinimumFiringImages?: Nullable<number>
	artisanProMinimumFiringImages?: Nullable<number>
	artisanProMinimumFiringVideos?: Nullable<number>

	csinkNetworkPreQuenchingImageRequired?: Nullable<boolean>
	artisanProPreQuenchingImageRequired?: Nullable<boolean>

	// Credits
	creditsToRegister?: Nullable<number>

	//Email booleans
	isBatchRejectionEmailEnabled?: Nullable<boolean>
	isBatchCommentEmailEnabled?: Nullable<boolean>
}

export type UpdatedAddConstant = Omit<TAddConstant, 'csinkManagerId'>

export interface IPropsConstantLeftSection {
	handleSubmit: UseFormHandleSubmit<IAllConstants, undefined>
	setValue: UseFormSetValue<IAllConstants>
	formState: FormState<IAllConstants>
	reset: UseFormReset<IAllConstants>
	register: UseFormRegister<IAllConstants>
	onSubmit: (formData: IAllConstants) => Promise<void>
	fetchCsinkManagers: UseQueryResult<
		{
			value: string
			label: string
		}[],
		Error
	>
	isCsinkManagerLogin: boolean
	paramsCsinkManagerId: string
	CsinkManagerName: Nullable<string>
	clearErrors: UseFormClearErrors<IAllConstants>
	setSearchParams: SetURLSearchParams
	watch: UseFormWatch<IAllConstants>
}

export interface ICarbonConstant {
	id: string
	ccConstantNumerator: number
	ccConstantDenominator: number
	pacFraction: number
	spcFraction: number
}
export interface IAllConstants extends ICarbonConstant, TAddConstant {}

export interface ICarbonConstantsResponse {
	currentConstant: ICarbonConstant
	earlierConstant: ICarbonConstant[]
}
