export interface IUsersResponse {
	count: number
	users: User[]
}

export interface IUnassignedUsersResponse {
	unassignedUsers: UnassignedUser[]
}

export interface UnassignedUser {
	id: string
	name: string
	email: string
	countryCode: string
	phoneNumber: string
	csinkManagerId: string
	profileImage: ProfileImageURL
	accountType: string
}

export interface CertificateDetails {
	deletionReason?: string
	deletionStatus?: string
	fileType?: string
	id?: string
	path?: string
	url?: string
}
export interface User {
	id: string
	name: string
	email: string
	countryCode: string
	number: string
	accountType: string
	profileImageUrl: ProfileImageURL
	csinkManagerId: string
	csinkManagerName: string | undefined
	csinkManagerShortName: string
	biomassAggregatorId: string
	biomassAggregatorName: string
	biomassAggregatorShortName: string
	artisanProNetworkId: string
	artisanProNetworkName: string
	artisanProNetworkShortName: string
	artisanProId: string
	artisanProName: string | undefined
	artisanProShortName: string
	csinkNetworkId: string
	csinkNetworkName: string | undefined
	csinkNetworkShortName: null
	trainingImageUrls: ProfileImageURL[] | null
	aadhaarImageUrl: ProfileImageURL | null
	aadhaarNumber: string | null
	artisanPros: IArtisanPro[]
	csinkNetworks:ICSinkNetwork[]
	pendingUploadsCount?: number
	pendingActionCount?: number
	certificateDetails?: CertificateDetails | null
	gender?: string
	address?: string
	cropId?: string
	landmark?: string
}

export interface ProfileImageURL {
	id: string
	url: string
	path: string
	fileName?: string
}

export interface IFarmForUser {
	id: string
	farmLocation: FarmLocation
	landmark: string
	fieldSize: number
	fieldSizeUnit: string
	farmCrops: FarmCrop[]
	farmImages: ProfileImageURL[]
}

export interface FarmCrop {
	id: string
	cropName: string
	createdAt: string
	cropStage: string
}

export interface FarmLocation {
	x: number
	y: number
}

export interface IArtisanPro {
	id: string
	name: string
	shortName: string
}
export interface ICSinkNetwork{
	id: string
	name: string
	shortName: string
}
export enum AddOperatorTypeEnum {
	kiln = 'kiln',
	csink = 'csinkOperator',
	artisanPro = 'artisanPro',
}
export type TModalTypeForUserManagement =
	| 'view'
	| 'add'
	| 'edit'
	| 'promote'
	| 'demote'
