export interface IStage {
	stage: string
	count: string
}

export interface IFarmer {
	id: string
	name: string
	address: string
	phoneNo: string
	countryCode: string
	email: string | null
	isOperator: boolean
	farmsCount: number
	cSinkNetworkId: string | null
	cSinkNetworkName: string | null
	cSinkNetworkShortCode: string | null
	artisanProId: string | null
	artisanProName: string | null
	artisanProShortCode: string | null
	kilnNames: string[] | null
	flagCount: number
	ProfileImagePath: string | null
	profileImageURL: string
	BAName: string | null
	totalBiocharProduced: number | null
	trainingImageUrls: string[] | null
	certificateUrl: string | null
	certificateStatus: string
	profileImageUrl: string | null
	aadhaarNumber: string | null
	aadhaarImageUrl: string | null
	createdAt: string
	totalFarmArea: number
	biomassQty: number
}

export interface IFarmerDetail {
	id: string
	name: string
	age: number | null
	gender: string | null
	address: string
	phoneNumber: string
	email: string | null
	countryCode: string
	profileImageUrl: string | null
	aadhaarNumber: string | null
	aadhaarImageUrl: string | null
	RegistrationStatus: string
	accountCreatedDate: string
	accountHolderName: string | null
	ifscCode: string | null
	bankAccountNumber: string | null
	farmerPreferredCrops: string[] | null
	biomassAggregatorId: string
	biomassAggregator: string
	biomassAggregatorShortName: string
	isCsinkManager: boolean
	cSinkNetworkId: string
	CSinkNetwork: string
	CSinkNetworkShortName: string
	trainingImageURLs: string[] | null
	stages: IStage[]
	declarationDocUrl: string | null
	language: string
	certificateUrl: string | null
	certificateStatus: string
	isKilnOperator: boolean
	artisanProId: string
	artisanPro: string
	artisanProShortName: string
}

export interface ITotalFarmersAndFarms {
	totalFarmers: number
	totalFarms: number
}

export interface IFarmerData {
	count: number
	limit: number
	page: number
	totalFarmersAndFarms: ITotalFarmersAndFarms
	farmers: IFarmer[]
}

export interface getFarmerData {
	count: number
	limit: number
	page: number
	farmers: IFarmer[]
}

export interface ILocation {
	x: number
	y: number
}

export interface IFarm {
	id: string
	landmark: string
	fieldSize: number
	fieldSizeUnit: string
	location: ILocation
	name?: string
	farmArea?: ILocation[]
	area: string
	farmerId: string
	farmerName: string
	size?: number
	farmLocation?: string

	sizeUnit?: string
	farmerNumber: string
	countryCode: string
	farmerProfileImage: string | null
	cSinkNetworkId: string | null
	cSinkNetworkName: string | null
	artisanProId: string | null
	artisanProName: string | null
	biomassAggregatorId: string
	biomassAggregatorName: string
	biomassAggregatorShortName: string
	createdAt: string
	farmerStatus: string
	cropStage: string
	cropName: string
}

export interface IFarmData {
	count: number
	limit: number
	page: number
	totalFarmersAndFarms: ITotalFarmersAndFarms
	farms: IFarm[]
}
