import { Details } from './artisanProNetworkDetails.type'
import { ILocation } from './farmFarmer.type'
import { IImage } from './image.type'

interface Operator {
	id: string
	name: string
	email?: string | null
	phoneNo: string
	countryCode: string
	imageURLs: IImage[]
	language: string
	certificateStatus: string
	certificateUrl?: string | null
	profileImageUrl: IImage
	aadhaarNumber?: string | null
	aadhaarImageUrl?: string | null
	accountType: string
}

interface Vehicle {
	id: string
	name: string
	number?: string | null
	type: string
	fuelType: string
	categoryId?: string | null
	categoryName?: string | null
	imageURLs: IImage[]
}

interface MeasuringContainer {
	id: string
	shortName: string
	diameter?: number | null
	diameterUnit: string
	height: number
	heightUnit: string
	imageURL: IImage
	imageURLs: IImage[]
	createdAt?: string
	volume: number
	length: number
	lengthUnit: string
	breadth: number
	breadthUnit: string
	shape: string
	name: string
	inUse: boolean
	isPartialFilled: boolean
	measuringContainerHeight?: number | null
	upperSurfaceDiameter?: number | null
	upperSurfaceDiameterUnit: string
	lowerSurfaceDiameter?: number | null
	lowerSurfaceDiameterUnit: string
}

export interface IFpu {
	address: string
	coordinate: string
	kmlCoordinates?:ILocation[]
	createdAt: string
	fpuArea: ILocation[] | null
	id: string
	name: string
}

export interface IVehicleCategory {
	ID: string
	Name: string
	CO2Emission: null
	CreatedAt: Date
}
export interface IKiln {
	farmerId:string
	id: string
	name: string
	ShortName?: string
	address?: string
	coordinate?: string
	networkId?: string | null
	networkName?: string | null
	siteId?: string
	siteName?: string
	siteShortName?: string
	apId?: string
	apName?: string
	apShortName?: string
	csManagerName?: string | null
	artisianProManagerName?: string | null
	kilnType: string
	baID?: string
	baName?: string
	baShortName?: string
	isCsinkManager?: boolean
	createdAt?: string
	kilnShape?: string
	upperSurfaceDiameter?: number
	upperSurfaceDiameterUnit?: string
	lowerSurfaceDiameter?: number
	lowerSurfaceDiameterUnit?: string
	lowerSide?: number | null
	lowerSideUnit?: string | null
	upperSide?: number | null
	upperSideUnit?: string | null
	volume?: number | null
	volumeUnit?: string | null
	depth?: number
	depthUnit?: string
	kilnOperators?: string | null
	imageURLs?: IImage[]
	biocharQuantity?: number
	distributedQuantity?: number
	packedQuantity?: number
	notAccessedQuantity?: number | null
	approvedQuantity?: number | null
	diameter?: number | null
	diameterUnit?: string | null
	carbonCredits?: number
	cropCarbonCredits?: number | null
	cropBiochar?: number | null
	longBase?: number | null
	shortBase?: number | null
	length?: number | null
	lowerBase?: number | null
	upperBase?: number | null
	frustumLength?: number | null
}

export interface IGlobalKiln {
	id?: string
	name?: string
	address?: string
	coordinate?: string
	networkId?: string | null
	kilnType?: string
	kilnShape?: string
	upperSurfaceDiameter?: number
	upperSurfaceDiameterUnit?: string
	lowerSurfaceDiameter?: number
	lowerSurfaceDiameterUnit?: string
	lowerSide?: number | null
	lowerSideUnit?: string | null
	upperSide?: number | null
	upperSideUnit?: string | null
	volume?: number | null
	volumeUnit?: string | null
	depth?: number
	depthUnit?: string
	imageURLs?: IImage[]
	diameter?: number | null
	diameterUnit?: string | null
	longBase?: number | null
	shortBase?: number | null
	length?: number | null
	lowerBase?: number | null
	upperBase?: number | null
	frustumLength?: number | null
	sno?: number | null
	kilnShapeName?: string | null
}

export interface GlobalKilnResponse {
	count: number
	kilns: IKiln[]
	limit: number
	page: number
}

interface SamplingContainer {
	id: string
	diameter?: number | null
	height: number
	shortCode: string
	imageId?: string | null
	imageURLs: IImage[]
	imagePath?: string | null
	imageURL: string
	filled: boolean
	createdAt: string
	name: string
	length?: number | null
	breadth?: number | null
	shape: string
	volume: number
	inUse: boolean
	volumeFilled: number
	upperSurfaceDiameter: number
	upperSurfaceDiameterUnit: string
	lowerSurfaceDiameter: number
	lowerSurfaceDiameterUnit: string
}

export interface KilnEntityDetails {
	vehicles: Vehicle[]
	kilns: IKiln[]
	fpu: IFpu[]
	measuringContainers: MeasuringContainer[]
	samplingContainers: SamplingContainer[]
}

export interface ISite {
	id: string
	name: string
	address: string
	coordinate: string
	isSuspended:boolean
	biocharQuantity: number
	kmlCoordinates?: ILocation[]
	mixedQuantity: number
	packedQuantity: number
	createdAt: string
	shortCode: string
	networkId: string
	networkName: string
	artisianProName: string
	baName: string
	isCsinkManager: boolean
	approvedBiocharQuantity: number
	notAssessedBiocharQuantity: number
	kilnCount: number
	samplingContainerCount: number
	operators: Operator[]
	details: Details
	csinkManagerId: string 
}
