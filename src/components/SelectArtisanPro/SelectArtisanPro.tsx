import { authAxios } from '@/contexts'
import { User } from '@/interfaces'
import { TModal } from '@/types'
import { Cancel } from '@mui/icons-material'
import {
	Button,
	Dialog,
	DialogActions,
	DialogContent,
	DialogTitle,
	IconButton,
	MenuItem,
	TextField,
	Typography,
} from '@mui/material'
import { UseMutateFunction, useMutation } from '@tanstack/react-query'
import { FC, useMemo, useState } from 'react'
import { toast } from 'react-toastify'
import { Confirmation } from '../Confirmation'

export const SelectArtisanPro: FC<
	TModal & {
		artisanPros: User['artisanPros']
		artisanProNetworkId?: string
		cb?: () => void
		managerId?: string
		modalType: 'removeArtisanProAdmin' | 'demoteArtisanProAdminToOperator'
		userDetails?: any
	}
> = ({
	open,
	onClose,
	artisanPros,
	artisanProNetworkId,
	cb,
	managerId,
	modalType,
	userDetails,
}) => {
	const [selectArtisanPro, setSelectArtisanPro] = useState<string>('')
	const [showConfirmationDialog, setShowConfirmationDialog] =
		useState<boolean>(false)

	const removeArtisanProAdmin = useMutation({
		mutationKey: [
			'removeArtisanProAdmin',
			selectArtisanPro,
			managerId,
			artisanProNetworkId,
		],
		mutationFn: async () => {
			const { data } = await authAxios.delete(
				`/artisan-pro-network/${artisanProNetworkId}/artisian-pro/${selectArtisanPro}/artisian-pro-manager/${managerId}`
			)
			return data
		},
		onError: (error: any) => {
			toast(error?.response?.data?.messageToUser)
		},
		onSuccess: ({ data }) => {
			toast(data?.message)
			cb?.()
		},
	})

	const demoteArtisanProAdminToOperator = useMutation({
		mutationKey: [
			'demoteArtisanProAdminToOperator',
			selectArtisanPro,
			managerId,
			artisanProNetworkId,
		],
		mutationFn: async () => {
			const values = userDetails
			const payload = {
				profileImageId: values.profileImage === '' ? null : values.profileImage?.id,
				aadhaarNumber:
					values.aadhaarNumber === '' ? null : values.aadhaarNumber,
				aadhaarImageID: values.aadhaarImage === '' ? null : values.aadhaarImage,
				trainingImages: values.trainingImages?.map(
					(trainingId: any) => trainingId?.id
				),
			}
			const { data } = await authAxios.put(
				`/artisan-pro-network/${artisanProNetworkId}/artisian-pro/${selectArtisanPro}/artisian-pro-manager/${managerId}/demote-to-operator`,
				payload
			)
			return data
		},
		onError: (error: any) => {
			toast(error?.response?.data?.messageToUser)
		},
		onSuccess: ({ data }) => {
			toast(data?.message)
			cb?.()
		},
	})

	const confirmationData: {
		[key: string]: {
			title: string
			onYesClick: UseMutateFunction<any, any, void, unknown>
		}
	} = useMemo(
		() => ({
			removeArtisanProAdmin: {
				title: 'Are you sure you want to remove this artisan pro admin ?',
				onYesClick: removeArtisanProAdmin.mutate,
			},
			demoteArtisanProAdminToOperator: {
				title:
					'Are you sure you want to demote this artisan pro admin to operator ?',
				onYesClick: demoteArtisanProAdminToOperator.mutate,
			},
		}),
		[demoteArtisanProAdminToOperator.mutate, removeArtisanProAdmin.mutate]
	)

	return (
		<>
			{showConfirmationDialog ? (
				<Confirmation
					confirmationText={
						<Typography>{confirmationData[modalType].title}</Typography>
					}
					open={showConfirmationDialog}
					handleClose={() => setShowConfirmationDialog(false)}
					handleNoClick={() => setShowConfirmationDialog(false)}
					handleYesClick={confirmationData[modalType].onYesClick}
				/>
			) : null}
			<Dialog open={open} onClose={onClose} fullWidth maxWidth='sm'>
				<IconButton
					sx={{ position: 'absolute', right: 5, top: 0 }}
					onClick={onClose}>
					<Cancel />
				</IconButton>
				<DialogTitle>Select Artisan Pro</DialogTitle>
				<DialogContent>
					<TextField
						fullWidth
						select
						sx={{ mt: 2 }}
						label='Artisan Pros'
						value={selectArtisanPro}
						onChange={(e) => setSelectArtisanPro(e.target.value)}>
						{artisanPros?.length > 0 ? (
							artisanPros?.map((artisan) => (
								<MenuItem key={artisan?.id} value={artisan?.id}>
									{artisan?.name} ({artisan?.shortName})
								</MenuItem>
							))
						) : (
							<MenuItem>No Data</MenuItem>
						)}
					</TextField>
				</DialogContent>
				<DialogActions sx={{ display: 'flex', justifyContent: 'center' }}>
					<Button
						variant='contained'
						onClick={() => setShowConfirmationDialog(true)}>
						Continue
					</Button>
				</DialogActions>
			</Dialog>
		</>
	)
}
