// import {
// 	Button,
// 	FormControl,
// 	IconButton,
// 	Stack,
// 	TextField,
// 	Typography,
// 	styled,
// } from '@mui/material'
// import { Close } from '@mui/icons-material'
// import { IArtisanProNetworkList } from '@/interfaces'
// import { theme } from '@/lib/theme/theme'
// import { useForm } from 'react-hook-form'
// export const EditArtisanPro = ({
// 	artisanProDetails,
// 	handleClose,
// }: {
// 	artisanProDetails?: IArtisanProNetworkList
// 	handleClose: () => void
// }) => {
// 	const initialValues = {
// 		address: artisanProDetails?.address ?? '',
// 		name: artisanProDetails?.name ?? '',
// 		shortCode: artisanProDetails?.shortCode ?? '',
// 		managerDetails: artisanProDetails?.managerDetails?.[0],
// 		methaneCompensationStrategy: artisanProDetails?.methaneCompensationStrategy,
// 	}

// 	const {
// 		register,
// 		// handleSubmit,
// 		// formState: { errors },
// 		// setValue,
// 		// watch,
// 	} = useForm({
// 		defaultValues: initialValues,
// 	})
// 	return (
// 		<StyleContainer>
// 			<Stack className='header'>
// 				<Stack
// 					direction='row'
// 					spacing={1}
// 					alignItems='center'
// 					width='100%'
// 					justifyContent='space-between'>
// 					<Typography variant='h4'>Edit Artisan Pro</Typography>
// 					<IconButton
// 						// sx={{ position: 'absolute', right: 5, top: 5 }}
// 						onClick={handleClose}>
// 						<Close />
// 					</IconButton>
// 				</Stack>
// 			</Stack>
// 			<Stack className='container' component='form'>
// 				<Stack gap={theme.spacing(2.5)}>
// 					<FormControl fullWidth className='formcontrol'>
// 						<Typography className='label' variant='subtitle1'>
// 							Name
// 						</Typography>
// 						<TextField
// 							id='name'
// 							placeholder='Artisan Pro Name'
// 							variant='outlined'
// 							{...register('name')}
// 						/>
// 					</FormControl>
// 					<FormControl fullWidth className='formcontrol'>
// 						<Typography className='label' variant='subtitle1'>
// 							Artisan Pro ID
// 						</Typography>
// 						<TextField
// 							id='shortCode'
// 							placeholder='Artisan Pro ID'
// 							variant='outlined'
// 							{...register('shortCode')}
// 						/>
// 					</FormControl>
// 					<FormControl fullWidth className='formcontrol'>
// 						<Typography className='label' variant='subtitle1'>
// 							Location
// 						</Typography>
// 						<TextField
// 							id='location'
// 							placeholder='Location'
// 							variant='outlined'
// 							{...register('address')}
// 						/>
// 					</FormControl>
// 					<FormControl fullWidth className='formcontrol'>
// 						<Typography className='label' variant='subtitle1'>
// 							Methane Compensation Strategy
// 						</Typography>
// 						<TextField
// 							id='methane'
// 							placeholder='Enter Methane Compensation Strategy'
// 							variant='outlined'
// 							{...register('methaneCompensationStrategy')}
// 						/>
// 					</FormControl>
// 					<FormControl fullWidth className='formcontrol'>
// 						<Typography className='label' variant='subtitle1'>
// 							Admin Name
// 						</Typography>
// 						<TextField
// 							id='adminName'
// 							placeholder='Admin Name'
// 							variant='outlined'
// 							{...register('managerDetails.name')}
// 						/>
// 					</FormControl>
// 					<FormControl fullWidth className='formcontrol'>
// 						<Typography className='label' variant='subtitle1'>
// 							Admin Email
// 						</Typography>
// 						<TextField
// 							id='adminEmail'
// 							placeholder='Admin Email'
// 							variant='outlined'
// 							{...register('managerDetails.email')}
// 						/>
// 					</FormControl>
// 					<FormControl fullWidth className='formcontrol'>
// 						<Typography className='label' variant='subtitle1'>
// 							Admin Phone Number
// 						</Typography>
// 						<TextField
// 							id='name'
// 							placeholder='Admin Phone Number'
// 							variant='outlined'
// 							{...register('managerDetails.number')}
// 						/>
// 					</FormControl>
// 				</Stack>
// 				<Stack
// 					direction='row'
// 					justifyContent='space-between'
// 					className='buttonContainer'>
// 					<Button sx={{ border: ` 1px solid ${theme.palette.primary.main}` }}>
// 						Cancel
// 					</Button>{' '}
// 					<Button variant='contained'>Save</Button>
// 				</Stack>
// 			</Stack>
// 		</StyleContainer>
// 	)
// }

// const StyleContainer = styled(Stack)(({ theme }) => ({
// 	gap: theme.spacing(2),
// 	'.header': {
// 		flexDirection: 'row',
// 		justifyContent: 'space-between',
// 		alignItems: 'center',
// 		padding: theme.spacing(4.5, 2),
// 		borderBottom: `${theme.spacing(0.125)} solid ${
// 			theme.palette.neutral['100']
// 		}`,
// 	},
// 	'.container': {
// 		padding: theme.spacing(2, 4),
// 		// overflowY: 'scroll',
// 		gap: theme.spacing(5),
// 		'.formcontrol': {
// 			gap: theme.spacing(0.6),

// 			'.label': {
// 				color: theme.palette.neutral[500],
// 			},
// 		},
// 		'.buttonContainer button': {
// 			width: theme.spacing(30),
// 			height: theme.spacing(4.5),
// 			padding: theme.spacing(1, 2.5),
// 		},
// 	},
// }))
