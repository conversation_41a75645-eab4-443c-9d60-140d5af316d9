import { Box, Stack, styled, Typography } from '@mui/material'
import { FC } from 'react'

type TProps = {
	title?: string
	isTitle?: boolean
	size?: 'small' | 'normal'
	imageUrl?: string
	noDataLabel?: string
	noUsers?: boolean
	noDataMessage?: string | React.ReactNode
}

export const NoData: FC<TProps> = ({
	title,
	isTitle = true,
	size = 'normal',
	imageUrl,
	noDataLabel,
	noUsers,
	noDataMessage,
}) => {
	const finalImageUrl = noUsers
		? '/images/no-users.svg'
		: imageUrl || '/images/noData.svg'
	const showTitle = !noUsers && isTitle
	return (
		<StyledContainer>
			<Stack justifyContent='center' alignItems='center'>
				{showTitle && (
					<Typography className='title'>{title || 'No Data Found!'}</Typography>
				)}
				<Box display='flex' alignItems='center'>
					<Box
						component='img'
						src={finalImageUrl}
						className={`no_data_image ${size === 'small' ? 'small' : ''}`}
					/>
					{noDataLabel && <Typography ml={3}>{noDataLabel}</Typography>}
				</Box>
				{noUsers && <Typography textAlign='center'>{noDataMessage}</Typography>}
			</Stack>
		</StyledContainer>
	)
}

const StyledContainer = styled(Stack)(({ theme }) => ({
	justifyContent: 'center',
	alignItems: 'center',
	width: '100%',
	height: '100%',
	'.title': {
		color: theme.palette.neutral[300],
	},
	'.small': {
		height: 200,
	},
}))
