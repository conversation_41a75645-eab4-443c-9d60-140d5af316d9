import { theme } from '@/lib/theme/theme'
import { Cancel } from '@mui/icons-material'
import { LoadingButton } from '@mui/lab'
import {
	CircularProgress,
	Dialog,
	DialogActions,
	DialogContent,
	DialogTitle,
	IconButton,
} from '@mui/material'
import { ReactNode } from 'react'

interface IProps {
	open: boolean
	handleClose: () => void
	confirmationText: string | ReactNode
	confirmationSuccessBtnText?: string | ReactNode
	confirmationNotSuccessBtnText?: string | ReactNode
	successBtnVariant?: 'outlined' | 'text' | 'contained' | undefined
	handleYesClick: () => void
	handleNoClick: () => void
	title?: string | ReactNode
	isLoading?: boolean
}

export function Confirmation({
	open,
	handleClose,
	confirmationText,
	handleYesClick,
	handleNoClick,
	title,
	confirmationSuccessBtnText = 'Yes',
	confirmationNotSuccessBtnText = 'No',
	successBtnVariant = 'outlined',
	isLoading = false,
}: IProps) {
	return (
		<Dialog
			open={open}
			onClose={handleClose}
			aria-labelledby='alert-dialog-title'
			aria-describedby='alert-dialog-description'
			fullWidth
			maxWidth='xs'
			PaperProps={{
				style: {
					alignItems: 'center',
					padding: theme.spacing(1.2),
				},
			}}>
			<IconButton
				sx={{ position: 'absolute', right: 5, top: 0 }}
				onClick={handleClose}>
				<Cancel />
			</IconButton>
			{title ? <DialogTitle textAlign='center'>{title}</DialogTitle> : null}
			<DialogContent>{confirmationText}</DialogContent>
			<DialogActions>
				<LoadingButton variant={successBtnVariant} onClick={handleYesClick}>
					{isLoading ? (
						<CircularProgress size={20} />
					) : (
						confirmationSuccessBtnText
					)}
				</LoadingButton>
				<LoadingButton onClick={handleNoClick}>
					{confirmationNotSuccessBtnText}
				</LoadingButton>
			</DialogActions>
		</Dialog>
	)
}
