import { Close } from '@mui/icons-material'
import { IconButton, Stack, styled, Typography } from '@mui/material'
import { FC } from 'react'
import { TwoColumnLayout } from '../TwoColumnLayout'
import { TModal } from '@/types'
import { IFootprint } from '@/interfaces'

interface IProps extends TModal {
	footprint?: IFootprint[]
}

export const FootprintDetails: FC<IProps> = ({ footprint, onClose }) => {
	return (
		<>
			<StyleContainer>
				<Stack className='header'>
					<Stack
						direction='row'
						spacing={1}
						alignItems='center'
						width='100%'
						justifyContent='space-between'>
						<Typography variant='body2'>Footprints</Typography>
						<IconButton onClick={onClose}>
							<Close />
						</IconButton>
					</Stack>
				</Stack>
				<Stack className='container'>
					<Typography variant='subtitle1' fontWeight={500}>
						Transportation Footprints
					</Typography>

					{footprint?.map((item) => (
						<Stack className='footprint-content'>
							<TwoColumnLayout
								gridBreakpoints={[6, 6]}
								left={
									<TagComponent label='Operated by' value={item?.operatedBy} />
								}
								right={
									<TagComponent label='Fuel Type' value={item?.fuelType} />
								}
							/>
							<TwoColumnLayout
								gridBreakpoints={[6, 6]}
								left={
									<TagComponent
										label='Vehicle Machine Type'
										value={item?.process}
									/>
								}
								right={<TagComponent label='Distance' value={item?.distance} />}
							/>
							<TwoColumnLayout
								gridBreakpoints={[6, 6]}
								left={
									<TagComponent
										label='Carbon emission'
										value={`${
											Number(item?.carbonEmissionInGram || 0) / 1000
										} kgCO2e`}
									/>
								}
								right={<></>}
							/>
						</Stack>
					))}
				</Stack>
			</StyleContainer>
		</>
	)
}

const TagComponent: FC<{
	label: string
	value: string | number
}> = ({ label, value }) => {
	return (
		<Stack className='tag-component'>
			<Typography variant='caption'>{label}:</Typography>
			<Typography variant='subtitle1' textTransform='capitalize'>
				{value}
			</Typography>
		</Stack>
	)
}

const StyleContainer = styled(Stack)(({ theme }) => ({
	gap: theme.spacing(2),
	'.header': {
		flexDirection: 'column',
		padding: theme.spacing(2, 2),
		borderBottom: `${theme.spacing(0.125)} solid ${
			theme.palette.neutral['100']
		}`,
	},
	'.container': {
		padding: theme.spacing(1.5, 3),
		gap: theme.spacing(2.5),
		'.footprint-content': {
			flexDirection: 'column',
			gap: theme.spacing(1.4),
			borderBottom: `${theme.spacing(0.125)} solid ${
				theme.palette.neutral['100']
			}`,
		},
		'.tag-component': {
			alignItems: 'center',
			gap: theme.spacing(0.5),
			padding: theme.spacing(1),
			flexDirection: 'row',
		},
	},
}))
