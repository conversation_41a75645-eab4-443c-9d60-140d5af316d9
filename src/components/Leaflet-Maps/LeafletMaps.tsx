import React, { useEffect, useState } from 'react'
import {
	<PERSON><PERSON><PERSON><PERSON>,
	<PERSON><PERSON><PERSON><PERSON><PERSON>,
	<PERSON><PERSON>,
	<PERSON><PERSON>,
	ZoomControl,
	useMap,
} from 'react-leaflet'
import L, { LatLngBounds } from 'leaflet'
import 'leaflet/dist/leaflet.css'
import MarkerIcon from '@/assets/icons/mapMarker.svg'

interface Position {
	lat: number
	lng: number
	zoom: number
}

interface IMarker {
	id: string
	name: string
	desc?: string
	position: {
		lat: number
		lng: number
	}
}
interface IProps {
	markers?: IMarker[]
}

const SetBoundsComponent: React.FC<{ markers: Array<any> }> = ({ markers }) => {
	const map = useMap()

	useEffect(() => {
		if (markers?.length) {
			const bounds = new LatLngBounds([])

			markers.forEach(({ position }) => {
				bounds.extend(position)
			})

			map.fitBounds(bounds, {
				padding: [50, 50], // [top/bottom, left/right] padding in pixels
				maxZoom: 7, // how far it will zoom in
			})
		}
	}, [markers, map])

	return null
}

const LeafMapComponent: React.FC<IProps> = ({ markers }) => {
	// Example initial position
	const [position] = useState<Position>({
		lat: 0,
		lng: 0,
		zoom: 5,
	})

	// Custom map settings
	const mapSettings = {
		minZoom: 0,
		maxZoom: 20,
		maxBounds: [
			[-90, -180] as L.LatLngTuple, // Southwest coordinates
			[90, 180] as L.LatLngTuple, // Northeast coordinates
		],
		maxBoundsViscosity: 1.0,
	}
	const createCustomIcon = (label: string) => {
		return L.divIcon({
			className: 'custom-div-icon',
			html: `
                <div style="
                   
                    padding: 5px;
                    font-size: 12px;
                    white-space: nowrap;
                    position: relative;
                    bottom: 25px;
                    left: -50%;
                    text-align: center;
                ">
                <div style="background-color: white;  padding: 5px;
                    font-size: 12px;
                    white-space: nowrap;
                    min-width:fit-content;
					border-radius: 5px;
                    ">

                ${label}</div>
                    <img src=${MarkerIcon} style="width: 30px; height: 30px; display: block; margin: 0 auto 5px auto;"/>
                </div>
            `,
			iconSize: [40, 40],
			iconAnchor: [20, 40],
		})
	}

	return (
		<MapContainer
			center={[position.lat, position.lng]}
			zoom={position.zoom}
			style={{
				height: '500px',
				width: '100%',
			}}
			{...mapSettings}
			zoomControl={false}>
			{/* Add the bounds setting component */}
			<SetBoundsComponent markers={markers ?? []} />

			{/* Custom position for zoom control */}
			<ZoomControl position='bottomright' />

			{/* Base map layer */}
			<TileLayer
				// attribution='&copy; <a href="https://www.openstreetmap.org/copyright">OpenStreetMap</a> contributors'
				url='https://{s}.tile.openstreetmap.org/{z}/{x}/{y}.png'
				noWrap={true}
			/>

			{/*marker */}
			{markers?.map((x) => (
				<Marker
					position={[x?.position.lat, x?.position.lng]}
					icon={createCustomIcon(x.name)}
					key={x.id + 'map'}>
					<Popup>
						{x?.name} - {x?.desc}
					</Popup>
				</Marker>
			))}
		</MapContainer>
	)
}

export default LeafMapComponent
