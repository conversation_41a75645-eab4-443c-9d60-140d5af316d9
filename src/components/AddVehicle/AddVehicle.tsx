import { yupResolver } from '@hookform/resolvers/yup'
import {
	FormControl,
	FormHelperText,
	IconButton,
	MenuItem,
	Stack,
	styled,
	Typography,
} from '@mui/material'
import { useCallback, useEffect, useMemo, useState } from 'react'
import { useForm } from 'react-hook-form'
import { useMutation, useQuery, useQueryClient } from '@tanstack/react-query'
import { authAxios } from '@/contexts'
import { toast } from 'react-toastify'
import { LoadingButton } from '@mui/lab'
import { useParams, useSearchParams } from 'react-router-dom'
import { Close } from '@mui/icons-material'
import { CustomFileUploader } from '../CustomFileUploader'
import { addVehicleSchema, TAddVehicle } from './schema'
import { INetwork, IVehicle, IVehicleCategory } from '@/interfaces'
import { AxiosError } from 'axios'
import { generatePayloadForNonNullableFields } from '@/utils/helper/generatePayloadforNonNullableFields'
import { CustomTextField } from '@/utils/components'

const acceptedImageTypes = ['png', 'jpg', 'jpeg', 'webp']
export enum FuelType {
	Petrol = 'petrol',
	Diesel = 'diesel',
	electric = 'electric',
	NonMotorised = 'non_motorised',
	BullockCart = 'bullock_cart',
}

interface TProps {
	handleCloseDrawer: () => void
	cSinkNetworkDetails?: INetwork
	subheading?: string
	addText?: string
	vehicleDetails?: IVehicle
}

export const AddVehicle = ({
	handleCloseDrawer,
	subheading,
	addText = '',
	vehicleDetails,
}: TProps) => {
	const queryClient = useQueryClient()
	const [vehicleCategory, setVehicleCategory] = useState<IVehicleCategory[]>([])

	const initialValues = {
		name: addText ?? '',
		number: '',
		imageId: '',
		fuelType: '',
		vehicleType: '',
		type: '',
	}

	const { cSinkNetworkId, artisanProId } = useParams()
	const {
		register,
		formState: { errors },
		watch,
		setValue,
		clearErrors,
		handleSubmit,
	} = useForm<TAddVehicle>({
		defaultValues: initialValues,
		mode: 'all',
		resolver: yupResolver<TAddVehicle>(addVehicleSchema(!!artisanProId)),
	})
	// const watchType = watch('type')
	const watchFuelType = watch('fuelType')
	const watchVehicleType = watch('vehicleType')
	const [searchParams] = useSearchParams()
	const siteId = searchParams.get('siteTab') ?? ''

	const getFormFuelType = () => {
		if (watchFuelType === FuelType.NonMotorised && watchVehicleType)
			return watchVehicleType
		else if (vehicleDetails) return vehicleDetails.fuelType
		return watchFuelType
	}

	const addVehicle = useMutation({
		mutationKey: ['addVehicle'],
		mutationFn: async ({
			name,
			fuelType,
			number,
			type,
			imageId,
			vehicleType,
		}: TAddVehicle) => {
			const formFuelType = getFormFuelType()

			const formData = {
				name,
				number,
				...(!cSinkNetworkId && fuelType !== FuelType.NonMotorised
					? { categoryId: vehicleType }
					: null),
				...(!vehicleDetails ? { type } : null),
				imageIds: imageId ? [imageId] : [],
				fuelType: formFuelType,
			}
			const isEditing = !!vehicleDetails
			const api = cSinkNetworkId
				? `cs-network/${cSinkNetworkId}/vehicles/`
				: `/artisian-pro/${artisanProId}/site/${siteId}/vehicle${
						vehicleDetails ? `/${vehicleDetails.id}` : ''
				  }`
			const method = isEditing ? 'put' : 'post'
			return await authAxios[method](
				api,
				generatePayloadForNonNullableFields(formData)
			)
		},
		onError: (error: AxiosError) => {
			toast((error?.response?.data as { messageToUser: string })?.messageToUser)
		},
		onSuccess: ({ data }) => {
			toast(data?.message)
			queryClient.invalidateQueries({ queryKey: ['getVehicleList'] })
			handleCloseDrawer()
		},
	})
	
	const getVehicleCategoryListQuery = useQuery({
		queryKey: ['getVehicleCategory'],
		queryFn: async () => {
			const { data } = await authAxios.get<IVehicleCategory[]>(
				`/vehicle-category`
			)
			return data
		},
		enabled: !!artisanProId,
	})

	const handleaddVehicle = useCallback(
		(values: TAddVehicle) => {
			addVehicle.mutate(values)
		},
		[addVehicle]
	)

	const isLoading = useMemo(() => addVehicle.isPending, [addVehicle.isPending])

	useEffect(() => {
		if (!artisanProId) return
		setVehicleCategory(getVehicleCategoryListQuery?.data ?? [])
	}, [artisanProId, getVehicleCategoryListQuery?.data])

	useEffect(() => {
		if (vehicleDetails) {
			setValue('name', vehicleDetails.name ?? addText)
			setValue('number', vehicleDetails.number ?? '')
			setValue('imageId', vehicleDetails.imageURLs?.[0]?.id ?? '')
			setValue('fuelType', vehicleDetails.fuelType ?? '')
			setValue('vehicleType', vehicleDetails.categoryId ?? '')
			setValue('type', vehicleDetails.type ?? '')
		}
	}, [vehicleDetails, setValue])

	useEffect(() => {
		if (!vehicleDetails) return

		if (vehicleDetails.fuelType === FuelType.NonMotorised) {
			setValue('fuelType', FuelType.NonMotorised)

			setValue('vehicleType', 'other')
		} else if (
			vehicleDetails.fuelType === 'other' ||
			vehicleDetails.fuelType === FuelType.BullockCart
		) {
			setValue('fuelType', FuelType.NonMotorised)
			setValue('vehicleType', vehicleDetails.fuelType)
		}
	}, [setValue, vehicleDetails, watchFuelType, watchVehicleType])

	return (
		<StyleContainer>
			<Stack className='header'>
				<Stack
					direction='row'
					spacing={1}
					alignItems='center'
					width='100%'
					justifyContent='space-between'>
					<Stack>
						<Typography variant='h5'>
							{vehicleDetails ? 'Edit Vehicle' : 'Add Vehicle'}
						</Typography>
						<Typography variant='subtitle1'>{subheading}</Typography>
					</Stack>
					<IconButton onClick={handleCloseDrawer}>
						<Close />
					</IconButton>
				</Stack>
			</Stack>
			<Stack className='container' component='form'>
				<Stack gap={5}>
					<FormControl>
						<CustomTextField
							schema={addVehicleSchema(!!artisanProId)}
							disabled={!!vehicleDetails}
							select
							fullWidth
							value={watch('fuelType')}
							label='Select the Fuel Type'
							{...register('fuelType')}
							onChange={(e) => {
								setValue('fuelType', e.target.value)
								setValue('type', '')
								setValue('vehicleType', '')
							}}
							id='Vehicle-type'
							error={!!errors.fuelType?.message}
							helperText={errors.fuelType?.message}>
							<MenuItem value={FuelType.Petrol}>Petrol</MenuItem>
							<MenuItem value={FuelType.Diesel}>Diesel</MenuItem>
							<MenuItem value={FuelType.electric}>Electric</MenuItem>
							<MenuItem value={FuelType.NonMotorised}>Non-Motorised</MenuItem>
						</CustomTextField>
					</FormControl>
					{watchFuelType === FuelType.NonMotorised ? (
						<>
							<FormControl>
								<CustomTextField
									schema={addVehicleSchema(!!artisanProId)}
									disabled={!!vehicleDetails}
									select
									fullWidth
									value={watch('vehicleType')}
									label='Enter the Vehicle Type'
									{...register('vehicleType')}
									id='fuel-type'
									error={!!errors.fuelType?.message}
									helperText={errors.fuelType?.message}>
									<MenuItem value={FuelType.BullockCart}>Bullock Cart</MenuItem>
									<MenuItem value='other'>Other</MenuItem>
								</CustomTextField>
							</FormControl>
						</>
					) : null}

					{watchVehicleType === 'other' ? (
						// show name field only when the vehicle type is other
						<CustomTextField
							schema={addVehicleSchema(!!artisanProId)}
							fullWidth
							id='name'
							type='text'
							label='Enter Name '
							variant='outlined'
							error={!!errors.name?.message}
							helperText={errors?.name?.message}
							{...register('name')}
						/>
					) : null}

					{artisanProId &&
						['petrol', 'diesel'].includes(watchFuelType as string) && (
							<FormControl fullWidth>
								<CustomTextField
									disabled={!!vehicleDetails}
									schema={addVehicleSchema(!!artisanProId)}
									select
									fullWidth
									value={watch('vehicleType')}
									label='Select the Vehicle Category'
									{...register('vehicleType')}
									id='fuel-type'
									error={!!errors.vehicleType?.message}
									helperText={errors.vehicleType?.message}>
									{vehicleCategory?.map((i) => (
										<MenuItem value={i.ID} key={i.ID}>
											{i.Name}
										</MenuItem>
									))}
								</CustomTextField>
							</FormControl>
						)}
					{['petrol', 'diesel','electric'].includes(watchFuelType as string) ? (
						<FormControl>
							<CustomTextField
								schema={addVehicleSchema(!!artisanProId)}
								id='number'
								label='Enter Vehicle number'
								autoComplete='off'
								variant='outlined'
								error={!!errors.number?.message}
								helperText={errors?.number?.message}
								fullWidth
								{...register('number')}
								onKeyDown={(event) => {
									const regex = /^[a-zA-Z0-9 ]+$/
									if (!regex.test(event.key)) {
										event.preventDefault()
									}
								}}
								onChange={(e) =>
									setValue('number', e.target.value.toUpperCase())
								}
							/>
						</FormControl>
					) : null}
					{/* this needs ,to be rendered only when the other category is selected after non motorised is selected*/}
					<Stack rowGap={2}>
						<Typography variant='subtitle1'>Vehicle Image</Typography>
						<Stack rowGap={2} width='100%'>
							<CustomFileUploader
								acceptFileTypes={acceptedImageTypes}
								heading='Add Vehicle Image'
								sx={{
									height: { xs: 100, md: 150 },
									width: '100%',
								}}
								imageHeight={100}
								mediaType='image'
								setUploadData={(data) => {
									setValue('imageId', data?.id)
									clearErrors('imageId')
								}}
								imageUrl={vehicleDetails?.imageURLs?.[0].url}
							/>
						</Stack>
						<FormHelperText error={Boolean(errors.imageId)}>
							{errors?.imageId?.message}
						</FormHelperText>
					</Stack>

					<LoadingButton
						loading={isLoading}
						disabled={isLoading}
						onClick={handleSubmit(handleaddVehicle)}
						variant='contained'>
						{vehicleDetails ? 'Save' : 'Add'}
					</LoadingButton>
				</Stack>
			</Stack>
		</StyleContainer>
	)
}

const StyleContainer = styled(Stack)(({ theme }) => ({
	gap: theme.spacing(2),
	'.header': {
		flexDirection: 'row',
		justifyContent: 'space-between',
		alignItems: 'center',
		padding: theme.spacing(4.5, 2),
		borderBottom: `${theme.spacing(0.125)} solid ${
			theme.palette.neutral['100']
		}`,
	},

	'.container': {
		padding: theme.spacing(2, 3),
		gap: theme.spacing(2.5),
	},
}))
