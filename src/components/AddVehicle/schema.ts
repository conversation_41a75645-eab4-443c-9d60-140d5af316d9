import * as Yup from 'yup'
import { FuelType } from './AddVehicle'
export const addVehicleSchema = (isArtisan: boolean) =>
	Yup.object({
		name: Yup.string().when(['vehicleType'], {
			is: (vehicleType: string) => vehicleType === 'other',
			then: (schema) => schema.required('Please enter Vehicle name'),
			otherwise: (schema) => schema,
		}),
		number: Yup.string().when(['fuelType'], {
			is: (type: string) =>
				type === FuelType.Petrol || type === FuelType.Diesel || type===FuelType.electric,
			then: (schema) => schema.required('Please enter Vehicle Number'),
			otherwise: (schema) => schema,
		}),
		imageId: Yup.string().required('Please Upload Image'),
		type: Yup.string(),
		fuelType: Yup.string().required('Please enter Fuel Type'),
		vehicleType: Yup.string().when(['fuelType'], {
			is: (fuelType: string) =>
				isArtisan &&
				['petrol', 'diesel', 'bullock_cart', 'other'].includes(fuelType),
			then: (schema) => schema.required('Please select vehicle Category'),
			otherwise: (schema) => schema,
		}),
	})

export type TAddVehicle = Yup.InferType<ReturnType<typeof addVehicleSchema>>
