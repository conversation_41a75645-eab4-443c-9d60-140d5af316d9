import { Stack, styled, Tooltip, Typography } from '@mui/material'

export const AssignBiomassTagComponent = ({
	value,
	className,
}: {
	value: string
	className?: string
}) => {
	return (
		<StyledStack className={`tag_component ${className}`}>
			<Tooltip title={value} placement='bottom'>
				<Typography className='font_size_14 first_letter_capitalize'>
					{value}
				</Typography>
			</Tooltip>
		</StyledStack>
	)
}

const StyledStack = styled(Stack)(({ theme }) => ({
	flexDirection: 'row',
	columnGap: 5,
	alignItems: 'center',
	'.font_size_14': {
		fontSize: theme.typography.subtitle2.fontSize,
	},
	'.font_weight_600': {
		fontWeight: theme.typography.caption.fontWeight,
	},
}))
