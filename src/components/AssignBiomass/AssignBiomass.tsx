import { ICrop, ICSinkManagers } from '@/types'
import { Close } from '@mui/icons-material'
import {
	Autocomplete,
	Box,
	Button,
	IconButton,
	Stack,
	styled,
	TextField,
	Typography,
	useTheme,
} from '@mui/material'
import { FC, useCallback, useState } from 'react'
import { useForm } from 'react-hook-form'

import { authAxios, useAuthContext } from '@/contexts'
import { toast } from 'react-toastify'
import { useMutation, useQuery, useQueryClient } from '@tanstack/react-query'
import { userRoles } from '@/utils/constant'
import { LoadingButton } from '@mui/lab'
import { assignBiomass, TAssignBiomass } from './schema'
import { yupResolver } from '@hookform/resolvers/yup'
import { Confirmation } from '../Confirmation'
import { AssignBiomassTagComponent } from './AssignBiomassTagComponent'

interface IProps {
	handleClose: () => void
	biomassDetails?: ICrop | null
}
const initialValues = {
	csinkManagerId: '',
}
export const AssignBiomass: FC<IProps> = ({ handleClose, biomassDetails }) => {
	const theme = useTheme()
	const { userDetails } = useAuthContext()
	type ConfirmationDialogState = {
		state: boolean
		id: string
	}

	const [showConfirmationDialog, setShowConfirmationDialog] =
		useState<ConfirmationDialogState>({
			state: false,
			id: '',
		})
	const QueryClient = useQueryClient()
	const {
		handleSubmit,
		formState: { errors },
		setValue,
		clearErrors,
	} = useForm<TAssignBiomass>({
		defaultValues: initialValues,
		mode: 'all',
		resolver: yupResolver<TAssignBiomass>(assignBiomass),
	})

	const AllCSink = useQuery({
		queryKey: ['allCSink'],
		queryFn: async () => {
			const data = await authAxios.get<{
				csinkManagers: { id: string; name: string; shortName: string }[]
			}>(`/crops/${biomassDetails?.id}/csink-manager`)
			return data
		},
		select: ({ data }) =>
			data?.csinkManagers?.map((item) => ({
				label: `${item.name} (${item.shortName})`,
				value: item.id,
			})),
		enabled: [userRoles.Admin].includes(userDetails?.accountType as userRoles),
	})
	const addCSink = useMutation({
		mutationKey: ['addCSink'],
		mutationFn: async (payload: { csinkManagerId: string }) =>
			await authAxios.post(
				`/crops/${biomassDetails?.id}/assign-csink-manager`,
				payload
			),
		onError: (err: any) => {
			toast(err?.response?.data?.userToMessage)
		},
		onSuccess: ({ data }) => {
			toast(data?.message)
			QueryClient.refetchQueries({ queryKey: ['getBiomassTypes'] })
			handleClose()
		},
	})
	const unAssignCSink = useMutation({
		mutationKey: ['edit'],
		mutationFn: async (payload: { csinkManagerId: string }) =>
			await authAxios.put(
				`/crops/${biomassDetails?.id}/unassign-csink-manager`,
				payload
			),
		onError: (err: any) => {
			toast(err?.response?.data?.userToMessage)
		},
		onSuccess: ({ data }) => {
			toast(data?.message)
			QueryClient.refetchQueries({ queryKey: ['getBiomassTypes'] })
			handleClose()
			setShowConfirmationDialog({
				...showConfirmationDialog,
				state: false,
			})
		},
	})

	const handleaddCSink = useCallback(
		async (values: TAssignBiomass) => {
			addCSink.mutate(values)
		},
		[addCSink]
	)

	const handleUnassigned = useCallback(
		async (values: { csinkManagerId: string }) => {
			unAssignCSink.mutate(values)
		},
		[unAssignCSink]
	)

	const RenderContainer: React.FC<{ container: ICSinkManagers }> = ({
		container,
	}) => {
		return (
			<Box className='button-container' key={container?.id}>
				<AssignBiomassTagComponent value={`${container?.name || ''}`} />
				<Button
					className='subtitle1'
					onClick={() =>
						setShowConfirmationDialog({
							...showConfirmationDialog,
							state: true,
							id: container?.id,
						})
					}>
					Unassign
				</Button>
			</Box>
		)
	}
	return (
		<StyleContainer>
			{showConfirmationDialog.state ? (
				<Confirmation
					open={showConfirmationDialog.state}
					handleClose={() =>
						setShowConfirmationDialog({
							...showConfirmationDialog,
							state: false,
						})
					}
					handleNoClick={() =>
						setShowConfirmationDialog({
							...showConfirmationDialog,
							state: false,
						})
					}
					confirmationText={
						<Typography>Are you sure want to continue ?</Typography>
					}
					handleYesClick={() =>
						handleUnassigned({ csinkManagerId: showConfirmationDialog.id })
					}
				/>
			) : null}
			<Stack className='header'>
				<Stack
					direction='row'
					alignItems='center'
					width='100%'
					justifyContent='space-between'>
					<Typography variant='h4'>Edit Assign</Typography>
					<IconButton onClick={handleClose}>
						<Close />
					</IconButton>
				</Stack>
			</Stack>
			<Stack className='container' rowGap={2} mt={1}>
				<Stack className='view_container'>
					{biomassDetails?.csinkManagers.length !== 0 && (
						<Stack className='view_container'>
							<Typography variant='body2'>Assigned CSink Manager</Typography>
							<Stack className='container_item'>
								{biomassDetails?.csinkManagers?.map((item) => (
									<RenderContainer key={item.id} container={item} />
								))}
							</Stack>
						</Stack>
					)}
					<Typography variant='body2'>Assign Biomass</Typography>
					<Autocomplete
						options={AllCSink.data || []}
						renderInput={(params) => (
							<TextField
								{...params}
								label='Select CSink Managers'
								error={!!errors.csinkManagerId}
								helperText={errors.csinkManagerId?.message}
							/>
						)}
						onChange={(_, selectedOption) => {
							if (selectedOption) {
								setValue('csinkManagerId', selectedOption.value)
								clearErrors('csinkManagerId')
							} else {
								setValue('csinkManagerId', '')
							}
						}}
					/>
				</Stack>
				<Stack
					direction='row'
					gap={2}
					display='flex'
					justifyContent='space-between'
					className='buttonContainer'>
					<Button
						onClick={handleClose}
						sx={{ border: ` 1px solid ${theme.palette.primary.main}` }}>
						Cancel
					</Button>
					<LoadingButton
						loading={addCSink.isPending}
						disabled={addCSink.isPending}
						onClick={handleSubmit(handleaddCSink)}
						variant='contained'>
						Add
					</LoadingButton>
				</Stack>
			</Stack>
		</StyleContainer>
	)
}

const StyleContainer = styled(Stack)(({ theme }) => ({
	height: '100vh',
	'.header': {
		flexDirection: 'row',
		justifyContent: 'space-between',
		alignItems: 'center',
		padding: theme.spacing(3, 2),
		borderBottom: `${theme.spacing(0.125)} solid ${
			theme.palette.neutral['100']
		}`,
	},
	'.container': {
		padding: theme.spacing(1, 2),
		// overflowY: 'scroll',
		flex: 1,
		flexDirection: 'column',
		justifyContent: 'space-between',
		gap: theme.spacing(4),

		'.buttonContainer button': {
			width: theme.spacing(30),
			height: theme.spacing(4.5),
			padding: theme.spacing(1, 2.5),
			margin: theme.spacing(5, 0),
		},
		'.view_container': {
			gap: theme.spacing(3),
			'.container_item': {
				maxHeight: theme.spacing(50),
				overflow: 'auto',
				gap: theme.spacing(1, 4),
				'.subtitle1': { ...theme.typography.subtitle1 },
				'.button-container': {
					display: 'grid',
					gridTemplateColumns: '1fr 1fr',
				},
				'.item': {
					':nth-of-type(even)': {
						justifyContent: 'end',
					},
				},
			},
		},
	},
}))
