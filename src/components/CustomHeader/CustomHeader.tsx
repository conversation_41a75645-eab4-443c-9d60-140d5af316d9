import { Stack, styled, Typography } from '@mui/material'
import { CsinkManagerFilter } from '../CsinkManagerFilter'
import { TimeFilter } from '@/pages/dashboard'

interface IProps {
	showBottomBorder?: boolean
	heading?: string
	showButton?: boolean
	endComponent?: JSX.Element
	headingComponent?: JSX.Element
	showCsinkManagerFilter?: boolean
}

export const CustomHeader = ({
	showBottomBorder = false,
	heading,
	showButton = false,
	endComponent,
	headingComponent,
	showCsinkManagerFilter = false,
}: IProps) => {
	return (
		<StyledHeader
			className={`${showBottomBorder ? 'dark-border' : ''}`}
			justifyContent='space-between'>
			<Stack direction='row' spacing={2} justifyContent='flex-start'>
				{headingComponent ? (
					headingComponent
				) : (
					<Typography variant='h3' className='heading'>
						{heading}
					</Typography>
				)}
				{showCsinkManagerFilter && <CsinkManagerFilter />}
			</Stack>

			<Stack direction='row' spacing={2}>
				{showButton && <TimeFilter />}
				{endComponent}
			</Stack>
		</StyledHeader>
	)
}

const StyledHeader = styled(Stack)(({ theme }) => ({
	width: '100%',
	padding: theme.spacing(0, 3),
	gap: theme.spacing(2),
	flexDirection: 'row',
	'.dark-border': {
		borderBottom: `${theme.spacing(0.125)} solid ${
			theme.palette.neutral['100']
		}`,
	},
	'.heading': {
		textTransform: 'capitalize',
		color: theme.palette.neutral['900'],
	},
	'.tab-container': {
		flexDirection: 'row',
		alignItems: 'center',
		background: theme.palette.neutral['100'],
		padding: theme.spacing(0.25),
		borderRadius: theme.spacing(0.75),
		'.tab-button': {
			background: 'transparent',
			color: theme.palette.common.black,
			textTransform: 'none',
			height: 32,
			width: 88,
			fontWeight: 400,
		},
		'.selected-button': {
			background: theme.palette.common.white,
		},
		'.button-border': {
			height: '60%',
			borderRight: `2px solid ${theme.palette.neutral['100']}`,
			borderRadius: theme.spacing(0.25),
		},
	},
}))
