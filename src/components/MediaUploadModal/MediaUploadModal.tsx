import { theme } from '@/lib/theme/theme'
import { Cancel, PlayCircle } from '@mui/icons-material'
import PlayCircleOutlinedIcon from '@mui/icons-material/PlayCircleOutlined'
import { LoadingButton } from '@mui/lab'

import { MediaUploadModalDialog } from '.'
import { BatchProps, Payload, ResponseData } from './interface'

import {
	generateThumbnail,
	handleImageUpload,
	handleVideoUpload,
} from '@/utils/helper'
import {
	Box,
	CircularProgress,
	DialogActions,
	DialogContent,
	DialogTitle,
	IconButton,
} from '@mui/material'
import { ReactNode, useRef, useState } from 'react'
import { toast } from 'react-toastify'
import { MediaCarousal } from '../MedialCarousal'

interface IProps {
	open: boolean
	handleClose: () => void
	dialogeElement?: ReactNode
	handleSaveClick: (payload: Payload) => void
	handleCancelClick: () => void
	title?: string | ReactNode
	batchDetail?: BatchProps
	uploadType?: string
}

const ERROR_MESSAGES = {
	NO_FILE: 'No file selected. Please choose a file to upload.',
	INVALID_TYPE: 'Unsupported file type. Please upload a valid image.',
	FILE_TOO_LARGE:
		'File size exceeds the 10MB limit. Please choose a smaller file.',
	UPLOAD_FAILURE: 'File upload failed. Please try again later.',
}
type UploadState = 'idle' | 'uploading' | 'uploaded'

export const MediaUploadModal = ({
	open,
	handleClose,
	dialogeElement,
	handleSaveClick,
	handleCancelClick,
	title,
	uploadType,
	batchDetail,
}: IProps) => {
	const [selectedThumbnailFile, setSelectedThumbnailFile] =
		useState<ResponseData | null>(null)
	const [selectedVideoFile, setSelectedVideoFile] =
		useState<ResponseData | null>(null)

	const fileVideoInputRef = useRef<HTMLInputElement>(null)

	const handleFileVideoClick = () => {
		fileVideoInputRef.current?.click()
	}

	const handleError = (error: { type: string; message: string }) => {
		toast.error(error.message)
	}

	const [uploadState, setUploadState] = useState<UploadState>('idle')
	const handleFileChange = async (
		event: React.ChangeEvent<HTMLInputElement>
	) => {
		const file = event.target.files?.[0]

		try {
			if (!file) {
				throw { type: 'NO_FILE', message: ERROR_MESSAGES.NO_FILE }
			}
			setUploadState('uploading')
			const videoData = await handleVideoUpload(file)
			if (!videoData?.id || !videoData?.url) {
				throw { type: 'UPLOAD_FAILURE', message: ERROR_MESSAGES.UPLOAD_FAILURE }
			}

			const { blob, thumbnailBlobUrl } = await generateThumbnail(
				videoData.url,
				1
			)

			//  Check if thumbnail was generated successfully
			if (!blob || !thumbnailBlobUrl) {
				throw {
					type: 'THUMBNAIL_ERROR',
					message: 'Failed to generate video thumbnail.',
				}
			}

			const thumbnailFile = new File([blob], 'thumbnail.jpg', {
				type: blob.type,
			})
			const imageData = await handleImageUpload(thumbnailFile)

			if (!imageData?.id || !imageData?.url) {
				throw { type: 'UPLOAD_FAILURE', message: ERROR_MESSAGES.UPLOAD_FAILURE }
			}

			setSelectedVideoFile(videoData)
			setSelectedThumbnailFile(imageData)
		} catch (error: any) {
			handleError(error)
			setUploadState('idle')
		} finally {
			setUploadState('uploaded')
			event.target.value = ''
		}
	}

	const handleRemoveVideo = () => {
		setSelectedVideoFile(null)
		setUploadState('idle')
	}

	const handleSave = () => {
		const isBatchDetailsMissing = !batchDetail && !uploadType
		if (isBatchDetailsMissing) {
			toast.error('Batch details are missing.')
			return
		}

		// Final Payload
		const payload: Payload = {
			processVideos: [
				{
					videoId: selectedVideoFile?.id,
					thumbnailImageId: selectedThumbnailFile?.id,
				},
			],
		}

		handleSaveClick(payload)
	}
	const [showCarousal, setShowCarousal] = useState(false)

	return (
		<>
			{showCarousal ? (
				<MediaCarousal
					open={!!showCarousal}
					onClose={() => {
						setShowCarousal(false)
					}}
					gallery={[
						{
							id: selectedVideoFile?.id ?? '',
							url: selectedVideoFile?.url ?? '',
							path: selectedVideoFile?.url ?? '',
							type: 'video',
							thumbnailURL: selectedThumbnailFile?.url,
							fileType: 'video',
						},
					]}
					showDownloadButton={false}
				/>
			) : null}
			<MediaUploadModalDialog
				open={open}
				onClose={handleClose}
				aria-labelledby='alert-dialog-title'
				aria-describedby='alert-dialog-description'
				fullWidth
				maxWidth='xs'
				PaperProps={{
					style: {
						padding: theme.spacing(1.2),
					},
				}}>
				<IconButton className='icon-button-align' onClick={handleClose}>
					<Cancel />
				</IconButton>
				{title ? <DialogTitle textAlign='center'>{title}</DialogTitle> : null}
				<DialogContent className='dialog-content-align'>
					{dialogeElement}

					<RenderMediaComponent
						uploadState={uploadState}
						handleFileVideoClick={handleFileVideoClick}
						fileVideoInputRef={fileVideoInputRef}
						handleFileChange={handleFileChange}
						selectedThumbnailFile={selectedThumbnailFile}
						handleRemoveVideo={handleRemoveVideo}
						setShowCarousal={setShowCarousal}
					/>
				</DialogContent>

				<DialogActions className='dialog-actions-align'>
					<LoadingButton
						variant='contained'
						onClick={() => {
							handleSave()
						}}>
						Save
					</LoadingButton>
					<LoadingButton onClick={handleCancelClick}>Cancel</LoadingButton>
				</DialogActions>
			</MediaUploadModalDialog>
		</>
	)
}
const RenderMediaComponent = ({
	uploadState,
	handleFileVideoClick,
	fileVideoInputRef,
	handleFileChange,
	selectedThumbnailFile,
	handleRemoveVideo,
	setShowCarousal,
}: {
	uploadState: UploadState
	handleFileVideoClick: () => void
	fileVideoInputRef: React.RefObject<HTMLInputElement>
	handleFileChange: (
		event: React.ChangeEvent<HTMLInputElement>
	) => Promise<void>
	selectedThumbnailFile: ResponseData | null
	handleRemoveVideo: () => void
	setShowCarousal: (value: React.SetStateAction<boolean>) => void
}) => {
	switch (uploadState) {
		case 'idle':
			return (
				<IconButton onClick={handleFileVideoClick}>
					<PlayCircleOutlinedIcon className='upload-icon' />
					<input
						ref={fileVideoInputRef}
						id='processVideo'
						name='processVideo'
						type='file'
						accept='video/*'
						style={{ display: 'none' }}
						onChange={handleFileChange}
					/>
				</IconButton>
			)

		case 'uploading':
			return (
				<IconButton>
					<CircularProgress />
				</IconButton>
			)

		case 'uploaded':
			return (
				<IconButton sx={{ padding: 2 }}>
					<Box
						component='img'
						src={selectedThumbnailFile?.url}
						alt='image'
						className='img-btn'
						sx={{ width: theme.spacing(6), height: theme.spacing(6) }}
					/>
					<IconButton
						sx={{ position: 'absolute', bottom: 44, left: 48 }}
						onClick={handleRemoveVideo}>
						<Cancel fontSize='small' />
					</IconButton>
					<IconButton
						sx={{ position: 'absolute', top: 18, left: 18 }}
						onClick={() => setShowCarousal(true)}>
						<PlayCircle color='primary' />
					</IconButton>
				</IconButton>
			)

		default:
			return null
	}
}
