

export interface BatchProps {
	artisianProId?: string | null | undefined
	csNetworkId?: string | null | undefined
	siteId?: string | null | undefined
	kilnId: string | undefined
	processId: string | undefined
}

export interface ListImageType {
    id: string
    url: string
}

export interface ResponseData {
    id: string
    url: string
    fileName: string
}

export interface Processvideo {
    videoId: string | null | undefined
    thumbnailImageId: string | null | undefined
}

export type Payload = {
    processVideos: Processvideo[]
}