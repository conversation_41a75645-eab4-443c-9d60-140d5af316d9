import { Dialog, styled } from '@mui/material'

export const MediaUploadModalDialog = styled(Dialog)(({ theme }) => ({
	'.dialog-center': {
		display: 'flex',
		width: '100%',
		alignItems: 'center',
		justifyContent: 'center',
	},
	'.dialog-content-align': {
		display: 'flex',
		justifyContent: 'flex-start',
		alignItems: 'flex-start',
		flexDirection: 'column',
	},
	'.icon-button-align': {
		position: 'absolute',
		right: 5,
		top: 0,
	},
	'.select-menu-items': {
		width: '100%',
		minHeight: '50px',
		fontWeight: '400',
	},
	'.menu-item-font': {
		fontWeight: theme.typography.subtitle1, // Or hardcode it to 400
	},
	'.align-item-box': {
		display: 'flex',
		gap: 5,
		width: '100%',
		paddingTop: 4,
	},
	'.upload-icon': {
		color: 'black',
		fontSize: 30,
	},
	'.list-image-align': {
		flexDirection: 'row',
		gap: 10,
		flexWrap: 'wrap',
	},
	'.icon-image-cancel': {
		position: 'absolute',
		right: '-10px',
		top: '-5px',
		padding: '0',
	},
	'.dialog-actions-align': {
		display: 'flex',
		justifyContent: 'center',
		columnGap: 9,
	},
	'.selected-image-align': {
		width: 40,
	},
	'.image-cancel-button': {
		fontSize: '.9rem',
	},
	'.selected-video-file': {
		position: 'relative',
		minHeight: '25px',
		minWidth: '90px',
		backgroundColor: '#F2F0F0',
		padding: '5px',
	},
}))
