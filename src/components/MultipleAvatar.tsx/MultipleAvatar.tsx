import { IImage } from '@/interfaces'
import { proxyImage } from '@/utils/helper'
import { Avatar, AvatarGroup } from '@mui/material'

interface IProps {
	imageList?: IImage[]
	MaxAvatar?: number
	size?: number
}
export function MultipleAvatar(props: IProps) {
	const { imageList, MaxAvatar, size = 24 } = props

	return (
		<AvatarGroup spacing='small'>
			{imageList?.slice(0, MaxAvatar ?? 2)?.map((item, index) => (
				<Avatar
					key={index}
					src={proxyImage(item?.path ?? '')}
					sx={{
						height: size,
						width: size,
					}}
				/>
			))}
		</AvatarGroup>
	)
}
