import { CancelOutlined, DescriptionOutlined } from '@mui/icons-material'
import {
	Box,
	CircularProgress,
	IconButton,
	Stack,
	Typography,
} from '@mui/material'
import { ReactNode, useState } from 'react'
import { ImageGrid } from '../ImageGrid'
import { IMedia, IMediaLoading } from '@/interfaces'
import { DocumentViewer } from './DocumentViewer'
import { PdfPreviewDialog } from './PdfPreviewDialog'

type TMode = 'upload' | 'table'

interface IProp {
	media: IMedia[]
	componentSize?: number
	onClose?: () => void
	viewMode?: TMode
	loading?: IMediaLoading
	removeMedia?: (item: IMedia) => void
	hideTitle?: boolean
	showInRow?: boolean
	ShowDeleteOption?: boolean
	showDocumentName?: boolean
}

const ImageTypes = ['png', 'jpg', 'jpeg', 'webp', 'svg']

const DocsTypes = ['pdf', 'doc', 'docx', 'txt', 'ppt', 'pptx', 'xls', 'xlsx']

const MediaRender = ({
	type,
	url,
	removeMedia,
	fileName,
}: {
	type: 'img' | 'doc'
	url: string
	removeMedia: () => void
	fileName?: string
}) => (
	<Stack position='relative' width='fit-content'>
		<IconButton
			sx={{
				position: 'absolute',
				right: -10,
				top: -10,
				color: 'common.black',
				borderRadius: '50%',
				padding: 0.5,
			}}
			onClick={removeMedia}>
			<CancelOutlined fontSize='medium' />
		</IconButton>

		{type === 'img' ? (
			<Box
				component='img'
				src={url}
				width={100}
				height={100}
				borderRadius={1}
				sx={{ objectFit: 'cover' }}
			/>
		) : (
			<Box
				width={100}
				height={100}
				borderRadius={1}
				display='flex'
				flexDirection='column'
				justifyContent='center'
				rowGap={1}
				alignItems='center'>
				<DescriptionOutlined sx={{ fontSize: 50, color: 'grey.500' }} />
				<Typography
					sx={{ wordBreak: 'break-all' }}
					textAlign='center'
					px={1}
					fontSize='12px'>
					{fileName}
				</Typography>
			</Box>
		)}
	</Stack>
)

export const MediaContainer = ({
	children,
	title,
	isLoading,
	hideTitle = false,
}: {
	children: ReactNode | JSX.Element | JSX.Element[]
	title?: string
	isLoading?: boolean
	hideTitle?: boolean
}) => (
	<Stack>
		{hideTitle ? null : <Typography fontWeight='bold'>{title}</Typography>}
		<Stack
			flexDirection='row'
			flexWrap='wrap'
			alignItems='center'
			gap={2}
			mt={1}>
			{children}
			{isLoading ? <CircularProgress /> : null}
		</Stack>
	</Stack>
)

export const TrainingProofRenderer = ({
	media,
	componentSize = 38,
	viewMode = 'upload',
	loading = { docs: false, images: false },
	removeMedia,
	hideTitle = false,
	ShowDeleteOption = true,
	showDocumentName = false,
	showInRow = false,
}: IProp) => {
	const [showDocDialog, setShowDOcDialog] = useState<boolean>(false)
	const [selectedPdfUrl, setSelectedPdfUrl] = useState<string>('')

	const imagesArray = media?.filter((item) =>
		ImageTypes.includes(item?.fileName?.split('.')?.pop() as string)
	)

	const docsArray = media?.filter((item) =>
		DocsTypes.includes(item?.fileName?.split('.')?.pop() as string)
	)

	if (viewMode === 'upload')
		return (
			<Stack mt={2} rowGap={2}>
				<>
					{imagesArray?.length === 0 && loading?.images ? (
						<CircularProgress />
					) : null}
					{imagesArray?.length > 0 ? (
						<MediaContainer title='Images' isLoading={loading?.images}>
							{imagesArray?.map((img) => (
								<MediaRender
									url={img?.url}
									key={img?.id}
									type='img'
									removeMedia={() => removeMedia?.(img)}
								/>
							))}
						</MediaContainer>
					) : null}
				</>
				<>
					{docsArray?.length === 0 && loading?.docs ? (
						<CircularProgress />
					) : null}
					{docsArray?.length > 0 ? (
						<MediaContainer title='Documents' isLoading={loading?.docs}>
							{docsArray?.map((doc) => (
								<MediaRender
									url={doc?.url}
									key={doc?.id}
									type='doc'
									fileName={doc?.fileName}
									removeMedia={() => removeMedia?.(doc)}
								/>
							))}
						</MediaContainer>
					) : null}
				</>
			</Stack>
		)

	return (
		<>
			<Stack
				direction={showInRow ? 'row' : 'column'}
				alignItems='start'
				gap={1}>
				{imagesArray?.length > 0 ? (
					<MediaContainer
						title='Images'
						hideTitle={docsArray?.length === 0 || hideTitle}>
						<ImageGrid
							componentSize={componentSize}
							imageList={imagesArray}
							ShowDeleteOption={ShowDeleteOption}
						/>
					</MediaContainer>
				) : null}

				{docsArray?.length > 0 ? (
					<MediaContainer
						title='Docs'
						hideTitle={imagesArray?.length === 0 || hideTitle}>
						<Stack direction='row' columnGap={1}>
							{docsArray?.slice(0, 2)?.map((doc) => (
								<Stack key={doc?.id}>
									<Box
										width={componentSize}
										height={componentSize}
										borderRadius={1}
										display='flex'
										flexDirection='column'
										justifyContent='center'
										position='relative'
										rowGap={1}
										onClick={(e) => {
											e.stopPropagation()
											setSelectedPdfUrl(doc?.url)
										}}
										alignItems='center'>
										<DescriptionOutlined
											sx={{ fontSize: componentSize, color: 'grey.500' }}
										/>
									</Box>
									{showDocumentName ? (
										<Typography
											sx={{ wordBreak: 'break-all' }}
											textAlign='center'
											px={1}
											fontSize='12px'>
											{doc?.fileName
												? `${doc?.fileName?.substring(0, 5)}...`
												: ''}
										</Typography>
									) : null}
								</Stack>
							))}
							{docsArray?.length > 2 ? (
								<Box
									width={componentSize}
									height={componentSize}
									display='flex'
									alignItems='center'
									bgcolor='grey.300'
									borderRadius={1}
									onClick={(e) => {
										e.stopPropagation()
										e.preventDefault()
										setShowDOcDialog(true)
									}}
									justifyContent='center'>
									<Typography variant='subtitle2'>
										+{docsArray.length - 2}
									</Typography>
								</Box>
							) : null}
						</Stack>
					</MediaContainer>
				) : null}
			</Stack>
			{showDocDialog ? (
				<DocumentViewer
					open={showDocDialog}
					close={() => setShowDOcDialog(false)}
					documents={docsArray}
					showDownloadButton={true}
				/>
			) : null}
			{selectedPdfUrl ? (
				<PdfPreviewDialog
					open={!!selectedPdfUrl}
					close={() => setSelectedPdfUrl('')}
					pdfUrl={selectedPdfUrl}
				/>
			) : null}
		</>
	)
}
