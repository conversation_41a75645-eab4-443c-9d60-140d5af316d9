import { Cancel, DescriptionOutlined } from '@mui/icons-material'
import {
	Box,
	Dialog,
	DialogContent,
	DialogTitle,
	IconButton,
	Stack,
	Typography,
} from '@mui/material'
import { useState } from 'react'
import { useNavigate } from 'react-router-dom'
import { PdfPreviewDialog } from './PdfPreviewDialog'
import { IMedia } from '@/interfaces'

interface IProp {
	open: boolean
	close: () => void
	documents: IMedia[]
	showDownloadButton: boolean
}

export const DocumentViewer = ({
	open,
	close,
	documents,
	showDownloadButton,
}: IProp) => {
	const navigate = useNavigate()
	const [selectedPdf, setSelectedPdf] = useState<{ url: string }>({ url: '' })
	return (
		<>
			<Dialog
				open={open}
				onClose={close}
				fullWidth
				sx={{
					'& .MuiPaper-root': {
						p: 3,
						minWidth: 300,
					},
				}}>
				<IconButton
					sx={{ position: 'absolute', right: 5, top: 5, cursor: 'pointer' }}
					onClick={close}>
					<Cancel />
				</IconButton>
				<DialogTitle textAlign='center'>Documents</DialogTitle>
				<DialogContent>
					<Stack columnGap={2} rowGap={2} flexWrap='wrap' direction='row'>
						{documents?.map((doc) => (
							<Stack key={doc?.id} width={100} rowGap={2}>
								<Box
									borderRadius={1}
									display='flex'
									flexDirection='column'
									justifyContent='center'
									position='relative'
									rowGap={1}
									onClick={() => {
										if (doc.fileName?.includes('.pdf')) {
											setSelectedPdf({ url: doc.url })
											return
										}
										navigate(doc.url)
									}}
									alignItems='center'>
									<DescriptionOutlined
										sx={{ fontSize: 70, color: 'grey.500' }}
									/>
								</Box>
								{doc.fileName?.split('.')[0] ? (
									<Typography
										sx={{ wordBreak: 'break-all' }}
										textAlign='center'
										px={1}
										fontWeight={500}
										fontSize='12px'>
										{doc.fileName}
									</Typography>
								) : null}
							</Stack>
						))}
					</Stack>
				</DialogContent>
			</Dialog>
			{selectedPdf?.url ? (
				<PdfPreviewDialog
					open={!!selectedPdf?.url}
					close={() => setSelectedPdf({ url: '' })}
					pdfUrl={selectedPdf?.url}
					showDownloadButton={showDownloadButton}
				/>
			) : null}
		</>
	)
}
