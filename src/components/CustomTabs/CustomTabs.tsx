import { Tabs, TabsProps } from '@mui/material'
import { ReactNode, SyntheticEvent } from 'react'

interface IProps extends TabsProps {
	children: ReactNode
	value: string | number
	onChange: (_: SyntheticEvent, newValue: string) => void
}

export function CustomTabs({ children, value, onChange, ...props }: IProps) {
	return (
		<Tabs
			value={value}
			onChange={onChange}
			allowScrollButtonsMobile
			sx={{
				'& .Mui-disabled': {
					opacity: '0.3 !important',
				},
			}}
			{...props}>
			{children}
		</Tabs>
	)
}
