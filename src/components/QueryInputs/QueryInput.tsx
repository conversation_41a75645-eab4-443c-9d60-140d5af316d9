import { useDebounceValue } from '@/CommonHooks/useDebouncedValue'
import { TextField, TextFieldProps } from '@mui/material'
import { FC, useEffect, useRef, useState } from 'react'
import { useSearchParams } from 'react-router-dom'

interface Props extends Omit<TextFieldProps<'outlined'>, 'variant'> {
	queryKey: string
	setPageOnSearch?: boolean
}

export const QueryInput: FC<Props> = ({
	queryKey = 'search',
	setPageOnSearch,
	...props
}) => {
	const [searchParams, setSearchParams] = useSearchParams()
	const [searchValue, setSearchValue] = useState<string>(
		searchParams.get(queryKey) ?? ''
	)
	const prevSearchRef = useRef('')

	const debouncedValue = useDebounceValue(searchValue, 300)
	useEffect(() => {
		const nsp = new URLSearchParams(searchParams)
		if (debouncedValue) {
			if (prevSearchRef.current != searchValue && setPageOnSearch) {
				nsp.set('page', '0')
			}
			nsp.set(queryKey, debouncedValue)
			prevSearchRef.current = searchValue
		} else if (searchParams.get(queryKey) == null) {
			return
		} else {
			nsp.delete(queryKey)
		}
		setSearchParams(nsp)
	}, [
		debouncedValue,
		queryKey,
		searchParams,
		searchValue,
		setPageOnSearch,
		setSearchParams,
	])

	return (
		<>
			<TextField
				{...props}
				value={searchValue}
				onChange={(e) => {
					setSearchValue(e.target.value)
				}}
			/>
		</>
	)
}
