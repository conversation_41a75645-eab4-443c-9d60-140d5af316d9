import {
	<PERSON><PERSON>,
	<PERSON><PERSON><PERSON><PERSON><PERSON>,
	<PERSON><PERSON>,
	Tab,
	Typography,
	styled,
} from '@mui/material'
import { Close } from '@mui/icons-material'
import { theme } from '@/lib/theme/theme'
import { TabContext, TabList, TabPanel } from '@mui/lab'

import { useCallback, useEffect, useMemo, useState } from 'react'
import { ISamplingContainerDetails, ISite, IVehicle } from '@/interfaces'
import { useEditSiteEntity } from './useEditSiteEntity'
import { AddEntityType } from '../EditNetwork'
import { EntityTabEnum, fuelTypes, userRoles } from '@/utils/constant'
import { useAuthContext } from '@/contexts'
import { Confirmation } from '../Confirmation'
import { GoogleMapsDraw } from '../GoogleMap'
import { RenderEntityDetails } from './RenderEntityDetails'

const tabs = [
	{
		label: 'Kilns',
		value: EntityTabEnum.kilns,
	},
	{
		label: 'Containers',
		value: EntityTabEnum.containers,
	},
	{
		label: 'Sampling Containers',
		value: EntityTabEnum.samplingContainer,
	},
	{
		label: 'Vehicles',
		value: EntityTabEnum.vehicles,
	},
	{
		label: 'Biomass Source',
		value: EntityTabEnum.biomassSource,
	},
	{
		label: 'Operator',
		value: EntityTabEnum.operator,
	},
]

const findFuelType = (fuelType?: string) => {
	switch (fuelType) {
		case fuelTypes.PETROL:
			return 'Petrol'
		case fuelTypes.ELETRIC:
			return 'Eletric'
		case fuelTypes.DIESEL:
			return 'Diesel'
		case fuelTypes.NON_MOTORISED:
		case fuelTypes.BULLOCK_CART:
		case 'non-motorized':
			return 'Non Motorised'
		default:
			return ''
	}
}

const findOtherType = (FuelType?: string, categoryName?: string | null) => {
	switch (FuelType) {
		case fuelTypes.BULLOCK_CART:
			return 'Bullock Cart'
		case fuelTypes.OTHER:
			return 'Other'
		default:
			return categoryName
	}
}

const renderVehicleDetails = (item: IVehicle) => {
	const vehicleFuelType = findFuelType(item?.fuelType)

	const fueltype =
		item?.type == 'non-motorized' ? 'Non Motorised' : vehicleFuelType
	const otherType = findOtherType(item?.fuelType, item?.categoryName)
	const number = item?.number
	const name = item?.name
	if (item)
		return (
			<Stack>
				<Typography>{fueltype}</Typography>
				<Typography>
					{otherType} {number ? `(${number})` : ''} {name ? `(${name})` : ''}
				</Typography>
			</Stack>
		)
}

export const EditSiteEntity = ({
	siteDetails,
	handleClose,
	openTab,
}: {
	siteDetails?: ISite
	handleClose: () => void
	openTab?: EntityTabEnum
	handleFileUpload?: any
}) => {
	const {
		containerList,
		vehicleList,
		biomassSourceListQuery,
		navigate,
		showMap,
		setShowMap,
		farmCoordinates,
		setFarmCoordinates,
		setSearchParams,
		kilnListQuery,
		samplingContainerListQuery,
		handleSaveKml,
		handleDeleteContainer,
		handleDeleteSamplingContainer,
		handleDeleteVehicle,
		handleDeleteBiomassSource,
		setTab,
		tab,
		handleFillSamplingContainer,
	} = useEditSiteEntity()
	const { userDetails } = useAuthContext()
	const [openFilledConfirmation, setOpenFilledConfirmation] =
		useState<ISamplingContainerDetails | null>(null)
	const canDeleteContainer = useMemo(
		() =>
			userDetails?.accountType === userRoles.Admin ||
			userDetails?.accountType === userRoles.CsinkManager,
		[userDetails?.accountType]
	)
	const handleTabChange = useCallback(
		(_: unknown, newValue: EntityTabEnum) => {
			setTab(newValue)
		},
		[setTab]
	)
	const entityDetails = {
		name: siteDetails?.name,
		networkId: siteDetails?.shortCode,
		location: siteDetails?.address,
		managerDetails: siteDetails?.mixedQuantity,
	}
	const entitySubHeading = `Site Name: ${entityDetails?.name}`

	const isShowFilledEmptyButton = useMemo(() =>
		userDetails?.accountType === userRoles.Admin,
		[userDetails?.accountType]
	)

	const tabData = useMemo(() => {
		return [
			{
				label: 'Kilns',
				value: EntityTabEnum.kilns,
				disabled: false,
				addEntityTypeProps: {
					subheading: entitySubHeading,
					isCsink: false,
					siteDetails: siteDetails,
					label: 'Kiln',
				},
				stackDetails: (
					<RenderEntityDetails
						// dataList={kilnListQuery?.data?.kilns}
						dataList={
							kilnListQuery?.data?.kilns?.map((item) => ({
								...item,
								editButton: true,
							})) ?? []
						}
						entityType={EntityTabEnum.kilns}
						getSubText={(item) => item?.address}
						handleDelete={handleDeleteContainer}
					/>
				),
			},
			{
				label: 'Containers',
				value: EntityTabEnum.containers,
				disabled: false,
				addEntityTypeProps: {
					subheading: entitySubHeading,
					isCsink: false,
					siteDetails: siteDetails,
					label: 'Container',
				},
				stackDetails: (
					<RenderEntityDetails
						// dataList={containerList}
						dataList={(containerList ?? []).map((item) => ({
							...item,
							editButton: true,
						}))}
						entityType={EntityTabEnum.containers}
						getSubText={(item) =>
							`${item?.shape?.replace('_', ' ')} (${item?.volume} ltrs)`
						}
						handleDelete={handleDeleteContainer}
						deleteButton={canDeleteContainer}
					/>
				),
			},
			{
				label: 'Sampling Containers',
				value: EntityTabEnum.samplingContainer,
				disabled: false,
				addEntityTypeProps: {
					subheading: entitySubHeading,
					isCsink: false,
					siteDetails: siteDetails,
					label: 'Container',
				},
				stackDetails: (
					<RenderEntityDetails
						// dataList={samplingContainerListQuery?.data?.samplingContainers}
						dataList={
							samplingContainerListQuery?.data?.samplingContainers?.map(
								(item) => ({
									...item,
									editButton: true,
								})
							) ?? []
						}
						entityType={EntityTabEnum.samplingContainer}
						getSubText={(item) => `${item?.shape} (${item?.volume} ltrs)`}
						handleDelete={handleDeleteSamplingContainer}
						deleteButton={canDeleteContainer}
						renderExtraButtons={(item, subText) => (
							<>
								<Stack
									direction='row'
									alignItems='center'
									gap={theme.spacing(0.5)}>
									<Typography variant='subtitle1'>{subText}</Typography>
									{
										isShowFilledEmptyButton && (
											<Button onClick={() => setOpenFilledConfirmation(item)}>
												{item?.filled ? 'Filled' : 'Empty'}
											</Button>
										)
									}
								</Stack>
								{item?.filled &&
									<Typography
										variant='subtitle1'
										color='error'
									>
										This container is filled
									</Typography>
								}
							</>
						)}
					/>
				),
			},
			{
				label: 'Vehicles',
				value: EntityTabEnum.vehicles,
				addEntityTypeProps: {
					subheading: entitySubHeading,
					siteDetails: siteDetails,
					showSearchField: false,
					label: 'Vehicle',
				},
				stackDetails: (
					<RenderEntityDetails
						dataList={vehicleList}
						entityType={EntityTabEnum.vehicles}
						getSubText={(item) => renderVehicleDetails(item) || ''}
						handleDelete={handleDeleteVehicle}
						deleteButton={canDeleteContainer}
						showWrapperPadding
					/>
				),
			},
			{
				label: 'Biomass Source',
				value: EntityTabEnum.biomassSource,
				addEntityTypeProps: {
					subheading: entitySubHeading,
					siteDetails: siteDetails,
					isCsink: false,
					label: 'Biomass',
				},
				stackDetails: (
					<RenderEntityDetails
						dataList={biomassSourceListQuery?.data?.fpu}
						entityType={EntityTabEnum.biomassSource}
						getSubText={(item) => item?.address}
						handleDelete={handleDeleteBiomassSource}
						deleteButton
						navigate={navigate}
						setFarmCoordinates={setFarmCoordinates}
						setShowMap={setShowMap}
						setSearchParams={setSearchParams}
					/>
				),
			},
			{
				label: 'Operator',
				value: EntityTabEnum.operator,
				addEntityTypeProps: {
					subheading: entitySubHeading,
					siteDetails: siteDetails,
					isCsink: false,
					showSearchField: false,
					AddbuttonText: 'Assign Operator',
					label: 'Operator',
				},
				stackDetails: (
					<RenderEntityDetails
						dataList={siteDetails?.operators}
						entityType={EntityTabEnum.operator}
						getSubText={(item) =>
							item?.phoneNo ? `(${item?.countryCode}) ${item?.phoneNo}` : ''
						}
					/>
				),
			},
		]
	}, [
		isShowFilledEmptyButton,
		biomassSourceListQuery?.data?.fpu,
		canDeleteContainer,
		containerList,
		entitySubHeading,
		handleDeleteBiomassSource,
		handleDeleteContainer,
		handleDeleteSamplingContainer,
		handleDeleteVehicle,
		kilnListQuery?.data?.kilns,
		navigate,
		samplingContainerListQuery?.data?.samplingContainers,
		setFarmCoordinates,
		setSearchParams,
		setShowMap,
		siteDetails,
		vehicleList,
	])

	useEffect(() => {
		if (openTab) {
			setTab(openTab)
		}
	}, [openTab, setTab])
	return (
		<StyleContainer>
			<Stack className='header'>
				<Stack
					direction='row'
					spacing={1}
					alignItems='center'
					width='100%'
					justifyContent='space-between'>
					<Stack>
						<Typography variant='h5'>Site Details</Typography>
						<Typography variant='subtitle1'>
							{entityDetails?.name} {entityDetails?.location}
						</Typography>
					</Stack>
					<IconButton onClick={handleClose}>
						<Close />
					</IconButton>
				</Stack>
			</Stack>
			<Stack className='container' component='form'>
				<TabContext value={tab}>
					<TabList className='tabList' onChange={handleTabChange}>
						{tabs.map(({ label, value }, index) => (
							<Tab key={index} label={label} value={value} />
						))}
					</TabList>
					{tabData.map(
						({ value, addEntityTypeProps, stackDetails, disabled }, index) => (
							<TabPanel
								key={`${index}-${value}`}
								value={value}
								sx={{ padding: 0 }}>
								<AddEntityType
									{...addEntityTypeProps}
									entityType={value}
									disabled={disabled}
								/>
								{stackDetails}
							</TabPanel>
						)
					)}
				</TabContext>
			</Stack>
			{showMap ? (
				<GoogleMapsDraw
					open={showMap}
					handleModalClose={() => {
						setSearchParams((params) => {
							params.delete('lat')
							params.delete('long')
							params.delete('networkId')

							return params
						})
						setShowMap(false)
						setFarmCoordinates([])
					}}
					handleSave={handleSaveKml}
					initialPolygons={farmCoordinates}
				/>
			) : null}
			{openFilledConfirmation ? (
				<Confirmation
					confirmationText={`Are you sure you want to ${
						openFilledConfirmation?.filled ? 'Empty' : 'Fill'
					} the Sampling Container- ${openFilledConfirmation?.name}?`}
					handleClose={() => setOpenFilledConfirmation(null)}
					handleNoClick={() => setOpenFilledConfirmation(null)}
					handleYesClick={() => {
						handleFillSamplingContainer(openFilledConfirmation)
						setOpenFilledConfirmation(null)
					}}
					open={!!openFilledConfirmation}
				/>
			) : null}
		</StyleContainer>
	)
}

const StyleContainer = styled(Stack)(({ theme }) => ({
	gap: theme.spacing(2),
	'.header': {
		flexDirection: 'row',
		justifyContent: 'space-between',
		alignItems: 'center',
		padding: theme.spacing(4.5, 2),
		borderBottom: `${theme.spacing(0.125)} solid ${
			theme.palette.neutral['100']
		}`,
	},
	'.tabList': {
		'& .MuiTabs-scroller': {
			overflow: 'auto !important',
		},
		'& ::-webkit-scrollbar': {
			display: 'none',
		},
		'-ms-overflow-style': 'none' /* IE and Edge */,
		'scrollbar-width': 'none' /* Firefox */,
	},
	'.container': {
		height: '100%',
		padding: theme.spacing(2, 3),
		gap: theme.spacing(2.5),
		'.add_btn': {
			textTransform: 'none',
			maxHeight: theme.spacing(6),
			width: '100%',
			maxWidth: theme.spacing(22),
			borderColor: theme.palette.neutral[300],
			color: theme.palette.neutral[300],
			borderRadius: 8,
		},
	},
}))
