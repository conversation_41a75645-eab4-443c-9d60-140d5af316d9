import {
	<PERSON>ton,
	FormControl,
	IconButton,
	Stack,
	TextField,
	Typo<PERSON>,
	styled,
} from '@mui/material'
import { Close } from '@mui/icons-material'
import { ISiteList } from '@/interfaces'
import { theme } from '@/lib/theme/theme'
import { useForm } from 'react-hook-form'
import { GoogleMapsWithNonDraggableMarker } from '../GoogleMap'
import { useCallback, useEffect } from 'react'
import { authAxios } from '@/contexts'
import { useParams } from 'react-router-dom'
import { toast } from 'react-toastify'
import { yupResolver } from '@hookform/resolvers/yup'
import { useQueryClient } from '@tanstack/react-query'
import { AddSite, TAddSite } from '.'
export const EditSite = ({
	siteDetails,
	handleClose,
	isEdit = false,
}: {
	siteDetails?: ISiteList
	handleClose: () => void
	isEdit?: boolean
}) => {
	const { artisanProId } = useParams()
	const initialValues = {
		location: siteDetails?.address ?? '',
		name: siteDetails?.name ?? '',
		shortCode: siteDetails?.shortCode ?? '',
		latitude: siteDetails?.coordinate
			? Number(siteDetails?.coordinate?.split(',')[0].slice(1))
			: 0,
		longitude: siteDetails?.coordinate
			? Number(siteDetails?.coordinate?.split(',')[1].slice(1).replace(')', ''))
			: 0,
	}
	const queryClient = useQueryClient()

	const {
		register,
		handleSubmit,
		formState: { errors },
		setValue,
		watch,
	} = useForm({
		defaultValues: initialValues,
		resolver: yupResolver<TAddSite>(AddSite),
	})

	const setMapCenter = useCallback(
		(lat: number, lng: number) => {
			setValue('latitude', lat ?? 0)
			setValue('longitude', lng ?? 0)
		},
		[setValue]
	)
	const getCurrentLocation = useCallback(() => {
		navigator.geolocation.getCurrentPosition((position) => {
			setMapCenter(position.coords.latitude, position.coords.longitude)
		})
	}, [setMapCenter])

	const handleAddSite = useCallback(
		async (formValues: TAddSite) => {
			const { location, ...rest } = formValues
			try {
				const formBody = { address: location, ...rest }
				const { data } = await authAxios.post(
					`/artisian-pro/${artisanProId}/site`,
					formBody
				)
				handleClose()
				queryClient.refetchQueries({
					queryKey: ['allSites'],
				})

				toast(data?.message ?? 'Site Created')
			} catch (err: any) {
				toast(err.response?.data?.messageToUser)
			}
		},
		[artisanProId, close]
	)
	const handleEditSite = useCallback(
		async (_formValues: any) => {},
		[artisanProId, close]
	)

	useEffect(() => {
		if (isEdit) return
		getCurrentLocation()
	}, [getCurrentLocation, isEdit])
	return (
		<StyleContainer>
			<Stack className='header'>
				<Stack
					direction='row'
					alignItems='center'
					width='100%'
					justifyContent='space-between'>
					<Typography variant='h4'>
						{isEdit ? 'Edit Site' : 'Add Site'}
					</Typography>
					<IconButton onClick={handleClose}>
						<Close />
					</IconButton>
				</Stack>
			</Stack>
			<Stack
				className='container'
				component='form'
				onSubmit={!isEdit ? handleSubmit(handleAddSite) : handleEditSite}
				justifyContent='space-between'>
				<Stack gap={theme.spacing(2.5)} justifyContent='space-between'>
					<FormControl fullWidth className='formcontrol'>
						<Typography className='label' variant='subtitle1'>
							Name
						</Typography>
						<TextField
							id='name'
							placeholder='Site Name'
							variant='outlined'
							{...register('name')}
							error={!!errors?.name}
							helperText={errors?.name?.message}
						/>
					</FormControl>

					<FormControl fullWidth className='formcontrol'>
						<Typography className='label' variant='subtitle1'>
							Location
						</Typography>
						<TextField
							id='location'
							error={!!errors?.location}
							helperText={errors?.location?.message}
							placeholder='Location'
							variant='outlined'
							{...register('location')}
						/>
					</FormControl>
					<Stack flexDirection='row' gap={2}>
						<FormControl fullWidth className='formcontrol'>
							<Typography className='label' variant='subtitle1'>
								Longitude
							</Typography>
							<TextField
								id='longitude'
								placeholder='longitude'
								error={!!errors?.longitude}
								helperText={errors?.longitude?.message}
								variant='outlined'
								{...register('longitude')}
							/>
						</FormControl>
						<FormControl fullWidth className='formcontrol'>
							<Typography className='label' variant='subtitle1'>
								Latitude
							</Typography>
							<TextField
								id='latitude'
								placeholder='latitude'
								variant='outlined'
								error={!!errors?.latitude}
								helperText={errors?.latitude?.message}
								{...register('latitude')}
							/>
						</FormControl>
					</Stack>
					<Stack>
						<GoogleMapsWithNonDraggableMarker
							center={{
								lat: Number(watch('latitude')),
								lng: Number(watch('longitude')),
							}}
							zoom={8}
							setMapCenter={setMapCenter}
							mapContainerStyle={{
								width: '100%',
								height: 300,
								position: 'relative',
							}}
						/>
					</Stack>
				</Stack>
				<Stack
					direction='row'
					gap={2}
					justifyContent='space-between'
					className='buttonContainer'>
					<Button sx={{ border: ` 1px solid ${theme.palette.primary.main}` }}>
						Cancel
					</Button>{' '}
					<Button variant='contained' type='submit'>
						Save
					</Button>
				</Stack>
			</Stack>
		</StyleContainer>
	)
}

const StyleContainer = styled(Stack)(({ theme }) => ({
	height: '100%',
	gap: theme.spacing(2),
	'.header': {
		flexDirection: 'row',
		justifyContent: 'space-between',
		alignItems: 'center',
		padding: theme.spacing(3, 2),
		borderBottom: `${theme.spacing(0.125)} solid ${
			theme.palette.neutral['100']
		}`,
	},
	'.container': {
		padding: theme.spacing(2, 4),
		height: '100%',
		// overflowY: 'scroll',
		gap: theme.spacing(5),
		'.formcontrol': {
			gap: theme.spacing(0.6),

			'.label': {
				color: theme.palette.neutral[500],
			},
		},
		'.buttonContainer button': {
			width: theme.spacing(30),
			height: theme.spacing(4.5),
			padding: theme.spacing(1, 2.5),
		},
	},
}))
