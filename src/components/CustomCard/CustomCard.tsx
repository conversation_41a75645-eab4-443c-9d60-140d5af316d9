import { Card, CardProps, styled } from '@mui/material'
import { FC, ReactNode } from 'react'

interface CustomCardProps extends CardProps {
	headerComponent?: ReactNode
}

export const CustomCard: FC<CustomCardProps> = ({
	headerComponent = null,
	...props
}) => {
	return (
		<StyledCard {...props} elevation={0}>
			{headerComponent}
		</StyledCard>
	)
}

const StyledCard = styled(Card)(({ theme }) => ({
	width: '100%',
	borderRadius: theme.spacing(2),
	alignItems: 'center',
	padding: theme.spacing(2),
	border: `${theme.spacing(0.125)} solid ${theme.palette.neutral['100']}`,
}))
