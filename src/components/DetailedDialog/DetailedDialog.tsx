import * as React from 'react'
import Button from '@mui/material/Button'
import { styled } from '@mui/material/styles'
import Dialog from '@mui/material/Dialog'
import DialogTitle from '@mui/material/DialogTitle'
import DialogContent from '@mui/material/DialogContent'
import DialogActions from '@mui/material/DialogActions'
import IconButton from '@mui/material/IconButton'
import CloseIcon from '@mui/icons-material/Close'
import { Typography } from '@mui/material'
const BootstrapDialog = styled(Dialog)(({ theme }) => ({
	'& .MuiDialogContent-root': {
		padding: theme.spacing(2),
	},
	'& .MuiDialogActions-root': {
		padding: theme.spacing(1),
	},
}))

export const DetailedDialog = ({
	open,
	close,
	title,
	children,
}: {
	open: boolean
	close: () => void
	title: string
	children: React.ReactNode
}) => {
	return (
		<BootstrapDialog
			onClose={close}
			aria-labelledby='customized-dialog-title'
			open={open}
			maxWidth={'sm'}
			fullWidth>
			<DialogTitle sx={{ m: 0, p: 2 }} id='customized-dialog-title'>
				<Typography variant='h4' textAlign={'center'}>
					{title}
				</Typography>
			</DialogTitle>
			<IconButton
				aria-label='close'
				onClick={close}
				sx={(theme) => ({
					position: 'absolute',
					right: 8,
					top: 8,
					color: theme.palette.grey[500],
				})}>
				<CloseIcon />
			</IconButton>
			<DialogContent dividers>{children}</DialogContent>
			<DialogActions>
				<Button autoFocus onClick={close}>
					Close
				</Button>
			</DialogActions>
		</BootstrapDialog>
	)
}
