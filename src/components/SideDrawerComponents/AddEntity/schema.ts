import * as Yup from 'yup'

export const addBiomassAggregatorSchema = (editMode: boolean) =>
	Yup.object({
		name: Yup.string().required('Please enter name'),
		dummyBiomassAggregator: Yup.object()
			.shape({
				id: Yup.string(),
				name: Yup.string().optional(),
				locationName: Yup.string().optional(),
				shortName: Yup.string().optional(),
				csinkManagerId: Yup.string().optional(),
			})
			.nullable()
			.optional(),
		shortCode: Yup.string().when('dummyBiomassAggregator.id', {
			is: (id: Yup.Schema) => id != null, // If the id is not null
			then: (schema) => schema.notRequired(), // Make shortCode not required if id is present
			otherwise: (schema) =>
				schema
					.required('Please enter your BA-Short code') // Make shortCode required if id is null
					.matches(/^[a-zA-Z0-9]+$/, 'Only alphabets and numbers are allowed')
					.max(4, 'Max Limit Reached'),
		}),
		createManager: Yup.boolean(),
		managerDetails: Yup.object()
			.shape({
				name: Yup.string().required('Enter Manager Name'),
				email: Yup.string()
					.required('Enter Manager Email')
					.email('Enter valid Email'),
				phoneNo: Yup.string().nullable(),
				countryCode: Yup.string().nullable(),
				trained: Yup.string()
					.required('Enter Manager if Trained')
					.oneOf(['yes', 'no']),
				profileImageId: Yup.object()
					.shape({
						id: Yup.string(),
						url: Yup.string(),
						fileName: Yup.string().nullable(),
					})
					.nullable(),
				trainingImages: Yup.array()
					.of(
						Yup.object().shape({
							id: Yup.string(),
							url: Yup.string(),
							fileName: Yup.string().nullable(),
						})
					)
					.when('trained', {
						is: (trained: string) => {
							return trained === 'yes'
						},
						then: (DocYep) =>
							DocYep.required('Please Upload Training Document').min(
								1,
								'Please upload at least one training image'
							),
						otherwise: (DocYep) => DocYep.optional(),
					}),
			})
			.nullable()
			.when('createManager', {
				is: (val: boolean) => {
					return val
				},
				then: (managerDetailsYep) => managerDetailsYep.required(),
				otherwise: (managerDetailsYup) => managerDetailsYup.optional(),
			}),
		latitude: Yup.number().required('Please enter coordinates'),
		longitude: Yup.number().required('Please enter coordinates'),
		locationName: Yup.string().required('Please enter location'),
		cSinkManagerId: Yup.string().when([], {
			is: () => !editMode,
			then: (schema) => schema.required('Please select csink manager'),
			otherwise: (schema) => schema,
		}),
	})

export const addCSinkManagerSchema = Yup.object({
	name: Yup.string().required('Please enter name'),
	shortCode: Yup.string()
		.required('Please enter your Short code')
		.matches(/^[a-zA-Z0-9]+$/, 'Only alphabets and numbers are allowed')
		.max(4, 'Max Limit Reached'),
	createEntityOnly: Yup.boolean(),
	managerDetails: Yup.object()
		.shape({
			name: Yup.string().required('Enter Manager Name'),
			email: Yup.string()
				.required('Enter Manager Email')
				.email('Enter valid Email'),
			phoneNo: Yup.string().nullable(),
			countryCode: Yup.string().nullable(),
			trained: Yup.string()
				.required('Enter if manager is Trained')
				.oneOf(['yes', 'no']),
			profileImageId: Yup.object()
				.shape({
					id: Yup.string(),
					url: Yup.string(),
					fileName: Yup.string().nullable(),
				})
				.nullable(),
			trainingImages: Yup.array()
				.of(
					Yup.object().shape({
						id: Yup.string(),
						url: Yup.string(),
						fileName: Yup.string().nullable(),
					})
				)
				.when('trained', {
					is: (trained: string) => {
						return trained === 'yes'
					},
					then: (DocYep) =>
						DocYep.required('Please Upload Training Document').min(
							1,
							'Please upload at least one training image'
						),
					otherwise: (DocYep) => DocYep.optional(),
				}),
		})
		.nullable()
		.when('createEntityOnly', {
			is: (val: boolean) => {
				return val === false
			},
			then: (managerDetailsYep) => managerDetailsYep.required(),
			otherwise: (managerDetailsYup) => managerDetailsYup.optional(),
		}),
	latitude: Yup.number(),
	longitude: Yup.number(),
	// cropId: Yup.string().nullable(),
	cropIds: Yup.array().nullable(),
	locationName: Yup.string(),
})

export const addAPNSchema = () => {
	return Yup.object({
		name: Yup.string().required('Please enter name'),
		address: Yup.string().required('Please enter location'),
		biomassAggregatorId: Yup.string().nullable().optional(),
		createManager: Yup.boolean(),
		isEditMode: Yup.boolean(),
		managerDetails: Yup.object()
			.shape({
				name: Yup.string().required('Enter Manager Name'),
				email: Yup.string()
					.required('Enter Manager Email')
					.email('Enter valid Email'),
				phoneNo: Yup.string().nullable(),
				countryCode: Yup.string().nullable(),
				trained: Yup.string()
					.required('Enter Manager if Trained')
					.oneOf(['yes', 'no']),
				profileImageId: Yup.object()
					.shape({
						id: Yup.string(),
						url: Yup.string(),
						fileName: Yup.string().nullable(),
					})
					.nullable(),
				trainingImages: Yup.array()
					.of(
						Yup.object().shape({
							id: Yup.string(),
							url: Yup.string(),
							fileName: Yup.string().nullable(),
						})
					)
					.when('trained', {
						is: (trained: string) => {
							return trained === 'yes'
						},
						then: (DocYep) =>
							DocYep.required('Please Upload Training Document').min(
								1,
								'Please upload at least one training image'
							),
						otherwise: (DocYep) => DocYep.optional(),
					}),
			})
			.nullable()
			.when('createManager', {
				is: (val: boolean) => {
					return val
				},
				then: (managerDetailsYep) => managerDetailsYep.required(),
				otherwise: (managerDetailsYup) => managerDetailsYup.optional(),
			}),
		dummyArtisanProNetwork: Yup.object()
			.shape({
				id: Yup.string(),
				name: Yup.string().optional(),
				locationName: Yup.string().optional(),
				shortName: Yup.string().optional(),
				csinkManagerId: Yup.string().optional(),
			})
			.nullable()
			.optional(),
		cSinkManagerId: Yup.string().when('isEditMode', {
			is: true,
			then: (schema) => schema.optional(),
			otherwise: (schema) => schema.required('Please choose one'),
		}),
	})
}

export const addCSinkNetwork = (addDummyCsinkManagers: boolean) => {
	return Yup.object({
		biomassAggregator: Yup.string().when(['isEditMode'], {
			is: (isEditMode: boolean) => isEditMode || addDummyCsinkManagers,
			then: (schema) => schema.notRequired(),
			otherwise: (schema) => schema.required('Please choose one'),
		}),
		name: Yup.string().required('Please enter name'),
		isEditMode: Yup.boolean(),
		latitude: Yup.number().required('Please enter coordinates'),
		longitude: Yup.number().required('Please enter coordinates'),
		locationName: Yup.string().required('Please enter location'),
		methaneCompensateStrategies: Yup.array()
			.of(
				Yup.object().shape({
					biomassId: Yup.string().nullable(),
					methaneCompensateType: Yup.string().nullable(),
					description: Yup.string().nullable(),
					compensateType: Yup.string().nullable(),
					documentIds: Yup.array()
						.of(
							Yup.object().shape({
								id: Yup.string(),
								url: Yup.string(),
								fileName: Yup.string().nullable(),
							})
						)
						.nullable(),
				})
			)
			.optional(),
		createManager: Yup.boolean(),
		managerDetails: Yup.object()
			.shape({
				name: Yup.string().required('Enter Manager Name'),
				email: Yup.string()
					.required('Enter Manager Email')
					.email('Enter valid Email'),
				phoneNo: Yup.string().nullable(),
				countryCode: Yup.string().nullable(),
				trained: Yup.string()
					.required('Enter Manager if Trained')
					.oneOf(['yes', 'no']),
				profileImageId: Yup.object()
					.shape({
						id: Yup.string(),
						url: Yup.string(),
						fileName: Yup.string().nullable(),
					})
					.nullable(),
				trainingImages: Yup.array()
					.of(
						Yup.object().shape({
							id: Yup.string(),
							url: Yup.string(),
							fileName: Yup.string().nullable(),
						})
					)
					.when('trained', {
						is: (trained: string) => {
							return trained === 'yes'
						},
						then: (DocYep) =>
							DocYep.required('Please Upload Training Document').min(
								1,
								'Please upload at least one training image'
							),
						otherwise: (DocYep) => DocYep.optional(),
					}),
			})
			.nullable()
			.when('createManager', {
				is: (val: boolean) => {
					return val
				},
				then: (managerDetailsYep) => managerDetailsYep.required(),
				otherwise: (managerDetailsYup) => managerDetailsYup.optional(),
			}),
		bighaInHectare: Yup.number()
			.required('Please enter bigha')
			.typeError('Please enter bigha'),
		dryingType: Yup.string().nullable(),
		dryingStrategy: Yup.string().nullable(),
		dryingStrategyMedia: Yup.array()
			.of(
				Yup.object().shape({
					id: Yup.string(),
					url: Yup.string(),
				})
			)
			.nullable(),
		shreddingType: Yup.string().nullable(),
		shreddingStrategy: Yup.string().nullable(),
		shreddingStrategyMedia: Yup.array()
			.of(
				Yup.object().shape({
					id: Yup.string(),
					url: Yup.string(),
				})
			)
			.nullable(),
		cSinkManagerId: Yup.string().when('isEditMode', {
			is: true,
			then: (schema) => schema.optional(),
			otherwise: (schema) => schema.required('Please choose one'),
		}),
	})
}

export const addArtisanPro = (addDummyCsinkManagers: boolean) => {
	return Yup.object({
		address: Yup.string().required('Please enter location'),
		name: Yup.string().required('Please enter name'),
		bighaInHectare: Yup.number()
			.required('Please enter bigha')
			.typeError('Please enter bigha'),
		isEditMode: Yup.boolean(),
		apnId: Yup.string().when(['isEditMode'], {
			is: (isEditMode: boolean) => isEditMode || addDummyCsinkManagers,
			then: (schema) => schema.notRequired(),
			otherwise: (schema) => schema.required('Please choose one'),
		}),
		dryingType: Yup.string().nullable(),
		dryingStrategy: Yup.string().nullable(),
		dryingStrategyMedia: Yup.array()
			.of(
				Yup.object().shape({
					id: Yup.string(),
					url: Yup.string(),
				})
			)
			.nullable(),
		shreddingType: Yup.string().nullable(),
		shreddingStrategy: Yup.string().nullable(),
		shreddingStrategyMedia: Yup.array().of(
			Yup.object().shape({
				id: Yup.string(),
				url: Yup.string(),
			})
		),
		createManager: Yup.boolean(),
		managerDetails: Yup.object()
			.shape({
				name: Yup.string().required('Enter Manager Name'),
				email: Yup.string()
					.required('Enter Manager Email')
					.email('Enter valid Email'),
				phoneNo: Yup.string().nullable(),
				countryCode: Yup.string().nullable(),
				trained: Yup.string()
					.required('Enter Manager if Trained')
					.oneOf(['yes', 'no']),
				profileImageId: Yup.object()
					.shape({
						id: Yup.string(),
						url: Yup.string(),
						fileName: Yup.string().nullable(),
					})
					.nullable(),
				trainingImages: Yup.array()
					.of(
						Yup.object().shape({
							id: Yup.string(),
							url: Yup.string(),
							fileName: Yup.string().nullable(),
						})
					)
					.when('trained', {
						is: (trained: string) => {
							return trained === 'yes'
						},
						then: (DocYep) =>
							DocYep.required('Please Upload Training Document').min(
								1,
								'Please upload at least one training image'
							),
						otherwise: (DocYep) => DocYep.optional(),
					}),
			})
			.nullable()
			.when('createManager', {
				is: (val: boolean) => {
					return val
				},
				then: (managerDetailsYep) => managerDetailsYep.required(),
				otherwise: (managerDetailsYup) => managerDetailsYup.optional(),
			}),
		methaneCompensateStrategies: Yup.array()
			.of(
				Yup.object().shape({
					biomassId: Yup.string().nullable(),
					methaneCompensateType: Yup.string().nullable(),
					description: Yup.string().nullable(),
					compensateType: Yup.string().nullable(),
					documentIds: Yup.array()
						.of(
							Yup.object().shape({
								id: Yup.string(),
								url: Yup.string(),
							})
						)
						.nullable(),
				})
			)
			.optional(),
		cSinkManagerId: Yup.string().when('isEditMode', {
			is: true,
			then: (schema) => schema.optional(),
			otherwise: (schema) => schema.required('Please choose one'),
		}),
		latitude: Yup.number().required('Please enter coordinates'),
		longitude: Yup.number().required('Please enter coordinates'),
	})
}

export type TAddBiomassAggregator = Yup.InferType<
	ReturnType<typeof addBiomassAggregatorSchema>
>
export type TAddCSinkManager = Yup.InferType<typeof addCSinkManagerSchema>
export type TAddAPN = Yup.InferType<ReturnType<typeof addAPNSchema>>
export type TAddCSinkNetwork = Yup.InferType<ReturnType<typeof addCSinkNetwork>>
export type TAddArtisanPro = Yup.InferType<ReturnType<typeof addArtisanPro>>

export type TCustomCsinkManager = {
	id: string
	name: string
	shortName: string
	dummyBiomassAggregator: TDummyBiomassAggregator
}
export type TCustomArtisanProNetwork = {
	id: string
	name: string
	shortName: string
	dummyArtisanProNetwork: TDummyBiomassAggregator
}

export type TDummyBiomassAggregator = {
	id: string
	name: string
	locationName: string
	shortName: string
	csinkManagerId: string
}
