import { FC, useCallback, useEffect, useMemo } from 'react'
import { FormProvider, useForm } from 'react-hook-form'
import { addAPNSchema, TAddAPN, TCustomArtisanProNetwork } from '../schema'
import { yupResolver } from '@hookform/resolvers/yup'

import {
	Autocomplete,
	Button,
	Stack,
	Typography,
	useTheme,
} from '@mui/material'
import { LoadingButton } from '@mui/lab'
import {
	useMutation,
	useQuery,
	useQueryClient,
	UseQueryResult,
} from '@tanstack/react-query'
import { authAxios, useAuthContext } from '@/contexts'
import { toast } from 'react-toastify'
import { userRoles } from '@/utils/constant'
import { AxiosError } from 'axios'
import { Add, Remove } from '@mui/icons-material'
import { AddManagerDetailsFields } from '@/components/AddManagerDetails'
import { CustomTextField } from '@/utils/components'

type TProps = {
	handleCloseDrawer: () => void
	editMode?: boolean
	apnId?: string
	csinkManagersList?: UseQueryResult<
		{
			label: string
			value: string
		}[],
		Error
	>
}

const initialValues = {
	biomassAggregatorId: '',
	name: '',
	address: '',
	managerDetails: null,
	createManager: false,
	isEditMode: false,
	cSinkManagerId: '',
	dummyArtisanProNetwork: null,
}

export const AddAPN: FC<TProps> = ({
	handleCloseDrawer,
	editMode,
	apnId,
	csinkManagersList,
}) => {
	const { userDetails } = useAuthContext()
	const theme = useTheme()
	const queryClient = useQueryClient()

	const form = useForm<TAddAPN>({
		defaultValues: initialValues,
		mode: 'all',
		resolver: yupResolver<TAddAPN>(addAPNSchema()),
	})
	const {
		register,
		formState: { errors },
		setValue,
		handleSubmit,
		clearErrors,
		watch,
	} = form

	const handleAddManagerDetails = useCallback(() => {
		setValue('createManager', true)
		setValue('managerDetails', {
			name: '',
			email: '',
			phoneNo: null,
			countryCode: null,
			trained: 'no',
			profileImageId: null,
			trainingImages: [],
		})
	}, [setValue])

	const handleRemoveManagerDetails = useCallback(() => {
		setValue('createManager', false)
		setValue('managerDetails', null)
	}, [setValue])

	const AllBaQuery = useQuery({
		queryKey: ['allBA', watch('cSinkManagerId')],
		queryFn: () => {
			const queryParams = new URLSearchParams({
				limit: '1000',
				page: '0',
			})
			const data = authAxios.get<{
				biomassAggregators: TCustomArtisanProNetwork[]
			}>(
				`/csink-manager/${watch(
					'cSinkManagerId'
				)}/biomass-aggregators?${queryParams.toString()}`
			)
			return data
		},
		select: ({ data }) =>
			data?.biomassAggregators?.map((item) => ({
				label: `${item.name} (${item.shortName})`,
				value: item.id,
				dummyArtisanProNetwork: item?.dummyArtisanProNetwork,
			})),
		enabled:
			!!watch('cSinkManagerId') &&
			[userRoles.Admin, userRoles.CsinkManager].includes(
				userDetails?.accountType as userRoles
			),
	})

	const addAPN = useMutation({
		mutationKey: ['addAPN'],
		mutationFn: async (payload: TAddAPN) => {
			const URI = `/artisan-pro-network`

			const newPayload = {
				...payload,
				manager: payload?.createManager
					? {
							...payload?.managerDetails,
							trained: payload?.managerDetails?.trained === 'yes',
							profileImageId: payload?.managerDetails?.profileImageId?.id,
							trainingImages:
								payload?.managerDetails?.trainingImages?.map(({ id }) => id) ??
								[],
					  }
					: null,
				dummyArtisanProNetwork: undefined,
			}
			return await authAxios.post(URI, newPayload)
		},
		onError: (error: AxiosError) => {
			toast((error?.response?.data as { messageToUser: string }).messageToUser)
		},
		onSuccess: ({ data }) => {
			toast(data?.message)
			queryClient.refetchQueries({ queryKey: ['allAPN'] })
			handleCloseDrawer()
		},
	})
	const addDummyAPN = useMutation({
		mutationKey: ['addDummyAPN'],
		mutationFn: async (payload: TAddAPN) => {
			const URI = `/artisan-pro-network/${payload?.dummyArtisanProNetwork?.id}`
			const newPayload = {
				...payload,
				manager: payload?.createManager
					? {
							...payload?.managerDetails,
							trained: payload?.managerDetails?.trained === 'yes',
							profileImageId: payload?.managerDetails?.profileImageId?.id,
							trainingImages:
								payload?.managerDetails?.trainingImages?.map(({ id }) => id) ??
								[],
					  }
					: null,
				isDummy: false,
				dummyArtisanProNetwork: undefined,
			}
			return await authAxios.put(URI, newPayload)
		},
		onError: (error: AxiosError) => {
			toast((error?.response?.data as { messageToUser: string }).messageToUser)
		},
		onSuccess: ({ data }) => {
			toast(data?.message)
			queryClient.refetchQueries({ queryKey: ['allAPN'] })
			queryClient.refetchQueries({ queryKey: ['entityTabsCountQuery'] })
			handleCloseDrawer()
		},
	})

	useQuery({
		queryKey: ['artisan-pro-network-details', apnId],
		queryFn: async () => {
			try {
				const { data } = await authAxios.get(`/artisan-pro-network/${apnId}`)
				setValue('name', data?.name)
				setValue('address', data?.address)
				setValue('biomassAggregatorId', data?.biomassAggregatorID)

				return data
			} catch (error: any) {
				toast(error?.response?.data?.messageToUser)
				return null
			}
		},
		enabled: editMode,
	})

	const handleAddAPN = useCallback(
		(values: TAddAPN) => {
			const newPayload = { ...values, dummyBiomassAggregator: undefined }
			if (
				values?.dummyArtisanProNetwork !== null &&
				(userDetails?.accountType === userRoles.Admin ||
					userDetails?.accountType === userRoles.CsinkManager)
			) {
				addDummyAPN.mutate(values)
			} else {
				addAPN.mutate(newPayload)
			}
		},
		[addAPN, addDummyAPN, userDetails?.accountType]
	)

	const editAPN = useMutation({
		mutationKey: ['editAPN'],
		mutationFn: async (payload: TAddAPN) => {
			const updatedPayload = {
				name: payload?.name,
				address: payload?.address,
				biomassAggregatorId: payload?.biomassAggregatorId,
			}
			return await authAxios.put(
				`/artisan-pro-network/${apnId}`,
				updatedPayload
			)
		},
		onError: (err: any) => {
			toast(err?.response?.data?.userToMessage)
		},
		onSuccess: ({ data }) => {
			toast(data?.message)
			queryClient.refetchQueries({ queryKey: ['allAPN'] })
			handleCloseDrawer()
		},
	})

	useEffect(() => {
		if (editMode) setValue('isEditMode', true)
	}, [editMode, setValue])

	const handleEditAPN = useCallback(
		(values: TAddAPN) => {
			editAPN.mutate(values)
		},
		[editAPN]
	)

	const handleSave = useCallback(
		(values: TAddAPN) =>
			editMode ? handleEditAPN(values) : handleAddAPN(values),
		[editMode, handleAddAPN, handleEditAPN]
	)

	const isLoading = useMemo(
		() => editAPN?.isPending || addAPN.isPending,
		[addAPN.isPending, editAPN?.isPending]
	)

	useEffect(() => {
		if (userDetails?.accountType === userRoles.BiomassAggregator) {
			setValue('biomassAggregatorId', userDetails?.biomassAggregatorId)
		}
		if (
			userDetails?.accountType === userRoles.CsinkManager ||
			userDetails?.accountType === userRoles.BiomassAggregator
		) {
			setValue('cSinkManagerId', userDetails?.csinkManagerId ?? '')
		}
	}, [
		setValue,
		userDetails?.accountType,
		userDetails?.biomassAggregatorId,
		userDetails?.csinkManagerId,
	])

	useEffect(() => {
		if (AllBaQuery.isSuccess && AllBaQuery.data?.length === 1) {
			const defaultBiomassAggregator = AllBaQuery.data[0]
			setValue('biomassAggregatorId', defaultBiomassAggregator.value)
			setValue(
				'dummyArtisanProNetwork',
				defaultBiomassAggregator?.dummyArtisanProNetwork
			)
		}
	}, [AllBaQuery.isSuccess, AllBaQuery.data, setValue])

	return (
		<FormProvider {...form}>
			<Stack gap={5}>
				{[
					userRoles.Admin,
					userRoles.CsinkManager,
					userRoles.BiomassAggregator,
				].includes(userDetails?.accountType as userRoles) && !editMode ? (
					<Autocomplete
						value={
							csinkManagersList?.data?.find(
								(item) => item.value === watch('cSinkManagerId')
							) ?? null
						}
						onChange={(_, newValue: any) => {
							setValue('cSinkManagerId', newValue?.value)
							clearErrors('cSinkManagerId')
							setValue('biomassAggregatorId', '')
							clearErrors('biomassAggregatorId')
							setValue('dummyArtisanProNetwork', null)
						}}
						disabled={
							userDetails?.accountType === userRoles.CsinkManager ||
							userDetails?.accountType === userRoles.BiomassAggregator
						}
						options={csinkManagersList?.data || []}
						renderInput={(params) => (
							<CustomTextField
								schema={addAPNSchema()}
								name='cSinkManagerId'
								{...params}
								label='Csink Manager'
								error={!!errors?.cSinkManagerId?.message}
								helperText={errors?.cSinkManagerId?.message}
							/>
						)}
					/>
				) : null}

				{[userRoles.Admin, userRoles.CsinkManager].includes(
					userDetails?.accountType as userRoles
				) && !editMode ? (
					<Autocomplete
						options={AllBaQuery.data || []}
						getOptionLabel={(option) => option.label}
						disabled={watch('cSinkManagerId') === ''}
						value={
							AllBaQuery.data?.find(
								(item) => item.value === watch('biomassAggregatorId')
							) || null
						}
						renderInput={(params) => (
							<CustomTextField
								schema={addAPNSchema()}
								{...params}
								label='Select BA'
								name='biomassAggregatorId'
								error={!!errors.biomassAggregatorId?.message}
								helperText={errors.biomassAggregatorId?.message}
							/>
						)}
						onChange={(_, selectedOption) => {
							if (selectedOption) {
								setValue('biomassAggregatorId', selectedOption.value)
								clearErrors('biomassAggregatorId')
								if (selectedOption?.dummyArtisanProNetwork !== null) {
									setValue(
										'dummyArtisanProNetwork',
										selectedOption?.dummyArtisanProNetwork
									)
								} else {
									setValue('dummyArtisanProNetwork', null)
								}
							} else {
								setValue('biomassAggregatorId', '')
							}
						}}
					/>
				) : null}
				<CustomTextField
					schema={addAPNSchema()}
					fullWidth
					id='name'
					type='text'
					label='Name'
					variant='outlined'
					{...register('name')}
					error={!!errors.name?.message}
					helperText={errors?.name?.message}
					InputLabelProps={
						editMode
							? {
									shrink: editMode,
							  }
							: {}
					}
				/>
				<CustomTextField
					schema={addAPNSchema()}
					id='address'
					label='Location'
					variant='outlined'
					autoComplete='off'
					fullWidth
					error={!!errors.address?.message}
					helperText={errors?.address?.message}
					{...register('address')}
					InputLabelProps={
						editMode
							? {
									shrink: editMode,
							  }
							: {}
					}
				/>
				{!editMode ? (
					watch('createManager') ? (
						<Stack gap={2}>
							<Button
								variant='text'
								sx={{
									justifyContent: 'flex-start',
								}}
								onClick={handleRemoveManagerDetails}
								startIcon={<Remove color='primary' />}>
								Remove Manager Details
							</Button>
							<Typography variant='body2' pb={theme.spacing(1)}>
								Manager Details:
							</Typography>
							<AddManagerDetailsFields
								editMode={editMode}
								schema={addAPNSchema()}
							/>
						</Stack>
					) : (
						<Button
							variant='text'
							sx={{
								justifyContent: 'flex-start',
							}}
							onClick={handleAddManagerDetails}
							startIcon={<Add color='primary' />}>
							Add Manager Details
						</Button>
					)
				) : null}
				<Stack
					direction='row'
					justifyContent='space-between'
					className='buttonContainer'>
					<Button
						onClick={handleCloseDrawer}
						sx={{ border: ` 1px solid ${theme.palette.primary.main}` }}>
						Cancel
					</Button>{' '}
					<LoadingButton
						loading={isLoading}
						disabled={isLoading}
						onClick={handleSubmit(handleSave)}
						variant='contained'>
						{editMode ? 'Save' : 'Add'}
					</LoadingButton>
				</Stack>
			</Stack>
		</FormProvider>
	)
}
