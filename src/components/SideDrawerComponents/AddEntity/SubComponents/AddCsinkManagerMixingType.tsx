import {
	Box,
	Checkbox,
	IconButton,
	Stack,
	styled,
	Typography,
} from '@mui/material'
import { useState } from 'react'
import { LoadingButton } from '@mui/lab'
import CheckboxIcon from '@/assets/icons/checkboxIcon.svg'
import { CsinkManager, GetPackagingTypeResponse } from '@/interfaces'
import { Close } from '@mui/icons-material'
import { useMutation, useQuery, useQueryClient } from '@tanstack/react-query'
import { authAxios } from '@/contexts'
import { toast } from 'react-toastify'
import { AxiosError } from 'axios'
import { NoData } from '@/components/NoData'

interface TProps {
	handleCloseDrawer: () => void
	subheading?: string
	mixingTypeData?: CsinkManager['mixingTypes']
	csinkMangerId: string
}

export const AddCsinkManagerMixingType = ({
	handleCloseDrawer,
	subheading,
	mixingTypeData,
	csinkMangerId,
}: TProps) => {
	const queryClient = useQueryClient()
	const data =
		mixingTypeData
			?.map((item) => {
				if (!item?.id) return ''
				return item?.id
			})
			.filter((item) => !!item) ?? []

	const [selectedMixingType, setSelectedMixingType] = useState<string[]>(data)

	const fetchAllMixingTypeQuery = useQuery({
		queryKey: ['fetchAllMixingTypeQuery'],
		queryFn: () => {
			const URI = `/packaging-types `

			return authAxios<GetPackagingTypeResponse>(URI)
		},
		select: ({ data }) => {
			return data
		},
	})
	const addMixingType = useMutation({
		mutationKey: ['addMixingType', selectedMixingType],
		mutationFn: async () => {
			const payload = {
				mixingTypeIds: selectedMixingType,
			}
			const api = `/csink-manager/${csinkMangerId}/mixing-types/assign`

			return await authAxios.put(api, payload)
		},
		onError: (error: AxiosError) => {
			toast((error?.response?.data as { messageToUser: string })?.messageToUser)
		},
		onSuccess: ({ data }) => {
			toast(data?.message)
			queryClient.refetchQueries({
				queryKey: ['fetchCsinkManagerDetailsQuery'],
			})
			queryClient.refetchQueries({
				queryKey: ['allCsinkManager'],
			})
			handleCloseDrawer()
		},
	})
	return (
		<StyleContainer>
			<Stack className='header'>
				<Stack
					direction='row'
					spacing={1}
					alignItems='center'
					width='100%'
					justifyContent='space-between'>
					<Stack>
						<Typography variant='h5'>Select Mixing Type</Typography>
						<Typography variant='subtitle1'> {subheading}</Typography>
					</Stack>
					<IconButton onClick={handleCloseDrawer}>
						<Close />
					</IconButton>
				</Stack>
			</Stack>
			<Stack className='container' component='form'>
				<Stack direction='column' gap={2} height='72dvh' overflow='auto'>
					{fetchAllMixingTypeQuery?.data?.types?.length ? (
						fetchAllMixingTypeQuery?.data?.types?.map((item) => {
							const isChecked = selectedMixingType.includes(item?.id)
							return (
								<Stack
									key={item?.id}
									alignItems='center'
									direction='row'
									gap={1}>
									<Checkbox
										onClick={() => {
											setSelectedMixingType((p) => {
												if (p.includes(item?.id)) {
													return p.filter((v) => v !== item?.id)
												} else {
													return [...p, item?.id]
												}
											})
										}}
										checked={isChecked}
										checkedIcon={<Box component='img' src={CheckboxIcon} />}
									/>
									<Typography>{item?.name}</Typography>
								</Stack>
							)
						})
					) : (
						<NoData size='small' />
					)}
				</Stack>

				<LoadingButton
					disabled={
						!fetchAllMixingTypeQuery?.data?.types ||
						fetchAllMixingTypeQuery?.data?.types?.length === 0
					}
					className='add_btn'
					onClick={() => addMixingType.mutate()}
					variant='contained'>
					Add
				</LoadingButton>
			</Stack>
		</StyleContainer>
	)
}

const StyleContainer = styled(Stack)(({ theme }) => ({
	gap: theme.spacing(2),
	'.header': {
		flexDirection: 'row',
		justifyContent: 'space-between',
		alignItems: 'center',
		padding: theme.spacing(4.5, 2),
		borderBottom: `${theme.spacing(0.125)} solid ${
			theme.palette.neutral['100']
		}`,
	},

	'.container': {
		padding: theme.spacing(2, 3),
		gap: theme.spacing(2.5),
		justifyContent: 'space-between',
		paddingBottom: theme.spacing(1),
	},
}))
