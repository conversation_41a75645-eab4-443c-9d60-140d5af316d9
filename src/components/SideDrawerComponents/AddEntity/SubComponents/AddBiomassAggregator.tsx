import { FC, useCallback, useEffect, useMemo } from 'react'
import { FormProvider, useForm } from 'react-hook-form'
import {
	addBiomassAggregatorSchema,
	TAddBiomassAggregator,
	TCustomCsinkManager,
	TDummyBiomassAggregator,
} from '../schema'
import { yupResolver } from '@hookform/resolvers/yup'
import {
	Autocomplete,
	Button,
	Stack,
	Typography,
	useTheme,
} from '@mui/material'
import { GoogleMapsWithNonDraggableMarker } from '@/components/GoogleMap'
import { LoadingButton } from '@mui/lab'
import { useMutation, useQuery, useQueryClient } from '@tanstack/react-query'
import { authAxios, useAuthContext } from '@/contexts'
import { toast } from 'react-toastify'
import { IBiomassAggregatorDetailResponse } from '@/interfaces'
import { userRoles } from '@/utils/constant'
import { Nullable } from '@/types'
import { AxiosError } from 'axios'
import { Add, Remove } from '@mui/icons-material'
import { AddManagerDetailsFields } from '@/components/AddManagerDetails'
import { CustomTextField } from '@/utils/components'

type TProps = {
	handleCloseDrawer: () => void
	editMode?: boolean
	baId?: string
}

const initialValues = {
	name: '',
	shortCode: '',
	locationName: '',
	managerDetails: null,
	createManager: false,
	latitude: 0,
	longitude: 0,
	cSinkManagerId: '',
	dummyBiomassAggregator: null,
}

export const AddBiomassAggregator: FC<TProps> = ({
	handleCloseDrawer,
	editMode,
	baId,
}) => {
	const theme = useTheme()
	const queryClient = useQueryClient()
	const { userDetails } = useAuthContext()

	const form = useForm<TAddBiomassAggregator>({
		defaultValues: initialValues,
		mode: 'all',
		resolver: yupResolver<TAddBiomassAggregator>(
			addBiomassAggregatorSchema(editMode || false)
		),
	})
	const {
		register,
		formState: { errors },
		watch,
		setValue,
		handleSubmit,
		clearErrors,
	} = form


	const handleAddManagerDetails = useCallback(() => {
		setValue('createManager', true)
		setValue('managerDetails', {
			name: '',
			email: '',
			phoneNo: null,
			countryCode: null,
			trained: 'no',
			profileImageId: null,
			trainingImages: [],
		})
	}, [setValue])

	const handleRemoveManagerDetails = useCallback(() => {
		setValue('createManager', false)
		setValue('managerDetails', null)
	}, [setValue])

	const setMapCenter = useCallback(
		(lat: number, lng: number) => {
			setValue('latitude', Number(lat.toFixed(6)))
			setValue('longitude', Number(lng.toFixed(6)))
		},
		[setValue]
	)

	const getCurrentLocation = useCallback(() => {
		navigator.geolocation.getCurrentPosition((position) => {
			setMapCenter(position.coords.latitude, position.coords.longitude)
		})
	}, [setMapCenter])

	const addBiomassAggregator = useMutation({
		mutationKey: ['addBiomassAggregator'],
		mutationFn: async (payload: TAddBiomassAggregator) =>
			await authAxios.post('/biomass-aggregator', {
				...payload,
				manager: payload?.createManager
					? {
							...payload?.managerDetails,
							trained: payload?.managerDetails?.trained === 'yes',
							profileImageId: payload?.managerDetails?.profileImageId?.id,
							trainingImages:
								payload?.managerDetails?.trainingImages?.map(({ id }) => id) ??
								[],
					  }
					: null,
			}),
		onError: (error: AxiosError) => {
			toast((error?.response?.data as { messageToUser: string })?.messageToUser)
		},
		onSuccess: ({ data }) => {
			toast(data?.message)
			queryClient.refetchQueries({ queryKey: ['allBA'] })
			queryClient.refetchQueries({ queryKey: ['entityTabsCountQuery'] })
			handleCloseDrawer()
		},
	})

	const handleAddBiomassAggregator = useCallback(
		async (values: TAddBiomassAggregator) => {
			addBiomassAggregator.mutate(values)
		},
		[addBiomassAggregator]
	)

	const fetchCsinkManager = useQuery({
		queryKey: ['allCsinkManager'],
		queryFn: () => {
			const queryParams = new URLSearchParams({
				limit: '1000',
				page: '0',
			})
			return authAxios.get<{
				csinkManagers: TCustomCsinkManager[]
			}>(`/csink-manager?${queryParams.toString()}`)
		},
		select: ({ data }) =>
			data?.csinkManagers?.map((item) => ({
				label: `${item.name} (${item.shortName})`,
				value: item.id,
				dummyBiomassAggregator: item?.dummyBiomassAggregator,
			})),
		enabled:
			!!userDetails?.accountType &&
			[userRoles.Admin, userRoles.CsinkManager].includes(
				userDetails?.accountType as userRoles
			),
	})

	useQuery({
		queryKey: ['biomassAggregatorDetail', baId],
		queryFn: async () => {
			try {
				const { data } = await authAxios.get<IBiomassAggregatorDetailResponse>(
					`/biomass-aggregator/${baId}`
				)
				const [latitude, longitude] = data.location.slice(1, -1).split(',')
				setValue('name', data?.name)
				setValue('locationName', data?.locationName)
				setValue('shortCode', data?.shortName)
				setValue('latitude', Number(latitude || 0))
				setValue('longitude', Number(longitude || 0))
				return data
			} catch (error: any) {
				toast(error?.response?.data?.messageToUser)
				return null
			}
		},
		enabled: editMode,
	})

	const UpdateDummyBiomassAggregator = useMutation({
		mutationKey: ['UpdateDummyBiomassAggregator'],
		mutationFn: async (payload: TAddBiomassAggregator) =>
			await authAxios.put(
				`/biomass-aggregator/${payload?.dummyBiomassAggregator?.id}/v2`,
				{
					...payload,
					manager: payload?.createManager
						? {
								...payload?.managerDetails,
								trained: payload?.managerDetails?.trained === 'yes',
								profileImageId: payload?.managerDetails?.profileImageId?.id,
								trainingImages:
									payload?.managerDetails?.trainingImages?.map(
										({ id }) => id
									) ?? [],
						  }
						: null,
					isDummy: false,
					shortCode: undefined,
					dummyBiomassAggregator: undefined,
				}
			),
		onError: (error: AxiosError) => {
			toast((error?.response?.data as { messageToUser: string })?.messageToUser)
		},
		onSuccess: ({ data }) => {
			toast(data?.message)
			queryClient.refetchQueries({ queryKey: ['allBA'] })
			queryClient.refetchQueries({ queryKey: ['entityTabsCountQuery'] })
			handleCloseDrawer()
		},
	})

	const handleUpdateBiomassAggregator = useCallback(
		(values: TAddBiomassAggregator) => {
			UpdateDummyBiomassAggregator.mutate(values)
		},
		[UpdateDummyBiomassAggregator]
	)
	const editBiomassAggregator = useMutation({
		mutationKey: ['editBiomassAggregator'],
		mutationFn: async (payload: TAddBiomassAggregator) =>
			await authAxios.put(`/biomass-aggregator/${baId}/v2`, {
				...payload,
				manager: payload?.createManager
					? {
							...payload?.managerDetails,
							trained: payload?.managerDetails?.trained === 'yes',
							profileImageId: payload?.managerDetails?.profileImageId?.id,
							trainingImages:
								payload?.managerDetails?.trainingImages?.map(({ id }) => id) ??
								[],
					  }
					: null,
			}),
		onError: (error: AxiosError) => {
			toast((error?.response?.data as { messageToUser: string })?.messageToUser)
		},
		onSuccess: ({ data }) => {
			toast(data?.message)
			queryClient.refetchQueries({ queryKey: ['allBA'] })
			handleCloseDrawer()
		},
	})

	const handleEditBiomassAggregator = useCallback(
		(values: TAddBiomassAggregator) => {
			editBiomassAggregator.mutate(values)
		},
		[editBiomassAggregator]
	)

	const handleSave = useCallback(
		(values: TAddBiomassAggregator) => {
			const newPayload = { ...values, dummyBiomassAggregator: undefined }
			if (
				values?.dummyBiomassAggregator !== null &&
				(userDetails?.accountType === userRoles.Admin ||
					userDetails?.accountType === userRoles.CsinkManager)
			) {
				handleUpdateBiomassAggregator(values)
			} else if (editMode) {
				handleEditBiomassAggregator(newPayload)
			} else {
				handleAddBiomassAggregator(newPayload)
			}
		},
		[
			editMode,
			handleAddBiomassAggregator,
			handleEditBiomassAggregator,
			handleUpdateBiomassAggregator,
			userDetails?.accountType,
		]
	)

	const isLoading = useMemo(
		() => editBiomassAggregator?.isPending || addBiomassAggregator.isPending,
		[addBiomassAggregator.isPending, editBiomassAggregator?.isPending]
	)

	useEffect(() => {
		if (editMode) return
		getCurrentLocation()
	}, [editMode, getCurrentLocation])

	useEffect(() => {
		if (userDetails?.accountType !== userRoles.CsinkManager) return
		setValue('cSinkManagerId', userDetails?.csinkManagerId)
		if (userDetails?.accountType === userRoles.CsinkManager) {
			setValue(
				'dummyBiomassAggregator',
				fetchCsinkManager?.data?.find(
					(item) => item?.value === (userDetails?.csinkManagerId ?? '')
				)?.dummyBiomassAggregator
			)
		}
	}, [
		fetchCsinkManager?.data,
		setValue,
		userDetails?.accountType,
		userDetails?.csinkManagerId,
	])

	return (
		<FormProvider {...form}>
			<Stack gap={5}>
				{!editMode ? (
					<Autocomplete
						value={
							fetchCsinkManager?.data?.find(
								(item) => item.value === watch('cSinkManagerId')
							) ?? null
						}
						onChange={(
							_,
							newValue: Nullable<{
								label: string
								value: string
								dummyBiomassAggregator: TDummyBiomassAggregator
							}>
						) => {
							setValue('cSinkManagerId', newValue?.value)
							clearErrors('cSinkManagerId')
							if (newValue?.dummyBiomassAggregator !== null) {
								setValue(
									'dummyBiomassAggregator',
									newValue?.dummyBiomassAggregator
								)
								clearErrors('shortCode')
								setValue('shortCode', '')
							} else {
								setValue('dummyBiomassAggregator', null)
							}
						}}
						disabled={userDetails?.accountType === userRoles.CsinkManager}
						options={fetchCsinkManager.data || []}
						renderInput={(params) => (
							<CustomTextField
								schema={addBiomassAggregatorSchema(editMode || false)}
								name='cSinkManagerId'
								{...params}
								label='Csink Manager'
								error={!!errors?.cSinkManagerId?.message}
								helperText={errors?.cSinkManagerId?.message}
							/>
						)}
					/>
				) : null}
				<CustomTextField
					schema={addBiomassAggregatorSchema(editMode || false)}
					fullWidth
					id='name'
					type='text'
					label='Enter Name '
					variant='outlined'
					{...register('name')}
					error={!!errors.name?.message}
					helperText={errors?.name?.message}
					InputLabelProps={
						editMode
							? {
									shrink: editMode,
							  }
							: {}
					}
				/>

				{watch('dummyBiomassAggregator') === null ? (
					<CustomTextField
						schema={addBiomassAggregatorSchema(editMode || false)}
						id='shortCode'
						label='Enter short code'
						type='text'
						variant='outlined'
						placeholder='Please Enter Your short code'
						fullWidth
						inputProps={{
							maxLength: 4,
						}}
						value={watch('shortCode')}
						error={!!errors.shortCode?.message}
						helperText={errors?.shortCode?.message}
						{...register('shortCode', {
							setValueAs: (value) => {
								if (/^[a-zA-Z0-9]*$/.test(value)) return value

								return watch('shortCode')
							},
						})}
						InputLabelProps={
							editMode
								? {
										shrink: editMode,
								  }
								: {}
						}
					/>
				) : null}

				<CustomTextField
					schema={addBiomassAggregatorSchema(editMode || false)}
					id='location'
					label='Enter the Full Address'
					variant='outlined'
					autoComplete='off'
					fullWidth
					error={!!errors.locationName?.message}
					helperText={errors?.locationName?.message}
					{...register('locationName')}
					InputLabelProps={
						editMode
							? {
									shrink: editMode,
							  }
							: {}
					}
				/>
				<Stack direction='row' columnGap={2}>
					<CustomTextField
						schema={addBiomassAggregatorSchema(editMode || false)}
						id='latitude'
						label='Latitude'
						variant='outlined'
						fullWidth
						hideNumberArrows
						type='number'
						inputProps={{
							step: 'any',
						}}
						error={!!errors.latitude?.message}
						helperText={errors?.latitude?.message}
						{...register('latitude', {
							setValueAs: (value) => Number(parseFloat(value).toFixed(6)),
						})}
					/>

					<CustomTextField
						schema={addBiomassAggregatorSchema(editMode || false)}
						id='longitude'
						label='Longitude'
						variant='outlined'
						fullWidth
						hideNumberArrows
						type='number'
						inputProps={{
							step: 'any',
						}}
						error={!!errors.longitude?.message}
						helperText={errors?.longitude?.message}
						{...register('longitude', {
							setValueAs: (value) => Number(parseFloat(value).toFixed(6)),
						})}
					/>
				</Stack>

				<GoogleMapsWithNonDraggableMarker
					center={{
						lat: Number(watch('latitude')),
						lng: Number(watch('longitude')),
					}}
					setMapCenter={setMapCenter}
					mapContainerStyle={{
						width: '100%',
						height: 380,
						position: 'relative',
					}}
				/>
				{watch('createManager') ? (
					<Stack gap={2}>
						<Button
							variant='text'
							sx={{
								justifyContent: 'flex-start',
							}}
							onClick={handleRemoveManagerDetails}
							startIcon={<Remove color='primary' />}>
							Remove Manager Details
						</Button>
						<Typography variant='body2' pb={theme.spacing(1)}>
							Manager Details:
						</Typography>
						<AddManagerDetailsFields
							editMode={editMode}
							schema={addBiomassAggregatorSchema(editMode || false)}
						/>
					</Stack>
				) : (
					<Button
						variant='text'
						sx={{
							justifyContent: 'flex-start',
						}}
						onClick={handleAddManagerDetails}
						startIcon={<Add color='primary' />}>
						Add Manager Details
					</Button>
				)}
				<Stack
					direction='row'
					justifyContent='space-between'
					className='buttonContainer'>
					<Button
						onClick={handleCloseDrawer}
						sx={{ border: ` 1px solid ${theme.palette.primary.main}` }}>
						Cancel
					</Button>{' '}
					<LoadingButton
						loading={isLoading}
						disabled={isLoading}
						onClick={handleSubmit(handleSave)}
						variant='contained'>
						{editMode ? 'Save' : 'Add'}
					</LoadingButton>
				</Stack>
			</Stack>
		</FormProvider>
	)
}
