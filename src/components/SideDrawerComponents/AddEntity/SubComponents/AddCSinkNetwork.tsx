import { FC, useCallback, useEffect, useState } from 'react'
import { FormProvider, useFieldArray, useForm } from 'react-hook-form'
import { addCSinkNetwork, TAddCSinkNetwork } from '../schema'
import { yupResolver } from '@hookform/resolvers/yup'

import {
	Autocomplete,
	Button,
	IconButton,
	Stack,
	Typography,
	useTheme,
} from '@mui/material'
import { LoadingButton } from '@mui/lab'
import {
	useMutation,
	useQuery,
	useQueryClient,
	UseQueryResult,
} from '@tanstack/react-query'
import { authAxios, useAuthContext } from '@/contexts'
import { toast } from 'react-toastify'
import { userRoles } from '@/utils/constant'
import { AxiosError } from 'axios'
import { AddMethaneCompensateFields } from '@/components/AddMethaneCompensate'
import { AddBiomassPreProcessFields } from '@/components/AddBiomassPreProcessFields'
import { Add, Remove, RemoveCircleOutline } from '@mui/icons-material'
import { TwoColumnLayout } from '@/components/TwoColumnLayout'
import { GoogleMapsWithNonDraggableMarker } from '@/components/GoogleMap'
import { AddManagerDetailsFields } from '@/components/AddManagerDetails'
import { CustomTextField } from '@/utils/components'
import { BiomassOptionType } from '@/types'

type TProps = {
	handleCloseDrawer: () => void
	editMode?: boolean
	cSinkNetworkId?: string
	csinkManagersList?: UseQueryResult<
		{
			label: string
			value: string
		}[],
		Error
	>
	fetchBiomassTypeList?: BiomassOptionType[]
}

const initialValues = {
	name: '',
	biomassAggregator: '',
	locationName: '',
	latitude: 0,
	longitude: 0,
	bighaInHectare: 3.64,
	methaneCompensateStrategies: [],
	managerDetails: null,
	createManager: false,
	isEditMode: false,
	dryingType: null,
	dryingStrategy: null,
	dryingStrategyMedia: [],
	shreddingType: null,
	shreddingStrategy: null,
	shreddingStrategyMedia: [],
	cSinkManagerId: '',
}

export const AddCSinkNetwork: FC<TProps> = ({
	handleCloseDrawer,
	editMode,
	cSinkNetworkId,
	fetchBiomassTypeList,
	csinkManagersList,
}) => {
	const { userDetails } = useAuthContext()

	const theme = useTheme()
	const queryClient = useQueryClient()
	const [addDummyCsinkManagers, setAddDummyCsinkManagers] =
		useState<boolean>(false)

	const form = useForm<TAddCSinkNetwork>({
		defaultValues: initialValues,
		mode: 'all',
		resolver: yupResolver<TAddCSinkNetwork>(
			addCSinkNetwork(addDummyCsinkManagers)
		),
	})
	const {
		register,
		formState: { errors },
		setValue,
		handleSubmit,
		clearErrors,
		watch,
		control,
	} = form

	const { fields, append, remove } = useFieldArray({
		control,
		name: 'methaneCompensateStrategies',
	})

	const [showNoCropsError, setShowNoCropsError] = useState<boolean>(false)

	const handleAddManagerDetails = useCallback(() => {
		setValue('createManager', true)
		setValue('managerDetails', {
			name: '',
			email: '',
			phoneNo: null,
			countryCode: null,
			trained: 'no',
			profileImageId: null,
			trainingImages: [],
		})
	}, [setValue])

	const handleRemoveManagerDetails = useCallback(() => {
		setValue('createManager', false)
		setValue('managerDetails', null)
	}, [setValue])

	const setMapCenter = useCallback(
		(lat: number, lng: number) => {
			setValue('latitude', Number(lat.toFixed(6)))
			setValue('longitude', Number(lng.toFixed(6)))
		},
		[setValue]
	)

	const getCurrentLocation = useCallback(() => {
		navigator.geolocation.getCurrentPosition((position) => {
			setMapCenter(position.coords.latitude, position.coords.longitude)
		})
	}, [setMapCenter])

	const [showBiomassPreprocessingFields, setShowBiomassPreprocessingFields] =
		useState<boolean>(false)

	const AllBaQuery = useQuery({
		queryKey: ['allBA', watch('cSinkManagerId')],
		queryFn: () => {
			const queryParams = new URLSearchParams({
				limit: '1000',
				page: '0',
			})
			const data = authAxios.get<{
				biomassAggregators: { id: string; name: string; shortName: string }[]
			}>(
				`/csink-manager/${watch(
					'cSinkManagerId'
				)}/biomass-aggregators?${queryParams.toString()}`
			)

			data?.then((info) =>
				setAddDummyCsinkManagers(
					info.data?.biomassAggregators?.length === 0 ? true : false
				)
			)
			return data
		},
		select: ({ data }) =>
			data?.biomassAggregators?.map((item) => ({
				label: `${item.name} (${item.shortName})`,
				value: item.id,
			})),
		enabled:
			!!watch('cSinkManagerId') &&
			[userRoles.Admin, userRoles.CsinkManager].includes(
				userDetails?.accountType as userRoles
			),
	})

	const addCsinkNetwork = useMutation({
		mutationKey: ['addCsinkNetwork'],
		mutationFn: async ({
			methaneCompensateStrategies,
			shreddingStrategyMedia,
			dryingStrategyMedia,
			createManager,
			managerDetails,
			...rest
		}: TAddCSinkNetwork) => {
			const payload = {
				...rest,
				createManager,
				manager: createManager
					? {
							...managerDetails,
							trained: managerDetails?.trained === 'yes',
							profileImageId: managerDetails?.profileImageId?.id,
							trainingImages:
								managerDetails?.trainingImages?.map(({ id }) => id) ?? [],
					  }
					: null,
				methaneCompensateStrategies: methaneCompensateStrategies?.map((i) => ({
					...i,
					documentIds: i.documentIds?.map((doc) => doc?.id),
				})),
				shreddingDocumentIds:
					shreddingStrategyMedia?.map((item) => item?.id) ?? [],
				dryingDocumentIds: dryingStrategyMedia?.map((item) => item?.id) ?? [],
			}
			const URI = `/cs-network`

			return await authAxios.post(URI, payload)
		},
		onError: (err: AxiosError) => {
			toast((err?.response?.data as { userToMessage: string })?.userToMessage)
		},
		onSuccess: ({ data }) => {
			toast(data?.message)
			queryClient.refetchQueries({ queryKey: ['allcSinkNetwork'] })
			queryClient.refetchQueries({ queryKey: ['entityTabsCountQuery'] })
			handleCloseDrawer()
		},
	})

	const addDummyCsinkNetwork = useMutation({
		mutationKey: ['addDummyCsinkNetwork'],
		mutationFn: async ({
			methaneCompensateStrategies,
			shreddingStrategyMedia,
			dryingStrategyMedia,
			managerDetails,
			createManager,
			...rest
		}: TAddCSinkNetwork) => {
			const payload = {
				...rest,
				createManager,
				manager: createManager
					? {
							...managerDetails,
							trained: managerDetails?.trained === 'yes',
							profileImageId: managerDetails?.profileImageId?.id,
							trainingImages:
								managerDetails?.trainingImages?.map(({ id }) => id) ?? [],
					  }
					: null,
				methaneCompensateStrategies: methaneCompensateStrategies?.map((i) => ({
					...i,
					documentIds: i.documentIds?.map((doc) => doc?.id),
				})),
				shreddingDocumentIds:
					shreddingStrategyMedia?.map((item) => item?.id) ?? [],
				dryingDocumentIds: dryingStrategyMedia?.map((item) => item?.id) ?? [],
			}
			const URI = `/csink-manager/${payload?.cSinkManagerId}/dummy-csink-network`

			const newPayload = {
				...payload,
				biomassAggregator: undefined,
			}
			return await authAxios.post(URI, newPayload)
		},
		onError: (err: AxiosError) => {
			toast((err?.response?.data as { userToMessage: string })?.userToMessage)
		},
		onSuccess: ({ data }) => {
			toast(data?.message)
			queryClient.refetchQueries({ queryKey: ['allcSinkNetwork'] })
			handleCloseDrawer()
		},
	})

	useEffect(() => {
		if (editMode) setValue('isEditMode', true)
	}, [editMode, setValue])

	useQuery({
		queryKey: ['cSinkNetwork-details', cSinkNetworkId],
		queryFn: async () => {
			try {
				const { data } = await authAxios.get(`/cs-network/${cSinkNetworkId}`)
				setValue('name', data?.name)
				setValue('locationName', data?.locationName)
				setValue('latitude', data?.location?.x || 0)
				setValue('longitude', data?.location?.y || 0)
				return data
			} catch (error: any) {
				toast(error?.response?.data?.messageToUser)
				return null
			}
		},
		enabled: editMode,
	})

	const editcSinkNetwork = useMutation({
		mutationKey: ['editcSinkNetwork'],
		mutationFn: async (payload: TAddCSinkNetwork) => {
			const updatedPayload = {
				name: payload?.name,
				locationName: payload?.locationName,
				latitude: payload.latitude,
				longitude: payload.longitude,
			}
			return await authAxios.put(
				`/cs-network/${cSinkNetworkId}`,
				updatedPayload
			)
		},
		onError: (err: any) => {
			toast(err?.response?.data?.userToMessage)
		},
		onSuccess: ({ data }) => {
			toast(data?.message)
			queryClient.refetchQueries({ queryKey: ['allcSinkNetwork'] })
			handleCloseDrawer()
		},
	})

	const handleEditcSinkNetwork = useCallback(
		(values: TAddCSinkNetwork) => {
			editcSinkNetwork.mutate(values)
		},
		[editcSinkNetwork]
	)

	const handleAddCsinkNetwork = useCallback(
		(values: TAddCSinkNetwork) => {
			if (
				addDummyCsinkManagers &&
				(userDetails?.accountType === userRoles.Admin ||
					userDetails?.accountType === userRoles.CsinkManager)
			) {
				addDummyCsinkNetwork.mutate(values)
			} else {
				addCsinkNetwork.mutate(values)
			}
		},
		[
			addCsinkNetwork,
			addDummyCsinkManagers,
			addDummyCsinkNetwork,
			userDetails?.accountType,
		]
	)

	const handleSave = useCallback(
		(values: TAddCSinkNetwork) =>
			editMode ? handleEditcSinkNetwork(values) : handleAddCsinkNetwork(values),
		[editMode, handleAddCsinkNetwork, handleEditcSinkNetwork]
	)

	const handleAdd = useCallback(() => {
		if (fetchBiomassTypeList?.length ?? 0 > 0)
			append({
				biomassId: null,
				methaneCompensateType: null,
				description: null,
				compensateType: null,
				documentIds: [],
			})
		else setShowNoCropsError(true)
	}, [append, fetchBiomassTypeList?.length])

	const handleRemove = useCallback(
		(index: number) => {
			remove(index)
		},
		[remove]
	)

	useEffect(() => {
		if (editMode) return
		getCurrentLocation()
	}, [editMode, getCurrentLocation])

	useEffect(() => {
		if (userDetails?.accountType === userRoles.BiomassAggregator) {
			setValue('biomassAggregator', userDetails?.biomassAggregatorId || '')
		}
		if (
			userDetails?.accountType === userRoles.CsinkManager ||
			userDetails?.accountType === userRoles.BiomassAggregator
		) {
			setValue('cSinkManagerId', userDetails?.csinkManagerId ?? '')
		}
	}, [userDetails, setValue])

	useEffect(() => {
		if (AllBaQuery.isSuccess && AllBaQuery.data?.length === 1) {
			const defaultBiomassAggregator = AllBaQuery.data[0]
			setValue('biomassAggregator', defaultBiomassAggregator.value)
		}
	}, [AllBaQuery.isSuccess, AllBaQuery.data, setValue])

	return (
		<FormProvider {...form}>
			<Stack gap={theme.spacing(3)}>
				{[
					userRoles.Admin,
					userRoles.CsinkManager,
					userRoles.BiomassAggregator,
				].includes(userDetails?.accountType as userRoles) && !editMode ? (
					<Autocomplete
						value={
							csinkManagersList?.data?.find(
								(item) => item.value === watch('cSinkManagerId')
							) ?? null
						}
						onChange={(_, newValue: any) => {
							setValue('cSinkManagerId', newValue?.value)
							clearErrors('cSinkManagerId')
							setValue('biomassAggregator', '')
							clearErrors('biomassAggregator')
						}}
						options={csinkManagersList?.data || []}
						disabled={
							userDetails?.accountType === userRoles.CsinkManager ||
							userDetails?.accountType === userRoles.BiomassAggregator
						}
						renderInput={(params) => (
							<CustomTextField
								schema={addCSinkNetwork(addDummyCsinkManagers)}
								{...params}
								id='cSinkManagerId'
								name='cSinkManagerId'
								label='Csink Manager'
								error={!!errors?.cSinkManagerId?.message}
								helperText={errors?.cSinkManagerId?.message}
							/>
						)}
					/>
				) : null}
				{[userRoles.Admin, userRoles.CsinkManager].includes(
					userDetails?.accountType as userRoles
				) &&
				!editMode &&
				AllBaQuery.data?.length !== 0 ? (
					<Autocomplete
						options={AllBaQuery.data || []}
						getOptionLabel={(option) => option.label}
						disabled={watch('cSinkManagerId') === ''}
						value={
							AllBaQuery.data?.find(
								(item) => item.value === watch('biomassAggregator')
							) || null
						}
						renderInput={(params) => (
							<CustomTextField
								schema={addCSinkNetwork(addDummyCsinkManagers)}
								{...params}
								name='biomassAggregator'
								label='Select BA'
								error={!!errors.biomassAggregator?.message}
								helperText={errors.biomassAggregator?.message}
							/>
						)}
						onChange={(_, selectedOption) => {
							if (selectedOption) {
								setValue('biomassAggregator', selectedOption.value)
								clearErrors('biomassAggregator')
							} else {
								setValue('biomassAggregator', '')
							}
						}}
					/>
				) : null}
				<CustomTextField
					schema={addCSinkNetwork(addDummyCsinkManagers)}
					id='name'
					label='Name'
					variant='outlined'
					autoComplete='off'
					fullWidth
					error={!!errors.name?.message}
					helperText={errors?.name?.message}
					{...register('name')}
					InputLabelProps={
						editMode
							? {
									shrink: editMode,
							  }
							: {}
					}
				/>
				<CustomTextField
					schema={addCSinkNetwork(addDummyCsinkManagers)}
					id='locationName'
					label='Location Name'
					variant='outlined'
					autoComplete='off'
					fullWidth
					error={!!errors.locationName?.message}
					helperText={errors?.locationName?.message}
					{...register('locationName')}
					InputLabelProps={
						editMode
							? {
									shrink: editMode,
							  }
							: {}
					}
				/>

				<Stack direction='row' columnGap={2}>
					<CustomTextField
						schema={addCSinkNetwork(addDummyCsinkManagers)}
						id='latitude'
						label='Latitude'
						variant='outlined'
						fullWidth
						type='number'
						inputProps={{
							step: 'any',
						}}
						error={!!errors.latitude?.message}
						helperText={errors?.latitude?.message}
						sx={{
							'& input::-webkit-outer-spin-button, & input::-webkit-inner-spin-button':
								{
									display: 'none',
								},
							'& input[type=number]': {
								MozAppearance: 'textfield',
							},
						}}
						{...register('latitude', {
							setValueAs: (value) => Number(parseFloat(value).toFixed(6)),
						})}
					/>

					<CustomTextField
						schema={addCSinkNetwork(addDummyCsinkManagers)}
						id='longitude'
						label='Longitude'
						variant='outlined'
						fullWidth
						type='number'
						inputProps={{
							step: 'any',
						}}
						sx={{
							'& input::-webkit-outer-spin-button, & input::-webkit-inner-spin-button':
								{
									display: 'none',
								},
							'& input[type=number]': {
								MozAppearance: 'textfield',
							},
						}}
						error={!!errors.longitude?.message}
						helperText={errors?.longitude?.message}
						{...register('longitude', {
							setValueAs: (value) => Number(parseFloat(value).toFixed(6)),
						})}
					/>
				</Stack>

				<GoogleMapsWithNonDraggableMarker
					center={{
						lat: Number(watch('latitude')),
						lng: Number(watch('longitude')),
					}}
					setMapCenter={setMapCenter}
					mapContainerStyle={{
						width: '100%',
						height: 380,
						position: 'relative',
					}}
				/>
				{fields.map((field, index) => (
					<Stack key={field.id} direction='row' alignItems='center' gap={2}>
						<TwoColumnLayout
							left={
								<AddMethaneCompensateFields
									fieldIndex={index}
									editMode={editMode}
									schema={addCSinkNetwork(addDummyCsinkManagers)}
									fetchBiomassTypeList={fetchBiomassTypeList}
								/>
							}
							right={
								<IconButton
									color='error'
									onClick={() => handleRemove(index)}
									size='small'>
									<RemoveCircleOutline />
								</IconButton>
							}
							gridBreakpoints={[11, 0.5]}
						/>
					</Stack>
				))}

				{showNoCropsError && (
					<Stack direction='row' alignItems='center' gap={2}>
						<TwoColumnLayout
							left={
								<Stack
									minHeight={theme.spacing(40)}
									alignItems='center'
									justifyContent='center'
									textAlign='center'>
									<Typography color='error' textAlign='center'>
										There is no crop assigned to your Csink Manager. Please
										contact Admin for the same.
									</Typography>
								</Stack>
							}
							right={
								<IconButton
									color='error'
									onClick={() => setShowNoCropsError(false)}
									size='small'>
									<RemoveCircleOutline />
								</IconButton>
							}
							gridBreakpoints={[11, 0.5]}
						/>
					</Stack>
				)}

				{!editMode ? (
					<Stack flexDirection='column'>
						<Button
							variant='text'
							sx={{
								justifyContent: 'flex-start',
							}}
							onClick={handleAdd}
							startIcon={<Add color='primary' />}>
							Add Methane Compensate
						</Button>
						{watch('createManager') ? (
							<Stack gap={2}>
								<Button
									variant='text'
									sx={{
										justifyContent: 'flex-start',
									}}
									onClick={handleRemoveManagerDetails}
									startIcon={<Remove color='primary' />}>
									Remove Manager Details
								</Button>
								<Typography variant='body2' pb={theme.spacing(1)}>
									Manager Details:
								</Typography>
								<AddManagerDetailsFields
									editMode={editMode}
									schema={addCSinkNetwork(addDummyCsinkManagers)}
								/>
							</Stack>
						) : (
							<Button
								variant='text'
								sx={{
									justifyContent: 'flex-start',
								}}
								onClick={handleAddManagerDetails}
								startIcon={<Add color='primary' />}>
								Add Manager Details
							</Button>
						)}
						{!showBiomassPreprocessingFields ? (
							<Button
								variant='text'
								sx={{
									justifyContent: 'flex-start',
								}}
								onClick={() => setShowBiomassPreprocessingFields(true)}
								startIcon={<Add color='primary' />}>
								Add Biomass Pre-Processing
							</Button>
						) : null}
					</Stack>
				) : null}
				{showBiomassPreprocessingFields ? (
					<AddBiomassPreProcessFields
						editMode={editMode}
						schema={addCSinkNetwork(addDummyCsinkManagers)}
					/>
				) : null}
				<Stack
					direction='row'
					justifyContent='space-between'
					gap={2}
					className='buttonContainer'>
					<Button
						onClick={handleCloseDrawer}
						sx={{ border: ` 1px solid ${theme.palette.primary.main}` }}>
						Cancel
					</Button>{' '}
					<LoadingButton
						loading={addCsinkNetwork.isPending}
						disabled={addCsinkNetwork.isPending}
						onClick={handleSubmit(handleSave, (e) => console.log(e))}
						variant='contained'>
						{editMode ? 'Save' : 'Add'}
					</LoadingButton>
				</Stack>
			</Stack>
		</FormProvider>
	)
}
