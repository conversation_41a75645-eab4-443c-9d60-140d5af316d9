import { theme } from '@/lib/theme/theme'
import { InputLabel, Stack, TextField, Button, IconButton } from '@mui/material'
import AddIcon from '@mui/icons-material/Add'
import { FC, ReactElement, useState } from 'react'
import { CsinkManager, EntityEnum, IPackagingBag, ISite } from '@/interfaces'
import { ActionInformationDrawer } from '@/components/ActionInformationDrawer'
import { AddCsinkManagerApplicationType } from './AddCsinkManagerApplicationType'
import { AddCsinkManagerMixingType } from './AddCsinkManagerMixingType'
import { UseQueryResult } from '@tanstack/react-query'

interface IProps {
	entityType: EntityEnum
	label: string
	bagDetails?: IPackagingBag
	iconButton?: ReactElement
	showSearchField?: boolean
	subheading?: string
	fetchCsinkManagerDetailsQuery: UseQueryResult<CsinkManager, Error>
}
interface IRenderProps {
	entityType: EntityEnum
	siteDetails?: ISite
	handleCloseDrawer: () => void
	subheading?: string
	addText?: string
	fetchCsinkManagerDetailsQuery: UseQueryResult<CsinkManager, Error>
}

export const AddEntityContent = ({
	entityType,
	label,
	iconButton,
	showSearchField = true,
	subheading,
	fetchCsinkManagerDetailsQuery,
}: IProps) => {
	const [openAddEntityModal, setOpenAddEntityModal] = useState<string | null>(
		null
	)
	const [addText, setAddText] = useState<string>('')

	return (
		<>
			{showSearchField && <InputLabel>Enter {label} name</InputLabel>}

			<Stack
				sx={{
					flexDirection: 'row',
					justifyContent: showSearchField ? '-moz-initial' : 'flex-end',
					gap: theme.spacing(3),
				}}>
				{showSearchField && (
					<TextField
						id={`add${entityType}`}
						placeholder='Full name'
						value={addText}
						onChange={(e) => setAddText(e.target.value)}
						sx={{
							height: theme.spacing(6),
							width: '100%',
						}}
						InputProps={{
							sx: {
								height: '100%',
							},
						}}
					/>
				)}

				{iconButton ? (
					<IconButton onClick={() => setOpenAddEntityModal(label)}>
						{iconButton}
					</IconButton>
				) : (
					<Button
						variant='outlined'
						className='add_btn'
						onClick={() => setOpenAddEntityModal(label)}
						startIcon={<AddIcon fontSize='small' />}>
						Add {label}
					</Button>
				)}
			</Stack>
			{openAddEntityModal ? (
				<ActionInformationDrawer
					open={!!openAddEntityModal}
					onClose={() => {
						setAddText('')
						setOpenAddEntityModal(null)
					}}
					anchor='right'
					component={
						<RenderAddEntityContentModal
							addText={addText}
							subheading={subheading}
							entityType={entityType}
							fetchCsinkManagerDetailsQuery={fetchCsinkManagerDetailsQuery}
							handleCloseDrawer={() => {
								setAddText('')
								setOpenAddEntityModal(null)
							}}
						/>
					}
				/>
			) : null}
		</>
	)
}

const RenderAddEntityContentModal: FC<IRenderProps> = ({
	entityType,
	handleCloseDrawer,
	subheading,
	fetchCsinkManagerDetailsQuery,
}) => {
	switch (entityType) {
		case EntityEnum.mixingType:
			return (
				<AddCsinkManagerMixingType
					subheading={subheading}
					handleCloseDrawer={handleCloseDrawer}
					mixingTypeData={fetchCsinkManagerDetailsQuery?.data?.mixingTypes}
					csinkMangerId={fetchCsinkManagerDetailsQuery?.data?.id ?? ''}
				/>
			)
		case EntityEnum.applicationsType:
			return (
				<AddCsinkManagerApplicationType
					subheading={subheading}
					handleCloseDrawer={handleCloseDrawer}
					applicationTypeData={
						fetchCsinkManagerDetailsQuery?.data?.applicationTypes
					}
					csinkMangerId={fetchCsinkManagerDetailsQuery?.data?.id ?? ''}
				/>
			)
		default:
			return null
	}
}
