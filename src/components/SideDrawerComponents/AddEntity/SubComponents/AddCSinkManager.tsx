import { GoogleMapsWithNonDraggableMarker } from '@/components/GoogleMap'
import { yupResolver } from '@hookform/resolvers/yup'
import {
	Button,
	FormControl,
	InputLabel,
	MenuItem,
	MenuProps,
	Select,
	Stack,
	useTheme,
} from '@mui/material'
import { FC, useCallback, useEffect, useMemo, useState } from 'react'
import { FormProvider, useForm, useWatch } from 'react-hook-form'
import { addCSinkManagerSchema, TAddCSinkManager } from '../schema'
import { useMutation, useQuery, useQueryClient } from '@tanstack/react-query'
import { authAxios, useAuthContext } from '@/contexts'
import { toast } from 'react-toastify'
import { LoadingButton } from '@mui/lab'
import { CsinkManager } from '@/interfaces'
import { ICrop } from '@/types'
import { userRoles } from '@/utils/constant'
import { TwoColumnLayout } from '@/components/TwoColumnLayout'
import { CustomTextField } from '@/utils/components'
import AddOrRemoveManager from '@/pages/dashboard/Admin/OrganizationSettings/components/AddOrRemoveManager'
import { theme } from '@/lib/theme/theme'

const menuProps:Partial<MenuProps> = {
	anchorOrigin: {
		vertical: 'bottom',
		horizontal: 'center',
	},
	PaperProps: {
		style: {
			maxHeight: theme.spacing(50),
		},
	},
}
interface TextFieldConfig {
	id: FieldKey
	label: string
	type: string
	placeholder?: string
	disabled?: (editMode: boolean) => boolean
	inputProps?: object
	autoComplete?: string
	setValueAs?: (watch: (field: string) => string) => (value: string) => string
}
const textFields: TextFieldConfig[] = [
	{
		id: 'name',
		label: 'Enter Name',
		type: 'text',
	},
	{
		id: 'shortCode',
		label: 'Enter short code',
		type: 'text',
		placeholder: 'Please Enter Your short code',
		disabled: (editMode) => editMode,
		inputProps: { maxLength: 4 },
		setValueAs: (watch) => (value) =>
			/^[a-zA-Z0-9]*$/.test(value) ? value : watch('shortCode'),
	},
]

const locationFields: {
	id: FieldKey
	label: string
}[] = [
	{ id: 'latitude', label: 'Latitude' },
	{ id: 'longitude', label: 'Longitude' },
]
type TProps = {
	handleCloseDrawer: () => void
	editMode?: boolean
	cSinkId?: string
	type: string
	refetchEntities?: boolean
}

const initialValues = {
	name: '',
	shortCode: '',
	locationName: '',
	managerDetails: null,
	createEntityOnly: true,
	latitude: 0,
	longitude: 0,
	cropIds: [],
}
type FieldKey = keyof TAddCSinkManager

export const AddCSinkManager: FC<TProps> = ({
	handleCloseDrawer,
	editMode = false,
	refetchEntities = false,
	cSinkId,
}) => {
	const theme = useTheme()
	const queryClient = useQueryClient()
	const { userDetails } = useAuthContext()

	const form = useForm<TAddCSinkManager>({
		defaultValues: initialValues,
		mode: 'all',
		resolver: yupResolver<TAddCSinkManager>(addCSinkManagerSchema),
	})
	const {
		register,
		formState: { errors },
		watch,
		setValue,
		control,
		handleSubmit,
	} = form

	const createEntityOnly = useWatch({
		control,
		name: 'createEntityOnly',
	})
	const [showAssignBiomassButton, setShowAssignBiomassButton] = useState(false)
	const showAssignedBiomass = showAssignBiomassButton && !editMode
	const displayAssignBiomassButton = !showAssignBiomassButton && !editMode
	const handleAddManagerDetails = useCallback(() => {
		setValue('createEntityOnly', false)
		setValue('managerDetails', {
			name: '',
			email: '',
			phoneNo: null,
			countryCode: null,
			trained: 'no',
			profileImageId: null,
			trainingImages: [],
		})
	}, [setValue])

	const handleRemoveManagerDetails = useCallback(() => {
		setValue('createEntityOnly', true)
		setValue('managerDetails', null)
	}, [setValue])

	const setMapCenter = useCallback(
		(lat: number, lng: number) => {
			setValue('latitude', Number(lat.toFixed(6)))
			setValue('longitude', Number(lng.toFixed(6)))
		},
		[setValue]
	)

	const getCurrentLocation = useCallback(() => {
		navigator.geolocation.getCurrentPosition((position) => {
			setMapCenter(position.coords.latitude, position.coords.longitude)
		})
	}, [setMapCenter])

	const addCSinkManager = useMutation({
		mutationKey: ['addCSinkManager'],
		mutationFn: async ({
			name,
			latitude,
			longitude,
			locationName,
			shortCode,
			cropIds,
			createEntityOnly,
			managerDetails,
		}: TAddCSinkManager) => {
			const { phoneNo, ...rest } = managerDetails ?? {}
			const payload = {
				name,
				latitude,
				longitude,
				cropIds,
				createEntityOnly,
				manager: createEntityOnly
					? null
					: {
							...rest,
							phoneNumber: phoneNo,
							trained: managerDetails?.trained === 'yes',
							profileImageId: managerDetails?.profileImageId?.id,
							trainingImages:
								managerDetails?.trainingImages?.map(({ id }) => id) ?? [],
					  },
				locationName,
				shortCode: shortCode ? shortCode : null,
			}
			return await authAxios.post('/csink-manager', payload)
		},
		onError: (error: any) => {
			toast(error?.response?.data?.messageToUser)
		},
		onSuccess: ({ data }) => {
			toast(data?.message)
			queryClient.refetchQueries({ queryKey: ['allCsinkManager'] })
			queryClient.refetchQueries({ queryKey: ['entityTabsCountQuery'] })
			if (refetchEntities)
				queryClient.refetchQueries({ queryKey: ['entities'] })
			handleCloseDrawer()
		},
	})

	const handleAddCSinkManager = useCallback(
		(values: TAddCSinkManager) => {
			addCSinkManager.mutate(values)
		},
		[addCSinkManager]
	)
	const fetchBiomass = useQuery({
		queryKey: ['getBiomassTypes'],
		queryFn: () => {
			return authAxios<{ cropDetails: ICrop[] }>(`/drop-down/crops`)
		},
		enabled: userDetails?.accountType === userRoles.Admin,
	})

	useQuery({
		queryKey: ['cSinkDetail', cSinkId],
		queryFn: async () => {
			try {
				const { data } = await authAxios.get<CsinkManager>(
					`/csink-manager/${cSinkId}`
				)
				const [latitude, longitude] = data.location.slice(1, -1).split(',')
				setValue('name', data?.name)
				setValue('locationName', data?.locationName)
				setValue('shortCode', data?.shortName)
				setValue('latitude', Number(latitude || 0))
				setValue('longitude', Number(longitude || 0))

				return data
			} catch (error: any) {
				toast(error?.response?.data?.messageToUser)
				return null
			}
		},
		enabled: editMode,
	})

	const editCSink = useMutation({
		mutationKey: ['editCSink'],
		mutationFn: async (payload: TAddCSinkManager) => {
			const updatedPayload = {
				...payload,
				manager: payload?.createEntityOnly
					? null
					: {
							...payload?.managerDetails,
							trained: payload?.managerDetails?.trained === 'yes',
							profileImageId: payload?.managerDetails?.profileImageId?.id,
							trainingImages:
								payload?.managerDetails?.trainingImages?.map(({ id }) => id) ??
								[],
					  },
				latitude: String(payload.latitude),
				longitude: String(payload.longitude),
			}
			return await authAxios.put(`/csink-manager/${cSinkId}`, updatedPayload)
		},
		onError: (err: any) => {
			toast(err?.response?.data?.userToMessage)
		},
		onSuccess: ({ data }) => {
			toast(data?.message)
			queryClient.refetchQueries({ queryKey: ['allCsinkManager'] })
			handleCloseDrawer()
		},
	})

	const handleEditCSink = useCallback(
		(values: TAddCSinkManager) => {
			editCSink.mutate(values)
		},
		[editCSink]
	)

	const handleSave = useCallback(
		(values: TAddCSinkManager) =>
			editMode ? handleEditCSink(values) : handleAddCSinkManager(values),
		[editMode, handleAddCSinkManager, handleEditCSink]
	)

	const isLoading = useMemo(
		() => editCSink?.isPending || addCSinkManager.isPending,
		[addCSinkManager.isPending, editCSink?.isPending]
	)

	useEffect(() => {
		if (editMode) return
		getCurrentLocation()
	}, [editMode, getCurrentLocation])

	return (
		<FormProvider {...form}>
			<Stack gap={5}>
				{textFields.map((field) => (
					<CustomTextField
						key={field.id}
						watch={watch}
						schema={addCSinkManagerSchema}
						fullWidth
						id={field.id}
						type={field.type}
						label={field.label}
						variant='outlined'
						placeholder={field?.placeholder ?? ''}
						disabled={field?.disabled?.(editMode)}
						inputProps={field?.inputProps}
						autoComplete={field?.autoComplete}
						value={watch(field.id)}
						error={!!errors?.[field.id]?.message}
						helperText={errors?.[field.id]?.message}
						{...register(field.id, {
							setValueAs: field.setValueAs
								? field.setValueAs(watch)
								: undefined,
						})}
					/>
				))}

				{!editMode ? (
					<AddOrRemoveManager
						handleAddManagerDetails={handleAddManagerDetails}
						handleRemoveManagerDetails={handleRemoveManagerDetails}
						schema={addCSinkManagerSchema}
						createEntityOnly={createEntityOnly}
					/>
				) : null}
				<CustomTextField
						key='locationName'
						watch={watch}
						schema={addCSinkManagerSchema}
						fullWidth
						id='locationName'
						type='text'
						label='Enter the Full Address'
						variant='outlined'
						autoComplete='off'
						value={watch('locationName')}
						error={!!errors?.['locationName']?.message}
						helperText={errors?.['locationName']?.message}
						{...register('locationName')}
					/>
				<Stack direction='row' columnGap={2}>
					{locationFields.map((field) => (
						<CustomTextField
							key={field.id}
							watch={watch}
							schema={addCSinkManagerSchema}
							id={field.id}
							label={field.label}
							variant='outlined'
							fullWidth
							type='number'
							inputProps={{ step: 'any' }}
							error={!!errors?.[field.id]?.message}
							helperText={errors?.[field.id]?.message}
							{...register(field.id, {
								setValueAs: (value) => Number(parseFloat(value).toFixed(6)),
							})}
						/>
					))}
				</Stack>
				{showAssignedBiomass ? (
					<FormControl>
						<InputLabel id='selectBiomass-label'>Select the Biomass</InputLabel>
						<Select
							sx={{ height: theme.spacing(7) }}
							labelId='selectBiomass-label'
							id='selectBiomass'
							label='Select the Biomass'
							multiple
							value={watch('cropIds')}
							{...register('cropIds')}
							MenuProps={menuProps}>
							{fetchBiomass?.data?.data?.cropDetails?.map((crop) => (
								<MenuItem key={crop?.id} value={crop?.id}>
									{crop?.name}
								</MenuItem>
							))}
						</Select>
					</FormControl>
				) : null}
				{displayAssignBiomassButton ? (
					<TwoColumnLayout
						gridBreakpoints={[6]}
						right={
							<Button
								variant='text'
								onClick={() => setShowAssignBiomassButton(true)}>
								Assign Biomass
							</Button>
						}
						left={<></>}
					/>
				) : null}
				<GoogleMapsWithNonDraggableMarker
					center={{
						lat: Number(watch('latitude')),
						lng: Number(watch('longitude')),
					}}
					setMapCenter={setMapCenter}
					mapContainerStyle={{
						width: '100%',
						height: '28vh',
						position: 'relative',
						textAlign: 'initial',
					}}
				/>

				<Stack
					direction='row'
					gap={3}
					justifyContent='space-between'
					className='buttonContainer'>
					<Button
						onClick={handleCloseDrawer}
						sx={{ border: ` 1px solid ${theme.palette.primary.main}` }}>
						Cancel
					</Button>
					<LoadingButton
						loading={isLoading}
						disabled={isLoading}
						onClick={handleSubmit(handleSave)}
						variant='contained'>
						{editMode ? 'Save' : 'Add'}
					</LoadingButton>
				</Stack>
			</Stack>
		</FormProvider>
	)
}
