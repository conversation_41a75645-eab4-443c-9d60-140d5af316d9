import { FC, useCallback, useEffect, useState } from 'react'
import { FormProvider, useFieldArray, useForm } from 'react-hook-form'
import { addArtisanPro, TAddArtisanPro } from '../schema'
import { yupResolver } from '@hookform/resolvers/yup'

import {
	Autocomplete,
	Button,
	IconButton,
	Stack,
	Typography,
	useTheme,
} from '@mui/material'
import { LoadingButton } from '@mui/lab'
import {
	useMutation,
	useQuery,
	useQueryClient,
	UseQueryResult,
} from '@tanstack/react-query'
import { authAxios, useAuthContext } from '@/contexts'
import { toast } from 'react-toastify'
import { userRoles } from '@/utils/constant'
import { Add, Remove, RemoveCircleOutline } from '@mui/icons-material'
import { AddMethaneCompensateFields } from '@/components/AddMethaneCompensate'
import { AddBiomassPreProcessFields } from '@/components/AddBiomassPreProcessFields'
import { AxiosError } from 'axios'
import { TwoColumnLayout } from '@/components/TwoColumnLayout'
import { GoogleMapsWithNonDraggableMarker } from '@/components/GoogleMap'
import { AddManagerDetailsFields } from '@/components/AddManagerDetails'
import { CustomTextField } from '@/utils/components'
import { BiomassOptionType } from '@/types'

type TProps = {
	handleCloseDrawer: () => void
	editMode?: boolean
	apsId?: string
	csinkManagersList?: UseQueryResult<
		{
			label: string
			value: string
		}[],
		Error
	>
	fetchBiomassTypeList?: BiomassOptionType[]
}

const initialValues = {
	address: '',
	bighaInHectare: 3.64,
	name: '',
	apnId: '',
	isEditMode: false,
	methaneCompensateStrategies: [],
	methaneStrategyMedia: [],
	managerDetails: null,
	createManager: false,
	dryingType: null,
	dryingStrategy: null,
	dryingStrategyMedia: [],
	shreddingType: null,
	shreddingStrategy: null,
	shreddingStrategyMedia: [],
	cSinkManagerId: '',
	latitude: 0,
	longitude: 0,
}

export const AddArtisanPros: FC<TProps> = ({
	handleCloseDrawer,
	apsId,
	editMode,
	csinkManagersList,
	fetchBiomassTypeList,
}) => {
	const { userDetails } = useAuthContext()
	const theme = useTheme()
	const queryClient = useQueryClient()
	const [addDummyCsinkManagers, setAddDummyCsinkManagers] =
		useState<boolean>(false)

	const form = useForm<TAddArtisanPro>({
		defaultValues: initialValues,
		mode: 'all',
		resolver: yupResolver<TAddArtisanPro>(addArtisanPro(addDummyCsinkManagers)),
	})
	const [showNoCropsError, setShowNoCropsError] = useState<boolean>(false)

	const {
		register,
		formState: { errors },
		setValue,
		handleSubmit,
		clearErrors,
		watch,
		control,
	} = form

	const { fields, append, remove } = useFieldArray({
		control,
		name: 'methaneCompensateStrategies',
	})

	const handleAddManagerDetails = useCallback(() => {
		setValue('createManager', true)
		setValue('managerDetails', {
			name: '',
			email: '',
			phoneNo: null,
			countryCode: null,
			trained: 'no',
			profileImageId: null,
			trainingImages: [],
		})
	}, [setValue])

	const handleRemoveManagerDetails = useCallback(() => {
		setValue('createManager', false)
		setValue('managerDetails', null)
	}, [setValue])

	const [showBiomassPreprocessingFields, setShowBiomassPreprocessingFields] =
		useState(false)

	const AllAPNs = useQuery({
		queryKey: ['allAPNs', watch('cSinkManagerId')],
		queryFn: () => {
			const queryParams = new URLSearchParams({
				limit: '1000',
				page: '0',
				csinkManagerId: watch('cSinkManagerId') ?? '',
			})

			const resp = authAxios.get<{
				artisanProNetworks: { id: string; name: string; shortCode: string }[]
			}>(`/new/artisanpro-networks?${queryParams.toString()}`)

			resp?.then((info) =>
				setAddDummyCsinkManagers(
					(info.data?.artisanProNetworks ?? [])?.length === 0 ? true : false
				)
			)
			return resp
		},
		select: ({ data }) =>
			data?.artisanProNetworks?.map((item) => ({
				label: `${item.name} (${item.shortCode})`,
				value: item.id,
			})),
		enabled:
			!!watch('cSinkManagerId') &&
			[
				userRoles.Admin,
				userRoles.CsinkManager,
				userRoles.BiomassAggregator,
				userRoles.artisanProNetworkManager,
			].includes(userDetails?.accountType as userRoles),
	})

	const addAPs = useMutation({
		mutationKey: ['addAPs'],
		mutationFn: async ({
			methaneCompensateStrategies,
			shreddingStrategyMedia,
			dryingStrategyMedia,
			managerDetails,
			createManager,
			...rest
		}: TAddArtisanPro) => {
			const payload = {
				...rest,
				createManager,
				manager: createManager
					? {
							...managerDetails,
							trained: managerDetails?.trained === 'yes',
							profileImageId: managerDetails?.profileImageId?.id,
							trainingImages:
								managerDetails?.trainingImages?.map(({ id }) => id) ?? [],
					  }
					: null,
				methaneCompensateStrategies: methaneCompensateStrategies?.map((i) => ({
					...i,
					documentIds: i.documentIds?.map((doc) => doc?.id),
				})),
				shreddingDocumentIds:
					shreddingStrategyMedia?.map((item) => item?.id) ?? [],
				dryingDocumentIds: dryingStrategyMedia?.map((item) => item?.id) ?? [],
				apnId: addDummyCsinkManagers ? undefined : rest?.apnId,
			}

			const URI = addDummyCsinkManagers
				? `/csink-manager/${payload?.cSinkManagerId}/dummy-artisan-pro`
				: `/artisan-pro-network/${payload?.apnId}/artisian-pro`
			return await authAxios.post(URI, payload)
		},

		onError: (err: AxiosError) => {
			toast((err?.response?.data as { userToMessage: string })?.userToMessage)
		},
		onSuccess: ({ data }) => {
			toast(data?.message)
			queryClient.refetchQueries({ queryKey: ['allaps'] })
			queryClient.refetchQueries({ queryKey: ['entityTabsCountQuery'] })
			handleCloseDrawer()
		},
	})

	const handleAddAPs = useCallback(
		async (values: TAddArtisanPro) => {
			addAPs.mutate(values)
		},
		[addAPs]
	)

	const handleAdd = useCallback(() => {
		if (fetchBiomassTypeList?.length ?? 0 > 0)
			append({
				biomassId: null,
				methaneCompensateType: null,
				description: null,
				compensateType: null,
				documentIds: [],
			})
		else setShowNoCropsError(true)
	}, [append, fetchBiomassTypeList?.length])

	const setMapCenter = useCallback(
		(lat: number, lng: number) => {
			setValue('latitude', Number(lat.toFixed(6)))
			setValue('longitude', Number(lng.toFixed(6)))
		},
		[setValue]
	)

	const getCurrentLocation = useCallback(() => {
		navigator.geolocation.getCurrentPosition((position) => {
			setMapCenter(position.coords.latitude, position.coords.longitude)
		})
	}, [setMapCenter])

	const handleRemove = useCallback(
		(index: number) => {
			remove(index)
		},
		[remove]
	)

	useQuery({
		queryKey: ['artisan-pro-details', apsId],
		queryFn: async () => {
			try {
				const { data } = await authAxios.get(`/artisian-pro/${apsId}`)
				setValue('name', data?.name)
				setValue('address', data?.address)
				setValue('apnId', data?.artisianProNetworkId)
				setValue('latitude', data?.location?.x || 0)
				setValue('longitude', data?.location?.y || 0)
				return data
			} catch (error: any) {
				toast(error?.response?.data?.messageToUser)
				return null
			}
		},
		enabled: editMode,
	})

	const editAPs = useMutation({
		mutationKey: ['editAPs'],
		mutationFn: async (payload: TAddArtisanPro) => {
			const updatedPayload = {
				name: payload?.name,
				address: payload?.address,
				artisianProNetworkID: payload.apnId,
				latitude: payload.latitude,
				longitude: payload.longitude,
			}
			return await authAxios.put(`/artisian-pro/${apsId}`, updatedPayload)
		},
		onError: (err: any) => {
			toast(err?.response?.data?.userToMessage)
		},
		onSuccess: ({ data }) => {
			toast(data?.message)
			queryClient.refetchQueries({ queryKey: ['allaps'] })
			handleCloseDrawer()
		},
	})

	const handleEditAPs = useCallback(
		(values: TAddArtisanPro) => {
			editAPs.mutate(values)
		},
		[editAPs]
	)

	const handleSave = useCallback(
		(values: TAddArtisanPro) =>
			editMode ? handleEditAPs(values) : handleAddAPs(values),
		[editMode, handleAddAPs, handleEditAPs]
	)

	useEffect(() => {
		if (editMode) return
		getCurrentLocation()
	}, [editMode, getCurrentLocation])

	useEffect(() => {
		if (editMode) setValue('isEditMode', true)
	}, [editMode, setValue])

	useEffect(() => {
		if (userDetails?.accountType === userRoles.artisanProNetworkManager) {
			setValue('apnId', userDetails?.apNetworkId)
		}
		if (
			userDetails?.accountType === userRoles.CsinkManager ||
			userDetails?.accountType === userRoles.BiomassAggregator ||
			userDetails?.accountType === userRoles.artisanProNetworkManager
		) {
			setValue('cSinkManagerId', userDetails?.csinkManagerId ?? '')
		}
	}, [
		setValue,
		userDetails?.accountType,
		userDetails?.apNetworkId,
		userDetails?.csinkManagerId,
	])

	useEffect(() => {
		if (AllAPNs.isSuccess && AllAPNs.data?.length === 1) {
			const defaultArtisanProNetwork = AllAPNs?.data[0]
			setValue('apnId', defaultArtisanProNetwork.value)
		}
	}, [AllAPNs.data, AllAPNs.isSuccess, setValue])

	return (
		<FormProvider {...form}>
			<Stack gap={theme.spacing(3)}>
				{[
					userRoles.Admin,
					userRoles.CsinkManager,
					userRoles.BiomassAggregator,
				].includes(userDetails?.accountType as userRoles) && !editMode ? (
					<Autocomplete
						value={
							csinkManagersList?.data?.find(
								(item) => item.value === watch('cSinkManagerId')
							) ?? null
						}
						onChange={(_, newValue: any) => {
							setValue('cSinkManagerId', newValue?.value)
							clearErrors('cSinkManagerId')
							setValue('apnId', '')
							clearErrors('apnId')
						}}
						disabled={
							userDetails?.accountType === userRoles.CsinkManager ||
							userDetails?.accountType === userRoles.BiomassAggregator
						}
						options={csinkManagersList?.data || []}
						renderInput={(params) => (
							<CustomTextField
								schema={addArtisanPro(addDummyCsinkManagers)}
								{...params}
								name='cSinkManagerId'
								label='Csink Manager'
								error={!!errors?.cSinkManagerId?.message}
								helperText={errors?.cSinkManagerId?.message}
							/>
						)}
					/>
				) : null}
				{userDetails?.accountType !== userRoles.artisanProNetworkManager &&
				!editMode &&
				AllAPNs?.data?.length !== 0 ? (
					<Autocomplete
						options={AllAPNs.data || []}
						getOptionLabel={(option) => option.label}
						disabled={watch('cSinkManagerId') === ''}
						value={
							AllAPNs.data?.find((item) => item.value === watch('apnId')) ||
							null
						}
						renderInput={(params) => (
							<CustomTextField
								schema={addArtisanPro(addDummyCsinkManagers)}
								{...params}
								name='apnId'
								label='Select Artisan Pro Network'
								error={!!errors.apnId?.message}
								helperText={errors.apnId?.message}
							/>
						)}
						onChange={(_, selectedOption) => {
							if (selectedOption) {
								setValue('apnId', selectedOption.value)
								clearErrors('apnId')
							} else {
								setValue('apnId', '')
							}
						}}
					/>
				) : null}
				<CustomTextField
					schema={addArtisanPro(addDummyCsinkManagers)}
					id='name'
					label='Name'
					variant='outlined'
					autoComplete='off'
					fullWidth
					error={!!errors.name?.message}
					helperText={errors?.name?.message}
					{...register('name')}
					InputLabelProps={
						editMode
							? {
									shrink: editMode,
							  }
							: {}
					}
				/>
				<CustomTextField
					schema={addArtisanPro(addDummyCsinkManagers)}
					id='address'
					label='Location Name'
					variant='outlined'
					autoComplete='off'
					fullWidth
					error={!!errors.address?.message}
					helperText={errors?.address?.message}
					{...register('address')}
					InputLabelProps={
						editMode
							? {
									shrink: editMode,
							  }
							: {}
					}
				/>
				<>
					<Stack direction='row' columnGap={2}>
						<CustomTextField
							schema={addArtisanPro(addDummyCsinkManagers)}
							id='latitude'
							label='Latitude'
							variant='outlined'
							fullWidth
							type='number'
							hideNumberArrows
							inputProps={{
								step: 'any',
							}}
							error={!!errors.latitude?.message}
							helperText={errors?.latitude?.message}
							{...register('latitude', {
								setValueAs: (value) => Number(parseFloat(value).toFixed(6)),
							})}
						/>

						<CustomTextField
							schema={addArtisanPro(addDummyCsinkManagers)}
							id='longitude'
							label='Longitude'
							variant='outlined'
							fullWidth
							type='number'
							hideNumberArrows
							inputProps={{
								step: 'any',
							}}
							error={!!errors.longitude?.message}
							helperText={errors?.longitude?.message}
							{...register('longitude', {
								setValueAs: (value) => Number(parseFloat(value).toFixed(6)),
							})}
						/>
					</Stack>

					<GoogleMapsWithNonDraggableMarker
						center={{
							lat: Number(watch('latitude')),
							lng: Number(watch('longitude')),
						}}
						setMapCenter={setMapCenter}
						mapContainerStyle={{
							width: '100%',
							height: 380,
							position: 'relative',
						}}
					/>
				</>
				{fields.map((field, index) => (
					<Stack key={field.id} direction='row' alignItems='center' gap={2}>
						<TwoColumnLayout
							left={
								fetchBiomassTypeList?.length ?? 0 > 0 ? (
									<AddMethaneCompensateFields
										schema={addArtisanPro(addDummyCsinkManagers)}
										fieldIndex={index}
										editMode={editMode}
										fetchBiomassTypeList={fetchBiomassTypeList}
									/>
								) : (
									<Stack
										minHeight={theme.spacing(40)}
										alignItems='center'
										justifyContent='center'
										textAlign='center'>
										<Typography color='error' textAlign='center'>
											There is no crop assigned to your Csink Manager. Please
											contact Admin for the same.
										</Typography>
									</Stack>
								)
							}
							right={
								<IconButton
									color='error'
									onClick={() => handleRemove(index)}
									size='small'>
									<RemoveCircleOutline />
								</IconButton>
							}
							gridBreakpoints={[11, 0.5]}
						/>
					</Stack>
				))}
				{showNoCropsError && (
					<Stack direction='row' alignItems='center' gap={2}>
						<TwoColumnLayout
							left={
								<Stack
									minHeight={theme.spacing(40)}
									alignItems='center'
									justifyContent='center'
									textAlign='center'>
									<Typography color='error' textAlign='center'>
										There is no crop assigned to your Csink Manager. Please
										contact Admin for the same.
									</Typography>
								</Stack>
							}
							right={
								<IconButton
									color='error'
									onClick={() => setShowNoCropsError(false)}
									size='small'>
									<RemoveCircleOutline />
								</IconButton>
							}
							gridBreakpoints={[11, 0.5]}
						/>
					</Stack>
				)}
				{!editMode ? (
					<Stack flexDirection='column'>
						<Button
							variant='text'
							sx={{
								justifyContent: 'flex-start',
							}}
							onClick={handleAdd}
							startIcon={<Add color='primary' />}>
							Add Methane Compensate
						</Button>
						{watch('createManager') ? (
							<Stack gap={2}>
								<Button
									variant='text'
									sx={{
										justifyContent: 'flex-start',
									}}
									onClick={handleRemoveManagerDetails}
									startIcon={<Remove color='primary' />}>
									Remove Manager Details
								</Button>
								<Typography variant='body2' pb={theme.spacing(1)}>
									Manager Details:
								</Typography>
								<AddManagerDetailsFields
									editMode={editMode}
									schema={addArtisanPro(addDummyCsinkManagers)}
								/>
							</Stack>
						) : (
							<Button
								variant='text'
								sx={{
									justifyContent: 'flex-start',
								}}
								onClick={handleAddManagerDetails}
								startIcon={<Add color='primary' />}>
								Add Manager Details
							</Button>
						)}
						{!showBiomassPreprocessingFields ? (
							<Button
								variant='text'
								sx={{
									justifyContent: 'flex-start',
								}}
								onClick={() => setShowBiomassPreprocessingFields(true)}
								startIcon={<Add color='primary' />}>
								Add Biomass Pre-Processing
							</Button>
						) : null}
					</Stack>
				) : null}
				{showBiomassPreprocessingFields ? (
					<AddBiomassPreProcessFields
						editMode={editMode}
						schema={addArtisanPro(addDummyCsinkManagers)}
					/>
				) : null}
				<Stack
					direction='row'
					justifyContent='space-between'
					gap={2}
					className='buttonContainer'>
					<Button
						onClick={handleCloseDrawer}
						sx={{ border: `1px solid ${theme.palette.primary.main}` }}>
						Cancel
					</Button>
					<LoadingButton
						loading={addAPs.isPending}
						disabled={addAPs.isPending}
						onClick={handleSubmit(handleSave)}
						variant='contained'>
						{editMode ? 'Save' : 'Add'}
					</LoadingButton>
				</Stack>
			</Stack>
		</FormProvider>
	)
}
