import { authAxios, useAuthContext } from '@/contexts'
import { userRoles } from '@/utils/constant'
import { Close } from '@mui/icons-material'
import { theme } from '@/lib/theme/theme'
import {
	Box,
	Divider,
	IconButton,
	MenuItem,
	Stack,
	styled,
	TextField,
	Typography,
} from '@mui/material'
import { FC, useEffect, useMemo, useState } from 'react'
import EditIcon from '@/assets/icons/editIcon.svg'
import {
	AddBiomassAggregator,
	AddCSinkManager,
	AddAPN,
	AddCSinkNetwork,
	AddArtisanPros,
	AddEntityContent,
} from './SubComponents'
import { useQuery, UseQueryResult } from '@tanstack/react-query'
import { CsinkManager, EntityEnum, ICSinkManagerResponse } from '@/interfaces'
import { NoData } from '@/components/NoData'
import { BiomassOptionType, Nullable } from '@/types'

type TProps = {
	handleCloseDrawer: () => void
	editMode?: boolean
	baId?: string
	cSinkId?: string
	apnId?: string
	apsId?: string
	cSinkNetworkId?: string
	type: string
	fetchBiomassTypeList?: BiomassOptionType[]
}

type TRenderForm = TProps & {
	type: EntityEnum
	csinkManagersList?: UseQueryResult<
		{
			label: string
			value: string
		}[],
		Error
	>
	fetchBiomassTypeList?: BiomassOptionType[]
	fetchCsinkManagerDetailsQuery: UseQueryResult<CsinkManager, Error>
}

const DisplayContent: FC<{
	icon?: Nullable<{ id: string; url: string }>
	entityName: string
	subText: string
}> = ({ icon, entityName, subText }) => (
	<Stack flexDirection='row' gap={2} width={'100%'}>
		{icon ? (
			<Box
				component='img'
				height={theme.spacing(5)}
				width={theme.spacing(5)}
				alt='image'
				src={icon?.url}
			/>
		) : null}
		<Stack flexDirection='column'>
			<Typography variant='body1' fontWeight='bold'>
				{entityName}
			</Typography>
			{subText ? (
				<Typography variant='subtitle1' textTransform='capitalize'>
					{subText}
				</Typography>
			) : null}
		</Stack>
	</Stack>
)

const RenderForm: FC<TRenderForm> = ({
	type,
	handleCloseDrawer,
	editMode,
	baId,
	apnId,
	apsId,
	cSinkId,
	cSinkNetworkId,
	csinkManagersList,
	fetchBiomassTypeList,
	fetchCsinkManagerDetailsQuery,
}) => {
	switch (type) {
		case EntityEnum.cSinkManager:
			return (
				<AddCSinkManager
					handleCloseDrawer={handleCloseDrawer}
					editMode={editMode}
					cSinkId={cSinkId}
					type={type}
				/>
			)
		case EntityEnum.ba:
			return (
				<AddBiomassAggregator
					handleCloseDrawer={handleCloseDrawer}
					editMode={editMode}
					baId={baId}
				/>
			)
		case EntityEnum.apn:
			return (
				<AddAPN
					handleCloseDrawer={handleCloseDrawer}
					editMode={editMode}
					apnId={apnId}
					csinkManagersList={csinkManagersList}
				/>
			)
		case EntityEnum.cSinkNetwork:
			return (
				<AddCSinkNetwork
					fetchBiomassTypeList={fetchBiomassTypeList}
					cSinkNetworkId={cSinkNetworkId}
					handleCloseDrawer={handleCloseDrawer}
					editMode={editMode}
					csinkManagersList={csinkManagersList}
				/>
			)

		case EntityEnum.aps:
			return (
				<AddArtisanPros
					fetchBiomassTypeList={fetchBiomassTypeList}
					apsId={apsId}
					handleCloseDrawer={handleCloseDrawer}
					editMode={editMode}
					csinkManagersList={csinkManagersList}
				/>
			)
		case EntityEnum.mixingType:
			return (
				<Stack paddingTop={theme.spacing(2.5)} gap={theme.spacing(1.5)}>
					<AddEntityContent
						// subheading={entitySubHeading}
						label='Mixing'
						showSearchField={false}
						{...(!fetchCsinkManagerDetailsQuery?.data?.mixingTypes?.length
							? {
									iconButton: (
										<Box
											component='img'
											src={EditIcon}
											alt='edit-icon'
											height={28}
											width={24}
										/>
									),
							  }
							: {})}
						entityType={EntityEnum.mixingType}
						fetchCsinkManagerDetailsQuery={fetchCsinkManagerDetailsQuery}
					/>
					{fetchCsinkManagerDetailsQuery?.data?.mixingTypes?.length ? (
						fetchCsinkManagerDetailsQuery?.data?.mixingTypes?.map(
							(mixingType, index) => {
								return (
									<Stack key={index} alignItems='center' gap={2}>
										<DisplayContent
											subText={mixingType?.category}
											entityName={mixingType?.name}
										/>
										<Divider
											color={theme.palette.custom.grey[800]}
											sx={{
												width: '100%',
											}}
										/>
									</Stack>
								)
							}
						)
					) : (
						<NoData size='small' />
					)}
				</Stack>
			)
		case EntityEnum.applicationsType:
			return (
				<Stack paddingTop={theme.spacing(2.5)} gap={theme.spacing(1.5)}>
					<AddEntityContent
						// subheading={entitySubHeading}
						label='Application'
						showSearchField={false}
						{...(!fetchCsinkManagerDetailsQuery?.data?.applicationTypes?.length
							? {
									iconButton: (
										<Box
											component='img'
											src={EditIcon}
											alt='edit-icon'
											height={28}
											width={24}
										/>
									),
							  }
							: {})}
						entityType={EntityEnum.applicationsType}
						fetchCsinkManagerDetailsQuery={fetchCsinkManagerDetailsQuery}
					/>
					{fetchCsinkManagerDetailsQuery?.data?.applicationTypes?.length ? (
						fetchCsinkManagerDetailsQuery?.data?.applicationTypes?.map(
							(applicationType, index) => {
								return (
									<Stack key={index} alignItems='center'>
										<DisplayContent
											subText={applicationType?.category}
											entityName={applicationType?.type}
										/>
										<Divider
											color={theme.palette.custom.grey[200]}
											sx={{
												width: '100%',
											}}
										/>
									</Stack>
								)
							}
						)
					) : (
						<NoData size='small' />
					)}
				</Stack>
			)
		default:
			return null
	}
}

export const AddEntity: FC<TProps> = ({
	handleCloseDrawer,
	editMode = false,
	baId,
	cSinkId,
	apnId,
	apsId,
	cSinkNetworkId,
	type,
	fetchBiomassTypeList,
}) => {
	const { userDetails } = useAuthContext()
	const [selectedOption, setSelectedOption] = useState<EntityEnum | string>('')

	const csinkManagersList = useQuery({
		queryKey: ['csinkManagersList'],
		queryFn: () => {
			const queryParams = new URLSearchParams({
				limit: '1000',
				page: '0',
			})
			return authAxios.get<ICSinkManagerResponse>(
				`/csink-manager?${queryParams.toString()}`
			)
		},
		select: ({ data }) =>
			data?.csinkManagers?.map((item) => ({
				label: `${item.name} (${item.shortName})`,
				value: item.id,
			})),
	})

	const excludedTypes = [
		EntityEnum.apn,
		EntityEnum.ba,
		EntityEnum.aps,
		EntityEnum.cSinkNetwork,
	]
	const isEnabledType = !excludedTypes.includes(type as EntityEnum)

	const fetchCsinkManagerDetailsQuery = useQuery({
		queryKey: ['fetchCsinkManagerDetailsQuery'],
		queryFn: () => {
			const URI = `/csink-manager/${cSinkId}`
			return authAxios.get<CsinkManager>(URI)
		},
		select: ({ data }) => {
			return data
		},
		enabled: isEnabledType,
	})

	const options = useMemo(
		() => [
			{
				label: 'Csink Manager',
				value: EntityEnum.cSinkManager,
				show: [userRoles.Admin].includes(userDetails?.accountType as userRoles),
			},
			{
				label: 'Biomass Aggregator',
				value: EntityEnum.ba,
				show: [userRoles.Admin, userRoles.CsinkManager].includes(
					userDetails?.accountType as userRoles
				),
			},
			{
				label: 'Artisan Pro Network',
				value: EntityEnum.apn,
				show: [
					userRoles.Admin,
					userRoles.CsinkManager,
					userRoles.BiomassAggregator,
				].includes(userDetails?.accountType as userRoles),
			},
			{
				label: 'Csink Network',
				value: EntityEnum.cSinkNetwork,
				show: [
					userRoles.Admin,
					userRoles.CsinkManager,
					userRoles.BiomassAggregator,
				].includes(userDetails?.accountType as userRoles),
			},
			{
				label: 'Artisan Pros',
				value: EntityEnum.aps,
				show: [
					userRoles.Admin,
					userRoles.CsinkManager,
					userRoles.BiomassAggregator,
					userRoles.artisanProNetworkManager,
				].includes(userDetails?.accountType as userRoles),
			},
		],
		[userDetails?.accountType]
	)

	useEffect(() => {
		if (!editMode) return
		setSelectedOption(type)
	}, [editMode, type])

	const editHeading: {
		[key in EntityEnum | string]: string
	} = {
		[EntityEnum.ba]: 'Edit Biomass Aggregator',
		[EntityEnum.cSinkManager]: 'Edit CSink Manager',
		[EntityEnum.mixingType]: 'Select Mixing Type',
		[EntityEnum.applicationsType]: 'Select Application Type',
		[EntityEnum.apn]: 'Edit Artisan Pro Network',
		[EntityEnum.aps]: 'Edit Artisan Pros',
		[EntityEnum.cSinkNetwork]: 'Edit Csink Network',
	}
	return (
		<StyleContainer>
			<Stack className='header'>
				<Stack
					direction='row'
					spacing={1}
					alignItems='center'
					width='100%'
					justifyContent='space-between'>
					<Typography variant='h4'>
						{editMode ? editHeading[type] : 'Add Entity'}
					</Typography>
					<IconButton onClick={handleCloseDrawer}>
						<Close />
					</IconButton>
				</Stack>
			</Stack>
			<Stack className='container'>
				{!editMode ? (
					<TextField
						value={selectedOption}
						onChange={(e) => {
							setSelectedOption(e.target.value)
						}}
						select
						label='Select Entity Type'>
						{options.map((option) =>
							option.show ? (
								<MenuItem key={option.value} value={option.value}>
									{option.label}
								</MenuItem>
							) : null
						)}
					</TextField>
				) : null}
				{selectedOption !== '' ? (
					<RenderForm
						handleCloseDrawer={handleCloseDrawer}
						fetchBiomassTypeList={fetchBiomassTypeList}
						type={selectedOption as EntityEnum}
						editMode={editMode}
						baId={baId}
						cSinkId={cSinkId}
						apnId={apnId}
						apsId={apsId}
						cSinkNetworkId={cSinkNetworkId}
						csinkManagersList={csinkManagersList}
						fetchCsinkManagerDetailsQuery={fetchCsinkManagerDetailsQuery}
					/>
				) : null}
			</Stack>
		</StyleContainer>
	)
}

const StyleContainer = styled(Stack)(({ theme }) => ({
	gap: theme.spacing(2),
	'.header': {
		flexDirection: 'row',
		justifyContent: 'space-between',
		alignItems: 'center',
		padding: theme.spacing(4.5, 2),
		borderBottom: `${theme.spacing(0.125)} solid ${
			theme.palette.neutral['100']
		}`,
	},
	'.container': {
		padding: theme.spacing(2, 4),
		gap: theme.spacing(4),
		'.formcontrol': {
			gap: theme.spacing(0.6),
			'.label': {
				color: theme.palette.neutral[500],
			},
		},
		'.buttonContainer': {
			gap: theme.spacing(2),
			button: {
				width: theme.spacing(30),
				height: theme.spacing(4.5),
				padding: theme.spacing(1, 2.5),
				marginBottom: theme.spacing(10),
			},
		},
	},
}))
