import { IImage } from '@/interfaces'
import { proxyImage } from '@/utils/helper'
import {
	Avatar,
	AvatarGroup,
	Card,
	Stack,
	Tooltip,
	Typography,
	useTheme,
} from '@mui/material'
import { GridRenderCellParams } from '@mui/x-data-grid'

interface IParamsData {
	name: string
	number: string
	phoneNo: string
	email: string
}
interface IProps {
	params: GridRenderCellParams
	mapKey: string
	imageMaxCount?: number
}

export const MultiImageRowViewer = ({
	params,
	mapKey,
	imageMaxCount = 2,
}: IProps) => {
	const theme = useTheme()
	const managerDetailsLength =
		params?.row?.[mapKey ?? 'managerDetails']?.length - imageMaxCount
	const suffix = managerDetailsLength > 0 ? `+ ${managerDetailsLength}` : ''
	return (
		<Tooltip
			title={
				<Stack
					spacing={2}
					sx={{
						minHeight: 40,
						minWidth: 40,
						padding: 1,
					}}>
					{params?.row?.[mapKey ?? 'managerDetails']?.map(
						({ name, email, number, phoneNo }: IParamsData, index: number) => (
							<Card
								key={index}
								sx={{
									padding: 2,
								}}>
								<Typography variant='body1'>Name: {name || '-'}</Typography>
								<Typography variant='body1'>Email: {email || '-'}</Typography>
								<Typography variant='body1'>
									Phone Number: {(number ?? phoneNo) || '-'}
								</Typography>
							</Card>
						)
					)}
				</Stack>
			}
			placement='right'
			arrow>
			{params?.row?.[mapKey ?? 'managerDetails']?.length > 1 ? (
				<Stack direction='row' alignItems='center' spacing={1}>
					<AvatarGroup>
						{params?.row?.[mapKey ?? 'managerDetails']
							?.slice(0, imageMaxCount)
							?.map(
								(
									{ profileImageUrl }: { profileImageUrl: IImage },
									index: number
								) => (
									<Avatar
										key={index}
										src={proxyImage(profileImageUrl?.path)}
										alt={'avatar'}
										sx={{
											height: 37,
											width: 35,
										}}
									/>
								)
							)}
					</AvatarGroup>
					<Typography color='primary.light' variant='body1'>
						{suffix}
					</Typography>
				</Stack>
			) : (
				<Stack flexDirection='row' alignItems='center'>
					<Avatar
						src={proxyImage(params.row[mapKey]?.[0]?.profileImageUrl?.path)}
						alt={'avatar'}
						sx={{
							height: 37,
							width: 35,
						}}
					/>
					<Typography
						variant='subtitle1'
						pl={1}
						sx={{
							color: theme.palette.neutral['900'],
						}}>
						{params?.row?.[mapKey ?? 'managerDetails']?.[0].name ?? ''}
					</Typography>
				</Stack>
			)}
		</Tooltip>
	)
}
