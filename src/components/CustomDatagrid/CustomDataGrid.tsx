import {
	FormControl,
	LinearProgress,
	MenuItem,
	Select,
	SelectChangeEvent,
	Stack,
	SxProps,
	Theme,
	Typography,
	alpha,
	styled,
	useTheme,
} from '@mui/material'
import { DataGrid, DataGridProps } from '@mui/x-data-grid'
import { FC, ReactNode, useCallback } from 'react'
import { useNavigate, useSearchParams } from 'react-router-dom'
import { CustomPagination, NoData } from '..'
import { defaultLimit, defaultPage, pageLimits } from '@/utils/constant'
import { FormatNumber } from '@/utils/helper'

interface CustomDataGridProps extends DataGridProps {
	headerComponent?: ReactNode
	headerEndComponent?: ReactNode
	showPagination: boolean
	pageName?: string
	limitName?: string
	showPaginationDetails?: boolean
	showRowsPerPage?: boolean
	headerStyle?: SxProps<Theme>
}

export const CustomDataGrid: FC<CustomDataGridProps> = ({
	headerComponent,
	headerEndComponent,
	showPagination,
	rowCount,
	pageName,
	limitName,
	showPaginationDetails = true,
	showRowsPerPage = true,
	headerStyle,
	...props
}) => {
	const theme = useTheme()
	const navigate = useNavigate()
	const [searchParams] = useSearchParams()
	const paramsLimit = searchParams.get(limitName || 'limit') ?? defaultLimit
	const paramsPage = searchParams.get(pageName || 'page') ?? defaultPage

	const handleChangeRowsPerPage = useCallback(
		(event: SelectChangeEvent) => {
			const nsp = new URLSearchParams(window.location.search)
			nsp.set(limitName || 'limit', String(event.target.value || 25))
			nsp.set(pageName || 'page', String(0))

			navigate(`?${nsp.toString()}`, { replace: true })
		},
		[limitName, navigate, pageName]
	)

	return (
		<Stack spacing={2}>
			<Stack
				direction='row'
				justifyContent='space-between'
				alignItems='center'
				rowGap={1}
				flexWrap='wrap'
				sx={headerStyle}>
				<Stack>{headerComponent || null}</Stack>
				{showPaginationDetails && (
					<Stack direction='row' spacing={2} alignItems='center'>
						{headerEndComponent || null}
						{showRowsPerPage && (
							<>
								<Typography variant='overline' textTransform='none'>
									Row per page:
								</Typography>
								<FormControl>
									<Select
										value={String(paramsLimit)}
										onChange={handleChangeRowsPerPage}
										sx={{
											width: theme.spacing(12.5),
										}}>
										{pageLimits.map((limit, index) => (
											<MenuItem key={index} value={limit}>
												{limit}
											</MenuItem>
										))}
									</Select>
								</FormControl>
								<Typography variant='overline' textTransform='none'>
									{Number(paramsPage) * Number(paramsLimit) + 1}-
									{Math.min(
										(Number(paramsPage) + 1) * Number(paramsLimit),
										rowCount ?? 0
									)}{' '}
									of {FormatNumber(rowCount ?? 0)}
								</Typography>
							</>
						)}
					</Stack>
				)}
			</Stack>
			<StyledDataGrid
				slots={{
					loadingOverlay: LinearProgress,
					noRowsOverlay: NoData,
				}}
				hideFooter={true}
				paginationModel={{
					pageSize: Number(paramsLimit ?? 10),
					page: Number(paramsPage ?? 0),
				}}
				paginationMode='server'
				rowCount={rowCount ?? 0}
				getRowHeight={() => 'auto'}
				disableVirtualization
				disableColumnFilter
				disableColumnMenu
				sx={{
					...props.sx,
					'.MuiDataGrid-virtualScroller': {
						minHeight: rowCount || props.rows?.length > 0 ? 0 : 400,
					},
				}}
				{...props}
			/>
			<Stack direction='row' alignItems='center' justifyContent='center'>
				{showPagination && !!rowCount && (
					<CustomPagination
						rowCount={rowCount}
						pageName={pageName}
						limitName={limitName}
					/>
				)}
			</Stack>
		</Stack>
	)
}

const StyledDataGrid = styled(DataGrid)(({ theme }) => ({
	borderRadius: 0,
	'.MuiDataGrid-cellContent': {
		...theme.typography.subtitle1,
		color: theme.palette.neutral['900'],
	},
	'.MuiDataGrid-columnHeadersInner': {
		background: alpha(theme.palette.primary.light, 0.05),
	},
	'.MuiDataGrid-columnHeaderTitle': {
		color: theme.palette.primary.dark,
		...theme.typography.subtitle2,
		textOverflow: 'clip',
		whiteSpace: 'break-spaces',
		lineHeight: 1,
		height: '100%',
	},
	'.MuiDataGrid-withBorderColor': {
		borderColor: theme.palette.divider,
	},
	'.MuiDataGrid-cellContent, .MuiDataGrid-cellContent, .MuiTablePagination-selectLabel, .MuiTablePagination-displayedRows':
		{
			fontWeight: theme.typography.body1.fontWeight,
		},
	'.MuiDataGrid-cell:focus, .MuiDataGrid-cell:focus-within': {
		outline: 0,
	},
	'.MuiDataGrid-cell': {
		minHeight: `${theme.spacing(6.5)} !important`,
		paddingBottom: theme.spacing(1),
		paddingTop: theme.spacing(1),
		overflowWrap: 'anywhere',
	},
	'.MuiDataGrid-row:hover': {
		color: theme.palette.primary.main,
		cursor: 'pointer',
	},
	'&.MuiDataGrid-root': {
		flexDirection: 'column-reverse',
	},
	'.MuiDataGrid-selectedRowCount': {
		visibility: 'hidden',
	},
	'.MuiDataGrid-columnHeader:focus': {
		outline: 'none',
	},

	'.MuiDataGrid': {
		minHeight: 400,
	},
	'.no_data_image': {
		height: 200,
	},
}))
