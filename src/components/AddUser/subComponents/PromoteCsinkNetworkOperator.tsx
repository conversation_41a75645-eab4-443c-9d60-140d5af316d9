import { authAxios } from '@/contexts'
import {
	AddOperatorTypeEnum,
	TModalTypeForUserManagement,
	User,
} from '@/interfaces'
import { useMutation, useQueryClient } from '@tanstack/react-query'
import { FC, useCallback, useEffect, useRef, useState } from 'react'

import { toast } from 'react-toastify'
import {
	promoteCsinkNetworkOperatorSchema,
	TPromoteCsinkNetworkOperatorSchema,
} from '../schema'
import {
	Avatar,
	Button,
	CircularProgress,
	FormControl,
	FormHelperText,
	IconButton,
	MenuItem,
	Stack,
	StackOwnProps,
	styled,
	Typography,
	useTheme,
} from '@mui/material'
import { PhoneInputComponent } from '@/components/PhoneInput'
import { CustomTextField } from '@/utils/components'
import { useForm } from 'react-hook-form'
import { yupResolver } from '@hookform/resolvers/yup'
import { LoadingButton } from '@mui/lab'
import { handleImageUpload, proxyImage } from '@/utils/helper'
import { CreateOutlined } from '@mui/icons-material'
import { MultipleFileUploader } from '@/components/MultipleFileUploader'

type TProps = {
	setIsActionInfoDrawer: React.Dispatch<React.SetStateAction<boolean>>
	operatorFor: AddOperatorTypeEnum
	user?: User
	modalType: TModalTypeForUserManagement
}
const initialValues = {
	id: '',
	phoneNumber: '',
	countryCode: '',
	name: '',
	email: '',
	trained: false,
	aadhaarNumber: '',
	profileImage: {
		id: null,
		url: null,
	},
	aadhaarImage: '',
	kilnId: null,
	trainingImages: [],
	siteIds: [],
}

export const PromoteCsinkNetworkOperator: FC<TProps> = ({
	setIsActionInfoDrawer,
	operatorFor,
	user,
	modalType,
}) => {
	const [urls, setUrls] = useState('')
	const theme = useTheme()
	const ref = useRef<HTMLLabelElement>(null)
	const queryClient = useQueryClient()

	const {
		register,
		handleSubmit,
		formState: { errors },
		setValue,
		watch,
		clearErrors,
	} = useForm<TPromoteCsinkNetworkOperatorSchema>({
		defaultValues: initialValues,
		mode: 'all',
		resolver: yupResolver<TPromoteCsinkNetworkOperatorSchema>(
			promoteCsinkNetworkOperatorSchema
		),
	})

	const uploadMutation = useMutation({
		mutationFn: async (file: File) => {
			const data = await handleImageUpload(file)
			return data
		},
		onSuccess: (data) => {
			setValue('profileImage', data)
			setUrls(data.url)
			clearErrors('profileImage')
		},
		onError: () => {
			toast('Image upload failed')
		},
	})

	const handleUpload = (event: React.ChangeEvent<HTMLInputElement>) => {
		const file = event.target.files?.[0]
		if (file) {
			uploadMutation.mutate(file)
		}
	}

	const promoteCsinkNetworkOperatorToCsinkNetworkManager = useMutation({
		mutationKey: ['promoteToCsinkNetworkManager'],
		mutationFn: async (values: TPromoteCsinkNetworkOperatorSchema) => {
			const payload = {
				email: values.email || null,
			}
			const { data } = await authAxios.post(
				`/cs-network/${user?.csinkNetworkId}/operator/${user?.id}/promote-manager`,
				payload
			)
			return data
		},
		onSuccess: (data) => {
			toast(data?.message)
			setIsActionInfoDrawer(false)
			queryClient.refetchQueries({ queryKey: ['users'] })
		},
		onError: (error: any) => {
			toast(error?.response?.data?.messageToUser)
		},
	})

	useEffect(() => {
		setValue('name', user?.name || '')
		setValue('countryCode', user?.countryCode || '')
		setValue('email', user?.email || '')
		setValue('phoneNumber', user?.number)
		setValue('profileImage.id', user?.profileImageUrl?.id || '')
		setValue('profileImage.url', user?.profileImageUrl?.url || '')
		setValue('trained', (user?.trainingImageUrls ?? [])?.length > 0 || false)
		setValue('trainingImages', user?.trainingImageUrls || [])
		setUrls(proxyImage(user?.profileImageUrl?.path || ''))
	}, [modalType, operatorFor, setValue, user])

	const handleOnChange = useCallback(
		(value: string, dialCode: string) => {
			setValue('countryCode', `+${dialCode}`)
			setValue('phoneNumber', `${value}`)
			clearErrors('phoneNumber')
			clearErrors('email')
		},
		[clearErrors, setValue]
	)

	const handleFormSubmit = (values: TPromoteCsinkNetworkOperatorSchema) =>
		promoteCsinkNetworkOperatorToCsinkNetworkManager.mutate(values)
	return (
		<>
			<CustomStack component='form' onSubmit={handleSubmit(handleFormSubmit)}>
				<Stack alignItems='center'>
					{uploadMutation.isPending ? (
						<Stack padding={2} borderRadius={1} bgcolor='custom.blue.A100'>
							<CircularProgress />
						</Stack>
					) : (
						<Stack
							ref={ref}
							htmlFor='profile-image-upload-input'
							component='label'
							position='relative'>
							<Avatar
								src={urls || ''}
								alt='Profile error'
								sx={{
									width: 70,
									height: 70,
									border: errors?.profileImage
										? '1px solid red'
										: '1px solid black',
								}}
							/>

							<IconButton
								size='small'
								onClick={() => ref?.current?.click()}
								sx={{
									position: 'absolute',
									right: -15,
									bottom: -10,
									backgroundColor: 'grey.300',
									':hover': {
										backgroundColor: 'grey.300',
									},
								}}>
								<CreateOutlined fontSize='medium' color='inherit' />
							</IconButton>
							<input
								id='profile-image-upload-input'
								style={{
									width: '100%',
									height: '100%',
									cursor: 'pointer',
									position: 'absolute',
									opacity: 0,
								}}
								type='file'
								accept='image/*'
								onChange={(e) => handleUpload(e)}
							/>
						</Stack>
					)}
				</Stack>

				<PhoneInputComponent
					value={watch('phoneNumber') || ''}
					dialCode={watch('countryCode')}
					handleOnChange={handleOnChange}
					isValid={!!errors.phoneNumber?.message}
					getSelectedCountryDialCode={(dialCode) =>
						setValue('countryCode', dialCode)
					}
				/>
				<FormHelperText error={Boolean(errors?.phoneNumber)} sx={{ ml: 2 }}>
					{errors?.phoneNumber && (
						<Typography color='error' variant='caption'>
							{errors?.phoneNumber?.message}
						</Typography>
					)}
				</FormHelperText>
				<CustomTextField
					schema={promoteCsinkNetworkOperatorSchema}
					id='email'
					label='Email'
					variant='outlined'
					placeholder='Enter Your Email'
					fullWidth
					{...register('email')}
					onChange={(e) => {
						setValue('email', e.target.value)
						clearErrors('phoneNumber')
					}}
					error={!!errors.email?.message}
					helperText={errors.email?.message}
				/>
				<CustomTextField
					InputProps={{ readOnly: true }}
					disabled
					schema={promoteCsinkNetworkOperatorSchema}
					id='name'
					label='Name'
					variant='outlined'
					placeholder='Enter Your name'
					fullWidth
					{...register('name')}
					error={!!errors.name?.message}
					helperText={errors.name?.message}
				/>

				<FormControl fullWidth>
					<CustomTextField
						schema={promoteCsinkNetworkOperatorSchema}
						select
						{...register('trained')}
						label='Trained'
						id='select-type'
						value={watch('trained')}
						disabled={operatorFor !== AddOperatorTypeEnum.csink}
						onChange={(event) => {
							setValue('trained', event.target.value === 'true')
							clearErrors('trained')
						}}
						error={!!errors.trained?.message}
						helperText={errors.trained?.message}>
						<MenuItem value='true'>Yes</MenuItem>
						<MenuItem value='false'>No</MenuItem>
					</CustomTextField>
				</FormControl>
				{watch('trained') === true && (
					<Stack>
						<Stack>
							<MultipleFileUploader
								{...(modalType !== 'add' && {
									data: (watch('trainingImages') ?? [])?.map((i) => ({
										...i,
										fileName: i?.path,
									})),
								})}
								heading='Upload or Drag the Training Images '
								sx={{
									height: { xs: 100, md: 166 },
									width: '100%',
								}}
								imageHeight={100}
								setUploadData={(data) => {
									setValue('trainingImages', data)
									clearErrors('trainingImages')
								}}
							/>
						</Stack>
						<FormHelperText error={Boolean(errors.trainingImages)}>
							{errors.trainingImages && (
								<Typography color='error'>
									{errors?.trainingImages?.message}
								</Typography>
							)}
						</FormHelperText>
					</Stack>
				)}

				<Stack
					direction='row'
					justifyContent='space-between'
					mb={10}
					className='buttonContainer'>
					<Button
						onClick={() => setIsActionInfoDrawer(false)}
						sx={{ border: ` 1px solid ${theme.palette.primary.main}` }}>
						Cancel
					</Button>
					<LoadingButton
						loading={promoteCsinkNetworkOperatorToCsinkNetworkManager.isPending}
						disabled={
							promoteCsinkNetworkOperatorToCsinkNetworkManager.isPending
						}
						type='submit'
						variant='contained'>
						{'Promote'}
					</LoadingButton>
				</Stack>
			</CustomStack>
		</>
	)
}

interface CustomStackProps extends StackOwnProps {
	component?: React.ElementType
}
const CustomStack = styled(Stack)<CustomStackProps>(({ theme }) => ({
	marginTop: theme.spacing(2),
	rowGap: theme.spacing(2),
	'.show-subcolumns-btn': {
		fontSize: theme.typography.subtitle1.fontSize,
		fontWeight: theme.typography.subtitle1.fontWeight,
	},
}))
