import { authAxios } from '@/contexts'
import { User } from '@/interfaces'
import { theme } from '@/lib/theme/theme'
import { LoadingButton } from '@mui/lab'
import {
    But<PERSON>,
    Stack,
    StackOwnProps,
    styled,
} from '@mui/material'
import { useMutation, useQueryClient } from '@tanstack/react-query'
import { useForm } from 'react-hook-form'
import { toast } from 'react-toastify'
import {
    addComplianceManagerSchema,
} from '../schema'
import { yupResolver } from '@hookform/resolvers/yup'
import { CustomTextField } from '@/utils/components'
import { InferType } from 'yup'
import { proxyImage } from '@/utils/helper'
import { CustomProfileElement } from '@/components/CustomProfileElement'
import { AxiosError } from 'axios'


const initialValues = (user?: User) => {
    return {
        name: user?.name ?? '',
        email: user?.email ?? '',
        profileImage: {
            id: user?.profileImageUrl?.id || '',
            url: proxyImage(user?.profileImageUrl?.path || '') || '',
        }
    }
}

interface IProps {
    setIsActionInfoDrawer: React.Dispatch<React.SetStateAction<boolean>>
    user?: User
    editmode?: boolean
}
export const AddComplianceManager = ({
    setIsActionInfoDrawer,
    user,
    editmode = false,
}: IProps) => {
    const QueryClient = useQueryClient()
    type FormSchemaType = InferType<typeof addComplianceManagerSchema>

    const {
        register,
        handleSubmit,
        formState: { errors },
        watch,
        setValue,
        clearErrors
    } = useForm<FormSchemaType>({
        defaultValues: initialValues(user),
        mode: 'all',
        resolver: yupResolver(addComplianceManagerSchema),
    })

    const addComplianceManagerMutation = useMutation({
        mutationKey: ['addComplianceManager'],
        mutationFn: async (values: FormSchemaType) => {
            const { profileImage, ...rest } = values
            const { data } = await authAxios.post(
                `/compliance-manager`,
                {
                    ...rest,
                    profileImageId: profileImage?.id || null,
                }
            )
            return { data }
        },
        onSuccess: ({ data }) => {
            toast(data?.message)
            QueryClient.refetchQueries({ queryKey: ['users'] })
            setIsActionInfoDrawer(false)
        },
        onError: (error: AxiosError) => {
            toast((error?.response?.data as { messageToUser: string })?.messageToUser)
        },
    })

    const editComplianceManagerMutation = useMutation({
        mutationKey: ['editComplianceManager'],
        mutationFn: async (values: FormSchemaType) => {
            const { profileImage, ...rest } = values

            const { data } = await authAxios.put(
                `/compliance-manager/${user?.id}`,
                {
                    ...rest,
                    profileImageId: profileImage?.id ?? null,
                }
            )
            return { data }
        },
        onSuccess: ({ data }) => {
            toast(data?.message)
            QueryClient.refetchQueries({ queryKey: ['users'] })
            setIsActionInfoDrawer(false)
        },
        onError: (error: AxiosError) => {
            toast((error?.response?.data as { messageToUser: string })?.messageToUser)
        },
    })

    const handleFormSubmit = (formValues: FormSchemaType) => {
        if (editmode) {
            editComplianceManagerMutation.mutate(formValues)
        } else {
            addComplianceManagerMutation.mutate(formValues)
        }
    }

    return (
        <CustomStack component='form' onSubmit={handleSubmit(handleFormSubmit)}>
            <Stack gap={2}>
                <CustomProfileElement
                    // if editmode and profile image exist then pass it
                    {...(editmode && !!watch('profileImage')
                        && { value: watch('profileImage') })
                    }
                    errorMessage={errors?.profileImage?.id?.message}
                    setValue={(id, url) =>
                        setValue('profileImage', {
                            id,
                            url,
                        })
                    }
                    clearErrors={() => clearErrors('profileImage.id')}
                />
                <CustomTextField
                    watch={watch}
                    schema={addComplianceManagerSchema}
                    id='name'
                    label='Name'
                    // disabled={editmode}
                    variant='outlined'
                    placeholder='Enter Your name'
                    fullWidth
                    {...register('name')}
                    error={!!errors.name?.message}
                    helperText={errors.name?.message}
                />

                <CustomTextField
                    watch={watch}
                    schema={addComplianceManagerSchema}
                    id='email'
                    label='Email'
                    variant='outlined'
                    placeholder='Enter Your email'
                    fullWidth
                    {...register('email')}
                    error={!!errors.email?.message}
                    helperText={errors.email?.message}
                />

                <Stack
                    direction='row'
                    justifyContent='space-between'
                    mb={10}
                    className='buttonContainer'>
                    <Button
                        onClick={() => setIsActionInfoDrawer(false)}
                        sx={{ border: ` 1px solid ${theme.palette.primary.main}` }}>
                        Cancel
                    </Button>
                    <LoadingButton
                        loading={addComplianceManagerMutation.isPending || editComplianceManagerMutation.isPending}
                        disabled={addComplianceManagerMutation.isPending || editComplianceManagerMutation.isPending}
                        type='submit'
                        variant='contained'>
                        Save
                    </LoadingButton>
                </Stack>
            </Stack>
        </CustomStack>
    )
}

interface CustomStackProps extends StackOwnProps {
    component?: React.ElementType
}
const CustomStack = styled(Stack)<CustomStackProps>(({ theme }) => ({
    marginTop: theme.spacing(2),
    rowGap: theme.spacing(9),
}))
