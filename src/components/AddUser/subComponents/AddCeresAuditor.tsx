import { authAxios } from '@/contexts'
import { User } from '@/interfaces'
import { theme } from '@/lib/theme/theme'
import { LoadingButton } from '@mui/lab'
import { Button, Stack, StackOwnProps, styled } from '@mui/material'
import { useMutation, useQueryClient } from '@tanstack/react-query'
import { useForm } from 'react-hook-form'
import { toast } from 'react-toastify'
import { addCeresAuditorSchema } from '../schema'
import { yupResolver } from '@hookform/resolvers/yup'
import { CustomTextField } from '@/utils/components'
import { InferType } from 'yup'
import { proxyImage, showAxiosErrorToast } from '@/utils/helper'
import { CustomProfileElement } from '@/components/CustomProfileElement'
import { AxiosError } from 'axios'
import { useSearchParams } from 'react-router-dom'

const initialValues = (user?: User) => {
	return {
		name: user?.name ?? '',
		email: user?.email ?? '',
		profileImage: {
			id: user?.profileImageUrl?.id || '',
			url: proxyImage(user?.profileImageUrl?.path || '') || '',
		},
	}
}

interface IProps {
	setIsActionInfoDrawer: React.Dispatch<React.SetStateAction<boolean>>
	user?: User
	editMode?: boolean
}
export const AddCeresAuditor = ({
	setIsActionInfoDrawer,
	user,
	editMode = false,
}: IProps) => {
	const QueryClient = useQueryClient()
	type FormSchemaType = InferType<typeof addCeresAuditorSchema>

	const [selectedEntitieTypeParams] = useSearchParams()
	const entityId = selectedEntitieTypeParams.get('entityId') || ''

	const {
		register,
		handleSubmit,
		formState: { errors },
		watch,
		setValue,
		clearErrors,
	} = useForm<FormSchemaType>({
		defaultValues: initialValues(user),
		mode: 'all',
		resolver: yupResolver(addCeresAuditorSchema),
	})

	const addCeresAuditorMutation = useMutation({
		mutationKey: ['addCeresAuditor'],
		mutationFn: async (values: FormSchemaType) => {
			const { profileImage, ...rest } = values
			const { data } = await authAxios.post(`/ceres-auditor`, {
				...rest,
				profileImageId: profileImage?.id || null,
			})
			return { data }
		},
		onSuccess: ({ data }) => {
			toast(data?.message)
			QueryClient.refetchQueries({ queryKey: ['users'] })
			setIsActionInfoDrawer(false)
		},
		onError: (error: AxiosError) => {
			showAxiosErrorToast(error)
		},
	})

	const editCeresAuditorMutation = useMutation({
		mutationKey: ['editCeresAuditor'],
		mutationFn: async (values: FormSchemaType) => {
			const { profileImage, ...rest } = values

			const { data } = await authAxios.put(`/user/${user?.id}`, {
				...rest,
				profileImageId: profileImage?.id || null,
			})
			return { data }
		},
		onSuccess: ({ data }) => {
			toast(data?.message)
			QueryClient.refetchQueries({
				queryKey: ['users'],
			})
			if (entityId)
				QueryClient.invalidateQueries({
					queryKey: ['OrganizationSettingEntityUsers'],
				})
			else
				QueryClient.invalidateQueries({
					queryKey: ['OrganizationSettingUsers'],
				})
			setIsActionInfoDrawer(false)
		},
		onError: (error: AxiosError) => {
			showAxiosErrorToast(error)
		},
	})

	const handleFormSubmit = (formValues: FormSchemaType) => {
		if (editMode) {
			editCeresAuditorMutation.mutate(formValues)
		} else {
			addCeresAuditorMutation.mutate(formValues)
		}
	}

	return (
		<CustomStack component='form' onSubmit={handleSubmit(handleFormSubmit)}>
			<Stack gap={2}>
				<CustomProfileElement
					// if editmode and profile image exist then pass it
					{...(editMode &&
						!!watch('profileImage') && { value: watch('profileImage') })}
					errorMessage={errors?.profileImage?.id?.message}
					setValue={(id, url) =>
						setValue('profileImage', {
							id,
							url,
						})
					}
					clearErrors={() => clearErrors('profileImage.id')}
				/>
				<CustomTextField
					watch={watch}
					schema={addCeresAuditorSchema}
					id='name'
					label='Name'
					// disabled={editmode}
					variant='outlined'
					placeholder='Enter Your name'
					fullWidth
					{...register('name')}
					error={!!errors.name?.message}
					helperText={errors.name?.message}
				/>

				<CustomTextField
					watch={watch}
					schema={addCeresAuditorSchema}
					id='email'
					label='Email'
					variant='outlined'
					placeholder='Enter Your email'
					fullWidth
					{...register('email')}
					error={!!errors.email?.message}
					helperText={errors.email?.message}
				/>

				<Stack
					direction='row'
					justifyContent='space-between'
					mb={10}
					className='buttonContainer'>
					<Button
						onClick={() => setIsActionInfoDrawer(false)}
						sx={{ border: ` 1px solid ${theme.palette.primary.main}` }}>
						Cancel
					</Button>
					<LoadingButton
						loading={
							addCeresAuditorMutation.isPending ||
							editCeresAuditorMutation.isPending
						}
						disabled={
							addCeresAuditorMutation.isPending ||
							editCeresAuditorMutation.isPending
						}
						type='submit'
						variant='contained'>
						Save
					</LoadingButton>
				</Stack>
			</Stack>
		</CustomStack>
	)
}

interface CustomStackProps extends StackOwnProps {
	component?: React.ElementType
}
const CustomStack = styled(Stack)<CustomStackProps>(({ theme }) => ({
	marginTop: theme.spacing(2),
	rowGap: theme.spacing(9),
}))
