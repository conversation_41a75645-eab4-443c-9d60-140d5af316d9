import { authAxios } from '@/contexts'
import { Button, Stack, styled, useTheme } from '@mui/material'
import { useMutation, useQueryClient } from '@tanstack/react-query'
import { FieldError, useForm } from 'react-hook-form'
import { addAdminSchema, TAddAdmin } from '../schema'
import { yupResolver } from '@hookform/resolvers/yup'
import { FC, useCallback, useEffect } from 'react'
import { toast } from 'react-toastify'
import { LoadingButton } from '@mui/lab'
import { CustomProfileElement } from '@/components/CustomProfileElement'
import { User } from '@/interfaces'
import { proxyImage } from '@/utils/helper'
import { AxiosError } from 'axios'
import { CustomTextField } from '@/utils/components'

const initialValues = {
	id: ',',
	name: '',
	email: '',
	profileImage: null,
}

type TProps = {
	setIsActionInfoDrawer: React.Dispatch<React.SetStateAction<boolean>>
	editMode?: boolean
	userData?: User
	isAdmin?: boolean
}

export const AddAdmin: FC<TProps> = ({
	setIsActionInfoDrawer,
	editMode = false,
	userData,
	isAdmin = true,
}) => {
	const theme = useTheme()
	const queryClient = useQueryClient()
	const {
		handleSubmit,
		watch,
		setValue,
		register,
		formState: { errors },
		clearErrors,
	} = useForm<TAddAdmin>({
		defaultValues: initialValues,
		mode: 'all',
		resolver: yupResolver<TAddAdmin>(addAdminSchema),
	})

	const addAdminMutation = useMutation({
		mutationKey: ['addadmin'],
		mutationFn: async (payload: TAddAdmin) => {
			const { profileImage, ...rest } = payload
			const apiUrl = isAdmin ? `` : '/circonomy-employee'
			const body = {
				...rest,
				profileImageId: profileImage?.id ?? null,
			}
			return await authAxios.post(apiUrl, body)
		},
		onSuccess: ({ data }) => {
			toast(data?.message)
			setIsActionInfoDrawer(false)
			queryClient.refetchQueries({
				queryKey: ['users'],
			})
			close()
		},
		onError: (error: AxiosError) => {
			toast((error.response?.data as { messageToUser: string })?.messageToUser)
		},
	})

	const editAdminMutation = useMutation({
		mutationKey: ['editadmin'],
		mutationFn: async (payload: TAddAdmin) => {
			const { profileImage, ...rest } = payload
			const body = {
				...rest,
				profileImageId: profileImage?.id ?? null,
				id: userData?.id,
			}
			const apiUrl = isAdmin ? `` : '/circonomy-employee'
			return await authAxios.put(`${apiUrl}`, body)
		},
		onSuccess: ({ data }) => {
			toast(data?.message)
			setIsActionInfoDrawer(false)
			queryClient.refetchQueries({
				queryKey: ['users'],
			})
			close()
		},
		onError: (error: any) => {
			toast(error.response?.data?.messageToUsere)
		},
	})

	const handleAddOrEditadmin = useCallback(
		async (values: TAddAdmin) => {
			editMode
				? editAdminMutation.mutate(values)
				: addAdminMutation.mutate(values)
		},
		[addAdminMutation, editAdminMutation, editMode]
	)

	useEffect(() => {
		if (!editMode) return
		setValue('name', userData?.name || '')
		setValue('email', userData?.email || '')
		setValue('profileImage', {
			id: userData?.profileImageUrl?.id || '',
			url: proxyImage(userData?.profileImageUrl?.path || '') || '',
		})
	}, [editMode, setValue, userData])

	return (
		<StyledStack>
			<CustomProfileElement
				{...(editMode && !!watch('profileImage')
					? { value: watch('profileImage') }
					: {})}
				errorMessage={errors?.profileImage?.id?.message}
				setValue={(id, url) =>
					setValue('profileImage', {
						id,
						url,
					})
				}
				clearErrors={() => clearErrors('profileImage.id')}
			/>
			<CustomTextField
				schema={addAdminSchema}
				fullWidth
				id='name'
				type='text'
				label='Enter Name '
				variant='outlined'
				error={!!errors.name?.message}
				helperText={(errors?.name as FieldError)?.message}
				{...register('name')}
			/>

			<CustomTextField
				schema={addAdminSchema}
				id='email'
				label='Enter Email'
				autoComplete='off'
				variant='outlined'
				type='email'
				error={!!errors.email?.message}
				helperText={(errors?.email as FieldError)?.message}
				fullWidth
				inputProps={{
					form: {
						autocomplete: 'off',
					},
				}}
				{...register('email')}
			/>

			<Stack
				direction='row'
				justifyContent='space-between'
				className='buttonContainer'>
				<Button
					onClick={() => setIsActionInfoDrawer(false)}
					sx={{ border: ` 1px solid ${theme.palette.primary.main}` }}>
					Cancel
				</Button>{' '}
				<LoadingButton
					loading={addAdminMutation.isPending}
					disabled={addAdminMutation.isPending}
					onClick={handleSubmit(handleAddOrEditadmin)}
					variant='contained'>
					{editMode ? 'Save' : 'Add'}
				</LoadingButton>
			</Stack>
		</StyledStack>
	)
}

const StyledStack = styled(Stack)(({ theme }) => ({
	gap: theme.spacing(4),
}))
