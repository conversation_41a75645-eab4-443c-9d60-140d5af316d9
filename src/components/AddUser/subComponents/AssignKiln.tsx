import { authAxios } from '@/contexts'
import { AddOperatorTypeEnum } from '@/interfaces'
import { Autocomplete, TextField } from '@mui/material'
import { useQuery } from '@tanstack/react-query'

interface IProps {
	operatorFor: AddOperatorTypeEnum
	networkId?: string
	setValue: (id: string) => void
}
export const AssignKiln = ({ operatorFor, networkId, setValue }: IProps) => {
	const AllKilnQuery = useQuery({
		queryKey: ['allKilnsQuery', networkId],
		queryFn: async () => {
			if (operatorFor === AddOperatorTypeEnum.artisanPro) {
				const response = await authAxios.get<{
					siteList: { id: string; name: string; shortCode: string }[]
				}>(`/artisian-pro/${networkId}/site?sendAllSites=true`)
				return response.data
			} else {
				const response = await authAxios.get<{
					kilns: { id: string; name: string; ShortName: string }[]
				}>(`/cs-network/${networkId}/kilns`)
				return response.data
			}
		},
		select: (data) => {
			if ('kilns' in data) {
				return data.kilns.map((item) => ({
					label: `${item.name} (${item.ShortName})`,
					value: item.id,
				}))
			} else if ('siteList' in data) {
				return data.siteList.map((item) => ({
					label: `${item.name} (${item.shortCode})`,
					value: item.id,
				}))
			}
			return []
		},
	})
	return (
		<Autocomplete
			onChange={(_, newValue: any) => {
				setValue(newValue?.value)
			}}
			options={AllKilnQuery?.data ?? []}
			renderInput={(params) => (
				<TextField
					{...params}
					label={operatorFor === AddOperatorTypeEnum.kiln ? 'Kiln' : 'Site'}
				/>
			)}
		/>
	)
}
