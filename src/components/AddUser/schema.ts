import { emailValidator } from '@/pages/login/schema'
import * as Yup from 'yup'

export const addCsinkManager = Yup.object({
	id: Yup.string().required('Please Choose one csink manager'),
	name: Yup.string().required('Please enter name'),
	email: Yup.string().email().required('Please enter email'),
	phone: Yup.string().nullable(),
	countryCode: Yup.string().nullable(),
	profileImage: Yup.object({
		id: Yup.string(),
		url: Yup.string(),
	}).nullable(),
})

export const addBAManager = Yup.object({
	number: Yup.string().nullable(),
	countryCode: Yup.string(),
	name: Yup.string().required('Please enter name'),
	email: emailValidator,
	trained: Yup.boolean().required(),
	trainingImages: Yup.array()
		.nullable()
		.when(['trained'], {
			is: (trained: boolean) => trained,
			then: (schema) =>
				schema
					.required('Please enter training images')
					.min(1, 'Please enter training images'),
			otherwise: (schema) => schema,
		}),
	id: Yup.string().required('Please choose one'),
	profileImage: Yup.object({
		id: Yup.string(),
		url: Yup.string(),
	}).nullable(),
})

export const addAdminSchema = Yup.object({
	name: Yup.string().required('Please enter name'),
	email: Yup.string().email().required('Please enter email'),
	profileImage: Yup.object({
		id: Yup.string(),
		url: Yup.string(),
	}).nullable(),
})

export const promoteCsinkNetworkOperatorSchema = Yup.object({
	profileImage: Yup.object({
		id: Yup.string().nullable(),
		url: Yup.string().nullable(),
	}).test('profileImageRequired', 'Please upload your image', function (value) {
		return !!value?.id
	}),
	name: Yup.string().required('Please enter name'),
	countryCode: Yup.string().nullable(),
	phoneNumber: Yup.string()
		.nullable()
		.when(['email'], {
			is: (email: string) => !email,
			then: (schema) =>
				schema.required('Please enter either phone number or email'),
			otherwise: (schema) => schema,
		}),
	email: Yup.string().email().required('Please enter email'),
	trained: Yup.boolean(),
	trainingImages: Yup.array().nullable().notRequired(),
})

export const addOperator = Yup.object().shape(
	{
		phoneNumber: Yup.string()
			.nullable()
			.when(['email'], {
				is: (email: string) => !email,
				then: (schema) =>
					schema.required('Please enter either phone number or email'),
				otherwise: (schema) => schema,
			}),
		countryCode: Yup.string().nullable(),
		name: Yup.string().required('Please enter name'),
		email: Yup.string()
			.email()
			.when(['phoneNumber'], {
				is: (phoneNumber: string) => !phoneNumber,
				then: (schema) =>
					schema.required('please enter either email or phone number'),
				otherwise: (schema) => schema,
			}),
		aadhaarNumber: Yup.string()
			.nullable()
			.when(['aadhaarImage'], {
				is: (aadhaarImage: boolean) => aadhaarImage,
				then: (schema) => schema.required('Please enter Identification Number'),
				otherwise: (schema) => schema,
			}),
		siteIds: Yup.array().of(Yup.string().required()).min(0),

		aadhaarImage: Yup.string()
			.nullable()
			.when(['aadhaarNumber'], {
				is: (trained: boolean) => trained,
				then: (schema) => schema.required('Please upload your ID Proof'),
				otherwise: (schema) => schema,
			}),
		profileImage: Yup.object({
			id: Yup.string().nullable(),
			url: Yup.string().nullable(),
		}).test(
			'profileImageRequired',
			'Please upload your image',
			function (value) {
				return !!value?.id
			}
		),
		trained: Yup.boolean().required('Please select one'),
		trainingImages: Yup.array()
			.nullable()
			.when(['trained'], {
				is: (trained: boolean) => trained,
				then: (schema) =>
					schema
						.required('Please enter training images')
						.min(1, 'Please enter training images'),
				otherwise: (schema) => schema,
			}),
		kilnId: Yup.string().notRequired(),
		id: Yup.string().required('Please choose one'),
	},
	[
		['aadhaarImage', 'aadhaarNumber'],
		['email', 'phoneNumber'],
	]
)

export const addCsinkFamerOperatorSchema = Yup.object().shape(
	{
		name: Yup.string().required('Please enter name'),
		countryCode: Yup.string().nullable(),
		number: Yup.string()
			.nullable()
			.when(['email'], {
				is: (email: string) => !email,
				then: (schema) =>
					schema.required('Please enter either phone number or email'),
				otherwise: (schema) => schema,
			}),
		email: Yup.string()
			.email()
			.nullable()
			.when(['number'], {
				is: (phoneNumber: string) => !phoneNumber,
				then: (schema) =>
					schema.required('Please enter either email or phone number'),
				otherwise: (schema) => schema,
			}),

		address: Yup.string().nullable().required('Please enter your address'),

		gender: Yup.string().nullable().required('Please select your gender'),

		cropId: Yup.string().nullable().required('Please select a crop'),

		landmark: Yup.string().nullable().notRequired(),

		fieldSize: Yup.number()
			.min(0, 'Field size must be greater than 0')
			.required('Please enter the field size')
			.typeError('Field size must be a number'),

		fieldSizeUnit: Yup.string()
			.nullable()
			.required('Please select a field size unit'),

		farmLatitude: Yup.number()
			.min(0, 'Please enter the farm latitude')
			.required('Please enter the farm latitude')
			.typeError('Latitude must be a number'),

		farmLongitude: Yup.number()
			.min(0, 'Please enter the farm longitude')
			.required('Please enter the farm longitude')
			.typeError('Longitude must be a number'),
		id: Yup.string().required('Please choose one'),
		trained: Yup.boolean().required('Please select one'),
		trainingImages: Yup.array()
			.nullable()
			.when(['trained'], {
				is: (trained: boolean) => trained,
				then: (schema) =>
					schema
						.required('Please enter training images')
						.min(1, 'Please enter training images'),
				otherwise: (schema) => schema,
			}),
	},
	[['number', 'email']]
)

export const editCsinkFamerOperatorSchema = Yup.object().shape(
	{
		name: Yup.string().required('Please enter name'),
		countryCode: Yup.string().nullable(),
		number: Yup.string()
			.nullable()
			.when(['email'], {
				is: (email: string) => !email,
				then: (schema) =>
					schema.required('Please enter either phone number or email'),
				otherwise: (schema) => schema,
			}),
		email: Yup.string()
			.nullable()
			.email()
			.when(['number'], {
				is: (phoneNumber: string) => !phoneNumber,
				then: (schema) =>
					schema.required('Please enter either email or phone number'),
				otherwise: (schema) => schema,
			}),

		address: Yup.string().nullable().notRequired(),
		gender: Yup.string().nullable().notRequired(),
		cropId: Yup.string().nullable().notRequired(),
		landmark: Yup.string().nullable().notRequired(),
		fieldSize: Yup.number().notRequired(),
		fieldSizeUnit: Yup.string().notRequired(),
		farmLatitude: Yup.number().notRequired(),
		farmLongitude: Yup.number().notRequired(),
		id: Yup.string().notRequired(),
		trained: Yup.boolean().required('Please select one'),
		trainingImages: Yup.array()
			.nullable()
			.when(['trained'], {
				is: (trained: boolean) => trained,
				then: (schema) =>
					schema
						.required('Please enter training images')
						.min(1, 'Please enter training images'),
				otherwise: (schema) => schema,
			}),
	},
	[['number', 'email']]
)

export const addComplianceManagerSchema = Yup.object().shape({
	name: Yup.string().required('Please enter name'),
	email: Yup.string().email().required(),
	profileImage: Yup.object({
		id: Yup.string(),
		url: Yup.string(),
	}).nullable(),
})

export const addCeresAuditorSchema = Yup.object().shape({
	name: Yup.string().required('Please enter name'),
	email: Yup.string().email().required(),
	profileImage: Yup.object({
		id: Yup.string(),
		url: Yup.string(),
	}).nullable(),
})

export type TAddCSinkManager = Yup.InferType<typeof addCsinkManager>
export type TAddBaManager = Yup.InferType<typeof addBAManager>
export type TAddOperator = Yup.InferType<typeof addOperator>
export type TAddAdmin = Yup.InferType<typeof addAdminSchema>
export type TAddCsinkFamerOperator = Yup.InferType<
	typeof addCsinkFamerOperatorSchema
>

export type TPromoteCsinkNetworkOperatorSchema = Yup.InferType<
	typeof promoteCsinkNetworkOperatorSchema
>

export type TeditCsinkFamerOperator = Yup.InferType<
	typeof editCsinkFamerOperatorSchema
>
