import { useParams } from 'react-router-dom'
import { IEditOperator, ISite } from '@/interfaces'
import { useMutation, useQuery, useQueryClient } from '@tanstack/react-query'
import { authAxios } from '@/contexts'
import { toast } from 'react-toastify'
import { AxiosError } from 'axios'
import { AssignOperatorSideDrawer } from '../AssignOperatorSideDrawer'

interface TProps {
	handleCloseDrawer: () => void
	siteDetails?: ISite
	subheading?: string
}

export const AssignOperator = ({
	handleCloseDrawer,
	subheading,
	siteDetails,
}: TProps) => {
	const { cSinkNetworkId, artisanProId } = useParams()
	const queryClient = useQueryClient()

	const unassignedOperatorListQuery = useQuery({
		queryKey: ['unassignedOperatorListQuery', artisanProId, cSinkNetworkId],
		queryFn: async () => {
			const { data } = await authAxios<{
				count: number
				artisanProOperator: IEditOperator[]
			}>(`/artisian-pro/${artisanProId}/site/${siteDetails?.id}/operators/v2`)
			
			return data
		},
	})
	
	
	const assignOperatorMutation = useMutation({
		mutationKey: ['assignOperatorMutation', artisanProId],
		mutationFn: async (id : string) => {
			const payload = {
				artisanProOperatorIds: [id],
			}

			return await authAxios.post(
				`/artisian-pro/${artisanProId}/site/${siteDetails?.id}/assign-operator`,
				payload
			)
		},
		onError: (error: AxiosError) => {
			toast((error?.response?.data as { messageToUser: string })?.messageToUser)
		},
		onSuccess: ({ data }) => {
			toast(data?.message)
			queryClient.refetchQueries({
				queryKey: ['siteDetails'],
			})
			handleCloseDrawer()
		},
	})
	return (
		<AssignOperatorSideDrawer
			handleCloseDrawer={handleCloseDrawer}
			handleAdd={(id) => assignOperatorMutation?.mutate(id)}
			heading={'Assign Operator'}
			options={unassignedOperatorListQuery?.data?.artisanProOperator}
			loading={assignOperatorMutation.isPending}
			subheading={subheading}
			siteDetails={siteDetails}
		/>
	)
}
