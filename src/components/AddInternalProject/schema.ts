import * as Yup from 'yup'

export const addInternalProject = (isProjectCode: boolean = true, editMode: boolean) =>
	Yup.object({
		name: Yup.string().required('Please enter project name'),
		projectId: Yup.string().required('Please enter project ID'),
		registryProjectName: Yup.string().required('Please enter registry project name'),
		projectCode: Yup.string().when([], {
			is: () => isProjectCode,
			then: (schema) =>
				schema
					.required('Please enter project code')
					.matches(/^[A-Za-z]+$/, 'Only alphabets are allowed'),
			otherwise: (schema) => schema.notRequired(),
		}),

		startDate: Yup.date().nullable(),
		csinkManager: Yup.string().when([], {
			is: () => !editMode,
			then: (schema) =>
				schema.required('Please enter csinkManage'),
			otherwise: (schema) => schema.notRequired()
		})
		,
		selectedNetwork: Yup.array()
			.of(
				Yup.object({
					id: Yup.string().required('Please select network'),
					isArtisan: Yup.boolean().required('Please select network'),
				})
			)
			.min(1, 'Please select atleast on network')
			.required('Please select  network'),
		comment: Yup.string().notRequired(),
		producerId: Yup.string().nullable().notRequired(),
		producerNumber: Yup.string().notRequired(),
		projectLocation: Yup.string().required(),
		projectSummary: Yup.string().required(),
		projectImageId: Yup.string().required('Please Upload Project Image')
	})

export type TAddInternalProject = Yup.InferType<
	ReturnType<typeof addInternalProject>
>
