import { IImage, ManagerDetails } from '@/interfaces'
import { Close } from '@mui/icons-material'
import { Avatar, IconButton, Stack, styled, Typography } from '@mui/material'
import { ImageCarouselDialog } from '../ImageCarousel'
import { useState } from 'react'

interface IProps {
	close: () => void
	managerDetails: ManagerDetails[] | null
}

export const AdminDetails = ({ close, managerDetails }: IProps) => {
	const [imageToShow, setImageToView] = useState<IImage[]>([])
	return (
		<>
			<StyleContainer>
				<Stack className='header'>
					<Typography variant='h4'>Admins</Typography>
					<IconButton onClick={close}>
						<Close />
					</IconButton>
				</Stack>
				<Stack className='container' spacing={2}>
					{managerDetails?.map((item, index) => (
						<Stack key={index} className='sub-container'>
							<Stack flexDirection='row' gap={2}>
								<Avatar
									alt='image'
									src={item?.profileImageUrl?.url}
									onClick={() => {
										item?.profileImageUrl?.path &&
											setImageToView([
												{
													fileName: item?.profileImageUrl?.path,
													...item?.profileImageUrl,
												},
											])
									}}
								/>

								<Stack sx={{ flexDirection: 'column' }} width='100%'>
									<Typography variant='body1' fontWeight='bold'>
										{item?.managerName}
									</Typography>
									<Typography variant='subtitle1'>
										{item?.managerEmail}
									</Typography>
									{item?.managerPhone ? (
										<Typography variant='subtitle1' textTransform='capitalize'>
											({item?.countryCode}-{item?.managerPhone})
										</Typography>
									) : null}
								</Stack>
							</Stack>
						</Stack>
					))}
				</Stack>
			</StyleContainer>
			{imageToShow?.length ? (
				<ImageCarouselDialog
					open={!!imageToShow?.length}
					close={() => {
						setImageToView([])
					}}
					ImagesList={imageToShow ?? []}
					imageIndex={0}
					showDownload={false}
				/>
			) : null}
		</>
	)
}

const StyleContainer = styled(Stack)(({ theme }) => ({
	gap: theme.spacing(2),
	'.header': {
		flexDirection: 'row',
		justifyContent: 'space-between',
		alignItems: 'center',
		padding: theme.spacing(3.5, 2),
		borderBottom: `${theme.spacing(0.125)} solid ${
			theme.palette.neutral['100']
		}`,
	},
	'.container': {
		padding: theme.spacing(2),
		maxHeight: 'calc( 100vh - 200px)',
		overflowY: 'auto',
		'.sub-container': {
			flexDirection: 'row',
			justifyContent: 'space-between',
			width: '100%',
			padding: theme.spacing(1),
			borderBottom: `${theme.spacing(0.125)} solid ${
				theme.palette.neutral['100']
			}`,
		},
	},
}))
