import { EntityTabEnum } from '@/utils/constant'
import {
	Button,
	IconButton,
	Menu,
	MenuItem,
	Stack,
	styled,
	Typography,
} from '@mui/material'
import { TwoColumnLayout } from '../TwoColumnLayout'
import { Add, Close, EditOutlined, MoreVert } from '@mui/icons-material'
import { IArtisanProDetails, IFileData, IMedia, INetwork } from '@/interfaces'
import { useState, useMemo, useCallback } from 'react'
import { ActionInformationDrawer } from '../ActionInformationDrawer'
import { EditBiomassPreProcess } from './EditBiomassPreProcess'
import { EditMethaneCompensate } from './EditMethaneCompensate'
import { capitalizeFirstLetter } from '@/utils/helper'
import { NoData } from '../NoData'
import { TrainingProofRenderer } from '../TrainingProofRenderer'
import { AxiosError } from 'axios'
import { toast } from 'react-toastify'
import { useMutation, useQueryClient } from '@tanstack/react-query'
import { authAxios } from '@/contexts'
import { Confirmation } from '../Confirmation'
import DeleteOutlinedIcon from '@mui/icons-material/DeleteOutlined'
import { theme } from '@/lib/theme/theme'

interface StrategyContentProps {
	type: EntityTabEnum
	csinkNetworkDetails?: INetwork
	artisanProDetails?: IArtisanProDetails
	handleClose: () => void
	disabled?: boolean
}

const TagLine = ({ label, value = '' }: { label: string; value?: string }) => (
	<Stack gap={1} flexDirection={'row'} height={'100%'} alignItems={'center'}>
		<Typography variant='subtitle2'>{label}:</Typography>
		<Typography variant='subtitle1'>{value || '-'}</Typography>
	</Stack>
)

const EditableItem = ({
	label,
	value,
	onEdit,
	isEdit = true,
	disabled = false,
}: {
	label: string
	value: string | undefined
	onEdit: () => void
	isEdit?: boolean
	disabled?: boolean
}) => (
	<TwoColumnLayout
		left={<TagLine label={label} value={value} />}
		right={
			isEdit ? (
				<IconButton onClick={onEdit} size='small' disabled={disabled}>
					<EditOutlined fontSize='small' />
				</IconButton>
			) : (
				<></>
			)
		}
		gridBreakpoints={[10, 2]}
	/>
)

const MediaSection = ({ documents }: { documents?: IFileData[] }) => (
	<Stack flexWrap='wrap' mb={1}>
		<TrainingProofRenderer
			viewMode='table'
			showDocumentName
			componentSize={60}
			ShowDeleteOption={false}
			media={(documents as IMedia[]) || []}
		/>
	</Stack>
)

export const StrategyContent = ({
	type,
	csinkNetworkDetails,
	handleClose,
	artisanProDetails,
}: // disabled = false,
StrategyContentProps) => {
	const data =
		csinkNetworkDetails?.biomassPreprocessingDetails ??
		artisanProDetails?.biomassPreprocessingDetails

	const methaneData =
		csinkNetworkDetails?.methaneCompensateStrategies ??
		artisanProDetails?.methaneCompensateStrategies

	const [editItem, setEditItem] = useState<boolean>(false)
	const [selectedMethaneCompensateId, setSelectedMethaneCompensateId] =
		useState<string>('')

	const [anchorEl, setAnchorEl] = useState<null | HTMLElement>(null)
	const open = Boolean(anchorEl)
	const handleOpenMenu = (event: React.MouseEvent<HTMLButtonElement>) => {
		setAnchorEl(event.currentTarget)
	}
	const handleCloseMenu = () => {
		setAnchorEl(null)
	}

	const [methaneDeleteId, setMethaneDeleteId] = useState<string | null>(null)

	const queryClient = useQueryClient()
	const deleteMethaneCompensationMutation = useMutation({
		mutationKey: ['deleteMethaneCompensationMutation'],
		mutationFn: async (id: string) => {
			const api = csinkNetworkDetails
				? `/cs-network/${csinkNetworkDetails?.id}/methane-compensate-strategy/${id}` //delete for csink
				: `/artisian-pro/${artisanProDetails?.id}/methane-compensate-strategy/${id}` //delete for artisan
			const { data } = await authAxios.delete(api)
			return data
		},
		onError: (error: AxiosError) => {
			toast((error?.response?.data as { messageToUser: string })?.messageToUser)
		},
		onSuccess: (data) => {
			toast(data?.message)
			queryClient.refetchQueries({
				queryKey: [
					csinkNetworkDetails ? 'cSinkNetworkDetails' : 'artisanProDetail',
					csinkNetworkDetails ? csinkNetworkDetails.id : artisanProDetails?.id,
				],
			})
		},
	})

	const handleDelete = useCallback(() => {
		if (!methaneDeleteId) return
		deleteMethaneCompensationMutation.mutate(methaneDeleteId)
	}, [deleteMethaneCompensationMutation, methaneDeleteId])

	const displayContent = useMemo(() => {
		const renderBiomassStrategy = (
			<Stack gap={1.5}>
				<EditableItem
					label='Drying Type'
					value={capitalizeFirstLetter(data?.dryingType).split('_').join(' ')}
					onEdit={() => setEditItem(true)}
				/>
				<Typography variant='subtitle2'>More About it:</Typography>
				<Typography variant='subtitle1'>
					{capitalizeFirstLetter(data?.dryingStrategy).split('_').join(' ') ||
						'-'}
				</Typography>
				<Typography variant='subtitle2'>Attached Media: </Typography>
				<MediaSection documents={data?.dryingDocuments ?? []} />

				<EditableItem
					label='Shredding Type'
					value={capitalizeFirstLetter(data?.shreddingType)
						.split('_')
						.join(' ')}
					onEdit={() => setEditItem(true)}
					isEdit={false}
				/>
				<Typography variant='subtitle2'>More About it:</Typography>
				<Typography variant='subtitle1'>
					{capitalizeFirstLetter(data?.shreddingStrategy)
						.split('_')
						.join(' ') || '-'}
				</Typography>
				<Typography variant='subtitle2'>Attached Media: </Typography>
				<MediaSection documents={data?.shreddingDocuments ?? []} />
			</Stack>
		)

		const renderMethaneStrategy = methaneData?.length ? (
			<Stack gap={4}>
				{methaneData?.map((methaneDetails, idx) => (
					<Stack gap={1.5} key={idx}>
						<Stack direction={'row'} alignItems='center' marginBottom={-1.5}>
							<TagLine
								label={'Methane Type'}
								value={
									capitalizeFirstLetter(methaneDetails?.methaneCompensateType)
										.split('_')
										.join(' ') || '-'
								}
							/>
							<IconButton
								onClick={(e) => {
									handleOpenMenu(e)
									setSelectedMethaneCompensateId(methaneDetails?.id ?? '')
								}}
								sx={{ marginLeft: 'auto' }}>
								<MoreVert />
							</IconButton>
						</Stack>
						<TagLine
							label={'Biomass Type'}
							value={
								capitalizeFirstLetter(methaneDetails?.biomassName)
									.split('_')
									.join(' ') || '-'
							}
						/>
						{methaneDetails?.compensateType ? (
							<TagLine
								label={'Compensate Type'}
								value={
									capitalizeFirstLetter(methaneDetails?.compensateType)
										.split('_')
										.join(' ') || '-'
								}
							/>
						) : null}
						<Typography variant='subtitle2'>More About it:</Typography>
						<Typography variant='subtitle1'>
							{methaneDetails?.description || '-'}
						</Typography>

						<Typography variant='subtitle2'>Attached Media:</Typography>
						<MediaSection documents={methaneDetails?.documents || []} />
					</Stack>
				))}
				{csinkNetworkDetails?.id ? (
					<Button
						variant='text'
						sx={{
							justifyContent: 'flex-start',
						}}
						onClick={() => setEditItem(true)}
						startIcon={<Add color='primary' />}>
						Add Methane Compensate
					</Button>
				) : null}
			</Stack>
		) : (
			<Stack
				flexDirection='column'
				gap={theme.spacing(4)}
				paddingTop={0}
				alignItems='flex-end'>
				{csinkNetworkDetails?.id ? ( // only show this option to csink network because AP is rendered in Edit network entity
					<Button
						variant='outlined'
						className='add-btn-grey'
						onClick={() => setEditItem(true)}
						startIcon={<Add />}>
						Add Methane
					</Button>
				) : null}
				<NoData size='small' />
			</Stack>
		)

		switch (type) {
			case EntityTabEnum.biomassProcessingStrategy:
				return renderBiomassStrategy
			case EntityTabEnum.methaneCompensationStrategy:
				return renderMethaneStrategy
			default:
				return <></>
		}
	}, [
		data?.dryingType,
		data?.dryingStrategy,
		data?.dryingDocuments,
		data?.shreddingType,
		data?.shreddingStrategy,
		data?.shreddingDocuments,
		methaneData,
		csinkNetworkDetails?.id,
		type,
	])

	return (
		<>
			<Menu
				id='basic-menu'
				anchorEl={anchorEl}
				open={open}
				onClose={handleCloseMenu}
				MenuListProps={{
					'aria-labelledby': 'basic-button',
				}}>
				<MenuItem
					onClick={() => {
						handleCloseMenu()
						setEditItem(true)
					}}>
					{' '}
					<Stack flexDirection={'row'} gap={'5px'}>
						<EditOutlined fontSize='small' />
						<Typography>Edit</Typography>
					</Stack>
				</MenuItem>
				<MenuItem
					onClick={() => {
						setMethaneDeleteId(selectedMethaneCompensateId)
						handleCloseMenu()
					}}>
					<Stack flexDirection={'row'} gap={'5px'}>
						<DeleteOutlinedIcon fontSize='small' />
						<Typography>Delete</Typography>
					</Stack>
				</MenuItem>
			</Menu>
			<Confirmation
				confirmationText='Are you sure you want to delete this Methane Compensate Strategy?'
				open={!!methaneDeleteId}
				handleClose={() => setMethaneDeleteId(null)}
				handleNoClick={() => setMethaneDeleteId(null)}
				handleYesClick={() => {
					handleDelete()
					setMethaneDeleteId(null)
				}}
			/>
			{editItem && type === EntityTabEnum.biomassProcessingStrategy && (
				<ActionInformationDrawer
					open={editItem}
					onClose={() => setEditItem(false)}
					anchor='right'
					component={
						<EditBiomassPreProcess
							isCsink={!!csinkNetworkDetails?.id}
							handleCloseDrawer={() => setEditItem(false)}
							csinkNetworkDetails={csinkNetworkDetails}
							artisanProDetails={artisanProDetails}
						/>
					}
				/>
			)}
			{editItem && type === EntityTabEnum.methaneCompensationStrategy && (
				<ActionInformationDrawer
					open={editItem}
					onClose={() => setEditItem(false)}
					anchor='right'
					component={
						<EditMethaneCompensate
							isCsink={!!csinkNetworkDetails?.id}
							selectedMethaneCompensateId={selectedMethaneCompensateId}
							artisanProDetails={artisanProDetails}
							handleCloseDrawer={() => setEditItem(false)}
							csinkNetworkDetails={csinkNetworkDetails}
						/>
					}
				/>
			)}
			<StyleContainer>
				{type !== EntityTabEnum.methaneCompensationStrategy ||
				csinkNetworkDetails?.id ? (
					<Stack className='header'>
						<Stack
							direction='row'
							spacing={1}
							alignItems='center'
							width='100%'
							justifyContent='space-between'>
							<Typography variant='body2'>
								{type === EntityTabEnum.biomassProcessingStrategy
									? 'Biomass Preprocessing Strategy'
									: 'Methane Compensate Strategy'}
							</Typography>
							<IconButton onClick={handleClose}>
								<Close />
							</IconButton>
						</Stack>
					</Stack>
				) : null}

				<Stack className='container' gap={2}>
					{displayContent}
				</Stack>
			</StyleContainer>
		</>
	)
}

const StyleContainer = styled(Stack)(({ theme }) => ({
	gap: theme.spacing(2),
	'.header': {
		flexDirection: 'row',
		justifyContent: 'space-between',
		alignItems: 'center',
		padding: theme.spacing(2),
		borderBottom: `${theme.spacing(0.125)} solid ${
			theme.palette.neutral['100']
		}`,
	},
	'.container': {
		height: '100%',
		padding: theme.spacing(2, 3),
	},
	'.add-btn-grey': {
		justifyContent: 'flex-end',
		width: 'fit-content',
		borderColor: theme.palette.neutral[300],
		color: theme.palette.neutral[300],
	},
}))
