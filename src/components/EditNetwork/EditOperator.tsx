import {
	Box,
	But<PERSON>,
	Divider,
	FormControl,
	FormHelperText,
	IconButton,
	MenuItem,
	Stack,
	styled,
	Typography,
	useTheme,
} from '@mui/material'
import { FieldError, useForm, UseFormRegister, useWatch } from 'react-hook-form'
import { yupResolver } from '@hookform/resolvers/yup'
import { PhoneInputComponent } from '@/components/PhoneInput'
import { useCallback, useState } from 'react'
import { toast } from 'react-toastify'
import { LoadingButton } from '@mui/lab'
import { CustomProfileElement } from '@/components/CustomProfileElement'
import { CustomTextField } from '@/utils/components'
import { handleImageUpload, showAxiosErrorToast } from '@/utils/helper'
import { MultipleFileUploader } from '@/components/MultipleFileUploader'
import { addOperator, TAddOperator } from '@/components'
import { Close, CreateOutlined } from '@mui/icons-material'
import { IEditOperator, IImage, ISite } from '@/interfaces'
import { useMutation, useQueryClient } from '@tanstack/react-query'
import { authAxios } from '@/contexts'
import { useParams } from 'react-router-dom'
import { AxiosError } from 'axios'
import { userRoles } from '@/utils/constant'

type TMutationPayload = {
	name: string
	phoneNo: string | null
	email: string | null
	countryCode: string | null | undefined
	profileImageId: string | null
	aadhaarNumber: string | null | undefined
	aadhaarImageID: string | null | undefined
	trainingImages: string[] | undefined
	id?: string
	kilnId?: string
	siteId?: string
}
type EditOperatorProps = {
	handleCloseDrawer: () => void
	operatorDetails: IEditOperator | undefined
	siteDetails?: ISite
	operatorSiteId?: string
	cSinkManagerId?: string
	type?: string
}
const assignRoleAwaitingFn = async ({
	payload,
	siteId,
	csinkManagerId,
	operatorId,
	operatorSiteId,
}: {
	payload: TMutationPayload
	siteId: string
	csinkManagerId: string
	operatorId: string
	operatorSiteId?: string
}) => {
	const accountType = operatorSiteId
		? userRoles.csinkNetworkOperator
		: userRoles.artisanProOperator
	const { data } = await authAxios.post(
		`csink-manager/${csinkManagerId}/unassigned-user/${operatorId}/assign`,
		{
			...payload,
			accountType,
			entityId: siteId,
		}
	)
	return data
}

export const EditOperator = ({
	handleCloseDrawer,
	operatorDetails,
	siteDetails,
	operatorSiteId,
	cSinkManagerId,
	type,
}: EditOperatorProps) => {
	const theme = useTheme()
	const operatorInitialValues = {
		id: operatorDetails?.id || '',
		name: operatorDetails?.name || '',
		email: operatorDetails?.email || '',
		phoneNumber: operatorDetails?.phoneNo || '',
		countryCode: operatorDetails?.countryCode || '',
		aadhaarNumber: operatorDetails?.aadhaarNumber || '',
		aadhaarImage: operatorDetails?.aadhaarImageUrl?.id || '',
		profileImage: operatorDetails?.profileImageUrl || {},
		trainingImages: operatorDetails?.imageURLs || [],
		trained: true,
	}

	const { artisanProId } = useParams()
	const [url, setUrl] = useState(operatorDetails?.aadhaarImageUrl?.url || '')
	const queryClient = useQueryClient()

	const editArtisanProOperatorMutation = useMutation({
		mutationKey: ['editArtisanProOperator'],
		mutationFn: async ({ id, ...rest }: TMutationPayload) => {
			if (!id) throw new Error('Artisan pro id is missing')
			const { data } = await authAxios.put(
				`/artisian-pro/${id}/artisian-pro-operator`,
				{ operatorId: operatorDetails?.id, ...rest }
			)
			return data
		},
		onSuccess: ({ data }) => {
			queryClient.invalidateQueries({ queryKey: ['siteDetails'] })
			toast(data?.message || 'Operator updated successfully')
			handleCloseDrawer()
		},
		onError: (error: AxiosError) => {
			showAxiosErrorToast(error)
		},
	})

	const assignRoleAwaiting = useMutation({
		mutationKey: ['assignRoleAwaiting'],
		mutationFn: (payload: TMutationPayload) => {
			const siteId = siteDetails?.id || operatorSiteId
			const managerId = siteDetails?.csinkManagerId || cSinkManagerId
			const opId = operatorDetails?.id
		
			if (!siteId || !managerId || !opId) {
				throw new Error('Missing required information to assign operator.')
			}
		
			return assignRoleAwaitingFn({
				payload,
				siteId,
				csinkManagerId: managerId,
				operatorId: opId,
				operatorSiteId: operatorSiteId || '',
			})
		},
		onSuccess: ({ data }) => {
			toast(data?.message || 'Operator updated successfully')
			queryClient.invalidateQueries({ queryKey: ['siteDetails'] })
			queryClient.invalidateQueries({
				queryKey: ['unassignedOperatorListQuery'],
			})
			handleCloseDrawer()
		},
		onError: (error: AxiosError) => {
			showAxiosErrorToast(error)
		},
	})

	const {
		handleSubmit,
		watch,
		setValue,
		register,
		control,
		formState: { errors },
		clearErrors,
	} = useForm<TAddOperator>({
		defaultValues: operatorInitialValues,
		mode: 'all',
		resolver: yupResolver(addOperator, {}),
	})

	const trainingImages = useWatch({ name: 'trainingImages', control })
	const profileImage = useWatch({ name: 'profileImage', control })
	const uploadAadhaarImage = useCallback(
		async (event: React.ChangeEvent<HTMLInputElement>) => {
			const file = event.target.files?.[0]
			if (!file) return

			try {
				const data = await handleImageUpload(file)
				setValue('aadhaarImage', data.id)
				setUrl(data.url)
				clearErrors('aadhaarImage')
			} catch (err) {
				toast('Aadhaar image upload failed')
			}
		},
		[setValue, clearErrors]
	)

	const handleOnChange = (value: string, dialCode: string) => {
		setValue('countryCode', `+${dialCode}`)
		setValue('phoneNumber', value)
	}
	const handleEditOperator = (values: TAddOperator) => {
		const formData = {
			name: values.name,
			phoneNo: values.phoneNumber || null,
			email: values.email || null,
			countryCode: values.phoneNumber ? values.countryCode : null,
			profileImageId: values?.profileImage?.id || null,
			aadhaarNumber: values.aadhaarNumber === '' ? null : values.aadhaarNumber,
			aadhaarImageID: values.aadhaarImage === '' ? null : values.aadhaarImage,
			trainingImages: values.trainingImages?.map(
				(trainingId) => trainingId?.id
			),
		}
		if (type) {
			assignRoleAwaiting.mutate(formData)
		} else {
			editArtisanProOperatorMutation.mutate({
				...formData,
				id: artisanProId ?? '',
			})
		}
	}
	return (
		<Stack padding={theme.spacing(2)}>
			<Stack padding={theme.spacing(2)}>
				<Stack
					direction='row'
					spacing={2}
					alignItems='center'
					width='100%'
					justifyContent='space-between'>
					<Typography variant='h5'>
						{type ? 'Assign' : 'Edit'} Operator
					</Typography>
					<IconButton onClick={handleCloseDrawer}>
						<Close />
					</IconButton>
				</Stack>
			</Stack>
			<Divider />
			<StyledStack>
				<CustomProfileElement
					value={
						profileImage && profileImage.id && profileImage.url
							? (profileImage as IImage)
							: undefined
					}
					errorMessage={errors?.profileImage?.id?.message}
					setValue={(id, url) =>
						setValue('profileImage', {
							id,
							url,
						})
					}
					clearErrors={() => clearErrors('profileImage')}
				/>

				<CustomTextField
					schema={addOperator}
					fullWidth
					id='name'
					type='text'
					label='Enter Name'
					variant='outlined'
					error={!!errors.name?.message}
					helperText={(errors?.name as FieldError)?.message}
					{...register('name')}
				/>

				<CustomTextField
					schema={addOperator}
					id='email'
					label='Enter Email'
					autoComplete='off'
					variant='outlined'
					type='email'
					error={!!errors.email?.message}
					helperText={(errors?.email as FieldError)?.message}
					fullWidth
					inputProps={{
						form: {
							autocomplete: 'off',
						},
					}}
					{...register('email')}
				/>
				<FormControl>
					<PhoneInputComponent
						value={watch('phoneNumber') ?? ''}
						handleOnChange={handleOnChange}
						dialCode={watch('countryCode')}
						getSelectedCountryDialCode={(dialCode) =>
							setValue('countryCode', dialCode)
						}
					/>
					<FormHelperText error={Boolean(errors?.phoneNumber)}>
						{errors?.phoneNumber && (
							<Typography color='error' variant='caption'>
								{(errors?.phoneNumber as FieldError)?.message}
							</Typography>
						)}
					</FormHelperText>
				</FormControl>
				<IdentificationProofSection
					aadhaarNumberError={errors.aadhaarNumber}
					aadhaarImageError={errors.aadhaarImage}
					url={url}
					onUpload={uploadAadhaarImage}
					register={register}
				/>
				<FormControl fullWidth>
					<CustomTextField
						select
						label='Trained'
						id='select-type'
						value='true'
						disabled>
						<MenuItem value='true'>Yes</MenuItem>
					</CustomTextField>
				</FormControl>

				<Stack>
					<Stack rowGap={2} width='100%'>
						<MultipleFileUploader
							heading='Upload or Drag the Training Document'
							sx={{
								height: { xs: 100, md: 166 },
								width: '100%',
							}}
							data={trainingImages}
							imageHeight={100}
							setUploadData={(data) => {
								setValue('trainingImages', data)
								clearErrors('trainingImages')
							}}
						/>
					</Stack>
					<FormHelperText error={Boolean(errors.trainingImages)}>
						{errors?.trainingImages?.message}
					</FormHelperText>
				</Stack>

				<Stack
					direction='row'
					justifyContent='space-between'
					className='buttonContainer'>
					<Button
						onClick={handleCloseDrawer}
						sx={{ border: ` 1px solid ${theme.palette.primary.main}` }}>
						Cancel
					</Button>{' '}
					<LoadingButton
						onClick={handleSubmit(handleEditOperator)}
						variant='contained'>
						{type ? 'Add' : 'Save'}
					</LoadingButton>
				</Stack>
			</StyledStack>
		</Stack>
	)
}

interface IdentificationProofSectionProps {
	aadhaarNumberError?: FieldError
	aadhaarImageError?: FieldError
	url?: string
	onUpload: (e: React.ChangeEvent<HTMLInputElement>) => void
	register: UseFormRegister<TAddOperator>
}

export const IdentificationProofSection: React.FC<
	IdentificationProofSectionProps
> = ({ aadhaarNumberError, aadhaarImageError, url, onUpload, register }) => {
	return (
		<>
			<CustomTextField
				schema={addOperator}
				id='identification'
				label='Identification No.'
				type='number'
				placeholder='Enter Your Identification Number'
				variant='outlined'
				fullWidth
				{...register('aadhaarNumber')}
				error={!!aadhaarNumberError}
				helperText={aadhaarNumberError?.message}
			/>
			<Stack>
				<Typography fontWeight='bold'> Upload ID Proof</Typography>
				<label htmlFor='inputAadhar'>
					<StyledBox>
						<CreateOutlined
							fontSize='medium'
							color='inherit'
							sx={{ position: 'absolute' }}
						/>
						{url && (
							<Box
								component='img'
								src={url}
								height='90%'
								alt='Upload ID Proof'
							/>
						)}
						<input
							id='inputAadhar'
							type='file'
							accept='.jpg, .jpeg, .png, .heic, .webp'
							style={{ display: 'none' }}
							onChange={onUpload}
						/>
					</StyledBox>
				</label>
				<FormHelperText error={!!aadhaarImageError}>
					{aadhaarImageError?.message && (
						<Typography color='error'>{aadhaarImageError.message}</Typography>
					)}
				</FormHelperText>
			</Stack>
		</>
	)
}

const StyledStack = styled(Stack)(({ theme }) => ({
	gap: theme.spacing(4),
}))
const StyledBox = styled(Box)(({ theme }) => ({
	paddingTop: theme.spacing(1),
	position: 'relative',
	display: 'flex',
	justifyContent: 'center',
	alignItems: 'center',
	borderRadius: theme.spacing(1),
	height: theme.spacing(15.5),
	border: '1px dashed grey',
	marginTop: '16px',
}))
