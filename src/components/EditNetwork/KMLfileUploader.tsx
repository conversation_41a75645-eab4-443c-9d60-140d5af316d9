import React, { useRef, useState, useCallback, ChangeEvent } from 'react'
import { ListItemText } from '@mui/material'
import { toast } from 'react-toastify'
import { useNavigate, useParams, useSearchParams } from 'react-router-dom'
import { ILocation } from '@/interfaces'
import { getKmlCoordinates } from '@/utils/helper'
import { GoogleMapsDraw } from '../GoogleMap'
import { useMutation, useQueryClient } from '@tanstack/react-query'
import { authAxios } from '@/contexts'
import { AxiosError } from 'axios'

interface IProps {
	isFpu?: boolean
	fpuId?: string
}
const KMLFileUploader: React.FC<IProps> = ({ isFpu = false, fpuId }) => {
	const fileInputRef = useRef<HTMLInputElement>(null)
	const navigate = useNavigate()
	const [searchParams, setSearchParams] = useSearchParams()
	const [showUploadKmlFileDialog, setShowUploadKmlFileDialog] = useState(false)
	const [coordinates, setCoordinates] = useState<google.maps.LatLng[]>([])
	const { artisanProId } = useParams()
	const siteTab = searchParams.get('siteTab') || ''
	const queryClient = useQueryClient()

	const handleSaveKmlMutation = useMutation({
		mutationKey: ['SaveKml', fpuId, artisanProId, siteTab],
		mutationFn: async (mapData: ILocation[]) => {
			const payload = {
				kmlCoordinates: mapData,
			}
			const fpuPayload = {
				fpuArea: mapData,
			}
			const payloadToUse = isFpu ? fpuPayload : payload

			const api = isFpu
				? `/artisian-pro/${artisanProId}/fpu/${fpuId}`
				: `artisian-pro/${artisanProId}/site/${siteTab}/kml-coordinates`
			await authAxios.put(api, payloadToUse)
		},
		onSuccess: () => {
			toast('KML added')
			queryClient.invalidateQueries({ queryKey: ['getBiomassSourceList'] })
		},
		onError: (error: AxiosError) => {
			toast((error?.response?.data as { userToMessage: string })?.userToMessage)
		},
	})

	const handleSaveKml = useCallback(
		async (mapData: ILocation[]) => {
			await handleSaveKmlMutation.mutateAsync(mapData)
		},
		[handleSaveKmlMutation]
	)

	const handleFileUpload = useCallback(
		async (event: ChangeEvent<HTMLInputElement>) => {
			const file = event?.target?.files?.[0]
			if (!file) return

			if (!file.name.toLowerCase().endsWith('.kml')) {
				toast('Please upload a KML file')
				setCoordinates([])
				return
			}
			console.log(file)
			if (file) {
				const coordinate = await getKmlCoordinates(file)
				const newarr = coordinate?.reduce(
					(arr: any[], curr: any) => [
						...arr,
						{
							lat: curr[1],
							lng: curr[0],
						},
					],
					[]
				)
				const searchParams = new URLSearchParams(window.location.search)
				searchParams.set('lat', newarr?.[0]?.lat || '')
				searchParams.set('long', newarr?.[0]?.lng || '')
				navigate(`?${searchParams.toString()}`, { replace: true })

				setShowUploadKmlFileDialog(true)
				setCoordinates(newarr)
			}
		},
		[navigate]
	)

	return (
		<>
			<ListItemText
				onClick={() => fileInputRef.current?.click()}
				style={{ cursor: 'pointer' }}>
				Upload KML
			</ListItemText>

			<input
				ref={fileInputRef}
				style={{ display: 'none' }}
				type='file'
				accept='.kml'
				onChange={handleFileUpload}
			/>

			{showUploadKmlFileDialog && (
				<GoogleMapsDraw
					open={showUploadKmlFileDialog}
					handleModalClose={() => {
						setSearchParams((params) => {
							params.delete('lat')
							params.delete('long')
							params.delete('networkId')
							return params
						})
						setShowUploadKmlFileDialog(false)
						setCoordinates([])
					}}
					disableDrawingMarker
					initialPolygons={coordinates}
					handleSave={handleSaveKml}
				/>
			)}
		</>
	)
}

export default KMLFileUploader
