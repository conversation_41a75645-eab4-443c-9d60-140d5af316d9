import { theme } from '@/lib/theme/theme'
import { InputLabel, Stack, TextField, Button, IconButton } from '@mui/material'
import AddIcon from '@mui/icons-material/Add'
import { FC, ReactElement, useState } from 'react'
import { ActionInformationDrawer } from '../ActionInformationDrawer'
import { AddPackagingBag } from '../AddPackagingBag'
import {
	IArtisanProDetails,
	IContainerDetails,
	ICsinkApplicationType,
	ICsinkMixingType,
	IIPreferredBiomass,
	INetwork,
	IPackagingBag,
	ISite,
} from '@/interfaces'
import { AddVehicle } from '../AddVehicle'
import { AddPreferredBiomass } from '../AddPreferredBiomass'
import { EntityTabEnum } from '@/utils/constant'
import { AddBiomassSource } from '../AddBiomassSource'
import { AddMixingType } from '../AddMixingType'
import { UseQueryResult } from '@tanstack/react-query'
import { AddApplicationType } from '../AddApplicationType'
import { AssignKilnTemplates } from '../AssignTemplates'
import { EditMethaneCompensate } from './EditMethaneCompensate'
import { AssignOperator } from '../AssignOperator'
import { AddKilnWithDimensionImages } from '../AddKilnWithDimensionImages'
import { AddContainerWithDimensionImages } from '../AddContainerWithDimensionImages'

interface IProps {
	entityType: EntityTabEnum
	label: string
	AddbuttonText?: string
	bagDetails?: IPackagingBag
	iconButton?: ReactElement
	showSearchField?: boolean
	containerDetails?: IContainerDetails
	siteDetails?: ISite
	subheading?: string
	cSinkNetworkDetails?: INetwork
	preferredBiomassList?: IIPreferredBiomass[]
	artisanProDetails?: IArtisanProDetails
	isCsink?: boolean
	disabled?: boolean
	mixingTypeQuery?: UseQueryResult<ICsinkMixingType[] | undefined, Error>
	applicationTypeQuery?: UseQueryResult<
		ICsinkApplicationType[] | undefined,
		Error
	>
}
interface IRenderProps {
	entityType: EntityTabEnum
	siteDetails?: ISite
	handleCloseDrawer: () => void
	subheading?: string
	artisanProDetails?: IArtisanProDetails
	addText?: string
	cSinkNetworkDetails?: INetwork
	isCsink?: boolean
	preferredBiomassList?: IIPreferredBiomass[]
	mixingTypeQuery?: UseQueryResult<ICsinkMixingType[] | undefined, Error>
	applicationTypeQuery?: UseQueryResult<
		ICsinkApplicationType[] | undefined,
		Error
	>
}

export const AddEntityType = ({
	entityType,
	label,
	isCsink = true,
	iconButton,
	showSearchField = true,
	preferredBiomassList,
	siteDetails,
	subheading,
	artisanProDetails,
	cSinkNetworkDetails,
	mixingTypeQuery,
	applicationTypeQuery,
	disabled = false,
	AddbuttonText,
}: IProps) => {
	const [openAddEntityModal, setOpenAddEntityModal] = useState<string | null>(
		null
	)
	const [addText, setAddText] = useState<string>('')
	const addLabel = label === 'Edit' ? label : 'Add ' + label
	const buttonText = AddbuttonText || addLabel
	return (
		<>
			{showSearchField && <InputLabel>Enter {label} name</InputLabel>}

			<Stack
				sx={{
					flexDirection: 'row',
					justifyContent: showSearchField ? '-moz-initial' : 'flex-end',
					gap: theme.spacing(3),
				}}>
				{showSearchField && (
					<TextField
						disabled={disabled}
						id={`add${entityType}`}
						placeholder='Full name'
						value={addText}
						onChange={(e) => setAddText(e.target.value)}
						sx={{
							height: theme.spacing(6),
							width: '100%',
						}}
						InputProps={{
							sx: {
								height: '100%',
							},
						}}
					/>
				)}

				{iconButton ? (
					<IconButton
						onClick={() => setOpenAddEntityModal(label)}
						disabled={disabled}>
						{iconButton}
					</IconButton>
				) : (
					<Button
						disabled={disabled}
						variant='outlined'
						className='add_btn'
						onClick={() => setOpenAddEntityModal(label)}
						startIcon={label === 'Edit' ? null : <AddIcon fontSize='small' />}>
						{buttonText}
					</Button>
				)}
			</Stack>
			{openAddEntityModal ? (
				<ActionInformationDrawer
					open={!!openAddEntityModal}
					onClose={() => {
						setAddText('')
						setOpenAddEntityModal(null)
					}}
					anchor='right'
					component={
						<RenderAddEntityModal
							addText={addText}
							subheading={subheading}
							isCsink={isCsink}
							siteDetails={siteDetails}
							cSinkNetworkDetails={cSinkNetworkDetails}
							artisanProDetails={artisanProDetails}
							preferredBiomassList={preferredBiomassList}
							entityType={entityType}
							handleCloseDrawer={() => {
								setAddText('')
								setOpenAddEntityModal(null)
							}}
							mixingTypeQuery={mixingTypeQuery}
							applicationTypeQuery={applicationTypeQuery}
						/>
					}
				/>
			) : null}
		</>
	)
}

const RenderAddEntityModal: FC<IRenderProps> = ({
	entityType,
	handleCloseDrawer,
	addText,
	preferredBiomassList,
	subheading,
	artisanProDetails,
	cSinkNetworkDetails,
	isCsink = true,
	mixingTypeQuery,
	applicationTypeQuery,
	siteDetails,
}) => {
	switch (entityType) {
		case EntityTabEnum.bags:
			return (
				<AddPackagingBag
					isCsink={isCsink}
					subheading={subheading}
					addText={addText}
					artisanProDetails={artisanProDetails}
					csinkNetworkDetails={cSinkNetworkDetails}
					editMode={false}
					handleCloseDrawer={handleCloseDrawer}
				/>
			)
		case EntityTabEnum.vehicles:
			return (
				<AddVehicle
					addText={addText}
					subheading={subheading}
					cSinkNetworkDetails={cSinkNetworkDetails}
					handleCloseDrawer={handleCloseDrawer}
				/>
			)
		case EntityTabEnum.containers:
			return (
				<AddContainerWithDimensionImages
					isCsink={isCsink}
					addText={addText}
					subheading={subheading}
					cSinkNetworkDetails={cSinkNetworkDetails}
					handleCloseDrawer={handleCloseDrawer}
				/>
			)
		case EntityTabEnum.samplingContainer:
			return (
				<AddContainerWithDimensionImages
					addText={addText}
					subheading={subheading}
					isSamplingContainer={true}
					cSinkNetworkDetails={cSinkNetworkDetails}
					handleCloseDrawer={handleCloseDrawer}
				/>
			)
		case EntityTabEnum.preferredBiomass:
			return (
				<AddPreferredBiomass
					isCsink={isCsink}
					subheading={subheading}
					artisanProDetails={artisanProDetails}
					cSinkNetworkDetails={cSinkNetworkDetails}
					preferredBiomassList={preferredBiomassList}
					handleCloseDrawer={handleCloseDrawer}
				/>
			)
		case EntityTabEnum.kilns:
			return (
				<AddKilnWithDimensionImages
					siteDetailsAddress={siteDetails?.address || ''}
					addText={addText}
					subheading={subheading}
					handleClose={handleCloseDrawer}
				/>
			)
		case EntityTabEnum.biomassSource:
			return (
				<AddBiomassSource
					addText={addText}
					subheading={subheading}
					handleClose={handleCloseDrawer}
				/>
			)
		case EntityTabEnum.mixingTypes:
			return (
				<AddMixingType
					isCsink={isCsink}
					subheading={subheading}
					handleCloseDrawer={handleCloseDrawer}
					mixingTypeQuery={mixingTypeQuery}
					cSinkNetworkDetails={cSinkNetworkDetails}
					artisanProDetails={artisanProDetails}
				/>
			)
		case EntityTabEnum.applicationTypes:
			return (
				<AddApplicationType
					isCsink={isCsink}
					subheading={subheading}
					handleCloseDrawer={handleCloseDrawer}
					applicationTypeQuery={applicationTypeQuery}
					artisanProDetails={artisanProDetails}
					cSinkNetworkDetails={cSinkNetworkDetails}
				/>
			)
		case EntityTabEnum.assignkiln:
			return (
				<AssignKilnTemplates
					isCsink={isCsink}
					subheading={subheading}
					handleCloseDrawer={handleCloseDrawer}
					artisanProDetails={artisanProDetails}
					isKiln
					cSinkNetworkDetails={cSinkNetworkDetails}
				/>
			)
		case EntityTabEnum.assignMeasuringContainer:
			return (
				<AssignKilnTemplates
					isCsink={isCsink}
					subheading={subheading}
					handleCloseDrawer={handleCloseDrawer}
					isKiln={false}
				/>
			)
		case EntityTabEnum.methaneCompensationStrategy:
			return (
				<EditMethaneCompensate
					editMode={false}
					isCsink={isCsink}
					artisanProDetails={artisanProDetails}
					handleCloseDrawer={handleCloseDrawer}
				/>
			)
		case EntityTabEnum.operator:
			return (
				<AssignOperator
					handleCloseDrawer={handleCloseDrawer}
					siteDetails={siteDetails}
					subheading={subheading}
				/>
			)
		default:
			return null
	}
}
