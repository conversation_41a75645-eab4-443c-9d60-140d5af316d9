import { FormatNumber } from '@/utils/helper'
import {
	FormControl,
	MenuItem,
	Select,
	SelectChangeEvent,
	Stack,
	Typography,
	useTheme,
} from '@mui/material'
import { useNavigate, useSearchParams } from 'react-router-dom'
import { defaultLimit, defaultPage, pageLimits } from '@/utils/constant'
import { useCallback } from 'react'

export const CustomPaginationsDetails = ({
	limitName,
	pageName,
	rowCount,
	alignRowOrColumn = 'row',
}: {
	limitName?: string
	pageName?: string
	rowCount: number
	alignRowOrColumn?: 'row' | 'column'
}) => {
	const theme = useTheme()
	const navigate = useNavigate()
	const [searchParams] = useSearchParams()
	const paramsLimit = searchParams.get(limitName || 'limit') ?? defaultLimit
	const paramsPage = searchParams.get(pageName || 'page') ?? defaultPage

	const handleChangeRowsPerPage = useCallback(
		(event: SelectChangeEvent) => {
			const nsp = new URLSearchParams(window.location.search)
			nsp.set(limitName || 'limit', String(event.target.value || defaultLimit))
			nsp.set(pageName || 'page', String(0))

			navigate(`?${nsp.toString()}`, { replace: true })
		},
		[limitName, navigate, pageName]
	)

	return (
		<Stack
			direction={alignRowOrColumn}
			spacing={alignRowOrColumn === 'row' ? 2 : 0}
			alignItems='center'>
			<Typography variant='overline' textTransform='none'>
				Row per page:
			</Typography>
			<FormControl>
				<Select
					value={String(paramsLimit)}
					onChange={handleChangeRowsPerPage}
					sx={{
						width: theme.spacing(12.5),
						height: theme.spacing(3.75)
					}}>
					{pageLimits.map((limit, index) => (
						<MenuItem key={index} value={limit}>
							{limit}
						</MenuItem>
					))}
				</Select>
			</FormControl>
			<Typography variant='overline' textTransform='none'>
				{Number(paramsPage) * Number(paramsLimit) + 1}-
				{Math.min((Number(paramsPage) + 1) * Number(paramsLimit), rowCount)} of{' '}
				{FormatNumber(rowCount ?? 0)}
			</Typography>
		</Stack>
	)
}
