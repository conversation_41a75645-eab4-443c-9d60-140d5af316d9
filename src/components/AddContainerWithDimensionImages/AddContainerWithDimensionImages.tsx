import React from 'react'
import { Close } from '@mui/icons-material'
import { LoadingButton } from '@mui/lab'
import {
	FormControl,
	FormHelperText,
	IconButton,
	InputAdornment,
	MenuItem,
	Stack,
	styled,
	Typography,
} from '@mui/material'
import {
	FieldErrors,
	FormProvider,
	SubmitHandler,
	useForm,
	UseFormReturn,
	UseFormWatch,
	UseFormSetValue,
	UseFormRegister,
} from 'react-hook-form'
import { yupResolver } from '@hookform/resolvers/yup'
import {
	IContainerDetails,
	IContainerWithDimensionImages,
	IMedia,
	INetwork,
} from '@/interfaces'
import {
	convertCentimeterToMillimeter,
	convertMeterToMillimeter,
	convertMillimeterCubetoLitre,
	convertMillimeterToCentimeter,
	showAxiosErrorToast,
} from '@/utils/helper'
import {
	AddContainerWithDimensionImagesSchema,
	TAddContainerWithDimensionImages,
} from './schema'
import { useMutation, useQuery, useQueryClient } from '@tanstack/react-query'
import { AxiosError } from 'axios'
import { toast } from 'react-toastify'
import { useParams, useSearchParams } from 'react-router-dom'
import { useCallback, useMemo, useState } from 'react'
import { authAxios, useAuthContext } from '@/contexts'
import {
	containerShapesEnum,
	measuringUnitEnum,
	userRoles,
} from '@/utils/constant'
import {
	ContainerShapeTypes,
	ICalculateVolume,
} from '../AddContainer/AddContainer'
import { CustomTextField } from '@/utils/components'
import { theme } from '@/lib/theme/theme'
import { MultipleFileUploader } from '../MultipleFileUploader'
import { CustomFileUploader } from '../CustomFileUploader'
import { AnyObjectSchema } from 'yup'

// Types for form field configuration
interface FormFieldOption {
	value: string | number
	label: string
	key?: string
}

interface FormFieldConfig {
	id: string
	name: keyof TAddContainerWithDimensionImages
	label: string
	type: 'text' | 'select'
	fullWidth?: boolean
	disabled?: boolean
	className?: string
	options?: FormFieldOption[]
	onChange?: (value: string | number) => void
	show?: boolean
	variant?: 'outlined' | 'filled' | 'standard'
	wrapInFormControl?: boolean
}

// Reusable form field component
interface FormFieldProps {
	config: FormFieldConfig
	watch: UseFormWatch<TAddContainerWithDimensionImages>
	register: UseFormRegister<TAddContainerWithDimensionImages>
	setValue: UseFormSetValue<TAddContainerWithDimensionImages>
	errors: FieldErrors<TAddContainerWithDimensionImages>
	schema: typeof AddContainerWithDimensionImagesSchema
	customValue?: string | number // For non-form fields like measuringUnit
}

// Helper function to convert image ID to array
const convertImageIdToArray = (imageId?: string): string[] =>
	imageId ? [imageId] : []

const FormField: React.FC<FormFieldProps> = ({
	config,
	watch,
	register,
	setValue,
	errors,
	schema,
	customValue,
}) => {
	const {
		id,
		name,
		label,
		type,
		fullWidth = true,
		disabled = false,
		className,
		options = [],
		onChange,
		variant = 'outlined',
		wrapInFormControl = false,
	} = config

	// Use custom value if provided (for non-form fields), otherwise use form value
	const fieldValue = customValue !== undefined ? customValue : watch(name)
	const fieldError = errors[name]

	const handleChange = (e: React.ChangeEvent<HTMLInputElement>) => {
		const value = e.target.value
		if (customValue === undefined) {
			setValue(name, value as TAddContainerWithDimensionImages[typeof name])
		}
		if (onChange) {
			onChange(value)
		}
	}

	const textField = (
		<CustomTextField
			schema={schema}
			watch={watch}
			id={id}
			label={label}
			type={type}
			select={type === 'select'}
			fullWidth={fullWidth}
			disabled={disabled}
			className={className}
			value={fieldValue || ''}
			variant={variant}
			error={!!fieldError?.message}
			helperText={fieldError?.message}
			{...(customValue === undefined
				? { ...register(name), onChange: handleChange }
				: { onChange: handleChange })}>
			{type === 'select' &&
				options.map((option) => (
					<MenuItem key={option.key || option.value} value={option.value}>
						{option.label}
					</MenuItem>
				))}
		</CustomTextField>
	)

	return wrapInFormControl ? (
		<FormControl fullWidth={fullWidth}>{textField}</FormControl>
	) : (
		textField
	)
}

// Container-specific TextFieldWithImage component
interface ContainerTextFieldWithImageProps {
	label: string
	instructionText: string
	textFieldName: keyof TAddContainerWithDimensionImages
	imageFieldName: keyof TAddContainerWithDimensionImages
	form: UseFormReturn<TAddContainerWithDimensionImages>
	unit: string
	calcCallback?: (value: number) => void
	valueUpToThreeDecimalPlaces: (value: string) => string
	errors: FieldErrors<TAddContainerWithDimensionImages>
	schema: AnyObjectSchema
	getDimensionImageUrl: (
		fieldName: keyof TAddContainerWithDimensionImages
	) => string
	disabled?: boolean
}

const ContainerTextFieldWithImage: React.FC<
	ContainerTextFieldWithImageProps
> = ({
	label,
	instructionText,
	textFieldName,
	imageFieldName,
	form,
	calcCallback,
	valueUpToThreeDecimalPlaces,
	errors,
	schema,
	getDimensionImageUrl,
	disabled = false,
}) => {
	const { watch, register, setValue, clearErrors } = form

	return (
		<Stack gap={theme.spacing(1.5)}>
			<Typography
				variant='body1'
				color='text.secondary'
				fontSize={theme.spacing(1.5)}
				fontWeight={theme.typography.caption.fontWeight}>
				{instructionText}
			</Typography>
			<Stack direction='row' gap={2} alignItems='flex-start'>
				<Stack flex={1}>
					<CustomTextField
						label={label}
						watch={watch}
						schema={schema}
						disabled={disabled}
						fullWidth
						placeholder={`Enter Your ${label}`}
						variant='outlined'
						type='number'
						hideNumberArrows
						{...register(textFieldName)}
						error={!!errors?.[textFieldName]}
						helperText={errors?.[textFieldName]?.message}
						onChange={(e) => {
							const processed =
								parseFloat(valueUpToThreeDecimalPlaces(e.target.value)) || 0
							setValue(textFieldName, processed)
							if (calcCallback) {
								calcCallback(processed)
							}
						}}
					/>
				</Stack>
				<Stack alignItems='center' gap={1}>
					<CustomFileUploader
						heading=''
						sx={{
							height: 56,
							width: 56,
							marginTop: 0,
							'& .MuiIconButton-root': {
								height: 56,
								width: 56,
								borderRadius: 1,
							},
						}}
						imageHeight={56}
						imageUrl={getDimensionImageUrl(imageFieldName) || ''}
						setUploadData={(data) => {
							setValue(imageFieldName, data?.id || '')
							clearErrors(imageFieldName)
						}}
						acceptFileTypes={['png', 'jpg', 'jpeg', 'webp']}
						mediaType='image'
					/>
					{errors?.[imageFieldName]?.message && (
						<Typography
							variant='caption'
							color='error'
							textAlign='center'
							maxWidth={56}>
							{errors?.[imageFieldName]?.message}
						</Typography>
					)}
				</Stack>
			</Stack>
		</Stack>
	)
}

interface IProps {
	handleCloseDrawer: () => void
	editMode?: boolean
	containerDetails?: IContainerWithDimensionImages
	addText?: string
	isCsink?: boolean
	subheading?: string
	cSinkNetworkDetails?: INetwork
	isSamplingContainer?: boolean
}

const acceptedImageTypes = ['png', 'jpg', 'jpeg', 'webp']

// Custom hook for form field configurations
const useFormFieldConfigs = (
	shouldShowSelectContainer: boolean,
	allMeasuringContainer: {
		data?: { containers?: IContainerDetails[] }
		isLoading: boolean
	},
	setValue: UseFormSetValue<TAddContainerWithDimensionImages>,
	handleShapeChange: (value: string) => void,
	watch: UseFormWatch<TAddContainerWithDimensionImages>,
	containerDetails?: IContainerWithDimensionImages,
	isSamplingContainer?: boolean,
	handleUnit?: (value: measuringUnitEnum) => void
) => {
	const formFieldConfigs = useMemo((): FormFieldConfig[] => {
		const configs: FormFieldConfig[] = []

		// Select Measuring Container field
		if (shouldShowSelectContainer) {
			configs.push({
				id: 'container',
				name: 'container',
				label: 'Select Measuring Container',
				type: 'select',
				disabled: !allMeasuringContainer.data?.containers?.length,
				className: 'input-disable-class',
				options:
					allMeasuringContainer.data?.containers?.map(
						(option: IContainerDetails) => ({
							value: option.id,
							label: option.name,
							key: option.name,
						})
					) || [],
				onChange: (value) => {
					setValue('container', String(value))
					handleShapeChange(watch('shape'))
				},
			})
		}

		// Container Name field
		configs.push({
			id: 'name',
			name: 'name',
			label: 'Enter Container Name',
			type: 'text',
		})

		return configs
	}, [
		shouldShowSelectContainer,
		allMeasuringContainer.data?.containers,
		setValue,
		handleShapeChange,
		watch,
	])

	// Shape and Unit field configurations (separate because they're in a row)
	const shapeAndUnitConfigs = useMemo(
		(): FormFieldConfig[] => [
			{
				id: 'container-shape',
				name: 'shape',
				label: 'Select the Container Shape',
				type: 'select',
				disabled: containerDetails?.inUse,
				wrapInFormControl: true,
				options: ContainerShapeTypes(isSamplingContainer || false)
					.filter((container) => !container.hidden)
					.map((container, index) => ({
						value: container.value,
						label: container.label,
						key: container.value + index,
					})),
				onChange: (value) => handleShapeChange(String(value)),
			},
			{
				id: 'measuring-unit',
				name: 'shape', // Using shape as placeholder since measuringUnit is not a form field
				label: 'Measuring Unit',
				type: 'select',
				disabled: containerDetails?.inUse,
				wrapInFormControl: true,
				options: [
					{ value: 'cm', label: 'cm' },
					{ value: 'm', label: 'm' },
				],
				onChange: (value) => handleUnit?.(value as measuringUnitEnum),
			},
		],
		[
			containerDetails?.inUse,
			isSamplingContainer,
			handleShapeChange,
			handleUnit,
		]
	)

	return { formFieldConfigs, shapeAndUnitConfigs }
}

const getApiforGlobalContainers = (
	csinkManagerId?: string,
	accountType?: userRoles,
	artisanProId?: string,
	cSinkNetworkId?: string
) => {
	if (artisanProId)
		return `/artisian-pro/${artisanProId}/measuring-container-template`
	if (cSinkNetworkId)
		return `cs-network/${cSinkNetworkId}/measuring-container-template`
	switch (accountType) {
		case userRoles.BiomassAggregator:
			return `/csink-manager/${csinkManagerId}/settings/measuring-container-template`
		case userRoles.CsinkManager:
		default:
			return `/settings/measuring-container-template`
	}
}
const getAllGlobalMeasuringContainers = (
	paramsLimit: string,
	paramsPage: string,
	accountType: string | undefined,
	csinkManagerId: string | undefined,
	artisanProId: string,
	cSinkNetworkId: string
) => {
	const queryParams = new URLSearchParams({
		limit: paramsLimit,
		page: paramsPage,
		isAssigned: 'true',
	})

	const endpoint = `${getApiforGlobalContainers(
		csinkManagerId,
		accountType as userRoles,
		artisanProId,
		cSinkNetworkId
	)}?${queryParams}`
	const response = authAxios.get(endpoint)

	return response
}

const postApiAddContainer = ({
	isCsink,
	isSamplingContainer,
	artisanProId,
	cSinkNetworkId,
	siteId,
}: {
	isCsink?: boolean
	isSamplingContainer?: boolean
	artisanProId?: string
	cSinkNetworkId?: string
	siteId?: string
}) => {
	if (!isCsink && !isSamplingContainer)
		return `/artisian-pro/${artisanProId}/site/${siteId}/measuring-container`
	if (isSamplingContainer)
		return `/artisian-pro/${artisanProId}/site/${siteId}/container`
	else return `cs-network/${cSinkNetworkId}/container`
}
export const AddContainerWithDimensionImages = ({
	handleCloseDrawer,
	editMode = false,
	addText = '',
	containerDetails,
	subheading,
	isCsink = true,
	isSamplingContainer = false,
}: IProps) => {
	const queryClient = useQueryClient()
	const [searchParams] = useSearchParams()
	const siteId = searchParams.get('siteTab') ?? ''
	const { userDetails } = useAuthContext()
	const { cSinkNetworkId, artisanProId } = useParams()
	const [measuringUnit, setMeasuringUnit] = useState(measuringUnitEnum.cm)

	const initialName = editMode ? containerDetails?.name : addText

	const initialValuesEditMode = useMemo(
		() => ({
			name: initialName ?? '',
			container: '',
			diameter: containerDetails?.diameter
				? convertMillimeterToCentimeter(containerDetails.diameter)
				: null,
			breadth: containerDetails?.breadth
				? convertMillimeterToCentimeter(containerDetails.breadth)
				: null,
			length: containerDetails?.length
				? convertMillimeterToCentimeter(containerDetails.length)
				: null,
			height: containerDetails?.height
				? convertMillimeterToCentimeter(containerDetails.height)
				: null,
			upperSide: containerDetails?.upperSurfaceDiameter
				? convertMillimeterToCentimeter(containerDetails.upperSurfaceDiameter)
				: null,
			lowerSide: containerDetails?.lowerSurfaceDiameter
				? convertMillimeterToCentimeter(containerDetails.lowerSurfaceDiameter)
				: null,
			upperBase: containerDetails?.upperBase
				? convertMillimeterToCentimeter(containerDetails.upperBase)
				: null,
			lowerBase: containerDetails?.lowerBase
				? convertMillimeterToCentimeter(containerDetails.lowerBase)
				: null,
			shape: containerDetails?.shape ?? 'cylinder',
			containerImage: containerDetails?.imageURL ?? [],
			volume: containerDetails?.volume ?? 0,
			breadthImageIds: containerDetails?.breadthImages?.[0]?.id,
			lengthImageIds: containerDetails?.lengthImages?.[0]?.id,
			heightImageIds: containerDetails?.heightImages?.[0]?.id,
			diameterImageIds: containerDetails?.diameterImages?.[0]?.id,
			upperSurfaceDiameterImageIds:
				containerDetails?.upperSurfaceDiameterImages?.[0]?.id,
			lowerSurfaceDiameterImageIds:
				containerDetails?.lowerSurfaceDiameterImages?.[0]?.id,
			lowerBaseImageIds: containerDetails?.lowerBaseImages?.[0]?.id,
			upperBaseImageIds: containerDetails?.upperBaseImages?.[0]?.id,
		}),
		[initialName, containerDetails]
	)
	const form = useForm<TAddContainerWithDimensionImages>({
		defaultValues: initialValuesEditMode,
		mode: 'all',
		resolver: yupResolver<TAddContainerWithDimensionImages>(
			AddContainerWithDimensionImagesSchema
		),
	})
	const {
		register,
		formState: { errors },
		watch,
		setValue,
		clearErrors,
		handleSubmit,
	} = form

	const volume = watch('volume')

	const getApiEditContainer = () => {
		if (!isCsink && !isSamplingContainer)
			return `/artisian-pro/${artisanProId}/site/${siteId}/measuring-container/${containerDetails?.id}`
		if (isSamplingContainer)
			return `/artisian-pro/${artisanProId}/site/${siteId}/container/${containerDetails?.id}`
		else return `cs-network/${cSinkNetworkId}/container/${containerDetails?.id}`
	}
	const allMeasuringContainer = useQuery({
		queryKey: [
			'allMeasuringContainer',
			userDetails?.csinkManagerId,
			userDetails?.accountType,
		],
		queryFn: () => {
			return getAllGlobalMeasuringContainers(
				'1000',
				'0',
				userDetails?.accountType,
				userDetails?.csinkManagerId,
				artisanProId ?? '',
				cSinkNetworkId ?? ''
			)
		},
		select: ({ data }) => data,
	})

	const calculateCylinderVolume = useCallback(
		({
			diameter,
			height,
			unitValue,
		}: {
			diameter?: number | null
			height?: number | null
			unitValue?: string
		}) => {
			const unit = unitValue ?? measuringUnit
			const radius =
				unit !== 'm'
					? convertCentimeterToMillimeter(diameter || 0) / 2
					: convertMeterToMillimeter(diameter || 0) / 2

			const H =
				unit !== 'm'
					? convertCentimeterToMillimeter(height || 0)
					: convertMeterToMillimeter(height || 0)
			// cylindrical shape volume
			const cylinderVolume = Math.PI * radius ** 2 * H
			const volume = Number(convertMillimeterCubetoLitre(cylinderVolume ?? 0))
			return volume
		},
		[measuringUnit]
	)
	const calculateConicalVolume = useCallback(
		({
			upperSide,
			lowerSide,
			height,
			unitValue,
		}: {
			upperSide?: number | null
			lowerSide?: number | null
			height?: number | null
			unitValue?: string
		}) => {
			const unit = unitValue ?? measuringUnit
			const H =
				unit !== 'm'
					? convertCentimeterToMillimeter(height || 0)
					: convertMeterToMillimeter(height || 0)
			const upperSurfaceRadius =
				unit !== 'm'
					? convertCentimeterToMillimeter(upperSide || 0) / 2
					: convertMeterToMillimeter(upperSide || 0) / 2
			const lowerSurfaceRadius =
				unit !== 'm'
					? convertCentimeterToMillimeter(lowerSide || 0) / 2
					: convertMeterToMillimeter(lowerSide || 0) / 2

			const conicalVolume =
				((Math.PI * H) / 3) *
				(lowerSurfaceRadius * lowerSurfaceRadius +
					lowerSurfaceRadius * upperSurfaceRadius +
					upperSurfaceRadius * upperSurfaceRadius)
			const volume = Number(convertMillimeterCubetoLitre(conicalVolume ?? 0))
			return volume
		},
		[measuringUnit]
	)

	const calculateRectangularFrustumVolume = useCallback(
		({
			upperBase,
			lowerBase,
			height,
			length,
			unitValue,
		}: {
			upperBase?: number | null
			lowerBase?: number | null
			height?: number | null
			length?: number | null
			unitValue?: string
		}) => {
			const unit = unitValue ?? measuringUnit
			const H =
				unit !== 'm'
					? convertCentimeterToMillimeter(height || 0)
					: convertMeterToMillimeter(height || 0)
			const L =
				unit !== 'm'
					? convertCentimeterToMillimeter(length || 0)
					: convertMeterToMillimeter(length || 0)
			const upperBaseLength =
				unit !== 'm'
					? convertCentimeterToMillimeter(upperBase || 0)
					: convertMeterToMillimeter(upperBase || 0)
			const lowerBaseLength =
				unit !== 'm'
					? convertCentimeterToMillimeter(lowerBase || 0)
					: convertMeterToMillimeter(lowerBase || 0)

			// Volume of a trapezoidal prism: Area of trapezoid * Length
			// Area of trapezoid: ((base1 + base2) / 2) * height
			const rectangularFrustumVolume =
				((upperBaseLength + lowerBaseLength) / 2) * H * L
			const volume = Number(
				convertMillimeterCubetoLitre(rectangularFrustumVolume ?? 0)
			)
			return volume
		},
		[measuringUnit]
	)
	const calculateCuboidalVolume = useCallback(
		({
			length,
			breadth,
			height,
			unitValue,
		}: {
			length?: number | null
			breadth?: number | null
			height?: number | null
			unitValue?: string
		}) => {
			const unit = unitValue ?? measuringUnit
			const H =
				unit !== 'm'
					? convertCentimeterToMillimeter(height || 0)
					: convertMeterToMillimeter(height || 0)
			const upperSide =
				unit !== 'm'
					? convertCentimeterToMillimeter(length || 0)
					: convertMeterToMillimeter(length || 0)
			const lowerSide =
				unit !== 'm'
					? convertCentimeterToMillimeter(breadth || 0)
					: convertMeterToMillimeter(breadth || 0)

			const cuboidVolume =
				(H / 3) *
				(upperSide * upperSide +
					lowerSide * lowerSide +
					Math.sqrt(upperSide * upperSide * lowerSide * lowerSide))
			const volume = Number(convertMillimeterCubetoLitre(cuboidVolume ?? 0))
			return volume
		},
		[measuringUnit]
	)

	const handleShapeChange = useCallback(
		(shape: string) => {
			setValue('shape', shape)
			// Reset all dimensions when shape is changed
			setValue('diameter', null)
			setValue('height', null)
			setValue('breadth', null)
			setValue('length', null)
			setValue('upperSide', null)
			setValue('lowerSide', null)
			setValue('upperBase', null)
			setValue('lowerBase', null)
			setValue('volume', 0)
		},
		[setValue]
	)

	const calculateVolume = useCallback(
		({
			diameter,
			height,
			breadth,
			length,
			unitValue,
			upperSide,
			lowerSide,
			upperBase,
			lowerBase,
		}: ICalculateVolume) => {
			switch (watch('shape')) {
				case containerShapesEnum.cylinder:
					setValue(
						'volume',
						calculateCylinderVolume({ diameter, height, unitValue })
					)
					break
				case containerShapesEnum.conical:
					setValue(
						'volume',
						calculateConicalVolume({
							upperSide,
							lowerSide,
							height,
							unitValue,
						})
					)
					break
				case containerShapesEnum.rectangular:
					setValue(
						'volume',
						calculateRectangularFrustumVolume({
							upperBase,
							lowerBase,
							length,
							height,
							unitValue,
						})
					)
					break
				case containerShapesEnum.cuboid:
					setValue(
						'volume',
						calculateCuboidalVolume({
							length,
							breadth,
							height,
							unitValue,
						})
					)
					break
				default:
			}
		},
		[
			calculateConicalVolume,
			calculateCuboidalVolume,
			calculateRectangularFrustumVolume,
			calculateCylinderVolume,
			setValue,
			watch,
		]
	)
	const handleUnit = useCallback(
		(value: measuringUnitEnum) => {
			setMeasuringUnit(value)
			// Reset all dimensions and volume to 0 when unit is changed
			setValue('diameter', 0)
			setValue('height', 0)
			setValue('breadth', 0)
			setValue('length', 0)
			setValue('upperSide', 0)
			setValue('lowerSide', 0)
			setValue('upperBase', 0)
			setValue('lowerBase', 0)
			setValue('volume', 0)
		},
		[setValue]
	)

	const payloadForShape = useCallback(
		(shape: string, formValues: TAddContainerWithDimensionImages) => {
			if (shape === containerShapesEnum.cuboid) {
				return {
					height:
						measuringUnit !== 'm'
							? Number(formValues.height) * 10
							: Number(formValues.height) * 1000,
					length:
						measuringUnit !== 'm'
							? Number(formValues?.length) * 10
							: Number(formValues?.length) * 1000,
					breadth:
						measuringUnit !== 'm'
							? Number(formValues?.breadth) * 10
							: Number(formValues?.breadth) * 1000,
					breadthImageIds: convertImageIdToArray(formValues.breadthImageIds),
					lengthImageIds: convertImageIdToArray(formValues.lengthImageIds),
					heightImageIds: convertImageIdToArray(formValues.heightImageIds),
				}
			}
			if (shape === containerShapesEnum.cylinder) {
				return {
					height:
						measuringUnit !== 'm'
							? Number(formValues.height) * 10
							: Number(formValues.height) * 1000,
					diameter:
						measuringUnit !== 'm'
							? Number(formValues.diameter) * 10
							: Number(formValues.diameter) * 1000,
					diameterImageIds: convertImageIdToArray(formValues.diameterImageIds),
					heightImageIds: convertImageIdToArray(formValues.heightImageIds),
				}
			}

			if (shape === containerShapesEnum.conical) {
				return {
					height:
						measuringUnit !== 'm'
							? Number(formValues.height) * 10
							: Number(formValues.height) * 1000,
					upperSurfaceDiameter:
						measuringUnit !== 'm'
							? Number(formValues?.upperSide) * 10
							: Number(formValues?.upperSide) * 1000,
					lowerSurfaceDiameter:
						measuringUnit !== 'm'
							? Number(formValues?.lowerSide) * 10
							: Number(formValues?.lowerSide) * 1000,
					heightImageIds: convertImageIdToArray(formValues.heightImageIds),

					...(isSamplingContainer //  key changes for image ids
						? {
								upperSurfaceDiameterImageIds: convertImageIdToArray(
									formValues.upperSurfaceDiameterImageIds
								),
								lowerSurfaceDiameterImageIds: convertImageIdToArray(
									formValues.lowerSurfaceDiameterImageIds
								),
						  }
						: {
								upperSurfaceImageDiameterIds: convertImageIdToArray(
									formValues.upperSurfaceDiameterImageIds
								),
								lowerSurfaceImageDiameterIds: convertImageIdToArray(
									formValues.lowerSurfaceDiameterImageIds
								),
						  }),
				}
			}
			if (shape === containerShapesEnum.rectangular) {
				return {
					height:
						measuringUnit !== 'm'
							? Number(formValues.height) * 10
							: Number(formValues.height) * 1000,
					upperBase:
						measuringUnit !== 'm'
							? Number(formValues?.upperBase) * 10
							: Number(formValues?.upperBase) * 1000,
					lowerBase:
						measuringUnit !== 'm'
							? Number(formValues?.lowerBase) * 10
							: Number(formValues?.lowerBase) * 1000,
					length:
						measuringUnit !== 'm'
							? Number(formValues?.length) * 10
							: Number(formValues?.length) * 1000,
					lengthImageIds: convertImageIdToArray(formValues.lengthImageIds),
					heightImageIds: convertImageIdToArray(formValues.heightImageIds),
					lowerBaseImageIds: convertImageIdToArray(
						formValues.lowerBaseImageIds
					),
					upperBaseImageIds: convertImageIdToArray(
						formValues.upperBaseImageIds
					),
				}
			}
			if (shape === containerShapesEnum.other) {
				return {
					volume: Number(volume),
				}
			}
			return null
		},
		[isSamplingContainer, measuringUnit, volume]
	)
	const addContainerMutation = useMutation({
		mutationKey: [
			'addContainer',
			cSinkNetworkId,
			isCsink,
			isSamplingContainer,
			artisanProId,
			siteId,
		],
		mutationFn: async (formValues: TAddContainerWithDimensionImages) => {
			// Create payload based on form values
			const payload = {
				...payloadForShape(formValues?.shape, formValues),
				imageIds: formValues.containerImage
					? formValues?.containerImage?.map((image) => image?.id)
					: null,
				shape: formValues?.shape,
				name: formValues?.name,
			}
			const api = postApiAddContainer({
				isCsink,
				isSamplingContainer,
				artisanProId,
				cSinkNetworkId,
				siteId,
			})
			return await authAxios.post(api, payload)
		},
		onSuccess: ({ data }) => {
			toast(data?.message)
			queryClient.refetchQueries({
				queryKey: [
					isSamplingContainer ? 'getSamplingContainerList' : 'getContainerList',
				],
			})
			handleCloseDrawer()
		},
		onError: (error: AxiosError) => {
			showAxiosErrorToast(error)
		},
	})
	const editContainerMutation = useMutation({
		mutationKey: ['editContainer', cSinkNetworkId, containerDetails?.id],
		mutationFn: async (formValues: TAddContainerWithDimensionImages) => {
			const api = getApiEditContainer()
			// Create payload based on form values
			const payload = {
				...payloadForShape(formValues?.shape, formValues),
				imageIds: formValues.containerImage
					? formValues?.containerImage?.map((image) => image?.id)
					: null,
				shape: formValues?.shape,
				name: formValues?.name,
			}
			return await authAxios.put(api, payload)
		},
		onSuccess: ({ data }) => {
			toast(data?.message)
			queryClient.invalidateQueries({
				queryKey: [
					isSamplingContainer ? 'getSamplingContainerList' : 'getContainerList',
				],
			})
			handleCloseDrawer()
		},
		onError: (error: AxiosError) => {
			toast((error?.response?.data as { messageToUser: string }).messageToUser)
		},
	})

	// Image upload handlers
	const handleMainImageUpload = useCallback(
		(uploadedImages: IMedia[]) => {
			const formattedImages = uploadedImages.map((img) => ({
				id: img.id,
				url: img.url,
				fileName: img.fileName || '',
			}))
			setValue('containerImage', formattedImages)
			clearErrors('containerImage')
		},
		[clearErrors, setValue]
	)

	const getDimensionImageUrl = useCallback(
		(fieldName: keyof TAddContainerWithDimensionImages): string => {
			const imageId = form.watch(fieldName) as string
			if (!imageId) return ''

			if (editMode && containerDetails) {
				const fieldMappings: Record<
					string,
					keyof IContainerWithDimensionImages
				> = {
					heightImageIds: 'heightImages',
					breadthImageIds: 'breadthImages',
					lengthImageIds: 'lengthImages',
					diameterImageIds: 'diameterImages',
					upperSurfaceDiameterImageIds: 'upperSurfaceDiameterImages',
					lowerSurfaceDiameterImageIds: 'lowerSurfaceDiameterImages',
					lowerBaseImageIds: 'lowerBaseImages',
					upperBaseImageIds: 'upperBaseImages',
				}

				const imageField = fieldMappings[fieldName as string]
				if (imageField) {
					const images = containerDetails[imageField] as IMedia[]
					if (images && images.length > 0) {
						const matchingImage = images.find((img) => img.id === imageId)
						return matchingImage?.url || ''
					}
				}
			}

			return ''
		},
		[form, editMode, containerDetails]
	)

	const handleSaveContainer: SubmitHandler<TAddContainerWithDimensionImages> = (
		formValues
	) => {
		editMode
			? editContainerMutation.mutate(formValues)
			: addContainerMutation.mutate(formValues)
	}
	const shouldShowSelectContainer = useMemo(() => {
		return (
			!isSamplingContainer &&
			!editMode &&
			allMeasuringContainer.isLoading === false
		)
	}, [isSamplingContainer, editMode, allMeasuringContainer.isLoading])

	// Use custom hook for form field configurations
	const { formFieldConfigs, shapeAndUnitConfigs } = useFormFieldConfigs(
		shouldShowSelectContainer,
		allMeasuringContainer,
		setValue,
		handleShapeChange,
		watch,
		containerDetails,
		isSamplingContainer,
		handleUnit
	)

	// Get dimension fields configuration based on container shape
	const getDimensionFields = useCallback(() => {
		const containerShape = watch('shape')

		switch (containerShape) {
			case containerShapesEnum.cylinder:
				return [
					{
						label: 'Diameter',
						instructionText: 'Measure diameter and upload the image',
						textField: 'diameter' as keyof TAddContainerWithDimensionImages,
						imageField:
							'diameterImageIds' as keyof TAddContainerWithDimensionImages,
						calcCallback: (diameter: number) =>
							calculateVolume?.({ diameter, height: watch('height') }),
					},
					{
						label: 'Height',
						instructionText: 'Measure height and upload the image',
						textField: 'height' as keyof TAddContainerWithDimensionImages,
						imageField:
							'heightImageIds' as keyof TAddContainerWithDimensionImages,
						calcCallback: (height: number) =>
							calculateVolume?.({ height, diameter: watch('diameter') }),
					},
				]

			case containerShapesEnum.cuboid:
				return [
					{
						label: 'Upper Side',
						instructionText: 'Measure Upper Side and upload the image',
						textField: 'length' as keyof TAddContainerWithDimensionImages,
						imageField:
							'lengthImageIds' as keyof TAddContainerWithDimensionImages,
						calcCallback: (length: number) =>
							calculateVolume?.({
								length,
								breadth: watch('breadth'),
								height: watch('height'),
							}),
					},
					{
						label: 'Lower Side',
						instructionText: 'Measure Lower Side and upload the image',
						textField: 'breadth' as keyof TAddContainerWithDimensionImages,
						imageField:
							'breadthImageIds' as keyof TAddContainerWithDimensionImages,
						calcCallback: (breadth: number) =>
							calculateVolume?.({
								length: watch('length'),
								breadth,
								height: watch('height'),
							}),
					},
					{
						label: 'Height',
						instructionText: 'Measure height and upload the image',
						textField: 'height' as keyof TAddContainerWithDimensionImages,
						imageField:
							'heightImageIds' as keyof TAddContainerWithDimensionImages,
						calcCallback: (height: number) =>
							calculateVolume?.({
								length: watch('length'),
								breadth: watch('breadth'),
								height,
							}),
					},
				]

			case containerShapesEnum.rectangular:
				return [
					{
						label: 'Length',
						instructionText: 'Measure length and upload the image',
						textField: 'length' as keyof TAddContainerWithDimensionImages,
						imageField:
							'lengthImageIds' as keyof TAddContainerWithDimensionImages,
						calcCallback: (length: number) =>
							calculateVolume?.({
								length,
								upperBase: watch('upperBase'),
								lowerBase: watch('lowerBase'),
								height: watch('height'),
							}),
					},
					{
						label: 'Long Base',
						instructionText: 'Measure Long Base and upload the image',
						textField: 'lowerBase' as keyof TAddContainerWithDimensionImages,
						imageField:
							'lowerBaseImageIds' as keyof TAddContainerWithDimensionImages,
						calcCallback: (lowerBase: number) =>
							calculateVolume?.({
								upperBase: watch('upperBase'),
								lowerBase,
								height: watch('height'),
								length: watch('length'),
							}),
					},
					{
						label: 'Short Base',
						instructionText: 'Measure Short Base and upload the image',
						textField: 'upperBase' as keyof TAddContainerWithDimensionImages,
						imageField:
							'upperBaseImageIds' as keyof TAddContainerWithDimensionImages,
						calcCallback: (upperBase: number) =>
							calculateVolume?.({
								upperBase,
								lowerBase: watch('lowerBase'),
								height: watch('height'),
								length: watch('length'),
							}),
					},
					{
						label: 'Height',
						instructionText: 'Measure height and upload the image',
						textField: 'height' as keyof TAddContainerWithDimensionImages,
						imageField:
							'heightImageIds' as keyof TAddContainerWithDimensionImages,
						calcCallback: (height: number) =>
							calculateVolume?.({
								height,
								upperBase: watch('upperBase'),
								lowerBase: watch('lowerBase'),
								length: watch('length'),
							}),
					},
				]

			case containerShapesEnum.conical:
				return [
					{
						label: 'Upper Surface Diameter',
						instructionText:
							'Measure Upper Surface Diameter and upload the image',
						textField: 'upperSide' as keyof TAddContainerWithDimensionImages,
						imageField:
							'upperSurfaceDiameterImageIds' as keyof TAddContainerWithDimensionImages,
						calcCallback: (upperSide: number) =>
							calculateVolume?.({
								upperSide,
								lowerSide: watch('lowerSide'),
								height: watch('height'),
							}),
					},
					{
						label: 'Lower Surface Diameter',
						instructionText:
							'Measure Lower Surface Diameter and upload the image',
						textField: 'lowerSide' as keyof TAddContainerWithDimensionImages,
						imageField:
							'lowerSurfaceDiameterImageIds' as keyof TAddContainerWithDimensionImages,
						calcCallback: (lowerSide: number) =>
							calculateVolume?.({
								upperSide: watch('upperSide'),
								lowerSide,
								height: watch('height'),
							}),
					},
					{
						label: 'Height',
						instructionText: 'Measure height and upload the image',
						textField: 'height' as keyof TAddContainerWithDimensionImages,
						imageField:
							'heightImageIds' as keyof TAddContainerWithDimensionImages,
						calcCallback: (height: number) =>
							calculateVolume?.({
								upperSide: watch('upperSide'),
								lowerSide: watch('lowerSide'),
								height,
							}),
					},
				]

			default:
				return []
		}
	}, [watch, calculateVolume])

	// Value processing function for dimension fields
	const valueUpToThreeDecimalPlaces = useCallback(
		(value: string) => {
			const regexForSingleDecPlace = /([0-9]*[.|,]{0,1}[0-9]{0,1})/s
			const regexForThreeDecPlace = /([0-9]*[.|,]{0,1}[0-9]{0,3})/s

			return (
				value.match(
					measuringUnit === 'm' ? regexForThreeDecPlace : regexForSingleDecPlace
				)?.[0] || ''
			)
		},
		[measuringUnit]
	)

	const isLoading = useMemo(
		() => addContainerMutation.isPending || editContainerMutation.isPending,
		[addContainerMutation.isPending, editContainerMutation.isPending]
	)

	return (
		<StyleContainer>
			<Stack className='header'>
				<Stack
					direction='row'
					spacing={1}
					alignItems='center'
					width='100%'
					justifyContent='space-between'>
					<Stack>
						<Typography variant='h5'>
							{editMode ? 'Edit' : 'Add'} Container
						</Typography>
						<Typography variant='subtitle1'>{subheading}</Typography>
					</Stack>
					<IconButton onClick={handleCloseDrawer}>
						<Close />
					</IconButton>
				</Stack>
			</Stack>
			<Stack className='container'>
				<FormProvider {...form}>
					<Stack gap={5}>
						{/* Render main form fields */}
						{formFieldConfigs.map((config) => (
							<FormField
								key={config.id}
								config={config}
								watch={watch}
								register={register}
								setValue={setValue}
								errors={errors}
								schema={AddContainerWithDimensionImagesSchema}
							/>
						))}

						{/* Shape and Unit fields in a row */}
						<Stack flexDirection='row' gap={theme.spacing(1)}>
							<FormField
								config={shapeAndUnitConfigs[0]}
								watch={watch}
								register={register}
								setValue={setValue}
								errors={errors}
								schema={AddContainerWithDimensionImagesSchema}
							/>
							<FormField
								config={shapeAndUnitConfigs[1]}
								watch={watch}
								register={register}
								setValue={setValue}
								errors={errors}
								schema={AddContainerWithDimensionImagesSchema}
								customValue={measuringUnit}
							/>
						</Stack>
						{/* Dimension Fields with Images */}
						{watch('shape') !== containerShapesEnum.other ? (
							<Stack gap={theme.spacing(2)}>
								{getDimensionFields().map(
									({
										label,
										instructionText,
										textField,
										imageField,
										calcCallback,
									}) => (
										<ContainerTextFieldWithImage
											key={`${textField}-${imageField}-textfield`}
											label={label}
											instructionText={instructionText}
											textFieldName={textField}
											imageFieldName={imageField}
											form={form}
											unit={measuringUnit}
											calcCallback={calcCallback}
											valueUpToThreeDecimalPlaces={valueUpToThreeDecimalPlaces}
											errors={errors}
											schema={AddContainerWithDimensionImagesSchema}
											getDimensionImageUrl={getDimensionImageUrl}
											disabled={containerDetails?.inUse}
										/>
									)
								)}
							</Stack>
						) : (
							// Volume input for "other" shape
							<CustomTextField
								watch={watch}
								schema={AddContainerWithDimensionImagesSchema}
								id='Volume'
								type='number'
								hideNumberArrows
								label='Volume'
								placeholder='Enter Your Volume in litre'
								variant='outlined'
								fullWidth
								disabled={containerDetails?.inUse}
								{...register('volume')}
								InputProps={{
									endAdornment: (
										<InputAdornment position='end'>
											<Typography>litre</Typography>
										</InputAdornment>
									),
								}}
								InputLabelProps={{
									shrink: true,
								}}
								error={!!errors.volume?.message}
								helperText={errors?.volume?.message}
							/>
						)}

						{watch('shape') !== containerShapesEnum.other ? (
							<Stack>
								<Typography variant='subtitle1'>
									Total Volume: {volume}
									Ltrs.
								</Typography>
								{form.watch('volume') < 10 && (
									<Typography variant='subtitle1' color='error'>
										Warning: Volume of the Container is too low
									</Typography>
								)}
							</Stack>
						) : null}
						<Stack rowGap={2}>
							<Typography variant='subtitle1'>Container Image</Typography>
							<Typography
								variant='body1'
								color='text.secondary'
								fontSize={theme.spacing(1.5)}
								fontWeight={theme.typography.caption.fontWeight}>
								Please enter the Images after measuring all the Dimensions
							</Typography>
							<Stack rowGap={2} width='100%'>
								<MultipleFileUploader
									data={containerDetails?.imageURLs?.map((data) => ({
										...data,
										fileName: data.path,
									}))}
									fileType={acceptedImageTypes}
									training={false}
									heading='Add Container Image'
									sx={{
										height: { xs: 100, md: 150 },
										width: '100%',
									}}
									imageHeight={100}
									setUploadData={handleMainImageUpload}
								/>
							</Stack>
							<FormHelperText error={Boolean(errors.containerImage)}>
								{errors?.containerImage?.message}
							</FormHelperText>
						</Stack>

						<LoadingButton
							loading={isLoading}
							disabled={isLoading}
							onClick={handleSubmit(handleSaveContainer)}
							variant='contained'>
							{editMode ? 'Save' : 'Add'}
						</LoadingButton>
					</Stack>
				</FormProvider>
			</Stack>
		</StyleContainer>
	)
}

const StyleContainer = styled(Stack)(({ theme }) => ({
	gap: theme.spacing(2),
	'.header': {
		flexDirection: 'row',
		justifyContent: 'space-between',
		alignItems: 'center',
		padding: theme.spacing(4.5, 2),
		borderBottom: `${theme.spacing(0.125)} solid ${
			theme.palette.neutral['100']
		}`,
	},

	'.container': {
		padding: theme.spacing(2, 3),
		gap: theme.spacing(2.5),
	},
}))
