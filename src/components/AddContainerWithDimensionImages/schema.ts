import { containerShapesEnum } from '@/utils/constant'
import * as Yup from 'yup'

const imageSchema = Yup.object({
	id: Yup.string(),
	url: Yup.string(),
	fileName: Yup.string().notRequired(),
})

const isShapeValidation =
	(shapes: containerShapesEnum[]) =>
	(shape: containerShapesEnum) => {
		return shapes.includes(shape)
	}

export const AddContainerWithDimensionImagesSchema = Yup.object({
		name: Yup.string().required('Name is required'),
		shape: Yup.string().required('Shape is required'),
		container: Yup.string().notRequired(),
		diameter: Yup.number()
			.nullable()
			.when(['shape'], {
				is: (shape: string) => shape === containerShapesEnum.cylinder,
				then: (schema) =>
					schema
						.required('Please enter Dimensions')
						.typeError('Please enter Dimensions'),
				otherwise: (schema) => schema,
			}),
		height: Yup.number()
			.nullable()
			.when(['shape'], {
				is: (shape: string) => shape === containerShapesEnum.other,
				then: (schema) => schema,
				otherwise: (schema) =>
					schema
						.required('Please enter Dimensions')
						.typeError('Please enter Dimensions'),
			}),
		breadth: Yup.number()
			.nullable()
			.when(['shape'], {
				is: (shape: string) => shape === containerShapesEnum.cuboid,
				then: (schema) =>
					schema
						.required('Please enter Dimensions')
						.typeError('Please enter Dimensions'),
				otherwise: (schema) => schema,
			}),
		upperSide: Yup.number()
			.nullable()
			.when(['shape'], {
				is: (shape: string) => shape === containerShapesEnum.conical,
				then: (schema) =>
					schema
						.required('Please enter Dimensions')
						.typeError('Please enter Dimensions'),

				otherwise: (schema) => schema,
			}),
		lowerSide: Yup.number()
			.nullable()
			.when(['shape'], {
				is: (shape: string) => shape === containerShapesEnum.conical,
				then: (schema) =>
					schema
						.required('Please enter Dimensions')
						.typeError('Please enter Dimensions'),

				otherwise: (schema) => schema,
			}),
		upperBase: Yup.number()
			.nullable()
			.when(['shape'], {
				is: (shape: string) => shape === containerShapesEnum.rectangular,
				then: (schema) =>
					schema
						.required('Please enter Dimensions')
						.typeError('Please enter Dimensions'),

				otherwise: (schema) => schema,
			}),
		lowerBase: Yup.number()
			.nullable()
			.when(['shape'], {
				is: (shape: string) => shape === containerShapesEnum.rectangular,
				then: (schema) =>
					schema
						.required('Please enter Dimensions')
						.typeError('Please enter Dimensions'),

				otherwise: (schema) => schema,
			}),
		length: Yup.number()
			.nullable()
			.when(['shape'], {
				is: (shape: string) =>
					shape === containerShapesEnum.cuboid ||
					shape === containerShapesEnum.rectangular,
				then: (schema) =>
					schema
						.required('Please enter Dimensions')
						.typeError('Please enter Dimensions'),

				otherwise: (schema) => schema,
			}),

		containerImage: Yup.array().of(imageSchema)
					.nullable()
					.required('Container image is Required')
					.min(1, 'Add more Container images'),
		volume: Yup.number()
			.nullable()
			.min(1, 'Volume must be greater than 1')
			.required('volume is Required')
			.typeError('Volume is Required'),

		heightImageIds: Yup.string().when(['shape'], {
			is: isShapeValidation(
				[
					containerShapesEnum.cuboid,
					containerShapesEnum.cylinder,
					containerShapesEnum.conical,
					containerShapesEnum.rectangular,
				],
		
			),
			then: (schema) => schema.required('Please upload depth images'),
			otherwise: (schema) => schema,
		}),
		lowerBaseImageIds: Yup.string().when(['shape'], {
			is: isShapeValidation([containerShapesEnum.rectangular]),
			then: (schema) => schema.required('Please upload lower base images'),
			otherwise: (schema) => schema,
		}),
		upperBaseImageIds: Yup.string().when(['shape'], {
			is: isShapeValidation([containerShapesEnum.rectangular]),
			then: (schema) => schema.required('Please upload Upper base images'),
			otherwise: (schema) => schema,
		}),
		breadthImageIds: Yup.string().when(['shape'], {
			is: isShapeValidation([containerShapesEnum.cuboid]),
			then: (schema) => schema.required('Please upload lower side images'),
			otherwise: (schema) => schema,
		}),
		lengthImageIds: Yup.string().when(['shape'], {
			is: isShapeValidation(
				[containerShapesEnum.cuboid, containerShapesEnum.rectangular],
		
			),
			then: (schema) => schema.required('Please upload upper side images'),
			otherwise: (schema) => schema,
		}),
		diameterImageIds: Yup.string().when(['shape'], {
			is: isShapeValidation([containerShapesEnum.cylinder]),
			then: (schema) => schema.required('Please upload diameter images'),
			otherwise: (schema) => schema,
		}),
		upperSurfaceDiameterImageIds: Yup.string().when(['shape'], {
			is: isShapeValidation([containerShapesEnum.conical]),
			then: (schema) => schema.required('Please upload upper surface images'),
			otherwise: (schema) => schema,
		}),
		lowerSurfaceDiameterImageIds: Yup.string().when(['shape'], {
			is: isShapeValidation([containerShapesEnum.conical]),
			then: (schema) => schema.required('Please upload lower surface images'),
			otherwise: (schema) => schema,
		}),
	})

export type TAddContainerWithDimensionImages = Yup.InferType<typeof AddContainerWithDimensionImagesSchema>
