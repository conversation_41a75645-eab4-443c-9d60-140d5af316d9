import { kilnShapeEnum, kilnTypeEnum } from '@/utils/constant'
import * as Yup from 'yup'

const imageSchema = Yup.object({
	id: Yup.string(),
	url: Yup.string(),
	fileName: Yup.string().notRequired(),
})
export const addKilnSchema = (
	isGlobalAddKiln: boolean = false,
	isCsink: boolean = false
) =>
	Yup.object({
		name: Yup.string().required('Please enter kiln name'),
		kilnType: Yup.string().required('Please enter kiln type'),
		kilnShape: Yup.string().required('Please enter Kiln shape'),
		unit: Yup.string(),
		length: Yup.string().when(['kilnShape'], {
			is: (value: string) => value === kilnShapeEnum.Rectangular,
			then: (schema) =>
				schema.typeError('Please enter length').required('Please enter length'),
			otherwise: (schema) => schema,
		}),
		FarmerId: Yup.string().when([], {
			is: () => isCsink,
			then: (schema) => schema.required('Farmer Site ID is required'),
			otherwise: (schema) => schema.optional(),
		}),
		temperature: Yup.number()
			.nullable()
			.when('kilnType', {
				is: (value: string) => value !== kilnTypeEnum.RoCC,
				then: (schema) => schema.required('Temperature required'),
				otherwise: (schema) => schema.notRequired(),
			}),
		shortBase: Yup.string().when(['kilnShape'], {
			is: (value: string) => value === kilnShapeEnum.Rectangular,
			then: (schema) =>
				schema
					.typeError('Please enter short base')
					.required('Please enter short base'),
			otherwise: (schema) => schema,
		}),
		longBase: Yup.string().when(['kilnShape'], {
			is: (value: string) => value === kilnShapeEnum.Rectangular,
			then: (schema) =>
				schema
					.typeError('Please enter long base')
					.required('Please enter long base'),
			otherwise: (schema) => schema,
		}),
		upperSurfaceDiameter: Yup.string().when(['kilnShape'], {
			is: (value: string) =>
				![kilnShapeEnum.Cylindrical, kilnShapeEnum.Rectangular].includes(
					value as kilnShapeEnum
				),
			then: (schema) =>
				schema
					.typeError('Please enter upper surface diameter')
					.required('Please enter upper surface diameter'),
			otherwise: (schema) => schema,
		}),
		lowerSurfaceDiameter: Yup.string().when(['kilnShape'], {
			is: (value: string) =>
				![kilnShapeEnum.Cylindrical, kilnShapeEnum.Rectangular].includes(
					value as kilnShapeEnum
				),
			then: (schema) =>
				schema
					.typeError('Please enter lower surface diameter')
					.required('Please enter lower surface diameter'),
			otherwise: (schema) => schema,
		}),
		volume: Yup.number()
			.typeError('Please enter Volume')
			.min(1, 'Volume must be greater than 1')
			.required('Please enter Volume'),
		depth: Yup.string()
			.typeError('Please enter kiln depth')
			.required('Please enter kiln depth'),
		address: Yup.string().notRequired(),
		latitude: Yup.number()
			.when([], {
				is: () => !isGlobalAddKiln,
				then: (schema) => schema.required('Please enter latitude'),
				otherwise: (schema) => schema.nullable().notRequired(),
			})
			.typeError('Please enter latitude')
			.transform((value) => (Number.isNaN(value) ? null : value)),
		longitude: Yup.number()
			.when([], {
				is: () => !isGlobalAddKiln,
				then: (schema) => schema.required('Please enter longitude'),
				otherwise: (schema) => schema.nullable().notRequired(),
			})
			.typeError('Please enter longitude')
			.transform((value) => (Number.isNaN(value) ? null : value)),
		diameter: Yup.string().when(['kilnShape'], {
			is: (value: string) => value === kilnShapeEnum.Cylindrical,
			then: (schema) => schema.required('Please enter kiln diameter'),
			otherwise: (schema) => schema,
		}),
		images: Yup.array().when([], {
			is: () => !isGlobalAddKiln,
			then: (schema) =>
				schema
					.of(imageSchema)
					.min(1, 'Please upload more images')
					.required('Please upload Kiln image'),
			otherwise: (schema) => schema.notRequired(),
		}),
		selectKiln: Yup.string().notRequired(),
	})

export type AddKilnSchema = Yup.InferType<ReturnType<typeof addKilnSchema>>
