import { containerShapesEnum } from '@/utils/constant'
import { InputAdornment, Typography } from '@mui/material'
import { FieldError, useFormContext } from 'react-hook-form'
import { TAddContainer } from './schema'
import { ICalculateVolume } from './AddContainer'
import { AnyObjectSchema } from 'yup'
import { CustomTextField } from '@/utils/components'

interface IProps {
	containerShape: string
	calculateVolume?: (item: ICalculateVolume) => void
	inUse?: boolean
	valueUpToThreeDecimalPlaces?: (value: string) => void
	handleValueChange?: (key: keyof TAddContainer, value: string) => void
	schema?: AnyObjectSchema
}

export function ContainerFields({
	containerShape,
	schema,
	calculateVolume,
	valueUpToThreeDecimalPlaces,
	handleValueChange,
	inUse
}: IProps) {
	const {
		register,
		watch,
		formState: { errors },
	} = useFormContext()
	
	switch (containerShape) {
		case containerShapesEnum.cylinder:
			return (
				<>
					<CustomTextField
						watch={watch}
						schema={schema}
						disabled={inUse}
						id='diameter'
						type='number'
						hideNumberArrows
						label='Diameter'
						placeholder='Enter Your Diameter'
						variant='outlined'
						{...register('diameter')}
						fullWidth
						onChange={(e) => {
							calculateVolume?.({
								diameter: valueUpToThreeDecimalPlaces?.(e.target.value) ?? 0,
								height: watch('height'),
								breadth: watch('breadth'),
							})
							handleValueChange?.('diameter', e.target.value)
						}}
						error={!!errors.diameter?.message}
						helperText={(errors?.diameter as FieldError)?.message}
					/>

					<CustomTextField
						watch={watch}
						schema={schema}
						id='height'
						label='Height'
						type='number'
						disabled={inUse}
						hideNumberArrows
						placeholder='Enter Your Height'
						variant='outlined'
						fullWidth
						{...register('height')}
						onChange={(e) => {
							calculateVolume?.({
								diameter: watch('diameter'),
								height: valueUpToThreeDecimalPlaces?.(e.target.value) ?? 0,
							})
							handleValueChange?.('height', e.target.value)
						}}
						error={!!errors.height?.message}
						helperText={(errors?.height as FieldError)?.message}
					/>
				</>
			)

		case containerShapesEnum.cuboid:
			return (
				<>
					<CustomTextField
						watch={watch}
						schema={schema}
						id='upperSide'
						type='number'
						hideNumberArrows
						label='Upper Side'
						placeholder='Enter Upper Side'
						variant='outlined'
						{...register('length')}
						fullWidth
						onChange={(e) => {
							calculateVolume?.({
								length: valueUpToThreeDecimalPlaces?.(e.target.value) ?? 0,
								height: watch('height'),
								breadth: watch('breadth'),
							})
							handleValueChange?.('length', e.target.value)
						}}
						error={!!errors.length?.message}
						helperText={(errors?.length as FieldError)?.message}
					/>
					<CustomTextField
						watch={watch}
						schema={schema}
						id='breadth'
						type='number'
						hideNumberArrows
						label='Lower Side'
						placeholder='Enter Lower Side'
						variant='outlined'
						{...register('breadth')}
						fullWidth
						onChange={(e) => {
							calculateVolume?.({
								length: watch('length'),
								height: watch('height'),
								breadth: valueUpToThreeDecimalPlaces?.(e.target.value) ?? 0,
							})
							handleValueChange?.('breadth', e.target.value)
						}}
						error={!!errors.breadth?.message}
						helperText={(errors?.breadth as FieldError)?.message}
					/>
					<CustomTextField
						watch={watch}
						schema={schema}
						id='height'
						label='Height'
						type='number'
						hideNumberArrows
						placeholder='Enter Your Height'
						variant='outlined'
						fullWidth
						{...register('height')}
						onChange={(e) => {
							calculateVolume?.({
								length: watch('length'),
								height: valueUpToThreeDecimalPlaces?.(e.target.value) ?? 0,
								breadth: watch('breadth'),
							})
							handleValueChange?.('height', e.target.value)
						}}
						error={!!errors.height?.message}
						helperText={(errors?.height as FieldError)?.message}
					/>
				</>
			)
		case containerShapesEnum.rectangular:
			return (
				<>
					<CustomTextField
						watch={watch}
						schema={schema}
						id='upperBase'
						type='number'
						hideNumberArrows
						label='Long Base'
						placeholder='Enter Long Base'
						variant='outlined'
						defaultValue={watch('upperBase')}
						{...register('upperBase')}
						fullWidth
						onChange={(e) => {
							calculateVolume?.({
								length: watch('length'),
								height: watch('height'),
								upperBase: valueUpToThreeDecimalPlaces?.(e.target.value) ?? 0,
								lowerBase: watch('lowerBase'),
							})
							handleValueChange?.('upperBase', e.target.value)
						}}
						error={!!errors.upperBase?.message}
						helperText={(errors?.upperBase as FieldError)?.message}
					/>
					<CustomTextField
						watch={watch}
						schema={schema}
						id='lowerBase'
						type='number'
						hideNumberArrows
						label='Short Base'
						placeholder='Enter Short Base'
						variant='outlined'
						{...register('lowerBase')}
						fullWidth
						onChange={(e) => {
							calculateVolume?.({
								length: watch('length'),
								height: watch('height'),
								upperBase: watch('upperBase'),
								lowerBase: valueUpToThreeDecimalPlaces?.(e.target.value) ?? 0,
							})
							handleValueChange?.('lowerBase', e.target.value)
						}}
						error={!!errors.lowerBase?.message}
						helperText={(errors?.lowerBase as FieldError)?.message}
					/>
					<CustomTextField
						watch={watch}
						schema={schema}
						id='length'
						label='Length'
						type='number'
						hideNumberArrows
						placeholder='Enter Your Length'
						variant='outlined'
						fullWidth
						{...register('length')}
						onChange={(e) => {
							calculateVolume?.({
								length: valueUpToThreeDecimalPlaces?.(e.target.value) ?? 0,
								height: watch('height'),
								upperBase: watch('upperBase'),
								lowerBase: watch('lowerBase'),
							})
							handleValueChange?.('length', e.target.value)
						}}
						error={!!errors.height?.message}
						helperText={(errors?.height as FieldError)?.message}
					/>
					<CustomTextField
						watch={watch}
						schema={schema}
						id='height'
						label='Height'
						type='number'
						hideNumberArrows
						placeholder='Enter Your Height'
						variant='outlined'
						fullWidth
						{...register('height')}
						onChange={(e) => {
							calculateVolume?.({
								length: watch('length'),
								height: valueUpToThreeDecimalPlaces?.(e.target.value) ?? 0,
								upperBase: watch('upperBase'),
								lowerBase: watch('lowerBase'),
							})
							handleValueChange?.('height', e.target.value)
						}}
						error={!!errors.height?.message}
						helperText={(errors?.height as FieldError)?.message}
					/>
				</>
			)

		case containerShapesEnum.conical:
			return (
				<>
					<CustomTextField
						watch={watch}
						schema={schema}
						id='upperSide'
						type='number'
						hideNumberArrows
						label='Upper Surface Diameter'
						placeholder='Upper Surface Diameter'
						variant='outlined'
						{...register('upperSide')}
						fullWidth
						onChange={(e) => {
							calculateVolume?.({
								upperSide: valueUpToThreeDecimalPlaces?.(e.target.value) ?? 0,
								height: watch('height'),
								lowerSide: watch('lowerSide'),
							})
							handleValueChange?.('upperSide', e.target.value)
						}}
						error={!!errors.diameter?.message}
						helperText={(errors?.diameter as FieldError)?.message}
					/>
					<CustomTextField
						watch={watch}
						schema={schema}
						id='lowerSide'
						type='number'
						hideNumberArrows
						label='Lower Surface Diameter'
						placeholder='Lower Surface Diameter'
						variant='outlined'
						{...register('lowerSide')}
						fullWidth
						onChange={(e) => {
							calculateVolume?.({
								upperSide: watch('upperSide'),
								height: watch('height'),
								lowerSide: valueUpToThreeDecimalPlaces?.(e.target.value) ?? 0,
							})
							handleValueChange?.('lowerSide', e.target.value)
						}}
						error={!!errors.lowerSide?.message}
						helperText={(errors?.lowerSide as FieldError)?.message}
					/>
					<CustomTextField
						watch={watch}
						schema={schema}
						id='height'
						label='Height'
						type='number'
						hideNumberArrows
						placeholder='Enter Your Height'
						variant='outlined'
						fullWidth
						{...register('height')}
						onChange={(e) => {
							calculateVolume?.({
								upperSide: watch('upperSide'),
								height: valueUpToThreeDecimalPlaces?.(e.target.value) ?? 0,
								lowerSide: watch('lowerSide'),
							})
							handleValueChange?.('height', e.target.value)
						}}
						error={!!errors.height?.message}
						helperText={(errors?.height as FieldError)?.message}
					/>
				</>
			)

		case containerShapesEnum.other:
			return (
				<CustomTextField
					watch={watch}
					schema={schema}
					id='Volume'
					type='number'
					hideNumberArrows
					label='Volume'
					placeholder='Enter Your Volume in litre'
					variant='outlined'
					{...register('volume')}
					fullWidth
					InputProps={{
						endAdornment: (
							<InputAdornment position='end'>
								<Typography>litre</Typography>
							</InputAdornment>
						),
					}}
					InputLabelProps={{
						shrink: true,
					}}
					error={!!errors.volume?.message}
					helperText={(errors?.volume as FieldError)?.message}
				/>
			)
		default:
			return null
	}
}
