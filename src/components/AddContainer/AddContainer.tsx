import { Close } from '@mui/icons-material'
import { LoadingButton } from '@mui/lab'
import {
	FormControl,
	FormHelperText,
	IconButton,
	MenuItem,
	Stack,
	styled,
	Typography,
} from '@mui/material'
import { FormProvider, SubmitHandler, useForm } from 'react-hook-form'
import { yupResolver } from '@hookform/resolvers/yup'
import {
	IContainerDetails,
	IContainerWithDimensionImages,
	IMedia,
	INetwork,
} from '@/interfaces'
import { AddContainerSchema, TAddContainer } from './schema'
import { useCallback, useEffect, useMemo, useState } from 'react'
import { containerShapesEnum, measuringUnitEnum, userRoles } from '@/utils/constant'
import {
	convertCentimeterToMillimeter,
	convertMeterToMillimeter,
	convertMillimeterCubetoLitre,
	convertMillimeterToMeter,
	valueUpToThreeDecimalPlaces,
	valueWithFixedDecimalPlaces,
} from '@/utils/helper'
import { theme } from '@/lib/theme/theme'
import { authAxios, useAuthContext } from '@/contexts'
import { toast } from 'react-toastify'
import { useMutation, useQuery, useQueryClient } from '@tanstack/react-query'
import { useParams, useSearchParams } from 'react-router-dom'
import { AxiosError } from 'axios'
import { MultipleFileUploader } from '../MultipleFileUploader'
import { CustomTextField } from '@/utils/components'
import { DimensionFieldContainerWithImage } from './DimensionFieldContainerWithImage'
import { ContainerFields } from './ContainerFields'

// Helper function to convert image ID to array
const convertImageIdToArray = (imageId?: string): string[] =>
	imageId ? [imageId] : []

export const ContainerShapeTypes = (isSamplingContainer: boolean) => [
	{ label: 'Cuboidal', value: containerShapesEnum.cuboid, hidden: false },
	{ label: 'Cylinder', value: containerShapesEnum.cylinder, hidden: false },
	{ label: 'Conical', value: containerShapesEnum.conical, hidden: false },
	{
		label: 'Rectangular',
		value: containerShapesEnum.rectangular,
		hidden: isSamplingContainer,
	},
	{
		label: 'Other',
		value: containerShapesEnum.other,
		hidden: !isSamplingContainer,
	},
]
interface IProps {
	handleCloseDrawer: () => void
	editMode?: boolean
	containerDetails?: IContainerWithDimensionImages
	addText?: string
	isCsink?: boolean
	subheading?: string
	cSinkNetworkDetails?: INetwork
	isSamplingContainer?: boolean
	isGlobal?: boolean
}
export interface ICalculateVolume {
	diameter?: number | null
	length?: number | null
	height?: number | null
	breadth?: number | null
	unitValue?: string
	upperSide?: number | null
	lowerSide?: number | null
	upperBase?: number | null
	lowerBase?: number | null
}

const acceptedImageTypes = ['png', 'jpg', 'jpeg', 'webp']

const getApiforGlobalContainers = (
	csinkManagerId?: string,
	accountType?: userRoles,
	artisanProId?: string,
	cSinkNetworkId?: string
) => {
	if (artisanProId)
		return `/artisian-pro/${artisanProId}/measuring-container-template`
	if (cSinkNetworkId)
		return `cs-network/${cSinkNetworkId}/measuring-container-template`
	switch (accountType) {
		case userRoles.BiomassAggregator:
			return `/csink-manager/${csinkManagerId}/settings/measuring-container-template`
		case userRoles.CsinkManager:
		default:
			return `/settings/measuring-container-template`
	}
}

const getAllGlobalMeasuringContainers = (
	paramsLimit: string,
	paramsPage: string,
	accountType: string | undefined,
	csinkManagerId: string | undefined,
	artisanProId: string,
	cSinkNetworkId: string
) => {
	const queryParams = new URLSearchParams({
		limit: paramsLimit,
		page: paramsPage,
		isAssigned: 'true',
	})

	const endpoint = `${getApiforGlobalContainers(
		csinkManagerId,
		accountType as userRoles,
		artisanProId,
		cSinkNetworkId
	)}?${queryParams}`
	const response = authAxios.get(endpoint)

	return response
}

export const AddContainer = ({
	handleCloseDrawer,
	editMode = false,
	addText = '',
	containerDetails,
	subheading,
	isCsink = true,
	isSamplingContainer = false,
	isGlobal = false,
}: IProps) => {
	const { userDetails } = useAuthContext()
	const [measuringUnit, setMeasuringUnit] = useState(measuringUnitEnum.cm)

	const queryClient = useQueryClient()
	const [searchParams] = useSearchParams()
	const siteId = searchParams.get('siteTab') ?? ''

	const { cSinkNetworkId, artisanProId } = useParams()

	const initialValues = useMemo(
		() => ({
			name: '',
			diameter: null,
			breadth: null,
			length: null,
			height: null,
			upperSide: null,
			lowerSide: null,
			upperBase: null,
			lowerBase: null,
			shape: 'cylinder',
			containerImage: [],
			volume: NaN,
			container: '',
		}),
		[]
	)

	const allMeasuringContainer = useQuery({
		queryKey: [
			'allMeasuringContainer',
			userDetails?.csinkManagerId,
			userDetails?.accountType,
		],
		queryFn: () => {
			return getAllGlobalMeasuringContainers(
				'1000',
				'0',
				userDetails?.accountType,
				userDetails?.csinkManagerId,
				artisanProId ?? '',
				cSinkNetworkId ?? ''
			)
		},
		select: ({ data }) => data,
	})

	const initialName = editMode ? containerDetails?.name : addText

	const initialValuesEditMode = {
		name: initialName ?? '',
		container: '',
		diameter: containerDetails?.diameter
			? convertMillimeterToMeter(containerDetails.diameter)
			: null,
		breadth: containerDetails?.breadth
			? convertMillimeterToMeter(containerDetails.breadth)
			: null,
		length: containerDetails?.length
			? convertMillimeterToMeter(containerDetails.length)
			: null,
		height: containerDetails?.height
			? convertMillimeterToMeter(containerDetails.height)
			: null,
		upperSide: containerDetails?.upperSurfaceDiameter
			? convertMillimeterToMeter(containerDetails.upperSurfaceDiameter)
			: null,
		lowerSide: containerDetails?.lowerSurfaceDiameter
			? convertMillimeterToMeter(containerDetails.lowerSurfaceDiameter)
			: null,
		upperBase: containerDetails?.upperBase
			? convertMillimeterToMeter(containerDetails.upperBase)
			: null,
		lowerBase: containerDetails?.lowerBase
			? convertMillimeterToMeter(containerDetails.lowerBase)
			: null,
		shape: containerDetails?.shape ?? 'cylinder',
		containerImage: containerDetails?.imageURL ?? [],
		volume: containerDetails?.volume ?? 0,
		breadthImageIds: containerDetails?.breadthImages?.[0]?.id,
		lengthImageIds: containerDetails?.lengthImages?.[0]?.id,
		heightImageIds: containerDetails?.heightImages?.[0]?.id,
		diameterImageIds: containerDetails?.diameterImages?.[0]?.id,
		upperSurfaceDiameterImageIds:
			containerDetails?.upperSurfaceDiameterImages?.[0]?.id,
		lowerSurfaceDiameterImageIds:
			containerDetails?.lowerSurfaceDiameterImages?.[0]?.id,
		lowerBaseImageIds: containerDetails?.lowerBaseImages?.[0]?.id,
		upperBaseImageIds: containerDetails?.upperBaseImages?.[0]?.id,
	}

	const form = useForm<TAddContainer>({
		defaultValues: initialValuesEditMode,
		mode: 'all',
		resolver: yupResolver<TAddContainer>(AddContainerSchema(isGlobal)),
	})
	const {
		register,
		formState: { errors },
		watch,
		setValue,
		clearErrors,
		handleSubmit,
	} = form
	const volume = watch('volume')
	const selectedContainerId = watch('container')

	const editModeSetValues = useCallback(
		({
			editContainerDetails,
		}: {
			editContainerDetails: IContainerDetails | undefined
		}) => {
			if (!editContainerDetails) return
			Object.entries(editContainerDetails).forEach(
				([key, value]: [string, any]) => {
					const typedKey = key as keyof typeof initialValues
					if (
						value &&
						(key === 'upperSurfaceDiameter' || key === 'lowerSurfaceDiameter')
					) {
						setValue(
							'upperSide',
							convertMillimeterToMeter(
								editContainerDetails.upperSurfaceDiameter as number
							) ??
								null ??
								undefined
						)
						setValue(
							'lowerSide',
							convertMillimeterToMeter(
								editContainerDetails.lowerSurfaceDiameter as number
							) ??
								null ??
								undefined
						)
					} else if (key == 'volume') {
						setValue(key as keyof typeof initialValues, value)
					} else if (value && key in initialValues) {
						if (typeof initialValues[typedKey] !== 'string') {
							setValue(
								key as keyof typeof initialValues,
								convertMillimeterToMeter(value)
							)
						} else {
							setValue(key as keyof typeof initialValues, value)
						}
					}
				}
			)
		},
		[initialValues, setValue]
	)

	const calculateCylinderVolume = useCallback(
		({
			diameter,
			height,
			unitValue,
		}: {
			diameter?: number | null
			height?: number | null
			unitValue?: string
		}) => {
			const unit = unitValue ?? measuringUnit
			const radius =
				unit !== 'm'
					? convertCentimeterToMillimeter(diameter || 0) / 2
					: convertMeterToMillimeter(diameter || 0) / 2

			const H = unit !== 'm' ? Number(height) * 10 : Number(height) * 1000

			// cylindrical shape volume
			const cylinderVolume = Math.PI * radius ** 2 * H
			const volume = Number(convertMillimeterCubetoLitre(cylinderVolume ?? 0))
			return volume
		},
		[measuringUnit]
	)
	const calculateConicalVolume = useCallback(
		({
			upperSide,
			lowerSide,
			height,
			unitValue,
		}: {
			upperSide?: number | null
			lowerSide?: number | null
			height?: number | null
			unitValue?: string
		}) => {
			const unit = unitValue ?? measuringUnit
			const H =
				unit !== 'm'
					? convertCentimeterToMillimeter(height || 0)
					: convertMeterToMillimeter(height || 0)
			const upperSurfaceRadius =
				unit !== 'm'
					? convertCentimeterToMillimeter(upperSide || 0) / 2
					: convertMeterToMillimeter(upperSide || 0) / 2
			const lowerSurfaceRadius =
				unit !== 'm'
					? convertCentimeterToMillimeter(lowerSide || 0) / 2
					: convertMeterToMillimeter(lowerSide || 0) / 2

			const conicalVolume =
				((Math.PI * H) / 3) *
				(lowerSurfaceRadius * lowerSurfaceRadius +
					lowerSurfaceRadius * upperSurfaceRadius +
					upperSurfaceRadius * upperSurfaceRadius)
			const volume = Number(convertMillimeterCubetoLitre(conicalVolume ?? 0))
			return volume
		},
		[measuringUnit]
	)

	const calculateRectangularFrustumVolume = useCallback(
		({
			upperBase,
			lowerBase,
			height,
			length,
			unitValue,
		}: {
			upperBase?: number | null
			lowerBase?: number | null
			height?: number | null
			length?: number | null
			unitValue?: string
		}) => {
			const unit = unitValue ?? measuringUnit
			const H =
				unit !== 'm'
					? convertCentimeterToMillimeter(height || 0)
					: convertMeterToMillimeter(height || 0)
			const L =
				unit !== 'm'
					? convertCentimeterToMillimeter(length || 0)
					: convertMeterToMillimeter(length || 0)
			const upperBaseSurface =
				unit !== 'm'
					? convertCentimeterToMillimeter(upperBase || 0) / 2
					: convertMeterToMillimeter(upperBase || 0) / 2
			const lowerBaseSurface =
				unit !== 'm'
					? convertCentimeterToMillimeter(lowerBase || 0) / 2
					: convertMeterToMillimeter(lowerBase || 0) / 2

			const rectangularFrustumVolume =
				(upperBaseSurface + lowerBaseSurface) * L * H
			const volume = Number(
				convertMillimeterCubetoLitre(rectangularFrustumVolume ?? 0)
			)
			return volume
		},
		[measuringUnit]
	)
	const calculateCuboidalVolume = useCallback(
		({
			length,
			breadth,
			height,
			unitValue,
		}: {
			length?: number | null
			breadth?: number | null
			height?: number | null
			unitValue?: string
		}) => {
			const unit = unitValue ?? measuringUnit
			const H =
				unit !== 'm'
					? convertCentimeterToMillimeter(height || 0)
					: convertMeterToMillimeter(height || 0)
			const upperSide =
				unit !== 'm'
					? convertCentimeterToMillimeter(length || 0)
					: convertMeterToMillimeter(length || 0)
			const lowerSide =
				unit !== 'm'
					? convertCentimeterToMillimeter(breadth || 0)
					: convertMeterToMillimeter(breadth || 0)

			const cuboidVolume =
				(H / 3) *
				(upperSide * upperSide +
					lowerSide * lowerSide +
					Math.sqrt(upperSide * upperSide * lowerSide * lowerSide))
			const volume = Number(convertMillimeterCubetoLitre(cuboidVolume ?? 0))
			return volume
		},
		[measuringUnit]
	)

	const handleShapeChange = (shape: string) => {
		setValue('shape', shape)
		// Reset all dimensions when shape is changed
		setValue('diameter', null)
		setValue('height', null)
		setValue('breadth', null)
		setValue('length', null)
		setValue('upperSide', null)
		setValue('lowerSide', null)
		setValue('upperBase', null)
		setValue('lowerBase', null)
		setValue('volume', 0)
	}

	const calculateVolume = useCallback(
		({
			diameter,
			height,
			breadth,
			length,
			unitValue,
			upperSide,
			lowerSide,
			upperBase,
			lowerBase,
		}: ICalculateVolume) => {
			switch (watch('shape')) {
				case containerShapesEnum.cylinder:
					setValue(
						'volume',
						calculateCylinderVolume({ diameter, height, unitValue })
					)
					break
				case containerShapesEnum.conical:
					setValue(
						'volume',
						calculateConicalVolume({
							upperSide,
							lowerSide,
							height,
							unitValue,
						})
					)
					break
				case containerShapesEnum.rectangular:
					setValue(
						'volume',
						calculateRectangularFrustumVolume({
							upperBase,
							lowerBase,
							length,
							height,
							unitValue,
						})
					)
					break
				case containerShapesEnum.cuboid:
					setValue(
						'volume',
						calculateCuboidalVolume({
							length,
							breadth,
							height,
							unitValue,
						})
					)
					break
				default:
			}
		},
		[
			calculateConicalVolume,
			calculateCuboidalVolume,
			calculateRectangularFrustumVolume,
			calculateCylinderVolume,
			setValue,
			watch,
		]
	)
	const handleUnit = useCallback(
		(value: measuringUnitEnum) => {
			setMeasuringUnit(value)
			let diameter = watch('diameter')
			let height = watch('height')
			let breadth = watch('breadth')
			if (value === 'cm') {
				diameter = valueWithFixedDecimalPlaces(Number(diameter), 1)
				height = valueWithFixedDecimalPlaces(Number(height), 1)
				breadth = valueWithFixedDecimalPlaces(Number(breadth), 1)
			}
			calculateVolume({ diameter, height, breadth, length, unitValue: value })
			setValue('diameter', diameter)
			setValue('height', height)
			setValue('breadth', breadth)
		},
		[calculateVolume, setValue, watch]
	)

	const payloadForShape = useCallback(
		(shape: string, formValues: TAddContainer) => {
			if (shape === containerShapesEnum.cuboid) {
				return {
					height:
						measuringUnit !== 'm'
							? Number(formValues.height) * 10
							: Number(formValues.height) * 1000,
					length:
						measuringUnit !== 'm'
							? Number(formValues?.length) * 10
							: Number(formValues?.length) * 1000,
					breadth:
						measuringUnit !== 'm'
							? Number(formValues?.breadth) * 10
							: Number(formValues?.breadth) * 1000,
					breadthImageIds: convertImageIdToArray(formValues.breadthImageIds),
					lengthImageIds: convertImageIdToArray(formValues.lengthImageIds),
					heightImageIds: convertImageIdToArray(formValues.heightImageIds),
				}
			}
			if (shape === containerShapesEnum.cylinder) {
				return {
					height:
						measuringUnit !== 'm'
							? Number(formValues.height) * 10
							: Number(formValues.height) * 1000,
					diameter:
						measuringUnit !== 'm'
							? Number(formValues.diameter) * 10
							: Number(formValues.diameter) * 1000,
					diameterImageIds: convertImageIdToArray(formValues.diameterImageIds),
					heightImageIds: convertImageIdToArray(formValues.heightImageIds),
				}
			}

			if (shape === containerShapesEnum.conical) {
				return {
					height:
						measuringUnit !== 'm'
							? Number(formValues.height) * 10
							: Number(formValues.height) * 1000,
					upperSurfaceDiameter:
						measuringUnit !== 'm'
							? Number(formValues?.upperSide) * 10
							: Number(formValues?.upperSide) * 1000,
					lowerSurfaceDiameter:
						measuringUnit !== 'm'
							? Number(formValues?.lowerSide) * 10
							: Number(formValues?.lowerSide) * 1000,
					heightImageIds: convertImageIdToArray(formValues.heightImageIds),

					...(isSamplingContainer //  key changes for image ids
						? {
								upperSurfaceDiameterImageIds: convertImageIdToArray(
									formValues.upperSurfaceDiameterImageIds
								),
								lowerSurfaceDiameterImageIds: convertImageIdToArray(
									formValues.lowerSurfaceDiameterImageIds
								),
						  }
						: {
								upperSurfaceImageDiameterIds: convertImageIdToArray(
									formValues.upperSurfaceDiameterImageIds
								),
								lowerSurfaceImageDiameterIds: convertImageIdToArray(
									formValues.lowerSurfaceDiameterImageIds
								),
						  }),
				}
			}
			if (shape === containerShapesEnum.rectangular) {
				return {
					height:
						measuringUnit !== 'm'
							? Number(formValues.height) * 10
							: Number(formValues.height) * 1000,
					upperBase:
						measuringUnit !== 'm'
							? Number(formValues?.upperBase) * 10
							: Number(formValues?.upperBase) * 1000,
					lowerBase:
						measuringUnit !== 'm'
							? Number(formValues?.lowerBase) * 10
							: Number(formValues?.lowerBase) * 1000,
					length:
						measuringUnit !== 'm'
							? Number(formValues?.length) * 10
							: Number(formValues?.length) * 1000,
					lengthImageIds: convertImageIdToArray(formValues.lengthImageIds),
					heightImageIds: convertImageIdToArray(formValues.heightImageIds),
					lowerBaseImageIds: convertImageIdToArray(
						formValues.lowerBaseImageIds
					),
					upperBaseImageIds: convertImageIdToArray(
						formValues.upperBaseImageIds
					),
				}
			}
			if (shape === containerShapesEnum.other) {
				return {
					volume: Number(volume),
				}
			}
			return null
		},
		[measuringUnit, volume]
	)
	const postApiAddContainer = () => {
		if (isGlobal) {
			switch (userDetails?.accountType) {
				case userRoles.CsinkManager:
					return `/csink-manager/${userDetails?.csinkManagerId}/settings/measuring-container-template`
				case userRoles.BiomassAggregator:
					return `/biomass-aggregator/${userDetails?.biomassAggregatorId}/measuring-container-template`
				case userRoles.Admin:
				default:
					return `/settings/measuring-container-template`
			}
		}
		if (!isCsink && !isSamplingContainer)
			return `/artisian-pro/${artisanProId}/site/${siteId}/measuring-container`
		if (isSamplingContainer)
			return `/artisian-pro/${artisanProId}/site/${siteId}/container`
		else return `cs-network/${cSinkNetworkId}/container`
	}

	const getApiEditContainer = () => {
		if (!isCsink && !isSamplingContainer)
			return `/artisian-pro/${artisanProId}/site/${siteId}/measuring-container/${containerDetails?.id}`
		if (isSamplingContainer)
			return `/artisian-pro/${artisanProId}/site/${siteId}/container/${containerDetails?.id}`
		else return `cs-network/${cSinkNetworkId}/container/${containerDetails?.id}`
	}
	const addContainerMutation = useMutation({
		mutationKey: ['addContainer', cSinkNetworkId],
		mutationFn: async (formValues: TAddContainer) => {
			const payload = {
				...payloadForShape(formValues?.shape, formValues),
				imageIds: formValues.containerImage
					? formValues?.containerImage?.map((image) => image?.id)
					: null,
				shape: formValues?.shape,
				name: formValues?.name,
			}
			const api = postApiAddContainer()
			return await authAxios.post(api, payload)
		},
		onSuccess: ({ data }) => {
			toast(data?.message)
			queryClient.refetchQueries({
				queryKey: [
					isGlobal
						? isSamplingContainer
							? 'allMeasuringContainer'
							: 'allMeasuringContainer'
						: isSamplingContainer
						? 'getSamplingContainerList'
						: 'getContainerList',
				],
			})
			handleCloseDrawer()
		},
		onError: (error: AxiosError) => {
			toast((error?.response?.data as { messageToUser: string }).messageToUser)
		},
	})
	const editContainerMutation = useMutation({
		mutationKey: ['editContainer', cSinkNetworkId, containerDetails?.id],
		mutationFn: async (formValues: TAddContainer) => {
			const api = getApiEditContainer()
			const payload = {
				...payloadForShape(formValues?.shape, formValues),
				imageIds: formValues.containerImage
					? formValues?.containerImage?.map((image) => image?.id)
					: null,
				shape: formValues?.shape,
				name: formValues?.name,
			}
			return await authAxios.put(api, payload)
		},
		onSuccess: ({ data }) => {
			toast(data?.message)
			queryClient.invalidateQueries({
				queryKey: [
					isSamplingContainer ? 'getSamplingContainerList' : 'getContainerList',
				],
			})
			handleCloseDrawer()
		},
		onError: (error: AxiosError) => {
			toast((error?.response?.data as { messageToUser: string }).messageToUser)
		},
	})
	const handleSaveContainer: SubmitHandler<TAddContainer> = (formValues) => {
		editMode
			? editContainerMutation.mutate(formValues)
			: addContainerMutation.mutate(formValues)
	}
	const isLoading = useMemo(
		() => addContainerMutation.isPending,
		[addContainerMutation]
	)
	useEffect(() => {
		if (selectedContainerId) {
			const selectedContainerDetail: IContainerDetails[] =
				allMeasuringContainer.data?.containers?.filter(
					(item: IContainerDetails) => item.id === selectedContainerId
				)
			editModeSetValues({ editContainerDetails: selectedContainerDetail[0] })
		}
	}, [
		allMeasuringContainer.data?.containers,
		allMeasuringContainer.isLoading,
		editModeSetValues,
		selectedContainerId,
	])

	const shouldShowSelectContainer = useMemo(() => {
		if (isGlobal) {
			return (
				userDetails?.accountType !== userRoles.Admin &&
				allMeasuringContainer.isLoading === false
			)
		} else {
			return (
				!isSamplingContainer &&
				!editMode &&
				allMeasuringContainer.isLoading === false
			)
		}
	}, [
		isSamplingContainer,
		editMode,
		isGlobal,
		userDetails?.accountType,
		allMeasuringContainer.isLoading,
	])

	const getDimensionImageUrl = useCallback(
		(fieldName: keyof TAddContainer): string => {
			const imageId = form.watch(fieldName) as string
			if (!imageId) return ''

			if (editMode && containerDetails) {
				const fieldMappings: Record<
					string,
					keyof IContainerWithDimensionImages
				> = {
					heightImageIds: 'heightImages',
					breadthImageIds: 'breadthImages',
					lengthImageIds: 'lengthImages',
					diameterImageIds: 'diameterImages',
					upperSurfaceDiameterImageIds: 'upperSurfaceDiameterImages',
					lowerSurfaceDiameterImageIds: 'lowerSurfaceDiameterImages',
					lowerBaseImageIds: 'lowerBaseImages',
					upperBaseImageIds: 'upperBaseImages',
				}

				const imageField = fieldMappings[fieldName as string]
				if (imageField) {
					const images = containerDetails[imageField] as IMedia[]
					if (images && images.length > 0) {
						const matchingImage = images.find((img) => img.id === imageId)
						return matchingImage?.url || ''
					}
				}
			}

			return ''
		},
		[form, editMode, containerDetails]
	)

	// Image upload handlers
	const handleMainImageUpload = useCallback(
		(uploadedImages: IMedia[]) => {
			const formattedImages = uploadedImages.map((img) => ({
				id: img.id,
				url: img.url,
				fileName: img.fileName || '',
			}))
			setValue('containerImage', formattedImages)
			clearErrors('containerImage')
		},
		[clearErrors, setValue]
	)

	return (
		<StyleContainer>
			<Stack className='header'>
				<Stack
					direction='row'
					spacing={1}
					alignItems='center'
					width='100%'
					justifyContent='space-between'>
					<Stack>
						<Typography variant='h5'>
							{editMode ? 'Edit' : 'Add'} Container
						</Typography>
						<Typography variant='subtitle1'>{subheading}</Typography>
					</Stack>
					<IconButton onClick={handleCloseDrawer}>
						<Close />
					</IconButton>
				</Stack>
			</Stack>
			<Stack className='container'>
				<FormProvider {...form}>
					<Stack gap={5}>
						{shouldShowSelectContainer ? (
							<CustomTextField
								schema={AddContainerSchema(isGlobal)}
								label={'Select Measuring Container '}
								select
								fullWidth
								disabled={!allMeasuringContainer.data?.containers?.length}
								value={watch('container')}
								onChange={(e) => {
									setValue('container', e.target.value)
									handleShapeChange(watch('shape'))
								}}
								// {...register('container')}
								className='input-disable-class'>
								{allMeasuringContainer.data?.containers?.map(
									(option: IContainerDetails) => (
										<MenuItem key={option.name} value={option.id}>
											{option.name}
										</MenuItem>
									)
								)}
							</CustomTextField>
						) : null}
						<CustomTextField
							schema={AddContainerSchema(isGlobal)}
							fullWidth
							id='name'
							value={watch('name')}
							type='text'
							label='Enter Container Name '
							variant='outlined'
							error={!!errors.name?.message}
							helperText={errors?.name?.message}
							{...register('name')}
						/>
						<Stack flexDirection='row' gap={theme.spacing(1)}>
							<FormControl fullWidth>
								<CustomTextField
									schema={AddContainerSchema(isGlobal)}
									select
									fullWidth
									value={watch('shape')}
									label='Select the Container Shape'
									{...register('shape')}
									onChange={(e) => handleShapeChange(e.target.value)}
									id='container-shape'
									disabled={containerDetails?.inUse}
									error={!!errors.shape?.message}
									helperText={errors.shape?.message}>
									{ContainerShapeTypes(isSamplingContainer).map(
										(container, index) =>
											!container.hidden ? (
												<MenuItem
													value={container.value}
													key={container.value + index}>
													{container?.label}
												</MenuItem>
											) : null
									)}
								</CustomTextField>
							</FormControl>
							<FormControl fullWidth>
								<CustomTextField
									schema={AddContainerSchema(isGlobal)}
									select
									label='Measuring Unit'
									disabled={containerDetails?.inUse}
									fullWidth
									value={measuringUnit}
									onChange={(e) => {
										handleUnit(e.target.value as measuringUnitEnum)
									}}>
									<MenuItem value='cm'>cm</MenuItem>
									<MenuItem value='m'>m</MenuItem>
								</CustomTextField>
							</FormControl>
						</Stack>
						{isGlobal ? (
							<ContainerFields
								containerShape={watch('shape')}
								calculateVolume={calculateVolume}
								schema={AddContainerSchema(isGlobal)}
								inUse={containerDetails?.inUse}
								valueUpToThreeDecimalPlaces={(value: string) =>
									valueUpToThreeDecimalPlaces({ value, measuringUnit })
								}
							/>
						) : (
							<DimensionFieldContainerWithImage
								form={form}
								containerShape={watch('shape')}
								schema={AddContainerSchema(isGlobal)}
								inUse={containerDetails?.inUse}
								calculateVolume={calculateVolume}
								unit={measuringUnit}
								valueUpToThreeDecimalPlaces={(value: string) =>
									valueUpToThreeDecimalPlaces({ value, measuringUnit })
								}
								getImageUrl={(field) => getDimensionImageUrl(field)}
							/>
						)}

						{watch('shape') !== containerShapesEnum.other ? (
							<Stack>
								<Typography variant='subtitle1'>
									Total Volume: {volume}
									Ltrs.
								</Typography>
								{form.watch('volume') < 10 && (
									<Typography variant='subtitle1' color='error'>
										Warning: Volume of the Container is too low
									</Typography>
								)}
							</Stack>
						) : null}
						{!isGlobal && (
							<Stack rowGap={2}>
								<Typography variant='subtitle1'>Container Image</Typography>
								<Typography
									variant='body1'
									color='text.secondary'
									fontSize={theme.spacing(1.5)}
									fontWeight={theme.typography.caption.fontWeight}>
									Please enter the Images after measuring all the Dimensions
								</Typography>
								<Stack rowGap={2} width='100%'>
									<MultipleFileUploader
										data={containerDetails?.imageURLs?.map((data) => ({
											...data,
											fileName: data.path,
										}))}
										fileType={acceptedImageTypes}
										training={false}
										heading='Add Container Image'
										sx={{
											height: { xs: 100, md: 150 },
											width: '100%',
										}}
										imageHeight={100}
										setUploadData={handleMainImageUpload}
									/>
								</Stack>
								<FormHelperText error={Boolean(errors.containerImage)}>
									{errors?.containerImage?.message}
								</FormHelperText>
							</Stack>
						)}

						<LoadingButton
							loading={isLoading}
							disabled={isLoading}
							onClick={handleSubmit(handleSaveContainer)}
							variant='contained'>
							{editMode ? 'Save' : 'Add'}
						</LoadingButton>
					</Stack>
				</FormProvider>
			</Stack>
		</StyleContainer>
	)
}

const StyleContainer = styled(Stack)(({ theme }) => ({
	gap: theme.spacing(2),
	'.header': {
		flexDirection: 'row',
		justifyContent: 'space-between',
		alignItems: 'center',
		padding: theme.spacing(4.5, 2),
		borderBottom: `${theme.spacing(0.125)} solid ${
			theme.palette.neutral['100']
		}`,
	},

	'.container': {
		padding: theme.spacing(2, 3),
		gap: theme.spacing(2.5),
	},
}))
