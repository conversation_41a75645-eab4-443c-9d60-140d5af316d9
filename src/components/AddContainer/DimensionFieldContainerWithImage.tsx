// components/DimensionFieldContainerWithImage.tsx
import { Stack, Typography, InputAdornment } from '@mui/material'
import { CustomTextField } from '@/utils/components'
import { FieldError, UseFormReturn } from 'react-hook-form'
import { AnyObjectSchema } from 'yup'
import { containerShapesEnum } from '@/utils/constant'
import { TAddContainer } from './schema'
import { ICalculateVolume } from './AddContainer'
import { CustomFileUploader } from '../CustomFileUploader'
import { theme } from '@/lib/theme/theme'

interface Props {
	form: UseFormReturn<TAddContainer>
	schema: AnyObjectSchema
	containerShape: string
	inUse?: boolean
	unit: string
	valueUpToThreeDecimalPlaces: (value: string) => number
	calculateVolume?: (item: ICalculateVolume) => void
	getImageUrl?: (field: keyof TAddContainer) => string
}

export function DimensionFieldContainerWithImage({
	form,
	schema,
	containerShape,
	inUse,
	valueUpToThreeDecimalPlaces,
	calculateVolume,
	getImageUrl,
}: Props) {
	const {
		watch,
		register,
		setValue,
		clearErrors,
		formState: { errors },
	} = form
	const renderField = (
		label: string,
		placeholder: string,
		field: keyof TAddContainer,
		imageFieldName: keyof TAddContainer,
		instructionText: string,
		calcCallback: (value: number) => void
	) => (
		<Stack gap={theme.spacing(1.5)}>
			<Typography
				variant='body1'
				color='text.secondary'
				fontSize={theme.spacing(1.5)}
				fontWeight={theme.typography.caption.fontWeight}>
				{instructionText}
			</Typography>
			<Stack direction='row' gap={2} alignItems='flex-start'>
				<Stack flex={1}>
					<CustomTextField
						label={label}
						watch={watch}
						schema={schema}
						disabled={inUse}
						fullWidth
						placeholder={placeholder}
						variant='outlined'
						type='number'
						hideNumberArrows
						{...register(field)}
						error={!!errors?.[field]}
						helperText={errors?.[field]?.message}
						onChange={(e) => {
							const processed = valueUpToThreeDecimalPlaces(e.target.value) ?? 0
							setValue(field, processed)
							calcCallback(processed)
						}}
					/>
				</Stack>
				<Stack alignItems='center' gap={1}>
					<CustomFileUploader
						heading=''
						sx={{
							height: 56,
							width: 56,
							marginTop: 0,
							'& .MuiIconButton-root': {
								height: 56,
								width: 56,
								borderRadius: 1,
							},
						}}
						imageHeight={56}
						imageUrl={getImageUrl?.(imageFieldName) || ''}
						setUploadData={(data) => {
							setValue(imageFieldName as keyof TAddContainer, data?.id || '')
							clearErrors(imageFieldName as keyof TAddContainer)
						}}
						acceptFileTypes={['png', 'jpg', 'jpeg', 'webp']}
						mediaType='image'
					/>
					{errors?.[imageFieldName]?.message && (
						<Typography
							variant='caption'
							color='error'
							textAlign='center'
							maxWidth={56}>
							{errors?.[imageFieldName]?.message}
						</Typography>
					)}
				</Stack>
			</Stack>
		</Stack>
	)

	switch (containerShape) {
		case containerShapesEnum.cylinder:
			return (
				<Stack gap={2}>
					{renderField(
						'Diameter',
						'Enter Your Diameter',
						'diameter',
						'diameterImageIds',
						'Measure diameter and upload the image',
						(diameter) =>
							calculateVolume?.({ diameter, height: watch('height') })
					)}
					{renderField(
						'Height',
						'Enter Your Height',
						'height',
						'heightImageIds',
						'Measure height and upload the image',
						(height) =>
							calculateVolume?.({ height, diameter: watch('diameter') })
					)}
				</Stack>
			)

		case containerShapesEnum.cuboid:
			return (
				<Stack gap={2}>
					{renderField(
						'Upper Side',
						'Enter Upper Side',
						'length',
						'lengthImageIds',
						'Measure Upper Side and upload the image',
						(length) =>
							calculateVolume?.({
								length,
								breadth: watch('breadth'),
								height: watch('height'),
							})
					)}
					{renderField(
						'Lower Side',
						'Enter Lower Side',
						'breadth',
						'breadthImageIds',
						'Measure Lower Side and upload the image',
						(breadth) =>
							calculateVolume?.({
								length: watch('length'),
								breadth,
								height: watch('height'),
							})
					)}
					{renderField(
						'Height',
						'Enter Your Height',
						'height',
						'heightImageIds',
						'Measure height and upload the image',
						(height) =>
							calculateVolume?.({
								length: watch('length'),
								breadth: watch('breadth'),
								height,
							})
					)}
				</Stack>
			)

		case containerShapesEnum.rectangular:
			return (
				<Stack gap={2}>
					{renderField(
						'Length',
						'Enter Your Length',
						'length',
						'lengthImageIds',
						'Measure length and upload the image',
						(length) =>
							calculateVolume?.({
								length,
								upperBase: watch('upperBase'),
								lowerBase: watch('lowerBase'),
								height: watch('height'),
							})
					)}
					{renderField(
						'Long Base',
						'Enter Long Base',
						'lowerBase',
						'lowerBaseImageIds',
						'Measure Long Base and upload the image',
						(upperBase) =>
							calculateVolume?.({
								upperBase,
								lowerBase: watch('lowerBase'),
								height: watch('height'),
								length: watch('length'),
							})
					)}
					{renderField(
						'Short Base',
						'Enter Short Base',
						'upperBase',
						'upperBaseImageIds',
						'Measure Short Base and upload the image',
						(lowerBase) =>
							calculateVolume?.({
								upperBase: watch('upperBase'),
								lowerBase,
								height: watch('height'),
								length: watch('length'),
							})
					)}
					{renderField(
						'Height',
						'Enter Height',
						'height',
						'heightImageIds',
						'Measure height and upload the image',
						(height) =>
							calculateVolume?.({
								height,
								upperBase: watch('upperBase'),
								lowerBase: watch('lowerBase'),
								length: watch('length'),
							})
					)}
				</Stack>
			)

		case containerShapesEnum.conical:
			return (
				<Stack gap={2}>
					{renderField(
						'Upper Surface Diameter',
						'Upper Surface Diameter',
						'upperSide',
						'upperSurfaceDiameterImageIds',
						'Measure Upper Surface Diameter and upload the image',
						(upperSide) =>
							calculateVolume?.({
								upperSide,
								lowerSide: watch('lowerSide'),
								height: watch('height'),
							})
					)}
					{renderField(
						'Lower Surface Diameter',
						'Lower Surface Diameter',
						'lowerSide',
						'lowerSurfaceDiameterImageIds',
						'Measure Lower Surface Diameter and upload the image',
						(lowerSide) =>
							calculateVolume?.({
								upperSide: watch('upperSide'),
								lowerSide,
								height: watch('height'),
							})
					)}
					{renderField(
						'Height',
						'Enter Height',
						'height',
						'heightImageIds',
						'Measure height and upload the image',
						(height) =>
							calculateVolume?.({
								upperSide: watch('upperSide'),
								lowerSide: watch('lowerSide'),
								height,
							})
					)}
				</Stack>
			)

		case containerShapesEnum.other:
			return (
				<CustomTextField
					watch={watch}
					schema={schema}
					id='Volume'
					type='number'
					hideNumberArrows
					label='Volume'
					placeholder='Enter Your Volume in litre'
					variant='outlined'
					fullWidth
					{...register('volume')}
					InputProps={{
						endAdornment: (
							<InputAdornment position='end'>
								<Typography>litre</Typography>
							</InputAdornment>
						),
					}}
					InputLabelProps={{
						shrink: true,
					}}
					error={!!errors.volume?.message}
					helperText={(errors?.volume as FieldError)?.message}
				/>
			)

		default:
			return null
	}
}
