import { Box, BoxProps, Drawer } from '@mui/material'
import { ReactNode } from 'react'

interface IProps {
	open: boolean
	onClose: () => void
	component: ReactNode
	anchor?: 'right' | 'left'
	childComponentsProps?: BoxProps
}

export const ActionInformationDrawer = ({
	open,
	onClose,
	component,
	anchor = 'right',
	childComponentsProps,
}: IProps) => {
	return (
		<Drawer
			anchor={anchor}
			open={open}
			onClose={onClose}
			transitionDuration={400}>
			<Box sx={{ width: 450 }} {...childComponentsProps}>
				{component}
			</Box>
		</Drawer>
	)
}
