import { TModal } from '@/types'
import { yupResolver } from '@hookform/resolvers/yup'
import { CancelOutlined } from '@mui/icons-material'
import { LoadingButton } from '@mui/lab'
import {
	Dialog,
	DialogActions,
	DialogContent,
	DialogTitle,
	IconButton,
	TextField,
	Typography,
} from '@mui/material'
import { FC, useCallback, useState } from 'react'
import { useForm } from 'react-hook-form'
import * as Yup from 'yup'
import { Confirmation } from '../Confirmation'
import { useMutation, useQueryClient } from '@tanstack/react-query'
import { authAxios } from '@/contexts'
import { useParams } from 'react-router-dom'
import { toast } from 'react-toastify'
import { AxiosError } from 'axios'

type TProps = TModal & {
	forceReject?: boolean
	siteId?: string
	artisanProId?: string
	kilnId?: string
	networkId?: string
	handleCloseForRejection: () => void
}

const addRejectReason = Yup.object({
	reason: Yup.string().required('Please enter a reject reason'),
})

type TAddRejectReason = Yup.InferType<typeof addRejectReason>

export const AddRejectionReason: FC<TProps> = ({
	open,
	onClose,
	forceReject = false,
	networkId,
	artisanProId,
	kilnId,
	siteId,
	handleCloseForRejection,
}) => {
	const { id: batchId } = useParams()
	const [showConfirmationDialog, setShowConfirmationDialog] =
		useState<boolean>(false)
	const queryClient = useQueryClient()
	const {
		register,
		handleSubmit,
		watch,
		formState: { errors },
	} = useForm<TAddRejectReason>({
		defaultValues: { reason: '' },
		resolver: yupResolver<TAddRejectReason>(addRejectReason),
	})
	const apiRouteToReject = artisanProId
		? `/artisian-pro/${artisanProId}/site/${siteId}/kiln/${kilnId}/process/${batchId}/force-reject`
		: `/cs-network/${networkId}/kilns/${kilnId}/process/${batchId}/force-reject`

	const rejectBatch = useMutation({
		mutationKey: ['rejectBatch'],
		mutationFn: async () => {
			const payload = {
				status: 'rejected',
				reason: watch('reason'),
			}
			return await authAxios.put(
				forceReject ? apiRouteToReject : `kiln-process/${batchId}/assess`,
				payload
			)
		},
		onError: (error: AxiosError) => {
			toast((error?.response?.data as { messageToUser: string })?.messageToUser)
		},
		onSuccess: (data) => {
			toast(data?.data?.message)
			setShowConfirmationDialog(false)
			handleCloseForRejection()
			queryClient.refetchQueries({ queryKey: ['batchDetails'] })
		},
	})

	const handleRejectBatch = useCallback(() => {
		rejectBatch.mutate()
	}, [rejectBatch])

	return (
		<>
			{showConfirmationDialog ? (
				<Confirmation
					open={showConfirmationDialog}
					handleClose={() => {
						setShowConfirmationDialog(false)
					}}
					confirmationText={
						<Typography>
							Are you sure you want to reject this process?
						</Typography>
					}
					handleYesClick={handleRejectBatch}
					handleNoClick={() => setShowConfirmationDialog(false)}
				/>
			) : null}
			<Dialog
				open={open}
				onClose={onClose}
				fullWidth
				maxWidth='sm'
				sx={{
					'& .MuiPaper-root': {
						p: 3,
					},
				}}>
				<IconButton
					sx={{
						position: 'absolute',
						right: 10,
						top: 10,
					}}
					onClick={onClose}>
					<CancelOutlined />
				</IconButton>
				<DialogTitle textAlign='center' variant='h6'>
					Reject Reason
				</DialogTitle>
				<DialogContent>
					<TextField
						minRows={4}
						sx={{ mt: 2 }}
						multiline
						label='Enter the reject reason'
						fullWidth
						{...register('reason')}
						error={!!errors.reason?.message}
						helperText={errors.reason?.message}
					/>
				</DialogContent>
				<DialogActions sx={{ display: 'flex', justifyContent: 'center' }}>
					<LoadingButton
						onClick={handleSubmit(() => setShowConfirmationDialog(true))}>
						Submit
					</LoadingButton>
				</DialogActions>
			</Dialog>
		</>
	)
}
