import { Cancel } from '@mui/icons-material'
import { Box, Dialog, Typography } from '@mui/material'
import ReactPlayer, { ReactPlayerProps } from 'react-player'

export const CustomVideoPlayer = ({
	open,
	close,
	videoUrl,
	videoPlayerProps,
}: {
	open: boolean
	close: () => void
	videoUrl: string
	videoPlayerProps?: ReactPlayerProps
}) => (
	<Dialog
		open={open}
		fullWidth
		sx={{
			'& .MuiPaper-root': {
				p: 4,
			},
		}}>
		<Box
			sx={{ position: 'absolute', right: 10, top: 10, cursor: 'pointer' }}
			onClick={close}>
			<Cancel />
		</Box>
		<Typography variant='h6'>Video</Typography>
		<ReactPlayer url={videoUrl} controls width='100%' {...videoPlayerProps} />
	</Dialog>
)
