import {
	<PERSON>alog,
	<PERSON>alogT<PERSON>le,
	DialogContent,
	DialogActions,
	Button,
	IconButton,
	Typography,
} from '@mui/material'
import CloseIcon from '@mui/icons-material/Close'
import { useState } from 'react'
import { Document, Page, pdfjs } from 'react-pdf'
import { authAxios } from '@/contexts'
import { CertificateDetails } from '@/interfaces'
import { toast } from 'react-toastify'
import { Download } from '@mui/icons-material'

pdfjs.GlobalWorkerOptions.workerSrc = new URL(
	'pdfjs-dist/build/pdf.worker.min.mjs',
	import.meta.url
).toString()

export default function CertificateDialog({
	certificateData,
}: {
	certificateData: CertificateDetails
}) {
	const [showDialog, setShowDialog] = useState(false)

	const handleDownload = async () => {
		if (!certificateData?.url) return
		try {
			const response = await authAxios.get(certificateData?.url, {
				responseType: 'blob',
			})
			const blobUrl = URL.createObjectURL(response.data)
			const link = document.createElement('a')
			link.href = blobUrl
			link.download = 'certificate.pdf'
			document.body.appendChild(link)
			link.click()
			document.body.removeChild(link)
			URL.revokeObjectURL(blobUrl)
		} catch (error) {
			toast('Error downloading certificate')
		}
	}

	return (
		<>
			<Document
				file={certificateData?.url}
				loading={<Typography variant='caption'>Loading certificate</Typography>}
				onClick={() => setShowDialog(true)}>
				<Page height={40} width={40} pageNumber={1} />
			</Document>
			<Dialog
				open={showDialog}
				onClose={() => setShowDialog(false)}
				maxWidth='lg'
				fullWidth>
				<DialogTitle>
					Certificate
					<IconButton
						aria-label='close'
						onClick={() => setShowDialog(false)}
						sx={{ position: 'absolute', right: 8, top: 8 }}>
						<CloseIcon />
					</IconButton>
				</DialogTitle>

				<DialogContent
					dividers
					sx={{
						display: 'flex',
						justifyContent: 'center',
						flexDirection: 'column',
						alignItems: 'center',
					}}>
					<Document file={certificateData?.url}>
						<Page pageNumber={1} width={800} />
					</Document>
				</DialogContent>

				<DialogActions>
					<Button
						onClick={handleDownload}
						variant='contained'
						endIcon={<Download />}>
						Download
					</Button>
				</DialogActions>
			</Dialog>
		</>
	)
}
