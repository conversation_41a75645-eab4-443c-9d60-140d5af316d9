import {
	Box,
	Checkbox,
	IconButton,
	Stack,
	styled,
	Typography,
} from '@mui/material'
import { useState } from 'react'
import { LoadingButton } from '@mui/lab'
import CheckboxIcon from '@/assets/icons/checkboxIcon.svg'
import { Close } from '@mui/icons-material'
import { NoData } from '../NoData'

interface IOptions {
	id: string
	name?: string
}
interface TProps {
	handleCloseDrawer: () => void
	subheading?: string
	options?: IOptions[]
	heading?: string
	loading?: boolean
	handleAdd?: (ids: string[]) => void
}

export const AssignEntityListSideDrawer = ({
	handleCloseDrawer,
	options,
	heading,
	subheading,
	loading,
	handleAdd,
}: TProps) => {
	const [selectedEntity, setselectedEntity] = useState<string[]>([])

	return (
		<StyleContainer>
			<Stack className='header'>
				<Stack
					direction='row'
					spacing={1}
					alignItems='center'
					width='100%'
					justifyContent='space-between'>
					<Stack>
						<Typography variant='h5'>{heading}</Typography>
						<Typography variant='subtitle1'>{subheading}</Typography>
					</Stack>
					<IconButton onClick={handleCloseDrawer}>
						<Close />
					</IconButton>
				</Stack>
			</Stack>
			<Stack className='container' component='form'>
				<Stack direction='column' gap={2} height='72dvh' overflow='auto'>
					{!options?.length && <NoData size='small' />}
					{options?.map((item) => {
						const isChecked = selectedEntity.includes(item?.id)
						return (
							<Stack key={item?.id} alignItems='center' direction='row' gap={1}>
								<Checkbox
									onClick={() => {
										setselectedEntity((p) => {
											if (p.includes(item?.id)) {
												return p.filter((v) => v !== item?.id)
											} else {
												return [...p, item?.id]
											}
										})
									}}
									checked={isChecked}
									checkedIcon={<Box component='img' src={CheckboxIcon} />}
								/>
								<Typography>{item?.name}</Typography>
							</Stack>
						)
					})}
				</Stack>

				{!!options?.length && (
					<LoadingButton
						className='add_btn'
						loading={loading || false}
						onClick={() => handleAdd?.(selectedEntity)}
						variant='contained'>
						Add
					</LoadingButton>
				)}
			</Stack>
		</StyleContainer>
	)
}

const StyleContainer = styled(Stack)(({ theme }) => ({
	gap: theme.spacing(2),
	'.header': {
		flexDirection: 'row',
		justifyContent: 'space-between',
		alignItems: 'center',
		padding: theme.spacing(4.5, 2),
		borderBottom: `${theme.spacing(0.125)} solid ${
			theme.palette.neutral['100']
		}`,
	},

	'.container': {
		padding: theme.spacing(2, 3),
		gap: theme.spacing(2.5),
		justifyContent: 'space-between',
		paddingBottom: theme.spacing(1),
	},
}))
