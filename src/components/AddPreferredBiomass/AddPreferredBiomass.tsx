import {
	Box,
	Checkbox,
	IconButton,
	Stack,
	styled,
	Typography,
} from '@mui/material'
import { useState } from 'react'
import { LoadingButton } from '@mui/lab'
import CheckboxIcon from '@/assets/icons/checkboxIcon.svg'
import { useParams } from 'react-router-dom'
import { IArtisanProDetails, IIPreferredBiomass, INetwork } from '@/interfaces'
import { Close } from '@mui/icons-material'
import { useMutation, useQuery, useQueryClient } from '@tanstack/react-query'
import { authAxios } from '@/contexts'
import { ICrop } from '@/types'
import { toast } from 'react-toastify'
import { AxiosError } from 'axios'

interface TProps {
	handleCloseDrawer: () => void
	preferredBiomassList?: IIPreferredBiomass[]
	cSinkNetworkDetails?: INetwork
	artisanProDetails?: IArtisanProDetails
	subheading?: string
	isCsink?: boolean
}

export const AddPreferredBiomass = ({
	handleCloseDrawer,
	preferredBiomassList,
	subheading,
	isCsink = true,
}: TProps) => {
	const { cSinkNetworkId, artisanProId } = useParams()
	const queryClient = useQueryClient()
	const data =
		preferredBiomassList
			?.map((item) => {
				if (!item?.cropId) return ''
				return item?.cropId
			})
			.filter((item) => !!item) ?? []
	const [selectedBiomass, setSelectedBiomass] = useState<string[]>(data)

	const fetchBiomassQuery = useQuery({
		queryKey: ['getBiomassTypes'],
		queryFn: () => {
			return authAxios<{ count: number; crops: ICrop[] }>(`/crops?limit=1000`)
		},
		enabled: true,
	})
	const addBiomass = useMutation({
		mutationKey: ['addPrefferedBiomass', selectedBiomass],
		mutationFn: async () => {
			const payload = {
				biomassTypeIds: selectedBiomass,
			}
			const api = isCsink
				? `cs-network/${cSinkNetworkId}/preferred-biomass-type/`
				: `artisian-pro/${artisanProId}/preferred-biomass-type`

			return await authAxios.put(api, payload)
		},
		onError: (error: AxiosError) => {
			toast((error?.response?.data as { messageToUser: string })?.messageToUser)
		},
		onSuccess: ({ data }) => {
			toast(data?.message)
			queryClient.refetchQueries({ queryKey: ['getPreferredBiomassList'] })
			handleCloseDrawer()
		},
	})
	return (
		<StyleContainer>
			<Stack className='header'>
				<Stack
					direction='row'
					spacing={1}
					alignItems='center'
					width='100%'
					justifyContent='space-between'>
					<Stack>
						<Typography variant='h5'>Select Crop</Typography>
						<Typography variant='subtitle1'>{subheading}</Typography>
					</Stack>
					<IconButton onClick={handleCloseDrawer}>
						<Close />
					</IconButton>
				</Stack>
			</Stack>
			<Stack className='container' component='form'>
				<Stack direction='column' gap={2} height='72dvh' overflow='auto'>
					{fetchBiomassQuery?.data?.data?.crops?.map((item) => {
						const isChecked = selectedBiomass.includes(item?.id)
						return (
							<Stack key={item?.id} alignItems='center' direction='row' gap={1}>
								<Checkbox
									onClick={() => {
										setSelectedBiomass((p) => {
											if (p.includes(item?.id)) {
												return p.filter((v) => v !== item?.id)
											} else {
												return [...p, item?.id]
											}
										})
									}}
									checked={isChecked}
									checkedIcon={<Box component='img' src={CheckboxIcon} />}
								/>
								<Typography>{item?.name}</Typography>
							</Stack>
						)
					})}
				</Stack>

				<LoadingButton
					// loading={isLoading}
					// disabled={isLoading}
					className='add_btn'
					onClick={() => addBiomass.mutate()}
					variant='contained'>
					Add
				</LoadingButton>
			</Stack>
		</StyleContainer>
	)
}

const StyleContainer = styled(Stack)(({ theme }) => ({
	gap: theme.spacing(2),
	'.header': {
		flexDirection: 'row',
		justifyContent: 'space-between',
		alignItems: 'center',
		padding: theme.spacing(4.5, 2),
		borderBottom: `${theme.spacing(0.125)} solid ${
			theme.palette.neutral['100']
		}`,
	},

	'.container': {
		padding: theme.spacing(2, 3),
		gap: theme.spacing(2.5),
		justifyContent: 'space-between',
		paddingBottom: theme.spacing(1),
	},
}))
