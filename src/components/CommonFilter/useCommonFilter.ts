import { authAxios, useAuthContext } from '@/contexts'
import { useQuery } from '@tanstack/react-query'
import { useCallback, useMemo } from 'react'
import { useSearchParams } from 'react-router-dom'
import { extentedILabelWithValue } from '../QueryInputs'
import { userRoles } from '@/utils/constant'

interface IAllFilters {
	id?: string
	options?:
		| {
				label: string
				value: string
		  }[]
		| undefined
	queryKey: string
	filtersToReset?: string[]
	hideIfFilterNotPresent?: string[]
	label: string
	initialValue?: string
	isDisable: boolean
	onChangeSelect?: ({
		nsp,
	}: {
		selectedOption: extentedILabelWithValue | null
		nsp: URLSearchParams
	}) => URLSearchParams
}

const extractNetworkSuffix = (code: string): string => {
	// Convert to array and reverse to search from the end
	const chars = [...code].reverse()

	// Find the index of first 'F' or 'P' from the end
	const index = chars.findIndex((char) => char === 'F' || char === 'P')

	if (index === -1) {
		// If no 'F' or 'P' found, return original code
		return code
	}

	// Get the substring from F/P to the end
	return code.slice(code.length - index - 1)
}
export const useCommonFilter = (
	showOnlyBaAndNetwork = false,
	showChipsForKiln?: boolean
) => {
	const [searchParams, setSearchParams] = useSearchParams()
	const { userDetails } = useAuthContext()
	const paramsBAId = searchParams.get('baId') || ''
	const paramsNetworkId = searchParams.get('networkId') || ''
	const paramsIsArtisan = searchParams.get('isArtisan') || 'false'
	const paramsSiteId = searchParams.get('siteId') || ''
	const paramsFarmerId = searchParams.get('farmerId') || ''

	const AllBaQuery = useQuery({
		queryKey: ['allBA'],
		queryFn: () => {
			return authAxios.get<{
				baDetails: { id: string; name: string; shortCode: string }[]
			}>(`/drop-down/biomass-aggregators`)
		},
		select: ({ data }) =>
			data?.baDetails?.map((item) => ({
				label: `${item.name} (${item.shortCode})`,
				value: item.id,
			})),
		enabled:
			!!userDetails &&
			![
				userRoles.cSinkNetwork,
				userRoles.artisanProNetworkManager,
				userRoles.ArtisanPro,
			].includes(userDetails?.accountType as userRoles),
	})

	const AllNetworkQuery = useQuery({
		queryKey: ['allNetwork', paramsBAId, paramsIsArtisan, extractNetworkSuffix],
		queryFn: () => {
			const queryParams = new URLSearchParams({
				biomassAggregatorId: paramsBAId,
			})
			return authAxios.get<{
				networks: {
					id: string
					name: string
					isArtisan: boolean
					shortName: string
				}[]
			}>(`/new/networks?${queryParams.toString()}`)
		},
		select: ({ data }) =>
			data?.networks?.map((item) => ({
				label: `${item.name} (${extractNetworkSuffix(item.shortName)})`,
				value: item.id,
				isArtisan: item.isArtisan,
			})),
		enabled: !!paramsBAId,
	})

	const AllSiteQuery = useQuery({
		queryKey: ['allSites', paramsNetworkId, paramsIsArtisan],
		queryFn: () => {
			return authAxios.get<{
				siteDetails: { id: string; name: string; shortCode: string }[]
			}>(`/drop-down/sites?networkIds=${paramsNetworkId}`)
		},
		select: ({ data }) =>
			data?.siteDetails?.map((item) => ({
				label: `${item.name} (S${item?.shortCode?.split('S')[1]})`,
				value: item.id,
			})),
		enabled: !!paramsNetworkId && paramsIsArtisan === 'true',
	})

	const AllKilnQuery = useQuery({
		queryKey: ['allKilns', paramsFarmerId, paramsSiteId],
		queryFn: () => {
			const queryParams = new URLSearchParams({
				limit: '1000',
				page: '0',
			})
			return authAxios.get<{
				kilns: { id: string; name: string; ShortName: string }[]
			}>(
				`/site/${paramsSiteId || paramsFarmerId}/kiln?${queryParams.toString()}`
			)
		},
		select: ({ data }) =>
			data?.kilns?.map((item) => ({
				label: `${item.name} (K${item?.ShortName?.split('K')[1]})`,
				value: item.id,
			})),
		enabled: !!(paramsSiteId || paramsFarmerId),
	})

	const farmersQuery = useQuery({
		queryKey: ['farmersQuery', paramsNetworkId, paramsIsArtisan],
		queryFn: () => {
			return authAxios.get<{
				siteDetails: {
					id: string
					name: string
					ShortName: string
				}[]
			}>(`/drop-down/sites?networkIds=${paramsNetworkId}`)
		},
		select: ({ data }) =>
			data?.siteDetails?.map((item) => ({
				label: `${item.name}`,
				value: item.id,
			})),
		enabled: !!paramsNetworkId && paramsIsArtisan === 'false',
	})

	const biomassAggregatorFilter = useMemo(
		() => userDetails?.accountType !== 'admin',
		[userDetails?.accountType]
	)

	const networkFilter = useMemo(
		() =>
			userDetails?.accountType === 'network_admin' ||
			userDetails?.accountType === 'artisan_pro_admin',
		[userDetails?.accountType]
	)

	const biomassAggregatorValue = useMemo(
		() => ({
			value: biomassAggregatorFilter
				? userDetails?.biomassAggregatorId ||
				  userDetails?.cSinkNetworkManagerId ||
				  ''
				: '',
			label: biomassAggregatorFilter
				? userDetails?.biomassAggregatorName || ''
				: '',
		}),
		[
			biomassAggregatorFilter,
			userDetails?.biomassAggregatorId,
			userDetails?.biomassAggregatorName,
			userDetails?.cSinkNetworkManagerId,
		]
	)

	const handleChipsClick = useCallback(
		(key: string, value: string) => {
			setSearchParams(
				(urlParams) => {
					const currentValues = urlParams.get(key)

					// If no current values, simply add the new value
					if (!currentValues) {
						urlParams.set(key, value)
						return urlParams
					}

					// Split current values into array
					const valuesArray = currentValues.split(',')

					// Check if value exists
					if (valuesArray.includes(value)) {
						// Remove the value
						const filteredValues = valuesArray
							.filter((v) => v !== value)
							.join(',')
						if (filteredValues) {
							urlParams.set(key, filteredValues)
						} else {
							urlParams.delete(key) // Remove key if no values remain
						}
					} else {
						// Add the value
						urlParams.set(key, `${currentValues},${value}`)
					}

					return urlParams
				},
				{ replace: true }
			)
		},
		[setSearchParams]
	)

	const networkValue = useMemo(
		() => ({
			value: networkFilter
				? userDetails?.networkId || userDetails?.artisianProId || ''
				: '',
			label: networkFilter
				? userDetails?.networkName || userDetails?.artisanProName || ''
				: '',
			isArtisan:
				userDetails?.accountType === 'artisan_pro_admin' ? true : false,
		}),
		[
			networkFilter,
			userDetails?.accountType,
			userDetails?.artisanProName,
			userDetails?.artisianProId,
			userDetails?.networkId,
			userDetails?.networkName,
		]
	)
	const chipsFilter = useMemo(() => {
		return {
			id: 'kilnId',
			options: AllKilnQuery?.data,
			queryKey: 'kilnId',
			filtersToReset: ['subNetwork', 'limit', 'page'],
			hideIfFilterNotPresent: ['networkId'],
			label: 'Kiln',

			isDisable: true,
		}
	}, [AllKilnQuery?.data])
	const KilnFarmerFilter = useMemo(() => {
		return !showChipsForKiln
			? chipsFilter
			: {
					id: 'farmerId',
					options: farmersQuery?.data,
					queryKey: 'farmerId',
					filtersToReset: ['subNetwork', 'limit', 'page'],
					hideIfFilterNotPresent: ['networkId'],
					label: 'Farmer',
					isDisable: true,
			  }
	}, [AllKilnQuery?.data, farmersQuery?.data, showChipsForKiln])

	const isLoadingforSiteandFarmer = useMemo(() => {
		if (searchParams.get('isArtisan') === 'true') return AllSiteQuery.isPending
		if (showChipsForKiln) return farmersQuery.isPending
		return AllKilnQuery.isPending
	}, [
		searchParams,
		AllSiteQuery.isPending,
		showChipsForKiln,
		farmersQuery.isPending,
		AllKilnQuery.isPending,
	])

	const sideIdFilter = useMemo(
		() => [
			...(searchParams.get('networkId') &&
			!showOnlyBaAndNetwork &&
			!isLoadingforSiteandFarmer
				? [
						searchParams.get('isArtisan') === 'true'
							? {
									id: 'siteId',
									options: AllSiteQuery.data,
									queryKey: 'siteId',
									filtersToReset: [
										'subNetwork',
										'farmerId',
										'limit',
										'page',
										'kilnId',
									],
									hideIfFilterNotPresent: ['networkId'],
									label: 'Site',
									initialValue: '',
									isDisable: true,
							  }
							: KilnFarmerFilter,
				  ]
				: []),
		],
		[
			searchParams,
			showOnlyBaAndNetwork,
			isLoadingforSiteandFarmer,
			AllSiteQuery.data,
			KilnFarmerFilter,
		]
	)

	const allFilter: IAllFilters[] = useMemo(
		() => [
			{
				id: 'baId',
				options: AllBaQuery.data ?? [biomassAggregatorValue],
				queryKey: 'baId',
				// hideIfFilterNotPresent: ['/'],
				filtersToReset: [
					'limit',
					'page',
					'networkId',
					'siteId',
					'kilnId',
					'subNetwork',
					'isArtisan',
					'farmerId',
				],
				label: 'BA',
				isDisable: false,
				initialValue: biomassAggregatorValue.value,
			},

			...(!AllNetworkQuery?.isPending
				? [
						{
							id: 'networkId',
							options: AllNetworkQuery.data ?? [networkValue],
							queryKey: 'networkId',
							filtersToReset: [
								'limit',
								'page',
								'siteId',
								'kilnId',
								'subNetwork',
								'isArtisan',
								'farmerId',
							],
							hideIfFilterNotPresent: ['baId'],
							label: 'Network',
							isDisable: !![
								'artisan_pro_admin',
								'admin',
								'c_sink_manager',
								userRoles.artisanProNetworkManager,
								userRoles.BiomassAggregator,
								userRoles.compliance_manager,
								'network_admin',
							].includes(userDetails?.accountType ?? ''),
							initialValue: networkValue.value,
							onChangeSelect: ({
								selectedOption,
								nsp,
							}: {
								selectedOption: extentedILabelWithValue | null
								nsp: URLSearchParams
							}) => {
								if (!showOnlyBaAndNetwork) {
									nsp.set('isArtisan', `${!!selectedOption?.isArtisan}`)
								}
								return nsp
							},
						},
				  ]
				: []),
			...sideIdFilter,
		],

		[
			AllBaQuery.data,
			biomassAggregatorValue,
			userDetails?.accountType,
			AllNetworkQuery?.isPending,
			AllNetworkQuery.data,
			networkValue,
			sideIdFilter,
			showOnlyBaAndNetwork,
		]
	)

	return {
		allFilter,
		chipsFilter,
		handleChipsClick,
		searchParams,
	}
}
