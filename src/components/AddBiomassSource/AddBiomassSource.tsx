import { Close } from '@mui/icons-material'
import {
	Form<PERSON><PERSON>rol,
	IconButton,
	Stack,
	styled,
	Typography,
	useTheme,
} from '@mui/material'
import React, { useCallback, useEffect } from 'react'
import { FormProvider, useForm } from 'react-hook-form'
import { yupResolver } from '@hookform/resolvers/yup'
import { LoadingButton } from '@mui/lab'
import { GoogleMapsWithNonDraggableMarker } from '../GoogleMap'

import { useMutation, useQueryClient } from '@tanstack/react-query'
import { authAxios } from '@/contexts'
import { useParams, useSearchParams } from 'react-router-dom'
import { toast } from 'react-toastify'
import { AxiosError } from 'axios'
import { addBiomassSchema, TAddBiomass } from './schema'
import { IFpu } from '@/interfaces'
import { CustomTextField } from '@/utils/components'

type Props = {
	handleClose: () => void
	subheading?: string
	biomassSourceDetails?: IFpu
	editMode?: boolean
	addText?: string
}

export const AddBiomassSource: React.FC<Props> = ({
	handleClose,
	addText,
	editMode = false,
	biomassSourceDetails,
	subheading,
}) => {
	const theme = useTheme()
	const { artisanProId } = useParams()
	const [searchParams] = useSearchParams()
	const siteId = searchParams.get('siteTab') ?? ''

	const initialText = editMode ? biomassSourceDetails?.name : addText
	const lat =
		String(biomassSourceDetails?.coordinate?.split(',')[0].slice(1)) ?? ''
	const lng =
		String(biomassSourceDetails?.coordinate?.split(',')[1].slice(0, -1)) ?? ''
	const initialValues = {
		name: initialText ?? '',
		address: biomassSourceDetails?.address ?? '',
		latitude: biomassSourceDetails ? Number(lat) : 0,
		longitude: biomassSourceDetails ? Number(lng) : 0,
	}
	const form = useForm<TAddBiomass>({
		defaultValues: initialValues,
		resolver: yupResolver<TAddBiomass>(addBiomassSchema),
		mode: 'all',
	})
	const {
		watch,
		register,
		formState: { errors },
	} = form
	const queryClient = useQueryClient()

	const setMapCenter = useCallback(
		(lat: number, lng: number) => {
			form.setValue('latitude', Number(lat.toFixed(6)))
			form.setValue('longitude', Number(lng.toFixed(6)))
		},
		[form]
	)

	const addBiomassSourceMutation = useMutation({
		mutationKey: ['addKiln'],
		mutationFn: async (values: TAddBiomass) => {
			const payload = {
				name: values?.name,
				address: values?.address,
				latitude: Number(values.latitude),
				longitude: Number(values.longitude),
				siteId,
			}

			const api = `artisian-pro/${artisanProId}/fpu`
			const { data } = await authAxios.post(api, payload)
			return data
		},
		onError: (err: AxiosError) => {
			toast((err?.response?.data as { messageToUser: string })?.messageToUser)
		},
		onSuccess: (data) => {
			toast(data?.message)
			handleClose()
			queryClient.invalidateQueries({ queryKey: ['getBiomassSourceList'] })
		},
	})

	const editBiomassSourceMutation = useMutation({
		mutationKey: ['EditKiln'],
		mutationFn: async (values: TAddBiomass) => {
			const payload = {
				name: values?.name,
				address: values?.address,
				latitude: Number(values.latitude),
				longitude: Number(values.longitude),
				// siteId,
			}

			const api = `artisian-pro/${artisanProId}/site/${siteId}/fpu/${biomassSourceDetails?.id}`
			const { data } = await authAxios.put(api, payload)
			return data
		},
		onError: (err: AxiosError) => {
			toast((err?.response?.data as { messageToUser: string })?.messageToUser)
		},
		onSuccess: (data) => {
			toast(data?.message)
			handleClose()
			queryClient.invalidateQueries({ queryKey: ['getBiomassSourceList'] })
		},
	})
	const handleAddBiomassSource = useCallback(
		(values: TAddBiomass) => {
			!editMode
				? addBiomassSourceMutation.mutate(values)
				: editBiomassSourceMutation.mutate(values)
		},
		[addBiomassSourceMutation, editBiomassSourceMutation, editMode]
	)
	const getCurrentLocation = useCallback(() => {
		navigator.geolocation.getCurrentPosition((position) => {
			setMapCenter(position.coords.latitude, position.coords.longitude)
		})
	}, [setMapCenter])

	useEffect(() => {
		if (!biomassSourceDetails) {
			getCurrentLocation()
		}
	}, [biomassSourceDetails, getCurrentLocation])
	return (
		<FormProvider {...form}>
			<StyleContainer>
				<Stack className='header'>
					<Stack
						direction='row'
						spacing={1}
						alignItems='center'
						width='100%'
						justifyContent='space-between'>
						<Stack>
							<Typography variant='h5'>Add Biomass Source</Typography>
							<Typography variant='subtitle1'>{subheading}</Typography>
						</Stack>
						<IconButton onClick={handleClose}>
							<Close />
						</IconButton>
					</Stack>
				</Stack>
				<Stack className='container'>
					<FormControl fullWidth className='formStyle'>
						<Typography variant='subtitle1'>Biomass Source</Typography>
						<CustomTextField
							schema={addBiomassSchema}
							id='name'
							placeholder='Enter Biomass Source'
							error={!!errors?.name}
							helperText={errors?.name?.message}
							variant='outlined'
							{...register('name')}
						/>
					</FormControl>
					<FormControl fullWidth className='formStyle'>
						<Typography variant='subtitle1'>Address</Typography>

						<CustomTextField
							schema={addBiomassSchema}
							id='address'
							placeholder='Enter Address'
							error={!!errors?.address}
							helperText={errors?.address?.message}
							variant='outlined'
							{...register('address')}
						/>
					</FormControl>
					<Stack flexDirection='row' gap={theme.spacing(1)}>
						<FormControl fullWidth className='formStyle'>
							<Typography variant='subtitle1'>Latitude</Typography>

							<CustomTextField
								schema={addBiomassSchema}
								id='latitude'
								placeholder='Enter Latitude'
								error={!!errors?.latitude}
								helperText={errors?.latitude?.message}
								type='number'
								variant='outlined'
								{...register('latitude', {
									setValueAs: (value) => Number(parseFloat(value).toFixed(6)),
								})}
							/>
						</FormControl>
						<FormControl fullWidth className='formStyle'>
							<Typography variant='subtitle1'>Longitude</Typography>

							<CustomTextField
								schema={addBiomassSchema}
								id='longitude'
								placeholder='Enter Longitude'
								error={!!errors?.longitude}
								helperText={errors?.longitude?.message}
								type='number'
								variant='outlined'
								{...register('longitude', {
									setValueAs: (value) => Number(parseFloat(value).toFixed(6)),
								})}
							/>
						</FormControl>
					</Stack>

					<GoogleMapsWithNonDraggableMarker
						center={{
							lat: Number(watch('latitude')),
							lng: Number(watch('longitude')),
						}}
						setMapCenter={setMapCenter}
						mapContainerStyle={{
							width: '100%',
							height: 400,
							position: 'relative',
						}}
					/>

					<LoadingButton
						className='btn'
						loading={addBiomassSourceMutation.isPending}
						disabled={addBiomassSourceMutation.isPending}
						onClick={form.handleSubmit(handleAddBiomassSource)}
						variant='contained'>
						Add
					</LoadingButton>
				</Stack>
			</StyleContainer>
		</FormProvider>
	)
}

const StyleContainer = styled(Stack)(({ theme }) => ({
	gap: theme.spacing(2),
	'.header': {
		flexDirection: 'row',
		justifyContent: 'space-between',
		alignItems: 'center',
		padding: theme.spacing(4.5, 2),
		borderBottom: `${theme.spacing(0.125)} solid ${
			theme.palette.neutral['100']
		}`,
	},
	'.container': {
		height: '100%',
		padding: theme.spacing(2, 3),
		gap: theme.spacing(2.5),
		'.formStyle': {
			color: theme.palette.neutral[500],
			gap: theme.spacing(1.2),
		},
		'.btn': {
			position: 'sticky',
			bottom: 0,
		},
	},
}))
