import * as Yup from 'yup'

export const addBiomassSchema = Yup.object({
	name: Yup.string().required('This Field is Required'),
	address: Yup.string().required('This Field is Required'),
	latitude: Yup.number()
		.required('This Field is Required')
		.typeError('This Field is Required'),
	longitude: Yup.number()
		.required('This Field is Required')
		.typeError('This Field is Required'),
})

export type TAddBiomass = Yup.InferType<typeof addBiomassSchema>
