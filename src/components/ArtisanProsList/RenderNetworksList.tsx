import { IBaArtisanProNetworks } from '@/interfaces/BiomassAggregator.type'
import { PlaceOutlined } from '@mui/icons-material'
import { Box, Link, Stack, styled, Typography } from '@mui/material'
import { FC } from 'react'

interface IProps {
	isCsink?: boolean
	networks?: IBaArtisanProNetworks[]
	onClick?: (network: IBaArtisanProNetworks) => void
	title?: string
}
export const RenderNetworkList: FC<IProps> = ({ title, networks, onClick }) => {
	return (
		<StyleContainer className='network-container'>
			{title && (
				<Typography variant='body2' color='primary'>
					{title}
				</Typography>
			)}
			{networks?.map((network) => (
				<Box
					component={Link}
					onClick={() => onClick?.(network)}
					className='network-li'
					key={`networks-${network?.id}`}>
					<Typography className='link-text'>
						{`${network?.name}  (${
							network?.location || network?.address || ''
						})`}{' '}
						<PlaceOutlined color='error' />
					</Typography>
				</Box>
			))}
		</StyleContainer>
	)
}
const StyleContainer = styled(Stack)(({ theme }) => ({
	minWidth: theme.spacing(40),
	flexDirection: 'column',
	gap: theme.spacing(2),
	'.network-li': {
		cursor: 'pointer',
		textDecoration: 'none',
		color: theme.palette.neutral[900],
		'.link-text': {
			display: 'flex',
			alignItems: 'center',
		},
	},
}))
