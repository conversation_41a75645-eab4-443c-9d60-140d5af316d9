import { authAxios } from '@/contexts'
import { Close } from '@mui/icons-material'
import { IconButton, Stack, styled, Typography } from '@mui/material'
import { useQuery } from '@tanstack/react-query'
import { RenderNetworkList } from './RenderNetworksList'
import { useNavigate } from 'react-router-dom'

interface IProps {
	handleClose: () => void
	id: string
	name?: string
}

export const ArtisanProList = ({ handleClose, id, name }: IProps) => {
	const artisanProsQuery = useQuery({
		queryKey: ['artisanProList'],
		queryFn: async () => {
			const { data } = await authAxios.get(
				`/artisan-pro-network/${id}/artisian-pro/network?limit=5000`
			)
			return data
		},
	})
	const navigate = useNavigate()
	return (
		<StyleContainer>
			<Stack className='header'>
				<Typography variant='h5'>{name}</Typography>
				<IconButton onClick={handleClose}>
					<Close />
				</IconButton>
			</Stack>
			<Stack className='container' rowGap={2} mt={1}>
				<RenderNetworkList
					networks={artisanProsQuery?.data?.artisanPros || []}
					onClick={(network) =>
						navigate(`/dashboard/artisan-pro/${network?.id}/details`)
					}
				/>
			</Stack>
		</StyleContainer>
	)
}

const StyleContainer = styled(Stack)(({ theme }) => ({
	gap: theme.spacing(1),
	'.header': {
		flexDirection: 'row',
		justifyContent: 'space-between',
		alignItems: 'center',
		padding: theme.spacing(3),
		borderBottom: `${theme.spacing(0.125)} solid ${
			theme.palette.neutral['100']
		}`,
	},
	'.container': {
		height: '100%',
		padding: theme.spacing(2, 3),
		gap: theme.spacing(2.5),
	},
}))
