import {
	Box,
	Typography,
	Container,
	Paper,
	useTheme,
	Avatar,
	styled,
} from '@mui/material'
import ConstructionIcon from '@mui/icons-material/Construction'
import WarningAmberIcon from '@mui/icons-material/WarningAmber'

const MaintenancePage = () => {
	const theme = useTheme()

	return (
		<StyledContainer>
			<Container maxWidth='sm'>
				<Paper elevation={3} className='paper-container' sx={{}}>
					<Avatar
						sx={{
							width: 80,
							height: 80,
							bgcolor: '#9D392B',
							mb: 2,
						}}>
						<WarningAmberIcon sx={{ fontSize: 50 }} />
					</Avatar>

					<Typography
						variant='h4'
						component='h1'
						gutterBottom
						sx={{ fontWeight: 'bold' }}>
						Site Under Maintenance
					</Typography>

					<Box
						sx={{
							display: 'flex',
							alignItems: 'center',
							my: 3,
						}}>
						<ConstructionIcon
							sx={{
								fontSize: 30,
								color: theme.palette.warning.dark,
								mr: 1,
							}}
						/>
						<Typography variant='h6'>We're Working On It!</Typography>
					</Box>

					<Typography variant='body1' paragraph>
						We apologize for any inconvenience and appreciate your patience.
					</Typography>

					<Typography variant='body2' color='text.secondary'>
						Please check back soon.
					</Typography>
				</Paper>
			</Container>
		</StyledContainer>
	)
}

const StyledContainer = styled(Box)(({ theme }) => ({
	display: 'flex',
	flexDirection: 'column',
	minHeight: '100vh',
	alignItems: 'center',
	justifyContent: 'center',
	bgcolor: theme.palette.grey[100],
	'.paper-container': {
		padding: theme.spacing(0.5),
		display: 'flex',
		flexDirection: 'column',
		alignItems: 'center',
		textAlign: 'center',
	},
}))

export default MaintenancePage
