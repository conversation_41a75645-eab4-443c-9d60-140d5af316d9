import { handleImageUpload, isProxyPath, proxyImage } from '@/utils/helper'
import {
	CancelOutlined,
	CloudUploadOutlined,
	DescriptionOutlined,
} from '@mui/icons-material'
import {
	Box,
	CircularProgress,
	IconButton,
	Stack,
	SxProps,
	Theme,
	Tooltip,
	Typography,
	styled,
} from '@mui/material'
import { ReactNode, useCallback, useEffect, useState } from 'react'

import { FileUploader } from 'react-drag-drop-files'
import { toast } from 'react-toastify'
import { ImageCarouselDialog } from '../ImageCarousel'
import { IImage } from '@/interfaces'

interface IProps {
	heading?: string
	sx?: SxProps<Theme>
	setUploadData: (data: any) => void
	imageHeight?: number | `${number}${'px' | 'cm' | 'mm' | 'em'}`
	required?: boolean
	data?: any
	customIcon?: ReactNode | JSX.Element | JSX.Element[]
	fileType?: string[]
	flexDirection?: 'row' | 'row-reverse' | 'column' | 'column-reverse'
	training?: boolean
	isDisabled?: boolean
	nameSize?: number
}
const fileTypes = [
	'png',
	'jpg',
	'jpeg',
	'webp',
	'svg',
	'pdf',
	'doc',
	'docx',
	'txt',
	'ppt',
	'pptx',
	'xls',
	'xlsx',
	'csv',
]
const ImageTypes = ['png', 'jpg', 'jpeg', 'webp']

const DocsTypes = ['pdf', 'doc', 'docx', 'txt', 'ppt', 'pptx', 'xls', 'xlsx']
interface IMedia {
	id: string
	url: string
	fileName?: string
	path?: string
}

const MediaRender = ({
	type,
	url,
	removeMedia,
	fileName,
	imageSize,
	isDisabled,
	nameSize = 20,
	handleImageClick,
}: {
	type: 'img' | 'doc'
	url: string
	removeMedia: () => void
	fileName?: string
	imageSize?: number | `${number}${'px' | 'cm' | 'mm' | 'em'}`
	isDisabled?: boolean
	nameSize?: number
	handleImageClick?: () => void
}) => (
	<Stack position='relative' width='fit-content'>
		{!isDisabled && (
			<IconButton
				sx={{
					position: 'absolute',
					right: -10,
					top: -10,
					color: 'common.black',
					borderRadius: '50%',
					padding: 0.5,
				}}
				onClick={removeMedia}>
				<CancelOutlined fontSize='medium' />
			</IconButton>
		)}

		{type === 'img' ? (
			<Box
				component='img'
				src={fileName ? proxyImage(fileName) : url} // use proxy image only if filename is available
				width={imageSize ?? 100}
				onClick={() => {
					handleImageClick?.()
				}}
				height={imageSize ?? 100}
				borderRadius={1}
				sx={{ objectFit: 'contain' }}
			/>
		) : (
			<Box
				width={imageSize ?? 100}
				height={imageSize ?? 100}
				borderRadius={1}
				display='flex'
				flexDirection='column'
				justifyContent='center'
				rowGap={1}
				alignItems='center'>
				<DescriptionOutlined sx={{ fontSize: 50, color: 'grey.500' }} />
				<Tooltip title={fileName} arrow>
					<Typography
						sx={{ wordBreak: 'break-all' }}
						textAlign='center'
						px={1}
						fontSize='12px'>
						{fileName ? `${fileName?.substring(0, nameSize)}...` : ''}
					</Typography>
				</Tooltip>
			</Box>
		)}
	</Stack>
)

const MediaContainer = ({
	children,
	title,
	isLoading,
	hideTitle = false,
}: {
	children: ReactNode | JSX.Element | JSX.Element[]
	title?: string
	isLoading?: boolean
	hideTitle?: boolean
}) => (
	<Stack>
		{hideTitle ? null : <Typography fontWeight='bold'>{title}</Typography>}
		<Stack
			flexDirection='row'
			flexWrap='wrap'
			alignItems='center'
			gap={2}
			mt={1}>
			{children}
			{isLoading ? <CircularProgress /> : null}
		</Stack>
	</Stack>
)

export const MultipleFileUploader = ({
	heading,
	sx,
	setUploadData,
	imageHeight,
	required,
	data,
	fileType = fileTypes,
	flexDirection = 'column',
	customIcon,
	training = true,
	isDisabled = false,
	nameSize = 20,
}: IProps) => {
	const [imageUrls, setImageUrls] = useState<IMedia[]>([])
	const [docUrls, setDocUrls] = useState<IMedia[]>([])
	const [uploading, setUploading] = useState<boolean>(false)

	const removeImage = (index: number) => {
		const updatedUrls = [...imageUrls]
		updatedUrls.splice(index, 1)
		setImageUrls(updatedUrls)
		setUploadData([...docUrls, ...updatedUrls])
	}

	const removeDoc = (index: number) => {
		const updatedUrls = [...docUrls]
		updatedUrls.splice(index, 1)
		setDocUrls(updatedUrls)
		setUploadData([...updatedUrls, ...imageUrls])
	}
	useEffect(() => {
		if (data == null) {
			setImageUrls([])
			setDocUrls([])
		}
	}, [data])

	const seperateImageDocument = useCallback(
		(uploadedMedia: IMedia[]) => {
			setUploadData([...uploadedMedia, ...imageUrls, ...docUrls])
			const imageMedia = uploadedMedia.filter(
				(media) =>
					media?.fileName &&
					ImageTypes.includes(media?.fileName?.split('.').pop() || '')
			)

			const docMedia = uploadedMedia.filter(
				(media) =>
					media?.fileName &&
					DocsTypes.includes(media?.fileName.split('.').pop() || '')
			)

			const invalidFiles = uploadedMedia.filter(
				(media) =>
					media?.fileName &&
					!ImageTypes.includes(media?.fileName?.split('.').pop() || '') &&
					!DocsTypes.includes(media?.fileName?.split('.').pop() || '')
			)

			if (invalidFiles.length > 0) {
				const invalidExtensions = invalidFiles.map(
					(media) => media?.fileName?.split('.').pop() || ''
				)
				const errorMessage = `.${invalidExtensions} is invalid file type`
				toast.error(errorMessage)
			}
			setImageUrls((prevImageUrls: IMedia[]) => [
				...prevImageUrls,
				...imageMedia.map((media) => ({
					id: media?.id,
					url: media?.url,
					fileName: media?.fileName,
				})),
			])

			setDocUrls((prevDocUrls: IMedia[]) => [
				...prevDocUrls,
				...docMedia.map((media) => ({
					id: media?.id,
					url: media?.url,
					fileName: media?.fileName,
				})),
			])
		},
		[docUrls, imageUrls, setUploadData]
	)

	const handleChange = useCallback(
		async (files: FileList | null) => {
			if (!files) return

			setUploading(true)

			try {
				const uploadPromises = Array?.from(files)?.map(async (file) => {
					const data = await handleImageUpload(file)

					return {
						id: data?.id,
						url: data?.url,
						fileName: data?.fileName,
					}
				})

				const uploadedMedia: IMedia[] = await Promise.all(uploadPromises)
				seperateImageDocument(uploadedMedia)
			} catch (err) {
				toast('File upload failed')
			}

			setUploading(false)
		},
		[seperateImageDocument]
	)

	const [imageList, setImageList] = useState<IImage[]>([])
	const [openedImageId, setOpenedImageId] = useState<number>(0)

	useEffect(() => {
		if (data) {
			setImageUrls([])
			setDocUrls([])
			seperateImageDocument(data)
		}
	}, [])

	return (
		<>
			<Stack width='100%' gap={0.5} direction={flexDirection}>
				{/* {!uploading && imageUrls.length === 0 && docUrls.length === 0 ? ( */}
				<CustomFileUploaderStack maxWidth={customIcon ? 'fit-content' : '100%'}>
					{!isDisabled && (
						<FileUploader
							multiple
							handleChange={(files: any) => handleChange(files)}
							name='file'
							types={fileType}
							required={required}>
							<IconButton
								sx={{
									border: '1px dashed #BBB',
									width: '100%',
									height: 100,

									borderRadius: 1,
									...sx,
								}}>
								{customIcon ?? (
									<Stack
										direction='row'
										gap={2}
										alignItems='center'
										flexWrap='wrap'
										justifyContent='center'>
										<Typography variant='body1' flexWrap='wrap'>
											{heading ?? 'Upload or Drag the files'}
										</Typography>
										<CloudUploadOutlined />
									</Stack>
								)}
							</IconButton>
						</FileUploader>
					)}
				</CustomFileUploaderStack>

				{uploading ? (
					<Box component={CircularProgress} alignSelf='center' mb={2} mt={2} />
				) : null}

				<Stack mt={1} gap={2}>
					{imageUrls.length > 0 && (
						<MediaContainer title={training ? 'Training Images' : 'Images'}>
							<Stack direction='row' gap={2} width='100%' flexWrap='wrap'>
								{imageUrls.map((image, index) => (
									<MediaRender
										url={image.url}
										key={image.id}
										handleImageClick={() => {
											{
												setImageList([
													...imageUrls.map((image) => ({
														id: image?.id,
														url: image?.url,
														path: '',
														fileName: isProxyPath(image?.fileName || '')
															? image?.fileName
															: image?.url,
													})),
												])
												setOpenedImageId(index)
											}
										}}
										type='img'
										imageSize={imageHeight}
										isDisabled={isDisabled}
										// fileName={image?.fileName} // filename is used for proxy images
										nameSize={nameSize}
										removeMedia={() => removeImage(index)}
									/>
								))}
							</Stack>
						</MediaContainer>
					)}

					{docUrls.length > 0 && (
						<MediaContainer
							title={training ? 'Training Documents' : 'Documents'}>
							<Stack
								direction='row'
								mt={1}
								columnGap={2}
								rowGap={4}
								width='100%'
								flexWrap='wrap'>
								{docUrls.map((doc: any) => (
									<MediaRender
										url={doc?.url}
										key={doc?.id}
										type='doc'
										imageSize={imageHeight}
										isDisabled={isDisabled}
										fileName={doc?.fileName}
										nameSize={nameSize}
										removeMedia={() => removeDoc?.(doc)}
									/>
								))}
							</Stack>
						</MediaContainer>
					)}
				</Stack>
			</Stack>
			{imageList?.length ? (
				<ImageCarouselDialog
					open={!!imageList?.length}
					close={() => {
						setImageList([])
					}}
					imageIndex={openedImageId}
					ImagesList={imageList ?? []}
					showDownload={false}
				/>
			) : null}
		</>
	)
}
const CustomFileUploaderStack = styled(Stack)({
	'.juBESy:focus-within': {
		outline: 'none',
	},
})
