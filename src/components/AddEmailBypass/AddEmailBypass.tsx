import { <PERSON><PERSON>, Icon<PERSON>utton, Box, Typography, styled, <PERSON><PERSON><PERSON>, But<PERSON> } from "@mui/material"
import CloseIcon from '@/assets/icons/arrow-bar-right.svg'
import { useMutation, useQueryClient } from "@tanstack/react-query"
import { authAxios } from "@/contexts"
import { toast } from "react-toastify"
import { useForm } from "react-hook-form"
import { yupResolver } from "@hookform/resolvers/yup"
import { emailBypassSchema } from "./schema"
import { theme } from "@/lib/theme/theme"


interface IProps {
	close: () => void
}
export const AddEmailBypass=({ close }: IProps)=>{
    const queryClient=useQueryClient()

    
    const {
		register,
		handleSubmit,
		formState: { errors },
	} = useForm({
		defaultValues: {
            email:''
        },
		mode: 'all',
		resolver: yupResolver(
			emailBypassSchema
		),
	})
    const handleAddEmail=handleSubmit((formvalues)=>{
        const payload={
            email:formvalues.email
        }
        BypassEmailMutate.mutate(payload)
    })
const BypassEmailMutate=useMutation({
    mutationKey:['addBypassNumber'],
    mutationFn: (payload:{email:string}) =>
        authAxios.post(`/by-pass/email`,payload),
    onSuccess: () => {
        
        toast('Email-Bypass Added')
        queryClient.refetchQueries({
            queryKey: ['getEmailBypassList'],
            // exact: true,
            // refetchType:'active'
        })
        close()
    },
    onError: (error: Error) => {
        toast(error?.message)
    },
})


 return(
    <StyleContainer>
			<Stack className='header'>
				<Stack direction='row' spacing={theme.spacing(1)} alignItems='center'>
					<IconButton onClick={close}>
						<Box
							component='img'
							src={CloseIcon}
							alt='closeIcon'
							height={20}
							width={20}
						/>
					</IconButton>
					<Typography variant='h4'>Add Bypass Email</Typography>
				</Stack>
				
			</Stack>
			<Stack className='container' component='form' spacing={theme.spacing(4)}>
            <TextField 
            id='email'
            {...register('email')}
            label='Email'
            error={!!errors?.email}
            helperText={errors?.email?.message}
            />
            <Button onClick={handleAddEmail}  variant="contained">
                Save
            </Button>
            </Stack>
            </StyleContainer>
 )   
}
const StyleContainer = styled(Stack)(({ theme }) => ({
	gap: theme.spacing(2),
	'.header': {
		flexDirection: 'row',
		justifyContent: 'space-between',
		alignItems: 'center',   
		padding: theme.spacing(4.5, 2),
		borderBottom: `${theme.spacing(0.125)} solid ${
			theme.palette.neutral['100']
		}`,
	},
	'.container': {
		padding: theme.spacing(2, 4),
		maxHeight: 'calc( 100vh - 200px)',
		overflowY: 'scroll',
	},
}))