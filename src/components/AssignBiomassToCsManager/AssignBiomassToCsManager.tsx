import { authAxios } from '@/contexts'
import { CropforCsinkManager } from '@/interfaces'
import { theme } from '@/lib/theme/theme'
import { ICrop, TModal } from '@/types'
import { Cancel } from '@mui/icons-material'
import {
	Dialog,
	IconButton,
	DialogTitle,
	DialogContent,
	Stack,
	DialogActions,
	Button,
	MenuItem,
	Box,
	Chip,
	OutlinedInput,
	Select,
	InputLabel,
	FormControl,
	CircularProgress,
} from '@mui/material'
import { SelectChangeEvent } from '@mui/material/Select'
import { useMutation, useQuery, useQueryClient } from '@tanstack/react-query'
import { AxiosError } from 'axios'
import { useState } from 'react'
import { toast } from 'react-toastify'

interface IProps extends TModal {
	id: string
	assignedBiomass?: CropforCsinkManager[]
}
interface DisplayCropsProps {
	selected: string[]
	isLoading: boolean
	crops: { id: string; name: string }[] | undefined
}
export const AssignBiomassToCsManager = ({
	open,
	onClose,
	id,
	assignedBiomass,
}: IProps) => {
	const assigned = assignedBiomass
		?.map((crop) => crop?.cropId ?? '')
		.filter(Boolean)
	const [selectedBioimass, setSelectedBioimass] = useState<string[]>(
		assigned ?? []
	)

	const queryClient = useQueryClient()
	const fetchBiomass = useQuery({
		queryKey: ['getBiomassTypes'],
		queryFn: () => {
			return authAxios<{ count: number; crops: ICrop[] }>(`/crops?limit=1000`)
		},
		enabled: true,
	})
	const saveBiomassMutation = useMutation({
		mutationKey: ['AssignBiomass'],
		mutationFn: async () => {
			const payload = {
				cropIds: selectedBioimass,
			}
			return authAxios.put(`/csink-manager/${id}/assign-crops`, payload)
		},
		onSuccess: (data) => {
			toast(data?.data?.message)
			queryClient.refetchQueries({ queryKey: ['allCsinkManager'] })
			onClose()
			setSelectedBioimass([])
		},
		onError: (err: AxiosError) => {
			toast((err?.response?.data as { messageToUser: string })?.messageToUser)
		},
	})

	const handleChange = (event: SelectChangeEvent<typeof selectedBioimass>) => {
		const {
			target: { value },
		} = event
		setSelectedBioimass(typeof value === 'string' ? value.split(',') : value)
	}

	return (
		<Dialog
			open={open}
			onClose={onClose}
			fullWidth
			sx={{
				'.MuiPaper-root': {
					padding: 2,
				},
			}}
			maxWidth='sm'>
			<IconButton sx={{ position: 'absolute', right: 10 }} onClick={onClose}>
				<Cancel />
			</IconButton>
			<DialogTitle textAlign='center'>Assign Biomass</DialogTitle>
			<DialogContent>
				<Stack marginTop={3} rowGap={3}>
					<FormControl fullWidth>
						<InputLabel id='selectBiomass-label'>Select the Biomass</InputLabel>
						<Select
							labelId='selectBiomass-label'
							id='selectBiomass'
							multiple
							value={selectedBioimass}
							onChange={handleChange}
							input={<OutlinedInput label='Select the Biomass' />}
							renderValue={(selected) => (
								<DisplayCrops
									selected={selected}
									isLoading={fetchBiomass?.isLoading}
									crops={fetchBiomass?.data?.data?.crops}
								/>
							)}
							MenuProps={{
								anchorOrigin: {
									vertical: 'bottom',
									horizontal: 'center',
								},
								PaperProps: {
									style: {
										maxHeight: theme.spacing(50),
									},
								},
							}}>
							{fetchBiomass?.data?.data?.crops?.map((crop) => (
								<MenuItem key={crop?.id} value={crop?.id}>
									{crop?.name}
								</MenuItem>
							))}
						</Select>
					</FormControl>
				</Stack>
			</DialogContent>
			<DialogActions sx={{ display: 'flex', justifyContent: 'center' }}>
				<Button
					onClick={() => saveBiomassMutation.mutate()}
					disabled={saveBiomassMutation.isPending}
					variant='contained'>
					Save
				</Button>
			</DialogActions>
		</Dialog>
	)
}

const DisplayCrops: React.FC<DisplayCropsProps> = ({
	selected,
	isLoading,
	crops,
}) => {
	if (isLoading) {
		return <CircularProgress />
	}

	return (
		<Box sx={{ display: 'flex', flexWrap: 'wrap', gap: 0.5 }}>
			{selected.map((value) => {
				const crop = crops?.find((c) => c.id === value)
				return <Chip key={value} label={crop ? crop.name : value} />
			})}
		</Box>
	)
}
