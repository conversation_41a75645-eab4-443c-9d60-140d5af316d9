import { Close } from '@mui/icons-material'
import {
	Box,
	Button,
	IconButton,
	Stack,
	styled,
	Typography,
} from '@mui/material'
import { FC, useCallback, useState } from 'react'

import { authAxios } from '@/contexts'
import { toast } from 'react-toastify'
import { useMutation, useQueryClient } from '@tanstack/react-query'
import { Confirmation } from '../Confirmation'
import { CropforCsinkManager } from '@/interfaces'

interface IProps {
	handleClose: () => void
	BiomassList: CropforCsinkManager[] | null
	csinkManagerId: string
}

export const AssignedBiomassList: FC<IProps> = ({
	handleClose,
	BiomassList,
	csinkManagerId,
}) => {
	const [showConfirmationDialog, setShowConfirmationDialog] =
		useState<CropforCsinkManager | null>(null)
	const QueryClient = useQueryClient()

	const unAssignBiomassMutation = useMutation({
		mutationKey: ['unAssignBiomassMutation'],
		mutationFn: async (payload: { cropId: string }) =>
			await authAxios.put(`/crops/${payload?.cropId}/unassign-csink-manager`, {
				csinkManagerId,
			}),
		onError: (err: any) => {
			toast(err?.response?.data?.userToMessage)
		},
		onSuccess: ({ data }) => {
			toast(data?.message)
			QueryClient.refetchQueries({ queryKey: ['allCsinkManager'] })
			handleClose()
			setShowConfirmationDialog(null)
		},
	})

	const handleUnassigned = useCallback(
		async (values: { cropId: string }) => {
			unAssignBiomassMutation.mutate(values)
		},
		[unAssignBiomassMutation]
	)

	const RenderContainer: React.FC<{ biomass: CropforCsinkManager }> = ({
		biomass,
	}) => {
		return (
			<Box className='biomass_item' key={biomass?.cropId}>
				<Typography variant='subtitle1'>{biomass?.cropName}</Typography>
				<Button
					variant='text'
					className='unassign_btn'
					onClick={() => setShowConfirmationDialog(biomass)}>
					Unassign Biomass
				</Button>
			</Box>
		)
	}
	return (
		<StyleContainer>
			{showConfirmationDialog ? (
				<Confirmation
					open={!!showConfirmationDialog}
					handleClose={() => setShowConfirmationDialog(null)}
					handleNoClick={() => setShowConfirmationDialog(null)}
					confirmationText={
						<Typography>
							Are you sure want to continue unassign Biomass{' '}
							{showConfirmationDialog?.cropName}?
						</Typography>
					}
					handleYesClick={() =>
						handleUnassigned({ cropId: showConfirmationDialog.cropId || '' })
					}
				/>
			) : null}
			<Stack className='header'>
				<Stack
					direction='row'
					alignItems='center'
					width='100%'
					justifyContent='space-between'>
					<Typography variant='h4'>Assigned Biomass</Typography>
					<IconButton onClick={handleClose}>
						<Close />
					</IconButton>
				</Stack>
			</Stack>
			<Stack className='container' rowGap={2} mt={1}>
				{BiomassList?.length !== 0 && (
					<Stack className='view_container'>
						{BiomassList?.map((item) => (
							<RenderContainer key={item.cropId} biomass={item} />
						))}
					</Stack>
				)}
			</Stack>
		</StyleContainer>
	)
}

const StyleContainer = styled(Stack)(({ theme }) => ({
	height: '100vh',
	'.header': {
		flexDirection: 'row',
		justifyContent: 'space-between',
		alignItems: 'center',
		padding: theme.spacing(3, 2),
		borderBottom: `${theme.spacing(0.125)} solid ${
			theme.palette.neutral['100']
		}`,
	},
	'.container': {
		padding: theme.spacing(1, 2),
		// overflowY: 'scroll',
		flex: 1,
		flexDirection: 'column',
		justifyContent: 'space-between',
		gap: theme.spacing(4),

		'.view_container': {
			gap: theme.spacing(3),
			'.biomass_item': {
				display: 'grid',
				gridTemplateColumns: '1fr 1fr',
				gap: theme.spacing(1, 4),
				'.unassign_btn': {
					...theme.typography.subtitle1,
					':nth-of-type(even)': {
						justifyContent: 'end',
					},
				},
			},
		},
	},
}))
