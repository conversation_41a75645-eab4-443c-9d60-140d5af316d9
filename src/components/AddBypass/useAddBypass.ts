import { authAxios } from "@/contexts"
import {  useQuery } from "@tanstack/react-query"

export const useAddBypass=()=>{
    const fetchBaListQuery=useQuery({
        queryKey:['BaList'],
        queryFn:async()=>{
            const{data}=await authAxios.get(`/ba-csink-managers`)
            return data
        },
        enabled:true
    })
   
    return{
        bioMassAggregatorList:fetchBaListQuery.data,

        }
    
}