import {
	Box,
	Stack,
	styled,
	Tooltip,
	Typography,
	TypographyProps,
	useTheme,
} from '@mui/material'
import React, { ReactNode } from 'react'

interface IProps {
	label: string
	value: string | ReactNode
	headingStyle?: TypographyProps
	clickEvents?: (event: React.MouseEvent<HTMLElement>) => void
	subTitle?: TypographyProps
	lighterHeading?: boolean
	tooltipComponent?: ReactNode
	showTooltip?: boolean
}

type ValueComponentWrapperProps = React.PropsWithChildren<
	Pick<IProps, 'lighterHeading' | 'subTitle'>
>

const ValueComponentWrapper = React.forwardRef<
	HTMLDivElement,
	ValueComponentWrapperProps
>(({ children, lighterHeading, subTitle, ...props }, forwardRef) => {
	if (typeof children === 'string') {
		return (
			<Typography
				ref={forwardRef}
				variant={lighterHeading ? 'body1' : 'subtitle1'}
				{...subTitle}
				{...props}>
				{children}
			</Typography>
		)
	}
	return (
		<Box ref={forwardRef} {...props}>
			{children}
		</Box>
	)
})

export const TagComponentWithToolTip: React.FC<IProps> = ({
	label,
	value,
	headingStyle,
	subTitle,
	lighterHeading,
	tooltipComponent,
	showTooltip = false,
	clickEvents,
}) => {
	const theme = useTheme()

	return (
		<StyledTag className='customTag' alignItems='center' onClick={clickEvents}>
			<Typography
				variant={lighterHeading ? 'caption' : 'h5'}
				color={lighterHeading ? theme.palette.neutral[300] : ''}
				className={`${lighterHeading ? 'heading_bold' : ''} ${
					clickEvents ? 'pointer' : ''
				}`}
				onClick={clickEvents}
				{...headingStyle}>
				{label}
			</Typography>

			{showTooltip ? (
				<Tooltip
					placement='bottom-start'
					title={tooltipComponent}
					arrow
					componentsProps={{
						tooltip: {
							sx: {
								color: 'common.black',
								bgcolor: theme.palette.neutral[10],
							},
						},
						arrow: {
							sx: {
								color: theme.palette.neutral[10],
							},
						},
					}}>
					<ValueComponentWrapper
						subTitle={subTitle}
						lighterHeading={lighterHeading}>
						{value}
					</ValueComponentWrapper>
				</Tooltip>
			) : (
				<ValueComponentWrapper
					subTitle={subTitle}
					lighterHeading={lighterHeading}>
					{value}
				</ValueComponentWrapper>
			)}
		</StyledTag>
	)
}

const StyledTag = styled(Stack)(({ theme }) => ({
	gap: theme.spacing(0.5),
	flexDirection: 'column',
	padding: theme.spacing(1),
	'.heading_bold': {
		fontWeight: theme.typography.h5.fontWeight,
	},
}))
