import {
	Stack,
	styled,
	Typography,
	TypographyProps,
	useTheme,
} from '@mui/material'
import { ReactNode } from 'react'

interface IProps {
	label: string
	value: string | ReactNode
	headingStyle?: TypographyProps
	subTitle?: TypographyProps
	lighterHeading?: boolean
}
export function CustomTagComponent(props: IProps) {
	const theme = useTheme()
	const { label, value, headingStyle, subTitle, lighterHeading } = props

	return (
		<StyledTag className='customTag'>
			<Typography
				variant={lighterHeading ? 'caption' : 'h5'}
				className={lighterHeading ? 'heading_bold' : ''}
				color={lighterHeading ? theme.palette.neutral[300] : ''}
				{...headingStyle}>
				{label}
			</Typography>
			{typeof value === 'string' ? (
				<Typography variant={'subtitle1'} {...subTitle}>
					{value}
				</Typography>
			) : (
				value
			)}
		</StyledTag>
	)
}

const StyledTag = styled(Stack)(({ theme }) => ({
	gap: theme.spacing(0.5),
	flexDirection: 'column',
	padding: theme.spacing(1),
	'.heading_bold': {
		fontWeight: theme.typography.h5.fontWeight,
	},
}))
