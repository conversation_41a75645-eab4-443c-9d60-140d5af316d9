import { Stack, styled, Typography } from '@mui/material'
import React from 'react'

export const TagComponent: React.FC<{
	label: string
	value: string | number
	className?: string
	labelClassName?: string
	valueClassName?: string
}> = ({ label, value, className, valueClassName, labelClassName }) => {
	return (
		<StyledStack className={`tag_component ${className}`}>
			<Typography
				className={`font_size_14 font_weight_600 ${labelClassName ?? ''}`}>
				{label}:
			</Typography>
			<Typography
				className={`font_size_14 first_letter_capitalize ${
					valueClassName ?? ''
				}`}>
				{value}
			</Typography>
		</StyledStack>
	)
}

const StyledStack = styled(Stack)(({ theme }) => ({
	flexDirection: 'row',
	columnGap: 5,
	alignItems: 'center',
	'.font_size_14': {
		fontSize: theme.typography.subtitle2.fontSize,
	},
	'.font_weight_600': {
		fontWeight: theme.typography.caption.fontWeight,
	},
}))
