import {
	Box,
	Checkbox,
	IconButton,
	Stack,
	styled,
	Typography,
} from '@mui/material'
import { useState } from 'react'
import { LoadingButton } from '@mui/lab'
import CheckboxIcon from '@/assets/icons/checkboxIcon.svg'
import { Close } from '@mui/icons-material'
import { NoData } from '../NoData'
import { EditOperator } from '../EditNetwork/EditOperator'
import { IEditOperator, ISite } from '@/interfaces'
import { toast } from 'react-toastify'
import { userRoles } from '@/utils/constant'
interface TProps {
	handleCloseDrawer: () => void
	subheading?: string
	options?: IEditOperator[]
	heading?: string
	loading?: boolean
	handleAdd?: (id: string) => void
	siteDetails?: ISite
}

export const AssignOperatorSideDrawer = ({
	handleCloseDrawer,
	options,
	heading,
	subheading,
	loading,
	handleAdd,
	siteDetails,
}: TProps) => {
	const [selectedId, setSelectedId] = useState<string | null>(null)
	const [showEditOperator, setShowEditOperator] = useState(false)

	const selectedItem = options?.find((item) => item.id === selectedId) || null

	const handleSubmit = () => {
		if (!selectedItem) return

		if (selectedItem.accountType === userRoles.artisanProOperator) {
			handleAdd?.(selectedItem.id)
		} else if (selectedItem.accountType === userRoles.role_awaiting) {
			toast("Please edit the operator details before assigning.")
			setShowEditOperator(true)
		}
	}

	if (
		showEditOperator &&
		selectedItem?.accountType === userRoles.role_awaiting
	) {
		return (
			<EditOperator
				operatorDetails={selectedItem}
				handleCloseDrawer={() => {
					setShowEditOperator(false)
					handleCloseDrawer()
				}}
				siteDetails={siteDetails}
				type='assign'
			/>
		)
	}

	return (
		<StyleContainer>
			<Stack className='header'>
				<Stack
					direction='row'
					spacing={1}
					alignItems='center'
					width='100%'
					justifyContent='space-between'>
					<Stack>
						<Typography variant='h5'>{heading}</Typography>
						<Typography variant='subtitle1'>{subheading}</Typography>
					</Stack>
					<IconButton onClick={handleCloseDrawer}>
						<Close />
					</IconButton>
				</Stack>
			</Stack>
			<Stack className='container' component='form'>
				<Stack direction='column' gap={2} height='72dvh' overflow='auto'>
					{!options?.length && <NoData size='small' />}
					{options?.map((item) => (
						<Stack key={item.id} alignItems='center' direction='row' gap={1}>
							<Checkbox
								onClick={() =>
									setSelectedId((prev) => (prev === item.id ? null : item.id))
								}
								checked={selectedId === item.id}
								checkedIcon={<Box component='img' src={CheckboxIcon} />}
							/>
							<Typography>{item.name}</Typography>
						</Stack>
					))}
				</Stack>

				{!!options?.length && (
					<LoadingButton
						className='add_btn'
						loading={!!loading}
						disabled={!selectedId}
						onClick={handleSubmit}
						variant='contained'>
						Add
					</LoadingButton>
				)}
			</Stack>
		</StyleContainer>
	)
}

const StyleContainer = styled(Stack)(({ theme }) => ({
	gap: theme.spacing(2),
	'.header': {
		flexDirection: 'row',
		justifyContent: 'space-between',
		alignItems: 'center',
		padding: theme.spacing(4.5, 2),
		borderBottom: `${theme.spacing(0.125)} solid ${
			theme.palette.neutral['100']
		}`,
	},

	'.container': {
		padding: theme.spacing(2, 3),
		gap: theme.spacing(2.5),
		justifyContent: 'space-between',
		paddingBottom: theme.spacing(1),
	},
}))
