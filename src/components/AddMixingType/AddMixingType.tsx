import {
	Box,
	Checkbox,
	IconButton,
	Stack,
	styled,
	Typography,
} from '@mui/material'
import { useState } from 'react'
import { LoadingButton } from '@mui/lab'
import CheckboxIcon from '@/assets/icons/checkboxIcon.svg'
import { useParams } from 'react-router-dom'
import {
	IArtisanProDetails,
	ICsinkMixingType,
	IIPreferredBiomass,
	INetwork,
} from '@/interfaces'
import { Close } from '@mui/icons-material'
import {
	useMutation,
	useQuery,
	useQueryClient,
	UseQueryResult,
} from '@tanstack/react-query'
import { authAxios } from '@/contexts'
import { toast } from 'react-toastify'
import { AxiosError } from 'axios'
import { NoData } from '../NoData'

interface TProps {
	handleCloseDrawer: () => void
	preferredBiomassList?: IIPreferredBiomass[]
	cSinkNetworkDetails?: INetwork
	artisanProDetails?: IArtisanProDetails
	subheading?: string
	isCsink?: boolean
	mixingTypeQuery?: UseQueryResult<ICsinkMixingType[] | undefined, Error>
}

export const AddMixingType = ({
	handleCloseDrawer,
	mixingTypeQuery,
	subheading,
	isCsink = true,
	cSinkNetworkDetails,
	artisanProDetails,
}: TProps) => {
	const { cSinkNetworkId, artisanProId } = useParams()
	const queryClient = useQueryClient()
	const data =
		mixingTypeQuery?.data
			?.map((item) => {
				if (!item?.id) return ''
				return item?.id
			})
			.filter((item) => !!item) ?? []

	const [selectedMixingType, setSelectedMixingType] = useState<string[]>(data)

	const fetchAllMixingTypeQuery = useQuery({
		queryKey: ['fetchAllMixingTypeQuery'],
		queryFn: () => {
			const URI = isCsink
				? `/csink-manager/${cSinkNetworkDetails?.csinkManagerId}/mixing-types`
				: `/csink-manager/${artisanProDetails?.csinkManagerId}/mixing-types`
			return authAxios<ICsinkMixingType[]>(URI)
		},
	})
	const addMixingType = useMutation({
		mutationKey: ['addMixingType', selectedMixingType],
		mutationFn: async () => {
			const payload = {
				mixingTypeIds: selectedMixingType,
			}
			const api = isCsink
				? `/cs-network/${cSinkNetworkId}/mixing-types/assign`
				: `/artisian-pro/${artisanProId}/mixing-types/assign`

			return await authAxios.put(api, payload)
		},
		onError: (error: AxiosError) => {
			toast((error?.response?.data as { messageToUser: string })?.messageToUser)
		},
		onSuccess: ({ data }) => {
			toast(data?.message)
			queryClient.refetchQueries({ queryKey: ['getMixingTypeQuery'] })
			handleCloseDrawer()
		},
	})
	return (
		<StyleContainer>
			<Stack className='header'>
				<Stack
					direction='row'
					spacing={1}
					alignItems='center'
					width='100%'
					justifyContent='space-between'>
					<Stack>
						<Typography variant='h5'>Select Mixing Type</Typography>
						<Typography variant='subtitle1'> {subheading}</Typography>
					</Stack>
					<IconButton onClick={handleCloseDrawer}>
						<Close />
					</IconButton>
				</Stack>
			</Stack>
			<Stack className='container' component='form'>
				<Stack direction='column' gap={2} height='72dvh' overflow='auto'>
					{fetchAllMixingTypeQuery?.data?.data?.length ? (
						fetchAllMixingTypeQuery?.data?.data?.map((item) => {
							const isChecked = selectedMixingType.includes(item?.id)
							return (
								<Stack
									key={item?.id}
									alignItems='center'
									direction='row'
									gap={1}>
									<Checkbox
										onClick={() => {
											setSelectedMixingType((p) => {
												if (p.includes(item?.id)) {
													return p.filter((v) => v !== item?.id)
												} else {
													return [...p, item?.id]
												}
											})
										}}
										checked={isChecked}
										checkedIcon={<Box component='img' src={CheckboxIcon} />}
									/>
									<Typography>{item?.name}</Typography>
								</Stack>
							)
						})
					) : (
						<NoData size='small' />
					)}
				</Stack>

				<LoadingButton
					disabled={
						!fetchAllMixingTypeQuery?.data?.data ||
						fetchAllMixingTypeQuery?.data?.data?.length === 0
					}
					className='add_btn'
					onClick={() => addMixingType.mutate()}
					variant='contained'>
					Add
				</LoadingButton>
			</Stack>
		</StyleContainer>
	)
}

const StyleContainer = styled(Stack)(({ theme }) => ({
	gap: theme.spacing(2),
	'.header': {
		flexDirection: 'row',
		justifyContent: 'space-between',
		alignItems: 'center',
		padding: theme.spacing(4.5, 2),
		borderBottom: `${theme.spacing(0.125)} solid ${
			theme.palette.neutral['100']
		}`,
	},

	'.container': {
		padding: theme.spacing(2, 3),
		gap: theme.spacing(2.5),
		justifyContent: 'space-between',
		paddingBottom: theme.spacing(1),
	},
}))
