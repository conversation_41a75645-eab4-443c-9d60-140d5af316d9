import { proxyImage } from '@/utils/helper'
import PhotoIcon from '@mui/icons-material/Photo'
import { Box, BoxProps, Stack, Typography } from '@mui/material'
import { SyntheticEvent, useState } from 'react'
import { ImageCarouselDialog } from '../ImageCarousel'
import { IMedia } from '@/interfaces'

interface IProp extends BoxProps {
	imageList: IMedia[]
	componentSize?: number
	noProxy?: boolean
	DeleteImageHandler?: (imgId: string) => void | undefined
	onGridClick?: () => void
	ShowDeleteOption?: boolean
	onClick?: () => void
}

export const ImageGrid = ({
	imageList,
	componentSize = 38,
	DeleteImageHandler,
	noProxy = false,
	ShowDeleteOption = true,
	onGridClick,
	...props
}: IProp) => {
	const [openCarouselDialog, setOpenCarouselDialog] = useState<boolean>(false)
	if (imageList === null || imageList === undefined || imageList.length === 0) {
		return <PhotoIcon />
	}

	return (
		<>
			{openCarouselDialog ? (
				<ImageCarouselDialog
					close={() => setOpenCarouselDialog(false)}
					open={openCarouselDialog}
					ImagesList={imageList ?? []}
					ShowDeleteOption={ShowDeleteOption}
					DeleteImageHandler={DeleteImageHandler}
				/>
			) : null}
			<Stack direction='row' columnGap={1}>
				{imageList.slice(0, 2)?.map((img) => (
					<Box
						component='img'
						width={componentSize}
						height={componentSize}
						src={noProxy ? img.url : proxyImage(img.path || img.fileName || '')}
						{...props}
						key={img.id}
						onClick={(e: SyntheticEvent) => {
							onGridClick?.()
							e.stopPropagation()
							e.preventDefault()
							setOpenCarouselDialog(true)
						}}
					/>
				))}
				{imageList.length > 2 ? (
					<Box
						width={componentSize}
						height={componentSize}
						display='flex'
						alignItems='center'
						bgcolor='grey.300'
						{...props}
						borderRadius={1}
						onClick={(e) => {
							onGridClick?.()
							e.stopPropagation()
							e.preventDefault()
							setOpenCarouselDialog(true)
						}}
						justifyContent='center'>
						<Typography variant='subtitle2'>+{imageList.length - 2}</Typography>
					</Box>
				) : null}
			</Stack>
		</>
	)
}
