import { FieldErrors, UseFormReturn } from 'react-hook-form'
import { AddKilnWithDimensionImagesSchema } from './schema'
import { AnyObjectSchema } from 'yup'
import { theme } from '@/lib/theme/theme'
import { CustomTextField } from '@/utils/components'
import { Stack, Typography } from '@mui/material'
import { CustomFileUploader } from '../CustomFileUploader'

interface IProps {
	label: string
	instructionText: string
	textFieldName: keyof AddKilnWithDimensionImagesSchema
	imageFieldName: keyof AddKilnWithDimensionImagesSchema
	form: UseFormReturn<AddKilnWithDimensionImagesSchema>
	calculateKilnVolume?: (column?: string, value?: string) => void
	valueUpToThreeDecimalPlaces: (value: string) => string
	errors: FieldErrors<AddKilnWithDimensionImagesSchema>
	schema: AnyObjectSchema
	getDimensionImageUrl?: (
		fieldName: keyof AddKilnWithDimensionImagesSchema
	) => string
}
export const DimensionFieldWithImage = ({
	label,
	instructionText,
	textFieldName,
	imageFieldName,
	form,
	calculateKilnVolume,
	valueUpToThreeDecimalPlaces,
	errors,
	schema,
	getDimensionImageUrl,
}: IProps) => {
	const handleValueChange = (value: string) => {
		if (!value.includes('.')) return
		form.setValue(textFieldName, valueUpToThreeDecimalPlaces(value))
	}

	return (
		<Stack gap={theme.spacing(1.5)}>
			<Typography
				variant='body1'
				color='text.secondary'
				fontSize={theme.spacing(1.5)}
				fontWeight={theme.typography.caption.fontWeight}>
				{instructionText}
			</Typography>
			<Stack direction='row' gap={theme.spacing(2)} alignItems='flex-start'>
				<Stack flex={1}>
					<CustomTextField
						label={label}
						watch={form.watch}
						schema={schema}
						fullWidth
						{...form.register(textFieldName)}
						hideNumberArrows
						error={!!errors?.[textFieldName]?.message}
						helperText={errors?.[textFieldName]?.message}
						onInputCapture={(e: React.ChangeEvent<HTMLInputElement>) => {
							calculateKilnVolume?.(
								textFieldName,
								valueUpToThreeDecimalPlaces(e.target.value)
							)
							handleValueChange(e.target.value)
						}}
					/>
				</Stack>
				<Stack alignItems='center' gap={1}>
					<CustomFileUploader
						heading=''
						sx={{
							height: 56,
							width: 56,
							marginTop: 0,
							'& .MuiIconButton-root': {
								height: 56,
								width: 56,
								borderRadius: 1,
							},
						}}
						imageHeight={56}
						imageUrl={
							getDimensionImageUrl ? getDimensionImageUrl(imageFieldName) : ''
						}
						setUploadData={(data) => {
							form.setValue(imageFieldName, data?.id || '')
							form.clearErrors(imageFieldName)
						}}
						acceptFileTypes={['png', 'jpg', 'jpeg', 'webp']}
						mediaType='image'
					/>
					{errors?.[imageFieldName]?.message && (
						<Typography
							variant='caption'
							color='error'
							textAlign='center'
							maxWidth={56}>
							{errors?.[imageFieldName]?.message}
						</Typography>
					)}
				</Stack>
			</Stack>
		</Stack>
	)
}
