import { kilnShapeEnum, kilnTypeEnum } from '@/utils/constant'
import * as Yup from 'yup'

const imageSchema = Yup.object({
	id: Yup.string(),
	url: Yup.string(),
	fileName: Yup.string().notRequired(),
})
export const addKilnWithDimensionImagesSchema = (isCsink: boolean = false) =>
	Yup.object({
		name: Yup.string().required('Please enter kiln name'),
		kilnType: Yup.string().required('Please enter kiln type'),
		kilnShape: Yup.string().required('Please enter Kiln shape'),
		unit: Yup.string(),
		length: Yup.string().when(['kilnShape'], {
			is: (value: string) => value === kilnShapeEnum.Rectangular,
			then: (schema) =>
				schema.typeError('Please enter length').required('Please enter length'),
			otherwise: (schema) => schema,
		}),

		FarmerId: Yup.string().when([], {
			is: () => isCsink,
			then: (schema) => schema.required('Farmer Site ID is required'),
			otherwise: (schema) => schema.optional(),
		}),
		temperature: Yup.number()
			.nullable()
			.when('kilnType', {
				is: (value: string) => value !== kilnTypeEnum.RoCC,
				then: (schema) => schema.required('Temperature required'),
				otherwise: (schema) => schema.notRequired(),
			}),
		shortBase: Yup.string().when(['kilnShape'], {
			is: (value: string) => value === kilnShapeEnum.Rectangular,
			then: (schema) =>
				schema
					.typeError('Please enter short base')
					.required('Please enter short base'),
			otherwise: (schema) => schema,
		}),
		longBase: Yup.string().when(['kilnShape'], {
			is: (value: string) => value === kilnShapeEnum.Rectangular,
			then: (schema) =>
				schema
					.typeError('Please enter long base')
					.required('Please enter long base'),
			otherwise: (schema) => schema,
		}),
		upperSurfaceDiameter: Yup.string().when(['kilnShape'], {
			is: (value: string) =>
				![kilnShapeEnum.Cylindrical, kilnShapeEnum.Rectangular].includes(
					value as kilnShapeEnum
				),
			then: (schema) =>
				schema
					.typeError('Please enter upper surface diameter')
					.required('Please enter upper surface diameter'),
			otherwise: (schema) => schema,
		}),
		lowerSurfaceDiameter: Yup.string().when(['kilnShape'], {
			is: (value: string) =>
				![kilnShapeEnum.Cylindrical, kilnShapeEnum.Rectangular].includes(
					value as kilnShapeEnum
				),
			then: (schema) =>
				schema
					.typeError('Please enter lower surface diameter')
					.required('Please enter lower surface diameter'),
			otherwise: (schema) => schema,
		}),
		volume: Yup.number()
			.typeError('Please enter Volume')
			.min(1, 'Volume must be greater than 1')
			.required('Please enter Volume'),
		depth: Yup.string()
			.typeError('Please enter kiln depth')
			.required('Please enter kiln depth'),
		address: Yup.string().notRequired(),
		latitude: Yup.number()
			.required('Please enter latitude')
			.typeError('Please enter latitude')
			.transform((value) => (Number.isNaN(value) ? null : value)),
		longitude: Yup.number()
			.required('Please enter longitude')
			.typeError('Please enter longitude')
			.transform((value) => (Number.isNaN(value) ? null : value)),
		diameter: Yup.string().when(['kilnShape'], {
			is: (value: string) => value === kilnShapeEnum.Cylindrical,
			then: (schema) => schema.required('Please enter kiln diameter'),
			otherwise: (schema) => schema,
		}),
		images: Yup.array()
			.of(imageSchema)
			.min(1, 'Please upload more images')
			.required('Please upload Kiln image'),
		selectKiln: Yup.string().notRequired(),

		frustumLengthImageIds: Yup.string().when(['kilnShape'], {
			is: (value: string) => value === kilnShapeEnum.Rectangular,
			then: (schema) => schema.required('Please upload length images'),
			otherwise: (schema) => schema,
		}),
		depthImageIds: Yup.string().when(['kilnShape'], {
			is: (value: kilnShapeEnum) =>
				[
					kilnShapeEnum.Rectangular,
					kilnShapeEnum.Cylindrical,
					kilnShapeEnum.Conical,
					kilnShapeEnum.Pyramidal,
				].includes(value),
			then: (schema) => schema.required('Please upload depth images'),
			otherwise: (schema) => schema,
		}),
		longBaseImageIds: Yup.string().when(['kilnShape'], {
			is: (value: string) => value === kilnShapeEnum.Rectangular,
			then: (schema) => schema.required('Please upload long base images'),
			otherwise: (schema) => schema,
		}),
		shortBaseImageIds: Yup.string().when(['kilnShape'], {
			is: (value: string) => value === kilnShapeEnum.Rectangular,
			then: (schema) => schema.required('Please upload short base images'),
			otherwise: (schema) => schema,
		}),
		lowerSideImageIds: Yup.string().when(['kilnShape'], {
			is: (value: kilnShapeEnum) => [kilnShapeEnum.Pyramidal].includes(value),
			then: (schema) => schema.required('Please upload lower side images'),
			otherwise: (schema) => schema,
		}),
		upperSideImageIds: Yup.string().when(['kilnShape'], {
			is: (value: kilnShapeEnum) => [kilnShapeEnum.Pyramidal].includes(value),
			then: (schema) => schema.required('Please upload upper side images'),
			otherwise: (schema) => schema,
		}),
		diameterImageIds: Yup.string().when(['kilnShape'], {
			is: (value: string) => value === kilnShapeEnum.Cylindrical,
			then: (schema) => schema.required('Please upload diameter images'),
			otherwise: (schema) => schema,
		}),

		upperSurfaceDiameterImageIds: Yup.string().when(['kilnShape'], {
			is: (value: kilnShapeEnum) => [kilnShapeEnum.Conical].includes(value),
			then: (schema) => schema.required('Please upload upper surface images'),
			otherwise: (schema) => schema,
		}),
		lowerSurfaceDiameterImageIds: Yup.string().when(['kilnShape'], {
			is: (value: kilnShapeEnum) => [kilnShapeEnum.Conical].includes(value),
			then: (schema) => schema.required('Please upload lower surface images'),
			otherwise: (schema) => schema,
		}),
	})

export type AddKilnWithDimensionImagesSchema = Yup.InferType<
	ReturnType<typeof addKilnWithDimensionImagesSchema>
>
