import { AxiosError } from 'axios'
import { useCallback, useEffect, useMemo, useState } from 'react'
import { toast } from 'react-toastify'
import { useParams, useSearchParams } from 'react-router-dom'
import { authAxios, useAuthContext } from '@/contexts'
import {
	kilnShapeEnum,
	KilnShapeLabelValue,
	kilnTypeEnum,
	KilnTypeLabelValue,
	measuringUnitEnum,
	measuringUnitsValue,
	userRoles,
} from '@/utils/constant'
import { useForm } from 'react-hook-form'
import { yupResolver } from '@hookform/resolvers/yup'
import {
	QueryFunctionContext,
	useMutation,
	useQuery,
	useQueryClient,
} from '@tanstack/react-query'
import { ICsinkFarmer, IKilnWithDimensionImages, IMedia } from '@/interfaces'
import {
	calculateVolumeConicalKiln,
	calculateVolumeCylindricalKiln,
	calculateVolumePyramidalKiln,
	calculateVolumeRectangularKiln,
	convertMillimeterCubetoLitre,
	convertMillimeterToCentimeter,
	convertUnit,
} from '@/utils/helper'
import {
	AddKilnWithDimensionImagesSchema,
	addKilnWithDimensionImagesSchema,
} from './schema'
type Props = {
	kilnDetails?: IKilnWithDimensionImages
	editMode?: boolean
	addText?: string
	siteDetailsAddress?: string
	handleClose: () => void
	isGlobalAddKiln: boolean
}

export type BaseInput = {
	label: string
	value: string
	type: string
	options?: { label: string; value: string }[]
	disabled?: boolean | undefined
	isNumericInput?: boolean | undefined
	forCalculation?: boolean | undefined
}

type RowInput = {
	showType: string
	children: BaseInput[]
}

type InputListType = BaseInput | RowInput

const INITIAL_VALUES: AddKilnWithDimensionImagesSchema = {
	name: '',
	kilnType: kilnTypeEnum.KonTiki,
	kilnShape: kilnShapeEnum.Conical,
	unit: measuringUnitEnum.cm,
	upperSurfaceDiameter: '',
	lowerSurfaceDiameter: '',
	volume: 0,
	depth: '',
	latitude: 0,
	longitude: 0,
	images: [],
	diameter: '',
	address: '',
	longBase: '',
	shortBase: '',
	length: '',
	selectKiln: '',
	frustumLengthImageIds: '',
	longBaseImageIds: '',
	shortBaseImageIds: '',
	lowerSideImageIds: '',
	upperSideImageIds: '',
	diameterImageIds: '',
	depthImageIds: '',
	upperSurfaceDiameterImageIds: '',
	lowerSurfaceDiameterImageIds: '',
} as const

const QUERY_PARAMS = {
	limit: '1000',
	page: '0',
} as const
// API endpoint builders
const buildKilnPostEndpoint = (
	cSinkNetworkId?: string,
	artisanProId?: string,
	siteId?: string,
	selectedFarmersSiteId?: string
): string => {
	if (cSinkNetworkId && selectedFarmersSiteId) {
		return `cs-network/${cSinkNetworkId}/site/${selectedFarmersSiteId}/kiln`
	}
	if (artisanProId && siteId) {
		return `artisian-pro/${artisanProId}/site/${siteId}/kiln`
	}
	return ''
}

const buildKilnGetEndpoint = (
	csinkNetworkId?: string,
	artisanProId?: string
): string => {
	if (csinkNetworkId) {
		return `/cs-network/${csinkNetworkId}/kiln-template?isAssigned=true`
	}
	if (artisanProId) {
		return `/artisian-pro/${artisanProId}/kiln-template?isAssigned=true`
	}
	return `/settings/kiln-template?`
}

// Query function for fetching global kilns
const fetchGlobalKilns = async ({ queryKey }: QueryFunctionContext) => {
	const [, cSinkNetworkId, artisanProId] = queryKey
	const queryParams = new URLSearchParams(QUERY_PARAMS)

	const endpoint = `${buildKilnGetEndpoint(
		cSinkNetworkId as string,
		artisanProId as string
	)}&${queryParams.toString()}`

	return authAxios.get(endpoint)
}

// Helper function to convert image ID to array
const convertImageIdToArray = (imageId?: string): string[] =>
	imageId ? [imageId] : []

// Generate payload for kiln creation/update
const generateKilnPayload = (
	values: AddKilnWithDimensionImagesSchema,
	unit: measuringUnitEnum,
	networkId?: string
) => {
	const {
		upperSurfaceDiameter,
		lowerSurfaceDiameter,
		depth,
		diameter,
		images,
		length,
		shortBase,
		longBase,
		frustumLengthImageIds,
		depthImageIds,
		lowerSideImageIds,
		upperSideImageIds,
		diameterImageIds,
		upperSurfaceDiameterImageIds,
		lowerSurfaceDiameterImageIds,
		longBaseImageIds,
		shortBaseImageIds,
		...rest
	} = values

	// Convert all measurements to consistent units
	const convertedValues = {
		lowerSurfaceDiameter: convertUnit(Number(lowerSurfaceDiameter), unit),
		upperSurfaceDiameter: convertUnit(Number(upperSurfaceDiameter), unit),
		depth: convertUnit(Number(depth), unit),
		length: convertUnit(Number(length), unit),
		shortBase: convertUnit(Number(shortBase), unit),
		longBase: convertUnit(Number(longBase), unit),
		diameter: convertUnit(Number(diameter), unit),
	}

	// Shape-specific payload configurations
	const shapePayloads = {
		[kilnShapeEnum.Conical]: {
			upperSurfaceDiameter: convertedValues.upperSurfaceDiameter,
			lowerSurfaceDiameter: convertedValues.lowerSurfaceDiameter,
			depth: convertedValues.depth,
		},
		[kilnShapeEnum.Pyramidal]: {
			upperSide: convertedValues.upperSurfaceDiameter,
			lowerSide: convertedValues.lowerSurfaceDiameter,
			depth: convertedValues.depth,
		},
		[kilnShapeEnum.Rectangular]: {
			frustumLength: convertedValues.length,
			depth: convertedValues.depth,
			shortBase: convertedValues.shortBase,
			longBase: convertedValues.longBase,
		},
		[kilnShapeEnum.Cylindrical]: {
			diameter: convertedValues.diameter,
			depth: convertedValues.depth,
		},
	}

	return {
		...rest,
		...shapePayloads[values.kilnShape as kilnShapeEnum],
		volume: Number(values.volume),
		latitude: Number(values.latitude),
		longitude: Number(values.longitude),
		networkId,
		imageIds: images?.map((img) => img.id) || [],
		frustumLengthImageIds: convertImageIdToArray(frustumLengthImageIds),
		lowerSideImageIds: convertImageIdToArray(lowerSideImageIds),
		upperSideImageIds: convertImageIdToArray(upperSideImageIds),
		diameterImageIds: convertImageIdToArray(diameterImageIds),
		depthImageIds: convertImageIdToArray(depthImageIds),
		longBaseImageIds: convertImageIdToArray(longBaseImageIds),
		shortBaseImageIds: convertImageIdToArray(shortBaseImageIds),
		upperSurfaceDiameterImageIds: convertImageIdToArray(
			upperSurfaceDiameterImageIds
		),
		lowerSurfaceDiameterImageIds: convertImageIdToArray(
			lowerSurfaceDiameterImageIds
		),
	}
}

export const useAddKilnWithDimensionImages = ({
	handleClose,
	editMode = false,
	kilnDetails,
	addText,
	siteDetailsAddress,
}: Props) => {
	const { cSinkNetworkId, artisanProId } = useParams()
	const [searchParams] = useSearchParams()
	const siteId = searchParams.get('siteTab') ?? ''
	const { userDetails } = useAuthContext()
	const [kilnSelected, setKilnSelected] = useState('')

	// Parse coordinates from kiln details
	const coordinates = useMemo(() => {
		if (!kilnDetails?.coordinate) return { lat: 0, lng: 0 }
		const [lat, lng] = kilnDetails.coordinate.split(',')
		return {
			lat: Number(lat.slice(1)),
			lng: Number(lng.slice(0, -1)),
		}
	}, [kilnDetails?.coordinate])

	const form = useForm<AddKilnWithDimensionImagesSchema>({
		defaultValues: INITIAL_VALUES,
		resolver: yupResolver<AddKilnWithDimensionImagesSchema>(
			addKilnWithDimensionImagesSchema(!!cSinkNetworkId)
		),
		mode: 'all',
	})

	const {
		formState: { errors },
		setValue,
	} = form

	const unit = form.watch('unit') as measuringUnitEnum
	const selectedKilnTemplate = form.watch('selectKiln')
	const queryClient = useQueryClient()

	// Memoized coordinate fields for better performance
	const coordinationTextFieldContent = useMemo(
		() => [
			{
				label: 'Latitude',
				value: 'latitude',
				isNumericInput: true,
				type: 'textField',
			},
			{
				label: 'Longitude',
				value: 'longitude',
				isNumericInput: true,
				type: 'textField',
			},
		],
		[]
	)

	const getAllGlobalKilnsQuery = useQuery({
		queryKey: ['getAllGlobalKilnsQuery', cSinkNetworkId, artisanProId],
		queryFn: fetchGlobalKilns,
		select(data) {
			const kilnsOptionsKeyValue =
				data?.data?.kilns?.map((item: IKilnWithDimensionImages) => ({
					label: item.name,
					value: item.id,
				})) || []

			return {
				globalData: data?.data,
				kilnOptions: kilnsOptionsKeyValue,
			}
		},
	})

	const getAllFarmers = async () => {
		const { data } = await authAxios.get(
			`/cs-network/${cSinkNetworkId}/farmers`,
			{
				params: {
					limit: 1000,
				},
			}
		)
		return data?.farmers || []
	}

	const getFarmers = useQuery({
		queryKey: ['getFarmers'],
		queryFn: () => getAllFarmers(),
		select: (data) =>
			data.map((farmer: ICsinkFarmer) => ({
				name: farmer.name,
				countryCode: farmer.countryCode,
				phoneNo: farmer.phoneNo,
				id: farmer.siteId,
				email: farmer?.email,
			})),
		enabled: !!cSinkNetworkId,
	})

	// Memoized input list generation for better performance
	const inputList = useMemo((): InputListType[] => {
		const kilnShape = form.watch('kilnShape')
		const kilnType = form.watch('kilnType')
		const isKilnShapeDisabled =
			kilnType === kilnTypeEnum.RoCC || (!!cSinkNetworkId && editMode)
		const isDimensionDisabled = !!cSinkNetworkId && editMode

		// Base input fields
		const baseFields: InputListType[] = [
			{
				label: 'Enter the Kiln Name',
				value: 'name',
				type: 'textField',
			},
			{
				label: 'Address',
				value: 'address',
				type: 'textField',
			},
			{
				label: 'Kiln Type',
				value: 'kilnType',
				type: 'select',
				options: KilnTypeLabelValue,
			},
			{
				showType: 'row',
				children: [
					{
						label: 'Kiln Shape',
						value: 'kilnShape',
						type: 'select',
						disabled: isKilnShapeDisabled,
						options: KilnShapeLabelValue,
					},
					{
						label: 'Measuring Unit',
						value: 'unit',
						type: 'select',
						options: measuringUnitsValue,
					},
				],
			},
		]

		// Kiln selection field
		const kilnSelectionField: InputListType[] = []
		const shouldShowKilnSelection =
			!editMode ||
			[userRoles.CsinkManager, userRoles.BiomassAggregator].includes(
				userDetails?.accountType as userRoles
			)

		if (shouldShowKilnSelection) {
			kilnSelectionField.push({
				label: 'Select Kiln',
				value: 'selectKiln',
				type: 'select',
				disabled: !getAllGlobalKilnsQuery?.data?.kilnOptions?.length,
				options: getAllGlobalKilnsQuery?.data?.kilnOptions || [],
			})
		}

		// Farmer selection field for CSink networks
		const farmerSelectionField: InputListType[] = []
		if (cSinkNetworkId) {
			farmerSelectionField.push({
				label: 'Select farmer',
				value: 'FarmerId',
				type: 'autocomplete',
				disabled: editMode,
				options:
					getFarmers?.data?.map(
						({ name, id, countryCode, phoneNo, email }: ICsinkFarmer) => ({
							label: `${name} ${
								phoneNo ? `(${countryCode} ${phoneNo})` : `(${email})`
							}`,
							value: id,
						})
					) || [],
			})
		}

		// Shape-specific dimension fields
		const getDimensionFields = (): InputListType[] => {
			const commonDepthField = {
				label: 'Depth',
				value: 'depth',
				isNumericInput: true,
				forCalculation: true,
				disabled: isDimensionDisabled,
				type: 'textField',
			}

			switch (kilnShape) {
				case kilnShapeEnum.Cylindrical:
					return [
						{
							label: 'Diameter',
							value: 'diameter',
							isNumericInput: true,
							forCalculation: true,
							disabled: isDimensionDisabled,
							type: 'textField',
						},
						commonDepthField,
					]

				case kilnShapeEnum.Rectangular:
					return [
						{
							label: 'Length',
							value: 'length',
							isNumericInput: true,
							forCalculation: true,
							disabled: isDimensionDisabled,
							type: 'textField',
						},
						{
							label: 'Short Base',
							value: 'shortBase',
							isNumericInput: true,
							forCalculation: true,
							disabled: isDimensionDisabled,
							type: 'textField',
						},
						{
							label: 'Long Base',
							value: 'longBase',
							isNumericInput: true,
							forCalculation: true,
							disabled: isDimensionDisabled,
							type: 'textField',
						},
						commonDepthField,
					]

				default: // Conical and Pyramidal
					return [
						{
							label: 'Upper Surface Diameter',
							value: 'upperSurfaceDiameter',
							isNumericInput: true,
							forCalculation: true,
							disabled: isDimensionDisabled,
							type: 'textField',
						},
						{
							label: 'Lower Surface Diameter',
							value: 'lowerSurfaceDiameter',
							isNumericInput: true,
							forCalculation: true,
							disabled: isDimensionDisabled,
							type: 'textField',
						},
						commonDepthField,
					]
			}
		}

		return [
			...kilnSelectionField,
			...farmerSelectionField,
			...baseFields,
			...getDimensionFields(),
		]
	}, [
		form,
		editMode,
		cSinkNetworkId,
		userDetails?.accountType,
		getAllGlobalKilnsQuery?.data?.kilnOptions,
		getFarmers?.data,
	])

	const setMapCenter = useCallback(
		(lat: number, lng: number) => {
			setValue('latitude', Number(lat.toFixed(6)))
			setValue('longitude', Number(lng.toFixed(6)))
		},
		[setValue]
	)

	const getCurrentLocation = useCallback(() => {
		navigator.geolocation.getCurrentPosition((position) => {
			setMapCenter(position.coords.latitude, position.coords.longitude)
		})
	}, [setMapCenter])

	// Image upload handlers
	const handleMainImageUpload = useCallback(
		(uploadedImages: IMedia[]) => {
			const formattedImages = uploadedImages.map((img) => ({
				id: img.id,
				url: img.url,
				fileName: img.fileName || '',
			}))
			form.setValue('images', formattedImages)
			form.clearErrors('images')
		},
		[form]
	)

	const handleDimensionImageUpload = useCallback(
		(fieldName: keyof AddKilnWithDimensionImagesSchema, imageData: IMedia) => {
			if (imageData?.id) {
				form.setValue(fieldName, imageData.id)
				form.clearErrors(fieldName)
			}
		},
		[form]
	)

	// Get image URL for dimension fields
	const getDimensionImageUrl = useCallback(
		(fieldName: keyof AddKilnWithDimensionImagesSchema): string => {
			const imageId = form.watch(fieldName) as string
			if (!imageId) return ''

			// If editing mode, try to get URL from existing kiln details
			if (editMode && kilnDetails) {
				// Map form field names to kiln detail image array names
				const fieldMappings: Record<string, keyof IKilnWithDimensionImages> = {
					depthImageIds: 'depth_images',
					diameterImageIds: 'diameter_images',
					frustumLengthImageIds: 'frustum_length_images',
					shortBaseImageIds: 'short_base_images',
					upperSurfaceDiameterImageIds: 'upper_surface_images',
					lowerSurfaceDiameterImageIds: 'lower_surface_images',
					upperSideImageIds: 'upper_side_images',
					lowerSideImageIds: 'lower_side_images',
					longBaseImageIds: 'long_base_images',
				}

				const imageField = fieldMappings[fieldName as string]
				if (imageField) {
					const images = kilnDetails[imageField] as IMedia[]
					if (images && images.length > 0) {
						const matchingImage = images.find((img) => img.id === imageId)
						return matchingImage?.url || ''
					}
				}
			}

			return ''
		},
		[form, editMode, kilnDetails]
	)

	// Enhanced image upload handler for dimension fields with URL support
	const handleDimensionImageUploadWithUrl = useCallback(
		(fieldName: keyof AddKilnWithDimensionImagesSchema) => {
			return (imageData: IMedia) => {
				if (imageData?.id) {
					form.setValue(fieldName, imageData.id)
					form.clearErrors(fieldName)
				}
			}
		},
		[form]
	)

	// Get dimension fields configuration based on kiln shape
	const getDimensionFieldsConfig = useMemo(() => {
		const kilnShape = form.watch('kilnShape')

		const baseConfig = {
			depth: {
				label: 'Depth',
				imageField: 'depthImageIds' as keyof AddKilnWithDimensionImagesSchema,
				required: true,
			},
		}

		switch (kilnShape) {
			case kilnShapeEnum.Cylindrical:
				return {
					diameter: {
						label: 'Diameter',
						imageField:
							'diameterImageIds' as keyof AddKilnWithDimensionImagesSchema,
						required: true,
					},
					...baseConfig,
				}

			case kilnShapeEnum.Rectangular:
				return {
					length: {
						label: 'Length',
						imageField:
							'frustumLengthImageIds' as keyof AddKilnWithDimensionImagesSchema,
						required: true,
					},
					shortBase: {
						label: 'Short Base',
						imageField:
							'shortBaseImageIds' as keyof AddKilnWithDimensionImagesSchema,
						required: true,
					},
					longBase: {
						label: 'Long Base',
						imageField:
							'longBaseImageIds' as keyof AddKilnWithDimensionImagesSchema,
						required: true,
					},
					...baseConfig,
				}

			case kilnShapeEnum.Conical:
				return {
					upperSurfaceDiameter: {
						label: 'Upper Surface Diameter',
						imageField:
							'upperSurfaceDiameterImageIds' as keyof AddKilnWithDimensionImagesSchema,
						required: true,
					},
					lowerSurfaceDiameter: {
						label: 'Lower Surface Diameter',
						imageField:
							'lowerSurfaceDiameterImageIds' as keyof AddKilnWithDimensionImagesSchema,
						required: true,
					},
					...baseConfig,
				}

			case kilnShapeEnum.Pyramidal:
				return {
					upperSurfaceDiameter: {
						label: 'Upper Side',
						imageField:
							'upperSideImageIds' as keyof AddKilnWithDimensionImagesSchema,
						required: true,
					},
					lowerSurfaceDiameter: {
						label: 'Lower Side',
						imageField:
							'lowerSideImageIds' as keyof AddKilnWithDimensionImagesSchema,
						required: true,
					},
					...baseConfig,
				}

			default:
				return baseConfig
		}
	}, [form])

	const calculateKilnVolume = useCallback(
		(column?: string, value?: string) => {
			const formValues = form.getValues()
			const kilnShape = formValues.kilnShape

			// Helper function to get dimension value
			const getDimensionValue = (fieldName: string): number => {
				const fieldValue =
					column === fieldName
						? Number(value)
						: Number(formValues[fieldName as keyof typeof formValues])
				return convertUnit(fieldValue, unit)
			}

			let volume = 0

			switch (kilnShape) {
				case kilnShapeEnum.Conical:
					volume = calculateVolumeConicalKiln(
						getDimensionValue('upperSurfaceDiameter'),
						getDimensionValue('lowerSurfaceDiameter'),
						getDimensionValue('depth')
					)
					break

				case kilnShapeEnum.Cylindrical:
					volume = calculateVolumeCylindricalKiln(
						getDimensionValue('diameter'),
						getDimensionValue('depth')
					)
					break

				case kilnShapeEnum.Pyramidal:
					volume = calculateVolumePyramidalKiln(
						getDimensionValue('upperSurfaceDiameter'),
						getDimensionValue('lowerSurfaceDiameter'),
						getDimensionValue('depth')
					)
					break

				case kilnShapeEnum.Rectangular:
					volume = calculateVolumeRectangularKiln(
						getDimensionValue('length'),
						getDimensionValue('depth'),
						getDimensionValue('shortBase'),
						getDimensionValue('longBase')
					)
					break

				default:
					volume = 0
					break
			}

			form.setValue('volume', convertMillimeterCubetoLitre(volume))
		},
		[form, unit]
	)

	const addKilnMutation = useMutation({
		mutationKey: ['addKiln', unit, cSinkNetworkId, userDetails],
		mutationFn: async (values: AddKilnWithDimensionImagesSchema) => {
			const payload = generateKilnPayload(values, unit, cSinkNetworkId)
			const selectedFarmersSiteId = form.watch('FarmerId')

			const endpoint = buildKilnPostEndpoint(
				cSinkNetworkId,
				artisanProId,
				siteId,
				selectedFarmersSiteId
			)

			const { data } = await authAxios.post(endpoint, payload)
			return data
		},
		onError: (err: AxiosError) => {
			const errorMessage = (err?.response?.data as { messageToUser: string })
				?.messageToUser
			toast(errorMessage || 'An error occurred while adding the kiln')
		},
		onSuccess: (data) => {
			toast(data?.message || 'Kiln added successfully')
			handleClose()

			// Refetch relevant queries
			const queriesToRefetch = [
				cSinkNetworkId ? 'kilnDetails' : 'getKilnList',
				'allKilns',
			]

			if (cSinkNetworkId) queriesToRefetch.push('cSinkNetworkDetails')
			if (artisanProId) queriesToRefetch.push('allSites')

			queriesToRefetch.forEach((queryKey) => {
				queryClient.invalidateQueries({ queryKey: [queryKey] })
			})
		},
	})

	const editKilnMutation = useMutation({
		mutationKey: ['editKiln', unit, cSinkNetworkId],
		mutationFn: async (values: AddKilnWithDimensionImagesSchema) => {
			const payload = generateKilnPayload(values, unit, cSinkNetworkId)
			const endpoint = `/kiln/${kilnDetails?.id}`

			const { data } = await authAxios.put(endpoint, {
				...payload,
				networkId: cSinkNetworkId,
			})
			return data
		},
		onError: (err: AxiosError) => {
			const errorMessage = (err?.response?.data as { messageToUser: string })
				?.messageToUser
			toast(errorMessage || 'An error occurred while updating the kiln')
		},
		onSuccess: (data) => {
			toast(data?.message || 'Kiln updated successfully')
			handleClose()
			queryClient.invalidateQueries({ queryKey: ['getKilnList'] })
			queryClient.invalidateQueries({ queryKey: ['kilnDetails'] })
		},
	})

	const handleAddKiln = useCallback(
		(values: AddKilnWithDimensionImagesSchema) => {
			editMode
				? editKilnMutation.mutate(values)
				: addKilnMutation.mutate(values)
		},
		[addKilnMutation, editKilnMutation, editMode]
	)

	useEffect(() => {
		getCurrentLocation()
	}, [getCurrentLocation, kilnSelected])

	const editModeSetValues = useCallback(
		({
			editModeKilnDetails,
		}: {
			editModeKilnDetails: IKilnWithDimensionImages | undefined
		}) => {
			if (!editModeKilnDetails) return

			// Helper to set form values with proper typing
			const setFormValue = (
				field: string,
				value:
					| string
					| number
					| { id?: string; url?: string; fileName?: string }[]
			) => {
				form.setValue(
					field as keyof AddKilnWithDimensionImagesSchema,
					value as any
				)
			}

			// Basic kiln information
			setFormValue('name', editModeKilnDetails.name ?? '')
			setFormValue(
				'kilnType',
				editModeKilnDetails.kilnType ?? kilnTypeEnum.KonTiki
			)
			setFormValue(
				'kilnShape',
				editModeKilnDetails.kilnShape ?? kilnShapeEnum.Conical
			)
			setFormValue('address', editModeKilnDetails.address ?? '')
			setFormValue('volume', editModeKilnDetails.volume ?? 0)

			// Format main images for the form
			const formattedImages =
				editModeKilnDetails.imageURLs?.map((img) => ({
					id: img.id || '',
					url: img.url || '',
					fileName: img.fileName || '',
				})) || []
			setFormValue('images', formattedImages)

			// Set dimension image IDs from the kiln details
			const dimensionImageMappings = {
				depthImageIds: editModeKilnDetails.depth_images?.[0]?.id,
				diameterImageIds: editModeKilnDetails.diameter_images?.[0]?.id,
				frustumLengthImageIds:
					editModeKilnDetails.frustum_length_images?.[0]?.id,
				shortBaseImageIds: editModeKilnDetails.short_base_images?.[0]?.id,
				longBaseImageIds: editModeKilnDetails.long_base_images?.[0]?.id,
				upperSurfaceDiameterImageIds:
					editModeKilnDetails.upper_surface_images?.[0]?.id,
				lowerSurfaceDiameterImageIds:
					editModeKilnDetails.lower_surface_images?.[0]?.id,
				upperSideImageIds: editModeKilnDetails.upper_side_images?.[0]?.id,
				lowerSideImageIds: editModeKilnDetails.lower_side_images?.[0]?.id,
			}

			Object.entries(dimensionImageMappings).forEach(([field, imageId]) => {
				if (imageId) {
					setFormValue(field, imageId)
				}
			})

			// Farmer ID for CSink networks
			if (cSinkNetworkId) {
				setFormValue('FarmerId', (editModeKilnDetails?.siteId as string) ?? '')
			}

			// Dimension fields with unit conversion
			const dimensionFields = {
				upperSurfaceDiameter:
					editModeKilnDetails.upperSurfaceDiameter ??
					editModeKilnDetails.upperSide ??
					0,
				lowerSurfaceDiameter:
					editModeKilnDetails.lowerSurfaceDiameter ??
					editModeKilnDetails.lowerSide ??
					0,
				longBase:
					editModeKilnDetails.longBase ?? editModeKilnDetails.lowerBase ?? 0,
				shortBase:
					editModeKilnDetails.shortBase ?? editModeKilnDetails.upperBase ?? 0,
				depth: editModeKilnDetails.depth ?? 0,
				diameter: editModeKilnDetails.diameter ?? 0,
			}

			Object.entries(dimensionFields).forEach(([field, value]) => {
				setFormValue(
					field as keyof AddKilnWithDimensionImagesSchema,
					String(convertMillimeterToCentimeter(value))
				)
			})

			// Length field handling (different for rectangular kilns)
			const lengthValue =
				editModeKilnDetails.kilnShape === kilnShapeEnum.Rectangular
					? editModeKilnDetails.frustumLength ?? 0
					: editModeKilnDetails.length ?? 0
			setFormValue('length', String(convertMillimeterToCentimeter(lengthValue)))

			// Coordinates
			if (editModeKilnDetails.coordinate) {
				const [lat, lng] = editModeKilnDetails.coordinate.split(',')
				setFormValue('latitude', Number(lat.slice(1)))
				setFormValue('longitude', Number(lng.slice(0, -1)))
			} else {
				setFormValue('latitude', coordinates.lat)
				setFormValue('longitude', coordinates.lng)
			}
		},
		[form, cSinkNetworkId, coordinates]
	)

	useEffect(() => {
		if (selectedKilnTemplate) {
			const selectedKilnForEdit =
				getAllGlobalKilnsQuery.data?.globalData?.kilns?.find(
					(item: IKilnWithDimensionImages) => item?.id === selectedKilnTemplate
				)
			if (selectedKilnForEdit) {
				editModeSetValues({ editModeKilnDetails: selectedKilnForEdit })
				setKilnSelected(selectedKilnTemplate)
			}
		}
	}, [
		getAllGlobalKilnsQuery.data?.globalData?.kilns,
		selectedKilnTemplate,
		editModeSetValues,
	])

	useEffect(() => {
		if (editMode && kilnDetails) {
			editModeSetValues({ editModeKilnDetails: kilnDetails })
		}
	}, [editMode, kilnDetails, editModeSetValues])

	useEffect(() => {
		if (addText) {
			form.setValue('name', addText)
		}
	}, [addText, form])

	useEffect(() => {
		if (siteDetailsAddress) {
			form.setValue('address', siteDetailsAddress)
		}
	}, [siteDetailsAddress, form])

	const selectedKilnType = form.watch('kilnType')
	const selectedKiln = KilnTypeLabelValue.find(
		(kiln) => kiln.value === selectedKilnType
	)
	const selectedTemperature = selectedKiln ? selectedKiln.temperature : 0

	useEffect(() => {
		if (selectedKilnType === kilnTypeEnum.RoCC) {
			form.unregister('temperature')
		} else if (selectedTemperature !== undefined) {
			form.setValue('temperature', selectedTemperature, {
				shouldValidate: true,
			})
		}
	}, [selectedKilnType, selectedTemperature, form])

	return {
		form,
		inputList,
		unit,
		addKilnMutation,
		calculateKilnVolume,
		coordinationTextFieldContent,
		handleAddKiln,
		getFarmers,
		errors,
		setMapCenter,
		cSinkNetworkId,
		// Image upload functions
		handleMainImageUpload,
		handleDimensionImageUpload,
		handleDimensionImageUploadWithUrl,
		getDimensionImageUrl,
		getDimensionFieldsConfig,
	}
}
