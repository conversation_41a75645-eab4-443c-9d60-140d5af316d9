import {
	kilnShapeEnum,
	kilnTypeEnum,
	measuringUnitEnum,
} from '@/utils/constant'
import { Close } from '@mui/icons-material'
import {
	Autocomplete,
	Box,
	Button,
	FormHelperText,
	IconButton,
	InputAdornment,
	MenuItem,
	Stack,
	styled,
	TextField,
	Typography,
} from '@mui/material'
import React, { useCallback } from 'react'
import { Controller, FormProvider, useFormContext } from 'react-hook-form'
import { ILabelWithValue } from '@/types'
import { LoadingButton } from '@mui/lab'
import { GoogleMapsWithNonDraggableMarker } from '../GoogleMap'
import { roundNumber } from '@/utils/helper'
import { IKilnWithDimensionImages } from '@/interfaces'
import { MultipleFileUploader } from '../MultipleFileUploader'
import { CustomTextField } from '@/utils/components'
import { AnyObjectSchema } from 'yup'
import { GridCloseIcon } from '@mui/x-data-grid'
import { theme } from '@/lib/theme/theme'
import {
	BaseInput,
	useAddKilnWithDimensionImages,
} from './useAddKilnWithDimensionImages'
import {
	AddKilnWithDimensionImagesSchema,
	addKilnWithDimensionImagesSchema,
} from './schema'
import { DimensionFieldWithImage } from './DimensionFieldWithImage'
import { isRowInput } from '../AddKiln/AddKiln'

type Props = {
	kilnDetails?: IKilnWithDimensionImages
	editMode?: boolean
	addText?: string
	subheading?: string
	siteDetailsAddress?: string
	handleClose: () => void
}
export const AddKilnWithDimensionImages: React.FC<Props> = ({
	handleClose,
	editMode = false,
	kilnDetails,
	subheading,
	addText,
	siteDetailsAddress,
}) => {
	const {
		form,
		inputList,
		unit,
		addKilnMutation,
		calculateKilnVolume,
		coordinationTextFieldContent,
		handleAddKiln,
		getFarmers,
		errors,
		setMapCenter,
		cSinkNetworkId,
		// Image upload functions
		handleMainImageUpload,
		getDimensionImageUrl,
	} = useAddKilnWithDimensionImages({
		handleClose,
		editMode,
		kilnDetails,
		addText,
		siteDetailsAddress,
		isGlobalAddKiln: false,
	})

	const selectedKilnShape = form.watch('kilnShape')

	const valueUpToThreeDecimalPlaces = useCallback(
		(value: string) => {
			const regexForSingleDecPlace = /([0-9]*[.|,]{0,1}[0-9]{0,1})/s
			const regexForThreeDecPlace = /([0-9]*[.|,]{0,1}[0-9]{0,3})/s

			return (
				value.match(
					unit === 'm' ? regexForThreeDecPlace : regexForSingleDecPlace
				)?.[0] || ''
			)
		},
		[unit]
	)

	const getDimensionFields = () => {
		switch (selectedKilnShape) {
			case kilnShapeEnum.Cylindrical:
				return [
					{
						label: 'Diameter',
						instructionText: 'Measure diameter and upload the image',
						textField: 'diameter' as const,
						imageField: 'diameterImageIds' as const,
					},
					{
						label: 'Height',
						instructionText: 'Measure height and upload the image',
						textField: 'depth' as const,
						imageField: 'depthImageIds' as const,
					},
				]
			case kilnShapeEnum.Rectangular:
				return [
					{
						label: 'Length',
						instructionText: 'Measure length and upload the image',
						textField: 'length' as const,
						imageField: 'frustumLengthImageIds' as const,
					},
					{
						label: 'Short Base',
						instructionText: 'Measure breadth and upload the image',
						textField: 'shortBase' as const,
						imageField: 'shortBaseImageIds' as const,
					},
					{
						label: 'Long Base',
						instructionText: 'Measure Long Base and upload the image',
						textField: 'longBase' as const,
						imageField: 'longBaseImageIds' as const,
					},
					{
						label: 'Depth',
						instructionText: 'Measure Depth and upload the image',
						textField: 'depth' as const,
						imageField: 'depthImageIds' as const,
					},
				]
			case kilnShapeEnum.Pyramidal:
				return [
					{
						label: 'Upper Surface Diameter',
						instructionText:
							'Measure Upper Surface Diameter and upload the image',
						textField: 'upperSurfaceDiameter' as const,
						imageField: 'upperSideImageIds' as const,
					},
					{
						label: 'Lower Surface Diameter',
						instructionText:
							'Measure Lower Surface Diameter and upload the image',
						textField: 'lowerSurfaceDiameter' as const,
						imageField: 'lowerSideImageIds' as const,
					},
					{
						label: 'Depth',
						instructionText: 'Measure Depth and upload the image',
						textField: 'depth' as const,
						imageField: 'depthImageIds' as const,
					},
				]
			default: // Conical
				return [
					{
						label: 'Upper Surface Diameter',
						instructionText:
							'Measure Upper Surface Diameter and upload the image',
						textField: 'upperSurfaceDiameter' as const,
						imageField: 'upperSurfaceDiameterImageIds' as const,
					},
					{
						label: 'Lower Surface Diameter',
						instructionText:
							'Measure Lower Surface Diameter and upload the image',
						textField: 'lowerSurfaceDiameter' as const,
						imageField: 'lowerSurfaceDiameterImageIds' as const,
					},
					{
						label: 'Depth',
						instructionText: 'Measure Depth and upload the image',
						textField: 'depth' as const,
						imageField: 'depthImageIds' as const,
					},
				]
		}
	}

	return (
		<FormProvider {...form}>
			<StyleContainer>
				<Stack className='header'>
					<Stack
						direction='row'
						spacing={1}
						alignItems='center'
						width='100%'
						justifyContent='space-between'>
						<Stack>
							<Typography variant='h5'>
								{editMode ? 'Edit' : 'Add'} Kiln
							</Typography>
							<Typography variant='subtitle1'>{subheading}</Typography>
						</Stack>
						<IconButton onClick={handleClose}>
							<Close />
						</IconButton>
					</Stack>
				</Stack>
				<Stack className='container'>
					{inputList
						.filter(
							(item) =>
								![
									'diameter',
									'depth',
									'length',
									'shortBase',
									'longBase',
									'upperSurfaceDiameter',
									'lowerSurfaceDiameter',
								].includes((item as BaseInput).value)
						)
						.map((item, index) =>
							isRowInput(item) ? (
								<Stack key={index} direction='row' spacing={2}>
									{item.children.map((child) => (
										<Box flex={1} key={child.value}>
											<RenderComponent
												type={child.type}
												label={child.label}
												schema={addKilnWithDimensionImagesSchema(
													!!cSinkNetworkId
												)}
												name={
													child.value as keyof AddKilnWithDimensionImagesSchema
												}
												forCalculation={!!child?.forCalculation}
												isNumericalInput={!!child?.isNumericInput}
												disabled={!!child?.disabled}
												unit={unit}
												options={child?.options || []}
												calculateKilnVolume={calculateKilnVolume}
												isLoading={getFarmers.isLoading}
											/>
										</Box>
									))}
								</Stack>
							) : (
								<RenderComponent
									key={item.value}
									type={item.type}
									label={item.label}
									schema={addKilnWithDimensionImagesSchema(!!cSinkNetworkId)}
									name={item.value as keyof AddKilnWithDimensionImagesSchema}
									forCalculation={!!item?.forCalculation}
									isNumericalInput={!!item?.isNumericInput}
									disabled={!!item?.disabled}
									unit={unit}
									options={item?.options || []}
									calculateKilnVolume={calculateKilnVolume}
									isLoading={getFarmers.isLoading}
								/>
							)
						)}

					{/* Dimension Fields with Images */}
					<Stack gap={theme.spacing(2)}>
						{getDimensionFields().map(
							({ label, instructionText, textField, imageField }) => (
								<DimensionFieldWithImage
									key={`${textField}-${imageField}-textfield`}
									label={label}
									instructionText={instructionText}
									textFieldName={textField}
									imageFieldName={imageField}
									form={form}
									calculateKilnVolume={calculateKilnVolume}
									valueUpToThreeDecimalPlaces={valueUpToThreeDecimalPlaces}
									errors={errors}
									schema={addKilnWithDimensionImagesSchema(!!cSinkNetworkId)}
									getDimensionImageUrl={getDimensionImageUrl}
								/>
							)
						)}
					</Stack>

					<Stack>
						<Typography variant='subtitle1'>
							Total Volume: {roundNumber(form.watch('volume') || 0)}
						</Typography>
						{form.watch('volume') < 10 && (
							<Typography variant='subtitle1' color='error'>
								Warning: Volume of the klin is too low
							</Typography>
						)}
					</Stack>
					<Stack gap={theme.spacing(2)}>
						<Stack>
							<Typography variant='subtitle1'>Kiln Image</Typography>
							<Typography
								variant='body1'
								color='text.secondary'
								fontSize={theme.spacing(1.5)}
								fontWeight={theme.typography.caption.fontWeight}>
								Please enter the Images after measuring the Kiln Dimensions
							</Typography>
						</Stack>
						<MultipleFileUploader
							heading='Add Kiln Image'
							sx={{
								height: { xs: 100, md: 166 },
								width: '100%',
							}}
							data={kilnDetails?.imageURLs}
							imageHeight={100}
							setUploadData={handleMainImageUpload}
							required
							training={false}
							fileType={['png', 'jpg', 'jpeg', 'webp']}
						/>
						<FormHelperText error={Boolean(errors.images)}>
							{errors?.images?.message}
						</FormHelperText>
					</Stack>
					<Stack direction='row' columnGap={1}>
						{coordinationTextFieldContent.map((item) => (
							<RenderComponent
								type={item.type}
								label={item.label}
								schema={addKilnWithDimensionImagesSchema(!!cSinkNetworkId)}
								key={item.label}
								name={item.value as keyof AddKilnWithDimensionImagesSchema}
								isNumericalInput={!item?.isNumericInput}
							/>
						))}
					</Stack>
					<GoogleMapsWithNonDraggableMarker
						center={{
							lat: Number(form.watch('latitude')),
							lng: Number(form.watch('longitude')),
						}}
						setMapCenter={setMapCenter}
						mapContainerStyle={{
							width: '100%',
							height: 300,
							position: 'relative',
						}}
					/>
					<Stack
						direction='row'
						gap={2}
						justifyContent='space-between'
						className='buttonContainer'>
						<Button
							onClick={handleClose}
							sx={{ border: ` 1px solid ${theme.palette.primary.main}` }}>
							Cancel
						</Button>
						<LoadingButton
							loading={addKilnMutation.isPending}
							disabled={addKilnMutation.isPending}
							onClick={form?.handleSubmit(handleAddKiln)}
							variant='contained'>
							Add
						</LoadingButton>
					</Stack>
				</Stack>
			</StyleContainer>
		</FormProvider>
	)
}

type RenderComponentProps = {
	type: string
	name: keyof AddKilnWithDimensionImagesSchema
	options?: ILabelWithValue[]
	label: string
	unit?: measuringUnitEnum
	isNumericalInput?: boolean
	forCalculation?: boolean
	schema?: AnyObjectSchema
	disabled?: boolean
	calculateKilnVolume?: (column?: string, value?: string) => void
	isLoading?: boolean
}

const RenderComponent: React.FC<RenderComponentProps> = ({
	type,
	name,
	options,
	label,
	unit,
	isNumericalInput,
	schema,
	forCalculation,
	disabled,
	calculateKilnVolume,
	isLoading = false,
}) => {
	const {
		register,
		formState: { errors },
		setValue,
		getValues,
		watch,
		clearErrors,
		reset,
	} = useFormContext<AddKilnWithDimensionImagesSchema>()

	const valueUpToThreeDecimalPlaces = useCallback(
		(value: string) => {
			const regexForSingleDecPlace = /([0-9]*[.|,]{0,1}[0-9]{0,1})/s
			const regexForThreeDecPlace = /([0-9]*[.|,]{0,1}[0-9]{0,3})/s

			return (
				value.match(
					unit === 'm' ? regexForThreeDecPlace : regexForSingleDecPlace
				)?.[0] || ''
			)
		},
		[unit]
	)

	const handleValueChange = useCallback(
		(key: keyof AddKilnWithDimensionImagesSchema, value: string) => {
			if (!value.includes('.')) return
			setValue(key, valueUpToThreeDecimalPlaces(value))
		},
		[setValue, valueUpToThreeDecimalPlaces]
	)
	const resetDimensionFields = useCallback(() => {
		setValue('upperSurfaceDiameter', '')
		setValue('lowerSurfaceDiameter', '')
		setValue('depth', '')
		setValue('volume', 0)
		setValue('diameter', '')
		setValue('length', '')
		setValue('shortBase', '')
		setValue('longBase', '')
	}, [setValue])
	const handleSelectChange = useCallback(
		(type: string, value: string) => {
			if (type === 'selectKiln') {
				setValue('selectKiln', value)
			} else if (type === 'kilnType') {
				setValue('kilnType', value)
				if (value === kilnTypeEnum.RoCC) {
					setValue('kilnShape', kilnShapeEnum.Cylindrical)
				}
				clearErrors('kilnType')
			} else if (type === 'unit') {
				setValue('unit', value)
				resetDimensionFields()
			} else {
				setValue('kilnShape', value)
				clearErrors('kilnShape')
				resetDimensionFields()
			}
		},
		[clearErrors, setValue, resetDimensionFields]
	)

	const onClear = () => {
		reset()
	}

	switch (type) {
		case 'textField':
			return (
				<CustomTextField
					schema={schema}
					label={label}
					fullWidth
					disabled={disabled}
					{...register(name)}
					watch={watch}
					hideNumberArrows={isNumericalInput}
					error={!!errors?.[name]?.message}
					helperText={errors?.[name]?.message}
					onInputCapture={(e: React.ChangeEvent<HTMLInputElement>) => {
						if (forCalculation) {
							calculateKilnVolume?.(
								name,
								valueUpToThreeDecimalPlaces(e.target.value)
							)
							handleValueChange(
								name as keyof AddKilnWithDimensionImagesSchema,
								e.target.value
							)
						}
					}}
				/>
			)
		case 'select':
			return (
				<CustomTextField
					schema={schema}
					label={label}
					select
					fullWidth
					value={watch(name)}
					{...register(name)}
					className='input-disable-class'
					error={!!errors?.[name]?.message}
					InputProps={{
						endAdornment: !!getValues('selectKiln') &&
							name === 'selectKiln' && (
								<InputAdornment
									sx={{
										paddingRight: 2,
									}}
									position='end'>
									<IconButton
										aria-label='clear field'
										onClick={onClear}
										edge='end'
										size='small'>
										<GridCloseIcon fontSize='small' />
									</IconButton>
								</InputAdornment>
							),
					}}
					disabled={disabled}
					helperText={errors?.[name]?.message}
					onChange={(e) => handleSelectChange(name, e.target.value)}>
					{(options || [])?.map((option) => (
						<MenuItem key={option.value} value={option.value}>
							{option.label}
						</MenuItem>
					))}
				</CustomTextField>
			)
		case 'autocomplete':
			return (
				<Controller
					name={name}
					render={({ field }) => (
						<Autocomplete
							{...field}
							options={options || []}
							getOptionLabel={(option) => option.label}
							value={options?.find((opt) => opt.value === field.value) || null}
							onChange={(_, newValue) => {
								field.onChange(newValue?.value || '')
							}}
							disabled={disabled}
							loading={isLoading}
							renderInput={(params) => (
								<TextField
									{...params}
									label='Select Farmer'
									variant='outlined'
									placeholder='Search by name or phone number'
									error={!!errors?.[name]?.message}
									helperText={errors?.[name]?.message}
								/>
							)}
						/>
					)}
				/>
			)

		default:
			return null
	}
}

const StyleContainer = styled(Stack)(({ theme }) => ({
	gap: theme.spacing(2),
	overflowX: 'hidden',
	'.header': {
		flexDirection: 'row',
		justifyContent: 'space-between',
		alignItems: 'center',
		padding: theme.spacing(4.5, 2),
		borderBottom: `${theme.spacing(0.125)} solid ${
			theme.palette.neutral['100']
		}`,
	},
	'.tabList': {
		'& .MuiTabs-scroller': {
			overflow: 'auto !important',
		},
		'& ::-webkit-scrollbar': {
			display: 'none',
		},
		'-ms-overflow-style': 'none' /* IE and Edge */,
		'scrollbar-width': 'none' /* Firefox */,
	},
	'.container': {
		height: '100%',
		padding: theme.spacing(2, 3),
		gap: theme.spacing(2.5),
		'.buttonContainer button': {
			width: theme.spacing(30),
			height: theme.spacing(4.5),
			padding: theme.spacing(1, 2.5),
			marginBottom: theme.spacing(10),
		},
	},
}))
