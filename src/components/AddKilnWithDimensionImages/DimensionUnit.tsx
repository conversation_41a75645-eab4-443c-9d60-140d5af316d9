import { MenuItem, Select } from '@mui/material'

interface IProps {
	unit: string
	setUnit: (value: string) => void
	onChange: () => void
}
export const DimensionUnit = ({ unit, setUnit, onChange }: IProps) => (
	<Select
		value={unit}
		onChange={(e) => {
			setUnit(e.target.value)
			onChange()
		}}
		sx={{
			'& fieldset': {
				border: 'none',
			},
		}}>
		<MenuItem value='cm'>cm</MenuItem>
		<MenuItem value='m'>m</MenuItem>
	</Select>
)
