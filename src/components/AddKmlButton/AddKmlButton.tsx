import { IFarm, ILocation } from '@/interfaces'
import { generateKML, generateKMLForFPU } from '@/utils/helper/generateKML'
import { Download, AddCircle, RemoveRedEyeOutlined } from '@mui/icons-material'
import { Button, ButtonProps, IconButton, Tooltip } from '@mui/material'
import { useCallback } from 'react'
import { SetURLSearchParams } from 'react-router-dom'

interface IData {
	name?: string
	landmark?: string
	size?: string
	sizeUnit?: string
	coordinate?: string
	farmArea: ILocation[]
	id?: string
	siteCoordinateLat?: string
	siteCoordinateLng?: string
}
interface IProps extends ButtonProps {
	data: IData
	setSearchParams: SetURLSearchParams
	setShowMap: (bool: boolean) => void
	addButtonText?: string
	downloadButton?: boolean
	showKMLButton?: boolean
	isFpu?: boolean
	showButtonText?: string
	farmId?: string
	handleViewKMLFile?: (
		farmCoordinates: ILocation[],
		center: { x?: string; y?: string },
		farmId: string
	) => void
}

export const AddKmlButton = ({
	data,
	setSearchParams,
	setShowMap,
	handleViewKMLFile,
	addButtonText,
	showButtonText,
	isFpu = false,
	farmId,
	showKMLButton = false,
	downloadButton = false,
	...props
}: IProps) => {
	const convertStringToArray = useCallback((farmLocation: string): string[] => {
		const coordinate: string[] = farmLocation.slice(1, -1).split(',')
		return coordinate
	}, [])

	if (data?.farmArea?.length) {
		return (
			<>
				{showKMLButton ? (
					<Tooltip title={'View KML'}>
						<IconButton
							{...props}
							onClick={(e) => {
								e.stopPropagation()
								e.preventDefault()
								const location = convertStringToArray(
									data?.coordinate ?? '(0,0)'
								)

								handleViewKMLFile?.(
									data?.farmArea,
									data.siteCoordinateLat
										? { x: data.siteCoordinateLat, y: data.siteCoordinateLng }
										: { x: location[0], y: location[1] },
									data?.id ?? ''
								)
							}}>
							<RemoveRedEyeOutlined />
						</IconButton>
					</Tooltip>
				) : (
					<Button
						variant='text'
						onClick={(e) => {
							e.preventDefault()
							e.stopPropagation()
							const location = convertStringToArray(data?.coordinate ?? '(0,0)')

							handleViewKMLFile?.(
								data?.farmArea,
								{ x: location[0], y: location[1] },
								data?.id ?? ''
							)
						}}
						{...props}>
						{showButtonText ?? 'View File'}
					</Button>
				)}

				{downloadButton ? (
					<Tooltip title={'Download KML'}>
						<IconButton
							variant='text'
							onClick={(event) => {
								event.stopPropagation()
								isFpu
									? generateKMLForFPU({
											...data,
									  } as IFarm)
									: generateKML({
											...data,
									  } as IFarm)
							}}
							{...props}>
							<Download />
						</IconButton>
					</Tooltip>
				) : (
					<Download
						onClick={(event) => {
							event.stopPropagation()
							isFpu
								? generateKMLForFPU({
										...data,
								  } as IFarm)
								: generateKML({
										...data,
								  } as IFarm)
						}}
					/>
				)}
			</>
		)
	}
	return (
		<Button
			variant='text'
			onClick={(e) => {
				e.stopPropagation()
				const location = convertStringToArray(data?.coordinate ?? '(0,0)')
				setSearchParams(
					(urlParams) => {
						urlParams.set('networkId', data?.id ?? '')
						urlParams.set('lat', location[0])
						urlParams.set('long', location[1])
						farmId && urlParams.set('farmId', farmId)
						return urlParams
					},
					{ replace: true }
				)
				setShowMap(true)
			}}
			startIcon={<AddCircle />}
			{...props}
			sx={{
				alignSelf: 'end',
			}}>
			{addButtonText ?? 'Add'}
		</Button>
	)
}
