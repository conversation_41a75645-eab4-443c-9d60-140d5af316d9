import * as Yup from 'yup'
export const addSite = Yup.object({
	name: Yup.string().required('Please enter kiln name'),
	address: Yup.string().required('Please enter address'),
	latitude: Yup.number()
		.required('Please enter latitude')
		.typeError('Please enter latitude'),
	longitude: Yup.number()
		.required('Please enter longitude')
		.typeError('Please enter longitude'),
})

export type AddSiteSchema = Yup.InferType<typeof addSite>
