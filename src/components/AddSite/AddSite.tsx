import { Close } from '@mui/icons-material'
import {
	Button,
	IconButton,
	Stack,
	styled,
	Typography,
	useTheme,
} from '@mui/material'
import React, { useCallback, useEffect } from 'react'
import { useForm } from 'react-hook-form'
import { addSite, AddSiteSchema } from './schema'
import { yupResolver } from '@hookform/resolvers/yup'
import { LoadingButton } from '@mui/lab'
import { GoogleMapsWithNonDraggableMarker } from '../GoogleMap'
import { useMutation, useQueryClient } from '@tanstack/react-query'
import { authAxios } from '@/contexts'
import { useParams } from 'react-router-dom'
import { toast } from 'react-toastify'
import { AxiosError } from 'axios'
import { CustomTextField } from '@/utils/components'
import { ISite } from '@/interfaces'

type Props = {
	handleClose: () => void
	siteDetails?: ISite
}

const initialValues = {
	name: '',
	latitude: 0,
	longitude: 0,
	address: '',
}

export const AddSite: React.FC<Props> = ({ handleClose, siteDetails }) => {
	const theme = useTheme()
	const { artisanProId } = useParams()

	const {
		register,
		handleSubmit,
		formState: { errors },
		setValue,
		watch,
	} = useForm<AddSiteSchema>({
		defaultValues: initialValues,
		mode: 'all',
		resolver: yupResolver<AddSiteSchema>(addSite),
	})

	const queryClient = useQueryClient()

	const setMapCenter = useCallback(
		(lat: number, lng: number) => {
			setValue('latitude', Number(lat.toFixed(6)))
			setValue('longitude', Number(lng.toFixed(6)))
		},
		[setValue]
	)

	const getCurrentLocation = useCallback(() => {
		navigator.geolocation.getCurrentPosition((position) => {
			setMapCenter(position.coords.latitude, position.coords.longitude)
		})
	}, [setMapCenter])

	const addKilnMutation = useMutation({
		mutationKey: ['addSite'],
		mutationFn: async (values: AddSiteSchema) => {
			const payload = {
				name: String(values.name),
				address: String(values.address),
				latitude: Number(values.latitude),
				longitude: Number(values.longitude),
			}

			const { data } = await authAxios.post(
				`artisian-pro/${artisanProId}/site`,
				payload
			)
			return data
		},
		onError: (err: AxiosError) => {
			toast((err?.response?.data as { messageToUser: string })?.messageToUser)
		},
		onSuccess: (data) => {
			toast(data?.message)
			handleClose()
			queryClient.refetchQueries({
				queryKey: ['allSites'],
			})
			queryClient.refetchQueries({
				queryKey: ['artisanPros'],
			})
			queryClient.refetchQueries({
				queryKey: ['artisanProDetail'],
			})
		},
	})

	const editKlinMutation = useMutation({
		mutationKey: ['editSite'],
		mutationFn: async (values: AddSiteSchema) => {
			const payload = {
				name: String(values.name),
			}
			const { data } = await authAxios.put(
				`artisian-pro/${artisanProId}/site/${siteDetails?.id}`,
				payload
			)
			return data
		},
		onError: (err: AxiosError) => {
			toast((err?.response?.data as { messageToUser: string })?.messageToUser)
		},
		onSuccess: (data) => {
			toast(data?.message)
			handleClose()
			queryClient.refetchQueries({
				queryKey: ['allSites'],
			})
		},
	})

	const handleAddSite = useCallback(
		(values: AddSiteSchema) => {
			addKilnMutation.mutate(values)
		},
		[addKilnMutation]
	)

	const handleEditSite = useCallback(
		(values: AddSiteSchema) => {
			editKlinMutation.mutate(values)
		},
		[editKlinMutation]
	)

	useEffect(() => {
		if (!siteDetails) {
			getCurrentLocation()
		}
	}, [getCurrentLocation, siteDetails])

	useEffect(() => {
		setValue('name', siteDetails?.name ??" ")
		setValue('address', siteDetails?.address ??" ")
		const match = siteDetails?.coordinate.match(/\(([^,]+),([^)]+)\)/)
		if (match) {
			const latitude = parseFloat(match[1].trim())
			const longitude = parseFloat(match[2].trim())
			setValue('latitude', latitude)
			setValue('longitude', longitude)
		}
	}, [siteDetails])

	return (
		<StyleContainer>
			<Stack className='header'>
				<Stack
					direction='row'
					spacing={1}
					alignItems='center'
					width='100%'
					justifyContent='space-between'>
					<Typography variant='h5'>
						{!!siteDetails ? 'Edit Site' : 'Add Site'}
					</Typography>
					<IconButton onClick={handleClose}>
						<Close />
					</IconButton>
				</Stack>
			</Stack>
			<Stack
				className='container'
				component='form'
				onSubmit={handleSubmit(handleAddSite)}
				rowGap={2}
				mt={1}>
				<Stack direction='column' gap={5}>
					<Stack rowGap={2} width='100%'>
						<CustomTextField
							schema={addSite}
							id='name'
							label='Enter name'
							variant='outlined'
							{...register('name')}
							fullWidth
							error={!!errors.name?.message}
							helperText={errors.name?.message}
						/>
						<CustomTextField
							disabled={!!siteDetails}
							schema={addSite}
							id='Address'
							label='Enter Address'
							variant='outlined'
							{...register('address')}
							fullWidth
							error={!!errors.address?.message}
							helperText={errors.address?.message}
						/>
						<Stack display='flex' flexDirection='row' gap={2}>
							<CustomTextField
								disabled={!!siteDetails}
								schema={addSite}
								id='latitude'
								label='Latitude'
								variant='outlined'
								type='number'
								inputProps={{
									step: 'any',
								}}
								{...register('latitude', {
									setValueAs: (value) => Number(parseFloat(value).toFixed(6)),
								})}
								fullWidth
								error={!!errors.latitude?.message}
								helperText={errors.latitude?.message}
							/>
							<CustomTextField
								disabled={!!siteDetails}
								schema={addSite}
								id='longitude'
								label='Longitude'
								variant='outlined'
								type='number'
								inputProps={{
									step: 'any',
								}}
								{...register('longitude', {
									setValueAs: (value) => Number(parseFloat(value).toFixed(6)),
								})}
								fullWidth
								error={!!errors.longitude?.message}
								helperText={errors.longitude?.message}
							/>
						</Stack>
					</Stack>
				</Stack>
				<GoogleMapsWithNonDraggableMarker
					dragEnabled={!siteDetails}
					center={{
						lat: Number(watch('latitude')),
						lng: Number(watch('longitude')),
					}}
					setMapCenter={setMapCenter}
					mapContainerStyle={{
						width: '100%',
						height: 300,
						position: 'relative',
					}}
				/>
				<Stack
					direction='row'
					gap={2}
					justifyContent='space-between'
					className='buttonContainer'>
					<Button
						onClick={handleClose}
						sx={{ border: ` 1px solid ${theme.palette.primary.main}` }}>
						Cancel
					</Button>
					{!!siteDetails ? (
						<LoadingButton
							loading={editKlinMutation.isPending}
							onClick={handleSubmit(handleEditSite)}
							variant='contained'>
							Edit
						</LoadingButton>
					) : (
						<LoadingButton
							loading={addKilnMutation.isPending}
							onClick={handleSubmit(handleAddSite)}
							variant='contained'>
							Add
						</LoadingButton>
					)}
				</Stack>
			</Stack>
		</StyleContainer>
	)
}

const StyleContainer = styled(Stack)(({ theme }) => ({
	gap: theme.spacing(1),
	'.header': {
		flexDirection: 'row',
		justifyContent: 'space-between',
		alignItems: 'center',
		padding: theme.spacing(3),
		borderBottom: `${theme.spacing(0.125)} solid ${
			theme.palette.neutral['100']
		}`,
	},
	'.tabList': {
		'& .MuiTabs-scroller': {
			overflow: 'auto !important',
		},
		'& ::-webkit-scrollbar': {
			display: 'none',
		},
		'-ms-overflow-style': 'none',
		'scrollbar-width': 'none',
	},
	'.container': {
		height: '100%',
		padding: theme.spacing(2, 3),
		gap: theme.spacing(2.5),
		'.buttonContainer button': {
			width: theme.spacing(30),
			height: theme.spacing(4.5),
			padding: theme.spacing(1, 2.5),
			marginBottom: theme.spacing(10),
		},
	},
}))
