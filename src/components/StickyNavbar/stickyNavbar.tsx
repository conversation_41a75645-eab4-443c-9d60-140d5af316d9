import { useDrawerStore } from '@/contexts'
import { Stack, styled, useTheme } from '@mui/material'
import { ReactNode } from 'react'

interface IProps {
	stickyComponent: ReactNode
}

export const StickyNavbar = ({ stickyComponent }: IProps) => {
	const theme = useTheme()
	const { isDrawerOpen } = useDrawerStore()
	return (
		<StyledNavbar
			sx={{
				width: `calc( 100% - ${theme.spacing(isDrawerOpen ? 31.25 : 7.5)} )`,
			}}>
			{stickyComponent}
		</StyledNavbar>
	)
}

const StyledNavbar = styled(Stack)(({ theme }) => ({
	minHeight: theme.spacing(10),
	position: 'fixed',
	top: 0,
	zIndex: 1,
	background: theme.palette.common.white,
	border: `${theme.spacing(0.125)} solid ${theme.palette.neutral['100']}`,
	borderRadius: theme.spacing(2),
	padding: theme.spacing(2),
	flexDirection: 'row',
	justifyContent: 'space-between',
	animation: 'fadeIn 0.7s',
	flexWrap: 'wrap',
	'@keyframes fadeIn': {
		from: { opacity: 0 },
		to: { opacity: 1 },
	},
}))
