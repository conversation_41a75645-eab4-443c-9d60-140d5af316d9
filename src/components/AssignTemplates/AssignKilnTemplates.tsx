import { useMemo } from 'react'
import { useParams } from 'react-router-dom'
import {
	GlobalKilnResponse,
	IArtisanProDetails,
	IContainerDetails,
	INetwork,
} from '@/interfaces'
import { useMutation, useQuery, useQueryClient } from '@tanstack/react-query'
import { authAxios } from '@/contexts'
import { toast } from 'react-toastify'
import { AxiosError } from 'axios'
import { AssignEntityListSideDrawer } from '../AssignEntityListSideDrawer'

interface TProps {
	handleCloseDrawer: () => void
	cSinkNetworkDetails?: INetwork
	artisanProDetails?: IArtisanProDetails
	subheading?: string
	isCsink?: boolean
	isKiln?: boolean
}

const postApi = (
	isCsink?: boolean,
	isKiln?: boolean,
	artisanProId?: string,
	csinkNetworkId?: string
) => {
	if (isKiln) {
		return isCsink
			? `/cs-network/${csinkNetworkId}/kiln-template/assign`
			: `/artisian-pro/${artisanProId}/kiln-template/assign`
	}
	return isCsink
		? `/cs-network/${csinkNetworkId}/measuring-container-template/assign`
		: `/artisian-pro/${artisanProId}/measuring-container-template/assign`
}

const getApiforMeasuringContainers = (
	isCsink?: boolean,
	artisanProId?: string,
	csinkNetworkId?: string
) => {
	if (isCsink)
		return `/cs-network/${csinkNetworkId}/measuring-container-template?isAssigned=false`
	return `/artisian-pro/${artisanProId}/measuring-container-template?isAssigned=false`
}
const getApiforGlobalKilns = (
	isCsink?: boolean,
	artisanProId?: string,
	csinkNetworkId?: string
) => {
	if (isCsink)
		return `/cs-network/${csinkNetworkId}/kiln-template?isAssigned=false`
	return `/artisian-pro/${artisanProId}/kiln-template?isAssigned=false`
}
export const AssignKilnTemplates = ({
	handleCloseDrawer,
	subheading,
	isKiln = true,
	isCsink = true,
}: TProps) => {
	const { cSinkNetworkId, artisanProId } = useParams()
	const queryClient = useQueryClient()

	const assignedKilnsListQuery = useQuery({
		queryKey: [
			'unassignedKilnsListQuery',
			isCsink,
			artisanProId,
			cSinkNetworkId,
		],
		queryFn: async () => {
			const { data } = await authAxios<GlobalKilnResponse>(
				getApiforGlobalKilns(isCsink, artisanProId, cSinkNetworkId)
			)
			return data
		},
		enabled: isKiln,
	})
	const assignedMeasuringContainersListQuery = useQuery({
		queryKey: [
			'unassignedMeasuringContainersListQuery',
			artisanProId,
			cSinkNetworkId,
			isCsink,
		],
		queryFn: async () => {
			const { data } = await authAxios<{
				containers: IContainerDetails[]
			}>(getApiforMeasuringContainers(isCsink, artisanProId, cSinkNetworkId))
			return data
		},
		enabled: !isKiln,
	})

	const options = useMemo(() => {
		if (isKiln) return assignedKilnsListQuery?.data?.kilns
		return assignedMeasuringContainersListQuery?.data?.containers
	}, [isKiln, assignedKilnsListQuery, assignedMeasuringContainersListQuery])

	const assignMutation = useMutation({
		mutationKey: [
			'addPrefferedBiomass',
			isCsink,
			isKiln,
			artisanProId,
			cSinkNetworkId,
		],
		mutationFn: async (ids: string[]) => {
			const payload = {
				templateIds: ids,
			}

			return await authAxios.post(
				postApi(isCsink, isKiln, artisanProId, cSinkNetworkId),
				payload
			)
		},
		onError: (error: AxiosError) => {
			toast((error?.response?.data as { messageToUser: string })?.messageToUser)
		},
		onSuccess: ({ data }) => {
			toast(data?.message)
			queryClient.refetchQueries({
				queryKey: [
					isKiln
						? 'assignedKilnsListQuery'
						: 'assignedMeasuringContainersListQuery',
				],
			})
			handleCloseDrawer()
		},
	})
	return (
		<AssignEntityListSideDrawer
			handleCloseDrawer={handleCloseDrawer}
			handleAdd={(ids) => assignMutation?.mutate(ids)}
			heading={isKiln ? 'Assign Kilns' : 'Assign Measuring Container'}
			options={options}
			loading={assignMutation.isPending}
			subheading={subheading}
		/>
	)
}
