export type TCountryData = {
	name: string
	regions: string[]
	iso2: string
	dialCode: string
	format: string
	priority: number
	hasAreaCodes: boolean
}

export type TSelectedCountryDialog = {
	open: boolean
	close: () => void
	filterOutCountry: TCountryData[]
	searchCountry: string
	setSearchCountry: (value: React.SetStateAction<string>) => void
	setSelectedCountry: (country: TCountryData) => void
}

export type TPhoneInputComponent = {
	defaultCountry?: string
	value: string
	handleOnChange: (phoneInput: string, dialCode: string) => void
	inputStyle?: any
	specialLabel?: string
	dialCode?: string | null
	isValid?: boolean
	getSelectedCountryDialCode?: (dialCode: string) => void
	disabled?: boolean
	fullWidth?:boolean
	smallSize?:boolean
}
