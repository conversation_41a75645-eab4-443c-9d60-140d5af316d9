import { Cancel } from '@mui/icons-material'
import {
	Dialog,
	DialogContent,
	DialogTitle,
	IconButton,
	ListItemIcon,
	ListItemText,
	MenuItem,
	TextField,
	Typography,
} from '@mui/material'
import { FlagIcon } from './FlagIcon'
import { TSelectedCountryDialog } from './phoneInput.types'

export const SelectCountryDialog = ({
	open,
	close,
	filterOutCountry,
	searchCountry,
	setSearchCountry,
	setSelectedCountry,
}: TSelectedCountryDialog) => (
	<Dialog maxWidth='sm' fullWidth open={open} onClose={close}>
		<IconButton sx={{ position: 'absolute', top: 0, right: 0 }} onClick={close}>
			<Cancel />
		</IconButton>
		<DialogTitle textAlign='center'>Select Country</DialogTitle>
		<DialogContent
			sx={{
				'input::-webkit-search-cancel-button': {
					cursor: 'pointer',
					fontSize: '20px',
				},
				pb: 0,
			}}>
			<TextField
				fullWidth
				placeholder='Enter Country Name'
				value={searchCountry}
				sx={{ mt: 1 }}
				onChange={(e) => setSearchCountry(e.target.value)}
				inputProps={{
					type: 'search',
				}}
			/>
		</DialogContent>
		<DialogContent sx={{ height: '300px' }}>
			{filterOutCountry?.length ? (
				filterOutCountry.map((country) => (
					<MenuItem
						onClick={() => {
							setSelectedCountry(country)
							close()
							setSearchCountry('')
						}}
						key={country.iso2}>
						<ListItemIcon>
							<FlagIcon code={country.iso2.toUpperCase()} />
						</ListItemIcon>
						<ListItemText>
							{country.name} (+{country.dialCode})
						</ListItemText>
					</MenuItem>
				))
			) : (
				<Typography textAlign='center' fontWeight={600}>
					Country not found
				</Typography>
			)}
		</DialogContent>
	</Dialog>
)
