import { <PERSON><PERSON>ropDown, Arrow<PERSON>ropUp } from '@mui/icons-material'
import { IconButton, Stack, TextField, Typography } from '@mui/material'
import { useCallback, useEffect, useMemo, useState } from 'react'
import { PatternFormat } from 'react-number-format'
import { CountryData, DEFAULT_NOT_FOUND_FORMAT } from './CountryData'
import { FlagIcon } from './FlagIcon'
import { SelectCountryDialog } from './SelectCountryDialog'
import { TCountryData, TPhoneInputComponent } from './phoneInput.types'
import { useMutation } from '@tanstack/react-query'
import axios from 'axios'
import { theme } from '@/lib/theme/theme'
import { toast } from 'react-toastify'
const { VITE_GOOGLE_MAPS_API_KEY } = import.meta.env

const smallInputStyle = {
	height: theme.spacing(6),
	'& .MuiInputBase-root': {
		height: '100%',
	},
	'& .MuiOutlinedInput-input': {
		padding: '8px 12px',
	},
}

const defaultInputStyle = {
	p: 0,
	'.MuiInputBase-input': {
		pl: 1,
	},
	minHeight: '58px',
}

export const PhoneInputComponent = ({
	defaultCountry = 'in',
	value,
	handleOnChange,
	inputStyle,
	specialLabel,
	dialCode,
	isValid,
	getSelectedCountryDialCode,
	disabled = false,
	fullWidth = false,
	smallSize = false,
}: TPhoneInputComponent) => {
	const [openCountryDialog, setOpenCountryDialog] = useState<boolean>(false)
	const [selectedCountry, setSelectedCountry] = useState<TCountryData>({
		name: '',
		regions: [],
		iso2: 'in',
		dialCode: '91',
		format: '.....-.....',
		priority: 0,
		hasAreaCodes: false,
	})

	const [searchCountry, setSearchCountry] = useState<string>('')

	const filterOutCountry = useMemo(
		() =>
			CountryData.filter(
				(country) =>
					country.name
						.toLowerCase()
						.includes(searchCountry.toString().toLowerCase()) ||
					country.dialCode.toString().includes(searchCountry.toString())
			),
		[searchCountry]
	)

	const phoneNumberFormat = useMemo(() => {
		const format =
			CountryData.find((i) => i.iso2 === selectedCountry.iso2)?.format ?? ''
		let newFormat = ''
		if (format.length > 0) {
			newFormat = format.replace(/\./g, '#')
			newFormat = newFormat.replace(/-/g, ' ')
			return newFormat
		}

		newFormat = DEFAULT_NOT_FOUND_FORMAT
		return newFormat
	}, [selectedCountry])

	useEffect(() => {
		if (dialCode) {
			const trimmedDialCode = dialCode.replace('+', '')
			const country = CountryData.find(
				(i) => i.dialCode === trimmedDialCode && i.priority === 0
			)
			if (country) {
				setSelectedCountry(country)
				return
			}
		}
		if (!dialCode && defaultCountry) {
			const country = CountryData.find((i) => i.iso2 === defaultCountry)
			if (country) {
				setSelectedCountry(country)
				return
			}
		}
		setSelectedCountry(CountryData[0])
	}, [defaultCountry, dialCode])

	const handleChange = useCallback(
		(value: string) => {
			const trimmedValue = value.replace(/[() ]/g, '')
			handleOnChange(trimmedValue, selectedCountry.dialCode)
		},
		[handleOnChange, selectedCountry]
	)

	const handleSelectCountry = useCallback(
		(country: TCountryData) => {
			setSelectedCountry(country)
			const formatLength = country.format.replace(/[()-]/g, '').length
			const newSlicedValue = value.slice(0, formatLength)
			handleChange(newSlicedValue)
			if (getSelectedCountryDialCode) {
				getSelectedCountryDialCode(`+${country.dialCode}`)
			}
		},
		[getSelectedCountryDialCode, handleChange, value]
	)

	const fetchCountryFromLatLng = async ({
		lat,
		lng,
	}: {
		lat: number
		lng: number
	}): Promise<string> => {
		const response = await axios.get(
			`https://maps.googleapis.com/maps/api/geocode/json`,
			{
				params: {
					latlng: `${lat},${lng}`,
					key: VITE_GOOGLE_MAPS_API_KEY,
				},
			}
		)

		const countryComponent = Array.isArray(response?.data?.results)
			? response?.data?.results
					.flatMap((result: any) => result?.address_components)
					.find((component: any) => component?.types?.includes('country'))
			: null
		return countryComponent?.long_name?.toLowerCase() ?? ''
	}

	const { mutate: getCountryFromCoords } = useMutation({
		mutationFn: fetchCountryFromLatLng,
		onSuccess: (countryName) => {
			const matchedCountry = CountryData.find(
				(country) => country.name.toLowerCase() === countryName.toLowerCase()
			)
			if (matchedCountry) setSelectedCountry(matchedCountry)
		},
	})

	const getCoordinates = (): Promise<GeolocationPosition> => {
		return new Promise((resolve, reject) => {
			const fetchLocation = () => {
				if (!navigator.geolocation) {
					reject(new Error('Geolocation is not supported'))

					return
				}
				navigator.geolocation.getCurrentPosition(resolve, reject)
			}

			if (navigator?.permissions) {
				navigator.permissions
					.query({ name: 'geolocation' })
					.then((permissionStatus) => {
						if (permissionStatus.state === 'denied') {
							toast(
								'Geolocation permission denied. Please enable it in your browser settings.'
							)
							reject(
								Object.assign(new Error('Geolocation permission denied'), {
									code: 'PERMISSION_DENIED',
								})
							)
							return
						}
						fetchLocation()
					})
					.catch(fetchLocation) // fallback if permissions query fails
			} else {
				fetchLocation()
			}
		})
	}

	const detectCountry = async () => {
		try {
			const pos = await getCoordinates()
			const lat = pos?.coords?.latitude
			const lng = pos?.coords?.longitude
			getCountryFromCoords({ lat, lng })
		} catch (err: any) {
			if (err.code !== 'PERMISSION_DENIED') {
				toast('Error in fetching country')
			}
		}
	}

	useEffect(() => {
		if (dialCode) return
		detectCountry()
	}, [])

	return (
		<>
			<Stack
				{...(fullWidth
					? {
							sx: {
								width: '100%',
							},
					  }
					: { maxWidth: 'sm' })}>
				<PatternFormat
					format={phoneNumberFormat}
					value={value}
					onChange={(e: any) => handleChange(e?.target?.value)}
					customInput={TextField}
					label={specialLabel ?? ''}
					sx={{
						...(smallSize ? smallInputStyle : defaultInputStyle),
						...inputStyle,
					}}
					{...(isValid ? { error: isValid } : {})}
					InputProps={{
						startAdornment: (
							<Stack direction='row' alignItems='center'>
								<IconButton
									disabled={disabled}
									onClick={() => setOpenCountryDialog(true)}
									disableRipple>
									<FlagIcon
										width='25px'
										height='20px'
										code={selectedCountry.iso2.toUpperCase()}
									/>
									{openCountryDialog ? <ArrowDropUp /> : <ArrowDropDown />}
								</IconButton>
								<Typography
									sx={{
										fontSize: '1rem',
										lineHeight: 'inherit',
										color: disabled ? 'grey' : 'inherit',
									}}>
									+{selectedCountry.dialCode}
								</Typography>
							</Stack>
						),
						sx: {
							...(disabled
								? {
										'& .Mui-disabled': {
											color: 'grey',
										},
										// eslint-disable-next-line no-mixed-spaces-and-tabs
								  }
								: {}),
						},
					}}
					disabled={disabled}
				/>
			</Stack>
			{openCountryDialog ? (
				<SelectCountryDialog
					open={openCountryDialog}
					close={() => setOpenCountryDialog(false)}
					filterOutCountry={filterOutCountry}
					searchCountry={searchCountry}
					setSearchCountry={setSearchCountry}
					setSelectedCountry={handleSelectCountry}
				/>
			) : null}
		</>
	)
}
