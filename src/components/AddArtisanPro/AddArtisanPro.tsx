import {
	Button,
	FormControl,
	FormControlLabel,
	FormHelperText,
	IconButton,
	InputAdornment,
	Radio,
	RadioGroup,
	Stack,
	TextField,
	Typography,
	styled,
} from '@mui/material'
import { theme } from '@/lib/theme/theme'
import { useForm } from 'react-hook-form'
import { Close } from '@mui/icons-material'
import { useCallback } from 'react'
import { PhoneInputComponent } from '..'
import { toast } from 'react-toastify'
import { authAxios } from '@/contexts'
import { useMutation, useQueryClient } from '@tanstack/react-query'
import { yupResolver } from '@hookform/resolvers/yup'
import { AddArtisanProSchema, TAddArtisanPro } from './schema'
import { MultipleFileUploader } from '../MultipleFileUploader'

interface IPayload {
	name: string
	address: string
	methaneCompensationStrategy?: string
	bighaInHectare: number
	manager: {
		name: string
		trainingImages?: string[]
		phoneNo: string
		countryCode: string
		trained: boolean
	}
}

export const AddArtisanPro = ({
	handleClose,
	networkId,
}: {
	handleClose: () => void
	networkId: string
}) => {
	const initialValues: TAddArtisanPro = {
		name: '',
		address: '',
		phoneNo: '',
		managerName: '',
		email: '',
		countryCode: '+91',
		trained: false,
		trainingImages: [],
		methaneCompensationStrategy: '',
		bighaInHectare: 0,
	}

	const {
		register,
		handleSubmit,
		formState: { errors },
		setValue,
		clearErrors,
		setError,
		watch,
	} = useForm<TAddArtisanPro>({
		defaultValues: initialValues,
		resolver: yupResolver<TAddArtisanPro>(AddArtisanProSchema),
	})
	const queryClient = useQueryClient()
	const hanldeSubmit = handleSubmit((formValues: TAddArtisanPro) => {
		const {
			name,
			address,
			managerName,
			trainingImages,
			phoneNo,
			countryCode,
			methaneCompensationStrategy,
			bighaInHectare,
			// language,
			...restValues
		} = formValues
		const payload = {
			name,
			address,
			methaneCompensationStrategy,
			bighaInHectare: Number(bighaInHectare),
			manager: {
				name: managerName,
				trainingImages: trainingImages?.map((img) => img?.id),
				phoneNo,
				countryCode,
				...restValues,
			},
		}

		mutate(payload)
	})

	const handleOnChange = useCallback(
		(value: string, dialCode: string) => {
			setValue('countryCode', `+${dialCode}`)

			setValue('phoneNo', `${value}`)
			if (value.length === 0) {
				setError('phoneNo', { message: 'Please enter phone number' })
				return
			}
			clearErrors('phoneNo')
		},
		[setValue]
	)

	const { mutate } = useMutation({
		mutationKey: ['addArtisanPro'],
		mutationFn: (data: IPayload) =>
			authAxios.post(`/artisan-pro-network/${networkId}/artisian-pro`, data),
		onSuccess: () => {
			toast('Artisan Pro added successfully')
			queryClient.refetchQueries({
				queryKey: ['artisanPros'],
			})
			handleClose()
		},
		onError: (error: Error) => {
			toast(error?.message)
		},
	})

	const handleTrainingChange = (event: React.ChangeEvent<HTMLInputElement>) => {
		const trainedValue =
			(event.target as HTMLInputElement).value === 'true' ? true : false
		setValue('trained', trainedValue)
		if (event.target.value !== 'true') {
			setValue('trainingImages', [])
		}
	}
	return (
		<StyleContainer>
			<Stack className='header'>
				<Stack
					direction='row'
					alignItems='center'
					width='100%'
					justifyContent='space-between'>
					<Typography variant='h4'>Add Artisan Pro</Typography>
					<IconButton
						// sx={{ position: 'absolute', right: 5, top: 5 }}
						onClick={handleClose}>
						<Close />
					</IconButton>
				</Stack>
			</Stack>
			<Stack className='container' component='form' onSubmit={hanldeSubmit}>
				<Stack gap={theme.spacing(2.5)}>
					<FormControl fullWidth className='formcontrol'>
						<Typography className='label' variant='subtitle1'>
							Name
						</Typography>
						<TextField
							id='name'
							placeholder='Full Name'
							variant='outlined'
							error={!!errors?.name}
							helperText={errors?.name?.message}
							{...register('name')}
						/>
					</FormControl>

					<FormControl fullWidth className='formcontrol'>
						<Typography className='label' variant='subtitle1'>
							Address
						</Typography>
						<TextField
							id='location'
							placeholder='Enter Your Address'
							variant='outlined'
							error={!!errors?.address}
							helperText={errors?.address?.message}
							{...register('address')}
						/>
					</FormControl>
					<FormControl fullWidth className='formcontrol'>
						<Typography className='label' variant='subtitle1'>
							Phone Number
						</Typography>
						<PhoneInputComponent
							value={watch('phoneNo')}
							handleOnChange={handleOnChange}
							getSelectedCountryDialCode={(dialCode) =>
								setValue('countryCode', dialCode)
							}
						/>

						<FormHelperText error={Boolean(errors.phoneNo)}>
							{errors?.phoneNo?.message}
						</FormHelperText>
					</FormControl>
					<FormControl fullWidth className='formcontrol'>
						<Typography className='label' variant='subtitle1'>
							Methane Compensation Strategy
						</Typography>
						<TextField
							id='methane'
							placeholder='Enter Methane Compensation Strategy'
							variant='outlined'
							error={!!errors?.methaneCompensationStrategy}
							helperText={errors?.methaneCompensationStrategy?.message}
							{...register('methaneCompensationStrategy')}
						/>
					</FormControl>
					<FormControl fullWidth className='formcontrol'>
						<Typography className='label' variant='subtitle1'>
							Bigha in Hectare
						</Typography>
						<TextField
							id='bigha'
							placeholder='Bigha in Hectare'
							type='number'
							error={!!errors?.bighaInHectare}
							helperText={errors?.bighaInHectare?.message}
							variant='outlined'
							{...register('bighaInHectare')}
							fullWidth
							sx={{
								'& input::-webkit-outer-spin-button, & input::-webkit-inner-spin-button':
									{
										display: 'none',
									},
								'& input[type=number]': {
									MozAppearance: 'textfield',
								},
							}}
							InputProps={{
								inputProps: {
									step: 'any',
									maxLength: 10,
								},
								endAdornment: (
									<InputAdornment position='end'>bigha</InputAdornment>
								),
								startAdornment: (
									<InputAdornment position='start'>1 hectare =</InputAdornment>
								),
							}}
						/>
					</FormControl>
					<FormControl fullWidth className='formcontrol'>
						<Typography className='label' variant='subtitle1'>
							Name of Manager
						</Typography>
						<TextField
							id='admin Name'
							placeholder='Manager Name'
							error={!!errors?.managerName}
							helperText={errors?.managerName?.message}
							variant='outlined'
							{...register('managerName')}
						/>
					</FormControl>
					<FormControl fullWidth className='formcontrol'>
						<Typography className='label' variant='subtitle1'>
							Email
						</Typography>
						<TextField
							id='name'
							placeholder='Email'
							error={!!errors?.email}
							helperText={errors?.email?.message}
							variant='outlined'
							{...register('email')}
						/>
					</FormControl>
					<FormControl fullWidth className='formcontrol'>
						<Typography className='label' variant='subtitle1'>
							Training Status
						</Typography>
						<RadioGroup
							row
							aria-labelledby='TrainingStatus'
							name='controlled-radio-buttons-group'
							value={watch('trained')}
							onChange={handleTrainingChange}>
							<FormControlLabel
								value='true'
								control={<Radio color='success' />}
								label='Trained'
							/>
							<FormControlLabel
								value='false'
								control={<Radio />}
								label='Not Trained'
							/>
						</RadioGroup>
					</FormControl>
					{watch('trained') && (
						<>
							<Stack rowGap={2} width='100%'>
								<MultipleFileUploader
									heading='Upload or Drag the Training Document '
									sx={{
										height: { xs: 100, md: 150 },
										width: '100%',
									}}
									imageHeight={100}
									setUploadData={(data) => {
										setValue('trainingImages', data)
										clearErrors('trainingImages')
									}}
								/>
							</Stack>
							<FormHelperText error={Boolean(errors.trainingImages)}>
								{errors?.trainingImages?.message}
							</FormHelperText>
						</>
					)}
				</Stack>
				<Stack
					direction='row'
					justifyContent='space-between'
					gap={2}
					className='buttonContainer'>
					<Button sx={{ border: ` 1px solid ${theme.palette.primary.main}` }}>
						Cancel
					</Button>{' '}
					<Button variant='contained' type='submit'>
						Save
					</Button>
				</Stack>
			</Stack>
		</StyleContainer>
	)
}

const StyleContainer = styled(Stack)(({ theme }) => ({
	gap: theme.spacing(2),
	'.header': {
		flexDirection: 'row',
		justifyContent: 'space-between',
		alignItems: 'center',
		padding: theme.spacing(3.5, 2),
		borderBottom: `${theme.spacing(0.125)} solid ${
			theme.palette.neutral['100']
		}`,
	},
	'.container': {
		padding: theme.spacing(2, 4),
		// overflowY: 'scroll',
		gap: theme.spacing(5),
		'.inputBox': {
			cursor: 'pointer',
		},
		'.formcontrol': {
			gap: theme.spacing(0.6),

			'.label': {
				color: theme.palette.neutral[500],
			},
		},
		'.buttonContainer button': {
			width: theme.spacing(30),
			height: theme.spacing(4.5),
			padding: theme.spacing(1, 2.5),
		},
	},
}))
