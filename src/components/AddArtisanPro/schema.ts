import * as Yup from 'yup'

export const AddArtisanProSchema = Yup.object({
	name: Yup.string().required('Please enter network name'),
	address: Yup.string().required('Please enter location'),
	phoneNo: Yup.string().required('Please enter phone number'),
	countryCode: Yup.string().required('dsad'),
	managerName: Yup.string().required('Please enter name'),
	email: Yup.string().required('Please enter Email'),
	trained: Yup.boolean().required(),
	trainingImages: Yup.array()
		.nullable()
		.when(['trained'], {
			is: (trained: boolean) => trained,
			then: (schema) =>
				schema
					.required('Please enter training images')
					.min(1, 'Please enter training images'),
			otherwise: (schema) => schema,
		}),
	methaneCompensationStrategy: Yup.string(),
	bighaInHectare: Yup.number()
		.typeError('Please enter number')
		.moreThan(0, 'Please enter value more than 0')
		.required('Please enter number'),
})
export type TAddArtisanPro = Yup.InferType<typeof AddArtisanProSchema>
