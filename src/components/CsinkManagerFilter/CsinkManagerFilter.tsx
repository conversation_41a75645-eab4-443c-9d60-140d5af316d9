import { authAxios } from '@/contexts'
import { ICSinkManagers } from '@/types'
import { Autocomplete, Stack, styled, TextField } from '@mui/material'
import { useQuery } from '@tanstack/react-query'
import { useSearchParams } from 'react-router-dom'

export const CsinkManagerFilter = () => {
	const [searchParams, setSearchParams] = useSearchParams()
	const csinkManagerId = searchParams.get('csinkManagerId') || ''
	const { data, isPending } = useQuery({
		queryKey: ['csinkManagerFilterQuery'],
		queryFn: () => {
			return authAxios.get<{ csinkManagers: ICSinkManagers[]; count: number }>(
				`/csink-manager?page=0&limit=9999`
			)
		},
		select: ({ data }) =>
			data?.csinkManagers?.map((item) => ({
				label: `${item.name} (${item.shortName})`,
				value: item.id,
			})),
	})

	return (
		<StyledContainer>
			<Autocomplete
				value={data?.find((option) => option.value === csinkManagerId) ?? null}
				className='auto-complete'
				onChange={(_, newValue: { label: string; value: string } | null) => {
					setSearchParams((prev) => {
						prev.set('csinkManagerId', newValue?.value ?? '')
						return prev
					})
				}}
				options={data || []}
				renderOption={(props, option) => <li {...props}>{option.label}</li>}
				loading={isPending}
				getOptionLabel={(option) => option?.label}
				renderInput={(params) => (
					<TextField {...params} label='Select Company' />
				)}
			/>
		</StyledContainer>
	)
}
const StyledContainer = styled(Stack)(({ theme }) => ({
	display: 'flex',
	flexDirection: 'row',
	gap: theme.spacing(2),
	'.auto-complete': {
		width: theme.spacing(30),
		'.MuiOutlinedInput-root': {
			height: 38,
			'.MuiInputBase-input': {
				paddingTop: '2.5px',
				fontSize: theme.typography.subtitle1,
			},
		},
	},
}))
