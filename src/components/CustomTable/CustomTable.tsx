/* eslint-disable no-mixed-spaces-and-tabs */
import {
	CircularProgress,
	FormControl,
	MenuItem,
	Select,
	SelectChangeEvent,
	Stack,
	Table,
	TableBody,
	TableCell,
	TableContainer,
	TableHead,
	TableRow,
	Typography,
	alpha,
	styled,
} from '@mui/material'
import { GridColDef, GridValidRowModel } from '@mui/x-data-grid'
import { FC, Fragment, PropsWithChildren, ReactNode, useCallback } from 'react'
import { useSearchParams } from 'react-router-dom'
import { CustomTableRow } from './CustomTableRow'
import { IRowClick, IRows } from './interface'
import { theme } from '@/lib/theme/theme'
import { defaultLimit, defaultPage, pageLimits } from '@/utils/constant'
import { FormatNumber } from '@/utils/helper'
import { CustomPagination } from '../CustomPagination'
import { NoData } from '../NoData'

interface IProps {
	columns: GridColDef<GridValidRowModel>[]
	rows: IRows[]
	collapseColumns?: GridColDef<GridValidRowModel>[]
	count: number
	isComponent?: boolean
	headerComponent?: ReactNode
	headerEndComponent?: ReactNode
	showPagination: boolean
	pageName?: string
	limitName?: string
	showPaginationDetails?: boolean
	component?: string
	handleRowClick?: (params: IRowClick) => void
	isLoading?: boolean
	isRowIndexLoading?: number[]
}
export function CustomTable(props: IProps) {
	const {
		columns,
		rows,
		collapseColumns,
		count,
		isComponent,
		component,
		pageName,
		limitName,
		handleRowClick,
		showPagination,
		headerComponent,
		headerEndComponent,
		showPaginationDetails,
		isLoading,
		isRowIndexLoading,
	} = props
	const [searchParams, setSearchParams] = useSearchParams()

	const paramsLimit = searchParams.get(limitName || 'limit') ?? defaultLimit
	const paramsPage = searchParams.get(pageName || 'page') ?? defaultPage

	const handleChangeRowsPerPage = useCallback(
		(event: SelectChangeEvent<string>) => {
			searchParams.delete('pages')
			setSearchParams((params) => {
				params.set('limit', event.target.value)
				return params
			})
		},
		[searchParams, setSearchParams]
	)

	return (
		<Stack spacing={2}>
			<Stack
				direction='row'
				justifyContent='space-between'
				alignItems='center'
				flexWrap='wrap'>
				<Stack>{headerComponent || null}</Stack>
				{showPaginationDetails && (
					<Stack direction='row' spacing={2} alignItems='center'>
						{headerEndComponent || null}
						<Typography variant='overline' textTransform='none'>
							Row per page:
						</Typography>
						<FormControl>
							<Select
								value={String(paramsLimit)}
								onChange={handleChangeRowsPerPage}
								sx={{
									width: theme.spacing(12.5),
								}}>
								{pageLimits.map((limit, index) => (
									<MenuItem key={index} value={limit}>
										{limit}
									</MenuItem>
								))}
							</Select>
						</FormControl>
						<Typography variant='overline' textTransform='none'>
							{Number(paramsPage) * Number(paramsLimit) + 1}-
							{(Number(paramsPage) + 1) * Number(paramsLimit)} of{' '}
							{FormatNumber(count ?? 0)}
						</Typography>
					</Stack>
				)}
			</Stack>
			<TableContainer>
				<StyledTable sx={{ minWidth: 650 }} aria-label='simple table'>
					<TableHead>
						<TableRow>
							{columns.map(
								(item: GridColDef<GridValidRowModel>, index: number) => (
									<TableCell
										key={index}
										sx={{
											minWidth: item?.minWidth ?? 100,
											flex: 1,
											...(item?.maxWidth ? { maxWidth: item.maxWidth } : {}),
										}}>
										{item?.headerName}
									</TableCell>
								)
							)}
						</TableRow>
					</TableHead>
					<TableBody>
						<RendererWrapper
							columnLength={columns?.length}
							noData={count === 0}
							loading={isLoading || false}>
							{rows.map((row: IRows, rowIndex: number) => (
								<Fragment key={rowIndex}>
									<CustomTableRow
										handleRowClick={handleRowClick}
										columns={columns}
										row={row}
										collapseColumns={collapseColumns ?? []}
										isComponent={isComponent}
										component={component}
										rowIndex={rowIndex}
										isRowIndexLoading={isRowIndexLoading}
									/>
								</Fragment>
							))}
						</RendererWrapper>
					</TableBody>
				</StyledTable>
			</TableContainer>
			<Stack direction='row' alignItems='center' justifyContent='center'>
				{showPagination && !!count && (
					<CustomPagination
						rowCount={count}
						pageName={pageName}
						limitName={limitName}
					/>
				)}
			</Stack>
		</Stack>
	)
}

const RendererWrapper: FC<
	PropsWithChildren<{ loading: boolean; noData: boolean; columnLength: number }>
> = ({ loading, noData, children, columnLength }) => {
	switch (true) {
		case loading:
			return (
				<TableRow>
					<TableCell
						sx={{
							pt: '30px !important',
						}}
						colSpan={columnLength || 0}>
						<Stack justifyContent='center' alignItems='center' py={10}>
							<CircularProgress size={35} />
						</Stack>
					</TableCell>
				</TableRow>
			)
		case noData:
			return (
				<TableRow>
					<TableCell
						sx={{
							pt: '80px !important',
						}}
						colSpan={columnLength || 0}>
						<NoData size='small' />
					</TableCell>
				</TableRow>
			)
		default:
			return children
	}
}

const StyledTable = styled(Table)(({ theme }) => ({
	padding: 1,
	'.MuiTableHead-root .MuiTableCell-head': {
		color: theme.palette.primary.dark,
		fontWeight: 700,
		fontSize: 14,
		background: alpha(theme.palette.primary.light, 0.05),
		height: theme.spacing(7),
	},
	'.MuiTableRow-root': {
		borderColor: theme.palette.divider,
	},
	'.MuiTableCell-root, .MuiTablePagination-selectLabel, .MuiTablePagination-selectRoot':
		{
			fontWeight: 400,
		},
	'.MuiTableCell-root:focus, .MuiTableCell-root:focus-within': {
		outline: 0,
	},
	'.MuiTableCell-root': {
		minHeight: '52px !important',
		paddingBottom: 5,
		paddingTop: 5,
		overflowWrap: 'anywhere',
		borderColor: theme.palette.divider,
		height: theme.spacing(6.5),
	},
	' .MuiTableRow-hover:hover > .MuiTableCell-root ': {
		color: theme.palette.primary.main,
		cursor: 'pointer',
	},
	'&.MuiTable-root': {
		fontWeight: 500,
	},

	'.MuiTableCell-head:focus': {
		outline: 'none',
	},
	'.MuiDataGrid-virtualScroller': {
		minHeight: 40,
	},

	'.approved.MuiTableRow-root': {
		backgroundColor: theme.palette.custom.green,
	},
	'.rejected.MuiTableRow-root': {
		backgroundColor: theme.palette.custom.red,
	},
	'.admin-approved.MuiTableRow-root': {
		backgroundColor: theme.palette.custom.green,
	},
	'.admin-rejected.MuiTableRow-root': {
		backgroundColor: theme.palette.custom.red,
	},
	'.partially-completed.MuiTableRow-root': {
		backgroundColor: theme.palette.custom.yellow,
	},
	'.not_assessed.MuiTableRow-root': {
		backgroundColor: theme.palette.custom.blue,
	},
}))
