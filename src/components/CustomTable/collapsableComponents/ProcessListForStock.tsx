import {
	Box,
	Dialog,
	DialogContent,
	DialogTitle,
	IconButton,
	<PERSON>ack,
	Typography,
} from '@mui/material'
import { FC, useMemo, useState } from 'react'
import { CustomTable } from '../CustomTable'
import { GridColDef, GridValidRowModel } from '@mui/x-data-grid'
import { format } from 'date-fns'
import { BioCharSinkQuantity } from '@/interfaces'
import { Cancel } from '@mui/icons-material'
import { roundNumber } from '@/utils/helper'

export const ProcessListForStock: FC<{
	data: any
	setSelectedProcess: React.Dispatch<React.SetStateAction<string[]>>
	selectedProcess: string[]
	setEditStockBool: React.Dispatch<React.SetStateAction<boolean>>
	editStockBool: boolean
}> = ({ data }) => {
	const [sinkQtyDetails, setSinkQtyDetails] =
		useState<BioCharSinkQuantity | null>(null)
	const [processes] = useState(data?.kilnProcess ?? [])

	const columns: GridColDef<GridValidRowModel>[] = useMemo(
		() => [
			{
				field: 'startDate',
				headerName: 'Date',
				minWidth: 80,
				flex: 1,
				renderCell: (params) => format(new Date(params?.value), 'yyyy-MM-dd'),
			},

			{
				field: 'bioCharQuantity',
				headerName: 'Biochar Quantity',
				minWidth: 100,
				flex: 1,
			},

			{
				field: 'siteName',
				headerName: 'Site Name',
				minWidth: 150,
				flex: 1,
				renderCell: (params) => (
					<Typography className='first_letter_capitalize'>
						{params?.value ?? '-'}
					</Typography>
				),
			},
			{
				field: 'carbonCredits',
				headerName: 'Carbon Credits',
				minWidth: 150,
				flex: 1,
			},
			{
				field: 'isSink',
				headerName: 'Sinked',
				minWidth: 150,
				flex: 1,
				renderCell: (params) => (
					<Typography className='first_letter_capitalize'>
						{`${params?.row?.bioCharSinkQuantity?.isBioCharSink ?? false}`}
					</Typography>
				),
			},

			// {
			// 	field: 'action',
			// 	headerName: 'Sinked Qty',
			// 	minWidth: 150,
			// 	flex: 1,
			// 	renderCell: (params) => (
			// 		<ActionButtons
			// 			editStockBool={editStockBool}
			// 			params={params}
			// 			setSinkQtyDetails={setSinkQtyDetails}
			// 			stockId={data?.stockId || ''}
			// 			setProcesses={setProcesses}
			// 			noOfProcess={processes?.length}
			// 		/>
			// 	),
			// },
		],
		[]
	)
	return (
		<>
			{sinkQtyDetails !== null ? (
				<ShowDistributedBioCharQtyDialog
					open={sinkQtyDetails !== null}
					onClose={() => setSinkQtyDetails(null)}
					sinkQty={sinkQtyDetails}
				/>
			) : null}
			<Box px={7}>
				<CustomTable
					showPagination={false}
					columns={columns}
					rows={processes}
					count={processes?.length ?? 0}
				/>
			</Box>
		</>
	)
}

const ShowDistributedBioCharQtyDialog: FC<{
	open: boolean
	onClose: () => void
	sinkQty: BioCharSinkQuantity
}> = ({ open, onClose, sinkQty }) => {
	const qtyList = useMemo(
		() => [
			{
				label: 'Packaged and Distributed Liquid Qty',
				value: sinkQty?.packedDistributedQty ?? 0,
			},
			{
				label: 'Packaged and Distributed Liquid Other',
				value: sinkQty?.packedDistributedOtherQty ?? 0,
			},
			{
				label: 'Not Packaged distributed Liquid Farmer',
				value: sinkQty?.notPackedDistributedQty ?? 0,
			},
			{
				label: 'Not Packaged Distributed Liquid Other',
				value: sinkQty?.notPackedDistributedOtherQty ?? 0,
			},
			{
				label: 'Packaged Distributed Biochar Only Farmer',
				value: sinkQty?.packedDistributedQtyBioChar ?? 0,
			},
			{
				label: 'Packed Distributed Biochar Only Other',
				value: sinkQty?.packedDistributedOtherQtyBioChar ?? 0,
			},
			{
				label: 'Packaged Distributed Biochar Mix Farmer',
				value: sinkQty?.packedDistributedQtyMix ?? 0,
			},
			{
				label: 'Packed Distributed Biochar Mix Other',
				value: sinkQty?.packedDistributedOtherQtyMix ?? 0,
			},
			{
				label: 'Not Packaged Distributed Biochar Mix Farmer',
				value: sinkQty?.notPackedDistributedQtyMix ?? 0,
			},
			{
				label: 'Not Packaged Distributed Biochar Mix Other',
				value: sinkQty?.notPackedDistributedOtherQtyMix ?? 0,
			},
			{
				label: 'Sinked For Solid Mix',
				value: sinkQty?.solidMixQty ?? 0,
			},
		],
		[sinkQty]
	)

	return (
		<Dialog open={open} onClose={onClose} fullWidth maxWidth='sm'>
			<IconButton
				sx={{ position: 'absolute', right: 10, top: 10, cursor: 'pointer' }}
				onClick={onClose}>
				<Cancel />
			</IconButton>
			<DialogTitle textAlign='center' variant='h5'>
				Sinked Biochar Quantity
			</DialogTitle>
			<DialogContent>
				{qtyList.map((item) => (
					<Stack
						key={item.label}
						direction='row'
						alignItems='center'
						justifyContent='space-between'>
						<Typography variant='body2'>{item.label} :</Typography>
						<Typography variant='body1'>
							{roundNumber(item.value, 4)} tonnes
						</Typography>
					</Stack>
				))}
			</DialogContent>
		</Dialog>
	)
}
