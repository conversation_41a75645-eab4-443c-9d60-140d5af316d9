import { Confirmation } from "@/components/Confirmation"
import { authAxios } from "@/contexts"
import { BioCharSinkQuantity } from "@/interfaces"
import { But<PERSON>, Stack } from "@mui/material"
import { useMutation, useQueryClient } from "@tanstack/react-query"
import { useCallback, useState } from "react"
import { toast } from "react-toastify"

type ConfirmationModal = {
	open: boolean
	title: string
	onYesClick: () => void
	onNoClick: () => void
	onCancelClick: () => void
	isDoubleConfirmationModal: boolean
}

const initialConfirmationModalValues: ConfirmationModal = {
	open: false,
	title: '',
	onYesClick: () => {},
	onNoClick: () => {},
	onCancelClick: () => {},
	isDoubleConfirmationModal: false,
}
export const ActionButtons: React.FC<{
	params: any
	stockId: string
	editStockBool: boolean
	setSinkQtyDetails: (
		value: React.SetStateAction<BioCharSinkQuantity | null>
	) => void
	setProcesses: React.Dispatch<any>
	noOfProcess: number
}> = ({
	params,
	editStockBool,
	setSinkQtyDetails,
	stockId,
	setProcesses,
	noOfProcess,
}) => {
	const queryClient = useQueryClient()

	const [confirmationModal, setConfirmationModal] = useState<ConfirmationModal>(
		initialConfirmationModalValues
	)

	const editStacksMutation = useMutation({
		mutationKey: ['editStock'],
		mutationFn: async () => {
			const payload = {
				stockId: stockId,
				processIds: [params?.row?.id],
			}
			const { data } = await authAxios.put(
				`/global-csink/process/stock`,
				payload
			)
			return data
		},
		onError: (error: any) => {
			toast(error?.response?.data?.messageToUser)
		},
		onSuccess: (data) => {
			toast(data?.message)
			if (noOfProcess > 1) {
				setProcesses((prev: any) =>
					prev.filter((process: any) => process.id !== params?.row?.id)
				)
				return
			}
			queryClient.refetchQueries({ queryKey: ['stockList'] })
		},
	})

	const handleEditStock = useCallback(() => {
		editStacksMutation.mutate()
	}, [editStacksMutation])

	return (
		<>
			<Stack direction='row' alignItems='center' columnGap={2}>
				<Button
					sx={{
						color: 'common.black',
					}}
					onClick={() => setSinkQtyDetails(params?.row?.bioCharSinkQuantity)}>
					View
				</Button>
				{editStockBool ? (
					<Button
						sx={{ fontSize: 14 }}
						onClick={(e) => {
							e.stopPropagation()
							setConfirmationModal((prev) => ({
								...prev,
								open: true,
								title: 'Are you sure you want to delete this Process ?',
								onYesClick: () => {
									setConfirmationModal((prev) => ({
										...prev,
										open: true,
										title: 'Are you sure you want to continue ?',
										onYesClick: () => {
											setConfirmationModal(initialConfirmationModalValues)
											handleEditStock()
										},
										onNoClick: () =>
											setConfirmationModal(initialConfirmationModalValues),
										onCancelClick: () =>
											setConfirmationModal(initialConfirmationModalValues),
										isDoubleConfirmationModal: true,
									}))
								},
								isDoubleConfirmationModal: false,
								onNoClick: () =>
									setConfirmationModal(initialConfirmationModalValues),
								onCancelClick: () =>
									setConfirmationModal(initialConfirmationModalValues),
							}))
						}}>
						Delete
					</Button>
				) : null}
			</Stack>
			{confirmationModal.open &&
			!confirmationModal.isDoubleConfirmationModal ? (
				<Confirmation
					open={confirmationModal.open}
					handleYesClick={confirmationModal.onYesClick}
					handleClose={confirmationModal.onCancelClick}
					handleNoClick={confirmationModal.onNoClick}
					confirmationText={confirmationModal.title}
				/>
			) : null}
			{confirmationModal.open && confirmationModal.isDoubleConfirmationModal ? (
				<Confirmation
					open={confirmationModal.open}
					handleYesClick={confirmationModal.onYesClick}
					handleClose={confirmationModal.onCancelClick}
					handleNoClick={confirmationModal.onNoClick}
					confirmationText={confirmationModal.title}
				/>
			) : null}
		</>
	)
}
