import { Biomass_Default_Unit } from '@/interfaces'
import { Stack, Typography, useTheme } from '@mui/material'
import { GridColDef, GridValidRowModel } from '@mui/x-data-grid'
import React, { useMemo } from 'react'
import { CustomTable } from '../CustomTable'

export const BiomassAvailableDetails: React.FC<{ data: any }> = ({ data }) => {
	const theme = useTheme()

	const rows = useMemo(
		() =>
			data?.map((item: any) => {
				return {
					...item,
					id: `${item?.biomassId}-${item?.entityId}-${item?.biomassQuantity}`,
				}
			}),
		[data]
	)

	const columns: GridColDef<GridValidRowModel>[] = useMemo(
		() => [
			{
				field: 'biomassName',
				headerName: 'Biomass Name',
				minWidth: 120,
				flex: 1,
			},

			{
				field: 'entityName',
				headerName: 'Kiln/Site Name',
				minWidth: 120,
				flex: 1,

				renderCell: (params) => {
					return (
						<Typography
							variant='subtitle1'
							sx={{
								color: theme.palette.neutral['900'],
							}}>
							{params?.row?.entityName ?? '-'}
						</Typography>
					)
				},
			},
			{
				field: 'biomassQuantity',
				headerName: `Biomass Qty (${Biomass_Default_Unit})`,
				align: 'center',
				headerAlign: 'center',
				minWidth: 120,
				flex: 1,

				renderCell: (params) => {
					return (
						<Typography
							variant='subtitle1'
							sx={{
								color: theme.palette.neutral['900'],
							}}>
							{params?.row?.biomassQuantity
								? params?.row?.biomassQuantity
								: '-'}
						</Typography>
					)
				},
			},
		],
		[theme.palette.neutral]
	)

	return (
		<Stack px={2}>
			<CustomTable
				columns={columns}
				rows={rows ?? []}
				showPagination={false}
				count={(rows || [])?.length}
			/>
		</Stack>
	)
}
