import { ActionInformationDrawer } from '@/components/ActionInformationDrawer'
import { AddOrEditBiocharContentDetail } from '@/components/AddOrEditBiocharContentDetail'
import { TrainingProofRenderer } from '@/components/TrainingProofRenderer'
import { authAxios } from '@/contexts'
import { IMedia } from '@/interfaces'
import { IBioCharContentDetail } from '@/types'
import { Close } from '@mui/icons-material'
import EditIcon from '@/assets/icons/editIcon.svg'

import {
	Box,
	IconButton,
	Stack,
	styled,
	Typography,
	useTheme,
} from '@mui/material'
import { useQuery } from '@tanstack/react-query'
import { FC, useState } from 'react'
import { NoData } from '@/components/NoData'

export const BiocharContentDetails: FC<{ id: string; onClose: () => void }> = ({
	id,
	onClose,
}) => {
	const theme = useTheme()
	const [isActionInfoDrawer, setIsActionInfoDrawer] = useState<boolean>(false)
	const [forEdit, setForEdit] = useState<boolean>(false)
	const [bioCharContentDetail, setBioCharContentDetail] =
		useState<IBioCharContentDetail | null>(null)

	const fetchBioCharContentDetail = useQuery({
		queryKey: [`bioCharContentDetail-${id}`],
		queryFn: async () => {
			const { data } = await authAxios.get<IBioCharContentDetail[]>(
				`crops/${id}/carbon-percentage`
			)
			return data
		},
		enabled: !!id,
	})

	const content: { [key: string]: { title: string; unit: string } } = {
		// temperature: { title: 'Temperature', unit: 'C' },
		carbonPercentage: { title: 'Total Carbon', unit: '%' },
		organicCarbonPercentage: { title: 'Organic Carbon', unit: '%' },
		inorganicCarbonPercentage: { title: 'Inorganic Carbon', unit: '%' },
		density: { title: 'Bulk Density', unit: 't/m3' },
		ph: { title: 'pH', unit: '' },
		HByC: { title: 'H/C Ratio', unit: '' },
		ash: { title: 'Ash Content', unit: '%' },
		hContent: { title: 'Hydrogen', unit: '%' },
		nContent: { title: 'Nitrogen', unit: '%' },
		sContent: { title: 'Sulphur', unit: '%' },
		technology: { title: 'Technology', unit: '' },
		waterHoldingCapacity: { title: 'Water Holding Capacity', unit: '%' },
	}

	const handleEdit = (data: IBioCharContentDetail) => {
		setIsActionInfoDrawer(true)
		setForEdit(true)
		setBioCharContentDetail(data)
	}

	return (
		<>
			<ActionInformationDrawer
				open={isActionInfoDrawer}
				onClose={() => setIsActionInfoDrawer(false)}
				anchor='right'
				component={
					<AddOrEditBiocharContentDetail
						handleClose={() => {
							setIsActionInfoDrawer(false)
							setBioCharContentDetail(null)
							setForEdit(false)
						}}
						type={forEdit ? 'edit' : 'add'}
						bioCharDetail={bioCharContentDetail}
						biomassId={id}
					/>
				}
			/>
			<StyleContainer>
				<Stack className='header'>
					<Stack
						direction='row'
						spacing={1}
						alignItems='center'
						width='100%'
						justifyContent='space-between'>
						<Typography variant='h4'>Biochar Details</Typography>
						<IconButton onClick={onClose}>
							<Close />
						</IconButton>
					</Stack>
				</Stack>

				<Stack width='100%' className='container'>
					<Stack>
						{fetchBioCharContentDetail?.data?.length ? (
							fetchBioCharContentDetail.data?.map((item) => (
								<Stack
									sx={{
										gap: theme.spacing(1.5),
										padding: theme.spacing(1),
										borderBottom: `${theme.spacing(0.125)} solid ${
											theme.palette.neutral['100']
										}`,
									}}>
									<Stack alignItems='flex-end'>
										<IconButton onClick={() => handleEdit(item)}>
											<Box
												component='img'
												src={EditIcon}
												alt='edit-icon'
												height={28}
												width={24}
											/>
										</IconButton>
									</Stack>
									<Box
										sx={{
											columnGap: theme.spacing(2),
											display: 'grid',
											gridTemplateColumns: '1fr 1fr',
										}}>
										{/* <TagComponent
											title='Total Carbon'
											value={item?.organicCarbonPercentage}
											unit='%'
										/> */}
										{Object.entries(content).map(([key, objectValue]) => {
											const { title, unit } = objectValue
											const value = item[key as keyof IBioCharContentDetail]
											return (
												<TagComponent
													key={key}
													title={title}
													value={value}
													unit={unit}
												/>
											)
										})}
									</Box>
									<div>
										<Typography variant='subtitle2'>Lab Test report:</Typography>
										<TrainingProofRenderer
											viewMode='table'
											media={[
												...(item.labReportDocs ?? []).map((i: IMedia) => ({
													...i,
													fileName: i?.path,
												})),
												...(item.labReportImages ?? []).map((i: IMedia) => ({
													...i,
													fileName: i?.path,
												})),
											]}
										/>
									</div>
								</Stack>
							))
						) : (
							<NoData size='small' />
						)}
					</Stack>
				</Stack>
			</StyleContainer>
		</>
	)
}

const TagComponent: FC<{ title: string; value: any; unit?: string }> = ({
	title,
	value,
	unit,
}) => {
	return (
		<Stack direction='row' alignItems='center' columnGap={1}>
			<Typography variant='subtitle2' fontWeight={600}>
				{title}:
			</Typography>
			{value ? (
				<Typography variant='subtitle1'>
					{value} {unit || ''}
				</Typography>
			) : (
				<Typography variant='subtitle1'>-</Typography>
			)}
		</Stack>
	)
}
const StyleContainer = styled(Stack)(({ theme }) => ({
	gap: theme.spacing(2),
	'.header': {
		flexDirection: 'row',
		justifyContent: 'space-between',
		alignItems: 'center',
		padding: theme.spacing(4.5, 2),
		borderBottom: `${theme.spacing(0.125)} solid ${
			theme.palette.neutral['100']
		}`,
	},
	'.container': {
		padding: theme.spacing(2, 4),
		gap: theme.spacing(4),
	},
}))
