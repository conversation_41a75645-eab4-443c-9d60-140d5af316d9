import { ActionInformationDrawer } from '@/components/ActionInformationDrawer'
import { ArtisanProList, RenderNetworkList } from '@/components/ArtisanProsList'
import { authAxios } from '@/contexts'
import {
	IBaArtisanProNetworks,
	IBaNetworkResponse,
} from '@/interfaces/BiomassAggregator.type'
import { CircularProgress, Stack, styled } from '@mui/material'
import { useQuery } from '@tanstack/react-query'
import { useState } from 'react'
import { useNavigate } from 'react-router-dom'

export const ArtisanandCsinkNetworkList = ({ id }: { id: string }) => {
	const [showArtisaProDrawer, setShowArtisaProDrawer] =
		useState<IBaArtisanProNetworks | null>(null)

	const baNetworksQuery = useQuery({
		queryKey: ['baNetworksQuery', id],
		queryFn: async () => {
			const { data } = await authAxios.get<IBaNetworkResponse>(
				`/biomass-aggregator/${id}/networks?limit=5000`
			)
			return data
		},
		enabled: !!id,
	})
	const navigate = useNavigate()
	return (
		<StyledContainer>
			{baNetworksQuery?.isLoading ? (
				<CircularProgress />
			) : (
				<>
					<RenderNetworkList
						title='Artisan Pro Networks'
						networks={baNetworksQuery?.data?.ArtisanProNetworks}
						onClick={(network) => setShowArtisaProDrawer(network)}
					/>

					<RenderNetworkList
						title='Csink Networks'
						networks={baNetworksQuery?.data?.CSinkNetworks || []}
						onClick={(network) =>
							navigate(`/dashboard/c-sink-network/${network?.id}/details?`)
						}
					/>
				</>
			)}

			<ActionInformationDrawer
				open={!!showArtisaProDrawer}
				onClose={() => setShowArtisaProDrawer(null)}
				anchor='right'
				component={
					<ArtisanProList
						name={showArtisaProDrawer?.name || ''}
						handleClose={() => setShowArtisaProDrawer(null)}
						id={showArtisaProDrawer?.id || ''}
					/>
				}
			/>
		</StyledContainer>
	)
}

const StyledContainer = styled(Stack)(({ theme }) => ({
	padding: theme.spacing(3, 5),
	justifyContent: 'space-around',
	flexDirection: 'row',
	maxHeight: theme.spacing(75),
	overflowY: 'auto',
}))
