import { CircularProgress, Stack, TableCell, TableRow } from '@mui/material'
import { GridColDef, GridValidRowModel } from '@mui/x-data-grid'
import { FC, PropsWithChildren, useCallback, useState } from 'react'
import { useSearchParams } from 'react-router-dom'
import { CustomCollapseTable } from './CustomCollapseTable'
import { IRowClick, IRows } from './interface'
import { defaultLimit, defaultPage } from '@/utils/constant'
import { NoData } from '../NoData'

interface IProps {
	columns: GridColDef<GridValidRowModel>[]
	collapseColumns: GridColDef<GridValidRowModel>[]
	row: IRows
	isComponent?: boolean
	handleRowClick?: (params: IRowClick) => void
	component?: string
	rowIndex: number
	isRowIndexLoading?: number[]
}

export function CustomTableRow(props: IProps) {
	const {
		columns,
		row,
		collapseColumns,
		isComponent,
		component,
		rowIndex,
		handleRowClick,
		isRowIndexLoading,
	} = props
	const [collapseRowDetails, setCollapseRowDetails] = useState<IRows[]>([])
	const [searchParams] = useSearchParams()
	const page = searchParams.get('page') || defaultPage
	const [hookData, setHookData] = useState<IRows>()
	const rowsPerPage = searchParams.get('limit') || defaultLimit
	const [open, setOpen] = useState(false)
	const [editStockBool, setEditStockBool] = useState<boolean>(false)
	const getSerialNo = useCallback(
		(rowIndex: number, cellData: IRows, value: string) => {
			if (value === 'id')
				return rowIndex + Number(rowsPerPage) * Number(page) + 1
			return cellData
		},
		[page, rowsPerPage]
	)
	const [selectedProcess, setSelectedProcess] = useState<string[]>([])

	return (
		<>
			<TableRow
				hover
				className={row?.status ?? ''}
				sx={{ '&:last-child td, &:last-child th': { border: 0 } }}
				onClick={() => {
					handleRowClick?.({
						row,
						open,
						setOpen,
						setCollapseRowDetails,
						hookData,
						setHookData,
					})
				}}>
				{columns.map((item: GridValidRowModel, colIndex: number) => (
					<TableCell
						key={colIndex}
						align='left'
						sx={{
							minWidth: item?.minWidth ?? 100,
							flex: 1,
							...(item?.maxWidth ? { maxWidth: item.maxWidth } : {}),
						}}>
						{item?.renderCell
							? item?.renderCell({
									row,
									value: row[item.field] ?? '',
									open,
									setOpen,
									setCollapseRowDetails,
									setSelectedProcess,
									selectedProcess,
									editStockBool,
									setEditStockBool,
							  })
							: getSerialNo(rowIndex, row[item.field], item.field)}
					</TableCell>
				))}
			</TableRow>

			{open === true ? (
				<RendererWrapper
					loading={isRowIndexLoading?.includes(rowIndex) || false}
					noData={false}
					columnLength={9}>
					<CustomCollapseTable
						open={open}
						collapseColumns={collapseColumns ?? []}
						collapseRowDetails={collapseRowDetails}
						isComponent={isComponent}
						componentData={hookData}
						component={component}
						setSelectedProcess={setSelectedProcess}
						selectedProcess={selectedProcess}
						editStockBool={editStockBool}
						setEditStockBool={setEditStockBool}
					/>
				</RendererWrapper>
			) : null}
		</>
	)
}

const RendererWrapper: FC<
	PropsWithChildren<{ loading: boolean; noData: boolean; columnLength: number }>
> = ({ loading, noData, children, columnLength }) => {
	switch (true) {
		case loading:
			return (
				<TableRow>
					<TableCell
						sx={{
							pt: '30px !important',
						}}
						colSpan={columnLength || 0}>
						<Stack justifyContent='center' alignItems='center' py={10}>
							<CircularProgress size={35} />
						</Stack>
					</TableCell>
				</TableRow>
			)
		case noData:
			return (
				<TableRow>
					<TableCell
						sx={{
							pt: '80px !important',
						}}
						colSpan={columnLength || 0}>
						<NoData size='small' />
					</TableCell>
				</TableRow>
			)
		default:
			return children
	}
}
