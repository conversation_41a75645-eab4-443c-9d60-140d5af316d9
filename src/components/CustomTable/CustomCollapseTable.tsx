import { theme } from '@/lib/theme/theme'
import {
	Box,
	Collapse,
	Stack,
	Table,
	TableBody,
	TableCell,
	TableHead,
	TableRow,
	Typography,
} from '@mui/material'
import { GridColDef, GridValidRowModel } from '@mui/x-data-grid'
import {
	ArtisanandCsinkNetworkList,
	ProcessListForStock,
} from './collapsableComponents'
import { BiomassAvailableDetails } from './collapsableComponents/BiomassAvailableDetails'
import { FarmsList } from '../FarmsList'

interface IProps {
	open: boolean
	collapseColumns: GridColDef<GridValidRowModel>[]
	collapseRowDetails: any[]
	isComponent?: boolean
	componentData?: any
	component?: string
	setSelectedProcess: React.Dispatch<React.SetStateAction<string[]>>
	selectedProcess: string[]
	setEditStockBool: React.Dispatch<React.SetStateAction<boolean>>
	editStockBool: boolean
}

export function CustomCollapseTable(props: IProps) {
	const {
		open,
		// rowIndex,
		// index,
		collapseColumns,
		componentData,
		collapseRowDetails,
		isComponent,
		component,
		setSelectedProcess,
		selectedProcess,
		setEditStockBool,
		editStockBool,
	} = props

	return (
		<TableRow sx={{ width: '100%' }}>
			<TableCell sx={{ p: 0 }} colSpan={12}>
				<Collapse in={open} timeout='auto' unmountOnExit>
					{isComponent ? (
						<RenderComponent
							component={component ?? ''}
							data={componentData}
							setSelectedProcess={setSelectedProcess}
							selectedProcess={selectedProcess}
							editStockBool={editStockBool}
							setEditStockBool={setEditStockBool}
						/>
					) : (
						<Box sx={{ margin: 1, padding: '0 60px' }}>
							<Table size='small' aria-label='purchases'>
								<>
									<TableHead>
										<TableRow>
											{collapseColumns.map(
												(
													collapseColumn: GridColDef<GridValidRowModel>,
													collapseindex: number
												) => (
													<TableCell
														sx={{
															minWidth: collapseColumn?.minWidth,
															flex: collapseColumn?.flex,
														}}
														key={collapseindex}>
														{collapseColumn.headerName}
													</TableCell>
												)
											)}
										</TableRow>
									</TableHead>
									<TableBody>
										{collapseRowDetails?.map(
											(collapseRow, collapseRowIndex: number) => (
												<TableRow hover key={collapseRowIndex}>
													{collapseColumns?.map(
														(
															collapseColumn: GridValidRowModel,
															collapseindex: number
														) => (
															<TableCell key={collapseindex}>
																{collapseColumn?.renderCell
																	? collapseColumn?.renderCell({
																			collapseRow,
																			value: collapseRow[collapseColumn.field],
																	  })
																	: collapseRow[collapseColumn.field]}
															</TableCell>
														)
													)}
												</TableRow>
											)
										)}
									</TableBody>
								</>
							</Table>
						</Box>
					)}
				</Collapse>
			</TableCell>
		</TableRow>
	)
}

const RenderComponent = ({
	component,
	data,
	setSelectedProcess,
	selectedProcess,
	setEditStockBool,
	editStockBool,
}: {
	component?: string
	data?: any
	setSelectedProcess: React.Dispatch<React.SetStateAction<string[]>>
	selectedProcess: string[]
	setEditStockBool: React.Dispatch<React.SetStateAction<boolean>>
	editStockBool: boolean
}) => {
	switch (component) {
		case 'description':
			return (
				<Stack
					flexDirection='column'
					gap={theme.spacing(3.5)}
					padding={theme.spacing(3.5, 5)}>
					<Typography
						variant='body2'
						sx={{ color: theme.palette.neutral[900] }}>
						Description
					</Typography>
					<Typography className='first_letter_capitalize' variant='subtitle1'>
						{data?.description}
					</Typography>
				</Stack>
			)

		// case 'bioChar_content_details':
		// 	return <BiocharContentDetails id={data?.id} />

		case 'stock_process_details':
			return (
				<ProcessListForStock
					data={data}
					setSelectedProcess={setSelectedProcess}
					selectedProcess={selectedProcess}
					editStockBool={editStockBool}
					setEditStockBool={setEditStockBool}
				/>
			)
		case 'biomass_available_list':
			return <BiomassAvailableDetails data={data?.biomassDetails} />

		case 'farmsList':
			return <FarmsList farmerId={data?.farmerId} />

		case 'ArtisanAndCsinkList':
			return <ArtisanandCsinkNetworkList id={data} />
		default:
			return null
	}
}
