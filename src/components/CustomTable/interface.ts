import { IQueries } from '@/interfaces'
import { GridCellParams } from '@mui/x-data-grid'
import { Dispatch, SetStateAction } from 'react'

export type IRows = IQueries | any

export interface IRowClick extends Partial<GridCellParams> {
	row: IRows
	open: boolean
	setOpen: Dispatch<SetStateAction<boolean>>
	setCollapseRowDetails: Dispatch<SetStateAction<IRows[]>>
	hookData: IRows | undefined
	setHookData: Dispatch<SetStateAction<IRows>> | undefined
}
