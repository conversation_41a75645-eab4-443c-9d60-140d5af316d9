import {
	Button,
	Dialog,
	DialogActions,
	DialogContent,
	DialogTitle,
	TextField,
} from '@mui/material'
import { FC, useState } from 'react'
import { Confirmation } from '../Confirmation'

type TProps = {
	title: string
	buttonText?: string
	confirmationText: string
	handleOnClick: (status: 'deleted' | 'rejected', reason: string) => void
	open: boolean
	onClose: () => void
}

export const AddDeleteReasonModal: FC<TProps> = ({
	title,
	buttonText = 'Add',
	confirmationText,
	handleOnClick,
	open,
	onClose,
}) => {
	const [reason, setReason] = useState<string>('')
	const [isConfirmationDialogOpen, setIsConfirmationDialogOpen] =
		useState<boolean>(false)
	return (
		<>
			<Dialog open={open} onClose={onClose} fullWidth maxWidth='sm'>
				<DialogTitle textAlign='center'> {title}</DialogTitle>
				<DialogContent>
					<TextField
						value={reason}
						onChange={(e) => setReason(e.target.value)}
						multiline
						fullWidth
						rows={5}
					/>
				</DialogContent>
				<DialogActions
					sx={{
						display: 'flex',
						justifyContent: 'center',
						flexDirection: 'row',
					}}>
					<Button
						variant='contained'
						onClick={() => setIsConfirmationDialogOpen(true)}>
						{buttonText}
					</Button>
				</DialogActions>
			</Dialog>
			{isConfirmationDialogOpen ? (
				<Confirmation
					confirmationText={confirmationText}
					open={isConfirmationDialogOpen}
					handleClose={() => setIsConfirmationDialogOpen(false)}
					handleNoClick={() => setIsConfirmationDialogOpen(false)}
					handleYesClick={() => {
						handleOnClick('deleted', reason)
						setIsConfirmationDialogOpen(false)
						onClose()
					}}
				/>
			) : null}
		</>
	)
}
