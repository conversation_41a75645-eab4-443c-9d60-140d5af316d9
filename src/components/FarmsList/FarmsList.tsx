import { dateFormats, farmTabTypes } from '@/utils/constant'
import { getGoogleMapLink, getFormattedDate } from '@/utils/helper'
import { LocationOnOutlined, Download, AddCircle } from '@mui/icons-material'
import { TabContext, TabPanel } from '@mui/lab'
import { Typography, Button, Stack, styled, Checkbox } from '@mui/material'
import { GridColDef, GridValidRowModel } from '@mui/x-data-grid'
import { useCallback, useMemo, useState } from 'react'
import { Link, useNavigate, useParams, useSearchParams } from 'react-router-dom'
import { CustomDataGrid } from '../CustomDatagrid'
import { useMutation, useQuery } from '@tanstack/react-query'
import { authAxios } from '@/contexts'
import { IFarm, ILocation } from '@/interfaces'
import { GoogleMapsDraw } from '../GoogleMap'
import { toast } from 'react-toastify'
import { AxiosError } from 'axios'
import { generateKML } from '@/utils/helper/generateKML'
import { theme } from '@/lib/theme/theme'

export const FarmsList = ({ farmerId }: { farmerId: string }) => {
	const [searchParams, setSearchParams] = useSearchParams()
	const navigate = useNavigate()
	const [showMap, setShowMap] = useState(false)
	const { cSinkNetworkId } = useParams()
	const [tab, setTab] = useState(farmTabTypes.all)
	const [kmlRequired, setKmlRequired] = useState(false)
	const [farmCoordinates, setFarmCoordinates] = useState<
		google.maps.LatLng[] | google.maps.LatLngLiteral[]
	>([])

	const convertStringToArray = useCallback((farmLocation: string): string[] => {
		const coordinate: string[] = farmLocation.slice(1, -1).split(',')
		return coordinate
	}, [])

	const getFarmsListQuery = useQuery({
		queryKey: ['getallFarms', farmerId, tab],
		queryFn: async () => {
			const { data } = await authAxios.get<IFarm[]>(
				`/cs-network/${cSinkNetworkId}/farmers/${farmerId}/farm?limit=50&page=0&greaterThanOneHectare=false&kLMNotAdded=false`
			)
			return data
		},
		enabled: !!farmerId && tab === farmTabTypes.all,
	})
	const getKmlListQuery = useQuery({
		queryKey: ['getFarmsKml', farmerId, tab, kmlRequired],
		queryFn: async () => {
			const { data } = await authAxios.get<IFarm[]>(
				`/cs-network/${cSinkNetworkId}/farmers/${farmerId}/farm?limit=50&page=0&greaterThanOneHectare=true&kLMNotAdded=${kmlRequired}`
			)
			return data
		},
		enabled: !!farmerId && tab === farmTabTypes.kml,
	})
	const handleSaveKmlMutation = useMutation({
		mutationKey: ['SaveKml'],
		mutationFn: async (mapData: ILocation[]) => {
			const farmId = searchParams.get('farmId')
			if (!farmId) {
				return
			}
			const payload = {
				id: farmId,
				area: mapData,
			}
			await authAxios.post(
				`/cs-network/${cSinkNetworkId}/farms/edit-area`,
				payload
			)
		},
		onSuccess: () => {
			getKmlListQuery.refetch()
			toast('Farm KML added')
		},
		onError: (error: AxiosError) => {
			toast((error?.response?.data as { userToMessage: string })?.userToMessage)
		},
	})
	const handleSave = useCallback(
		async (mapData: ILocation[]) => {
			await handleSaveKmlMutation.mutateAsync(mapData)
		},
		[handleSaveKmlMutation]
	)
	const handleViewKMLFile = useCallback(
		(
			farmCoordinates: ILocation[],
			center: { x: string; y: string },
			farmId: string
		) => {
			const formattedCoordinates = farmCoordinates.map((coordinate) => ({
				lat: coordinate.x,
				lng: coordinate.y,
			}))
			const searchParams = new URLSearchParams(window.location.search)
			searchParams.set('lat', center.x)
			searchParams.set('long', center.y)
			searchParams.set('farmId', farmId)
			navigate(`?${searchParams.toString()}`, { replace: true })
			setFarmCoordinates(formattedCoordinates)
			setShowMap(true)
		},
		[]
	)

	const farmsButtons = useMemo(
		() => [
			{
				value: farmTabTypes.all,
				displayText: 'All',
				handleClick: () => {
					setTab(farmTabTypes.all)
				},
			},
			{
				value: farmTabTypes.kml,
				displayText: 'Farms More than 1 hect',
				handleClick: () => {
					setTab(farmTabTypes.kml)
				},
			},
		],
		[]
	)
	const farmsAllColumn: GridColDef<GridValidRowModel>[] = useMemo(
		() => [
			{
				field: 'id',
				headerName: 'S. No',
				minWidth: 80,
				flex: 1,
				renderCell: (params) =>
					params.api.getRowIndexRelativeToVisibleRows(params.row.id) + 1,
			},
			{
				field: 'landmark',
				headerName: 'Farm Location',
				minWidth: 150,
				flex: 1,
				renderCell: (params) => {
					const location = params?.row?.location

					return (
						<Typography
							component={Link}
							to={getGoogleMapLink(location?.x, location?.y)}
							target='_blank'
							display='flex'
							alignItems='flex-start'
							sx={{
								color: 'black',
								gap: '4px',
								textDecoration: 'none',
								textTransform: 'capitalize',
							}}>
							<LocationOnOutlined color='error' />
							{params?.value}
						</Typography>
					)
				},
			},

			{
				field: 'size',
				headerName: 'Farms Size (hect)',
				flex: 1,
				minWidth: 150,
				renderCell: (params) => {
					if (params.value % 1 !== 0) {
						return params.value.toFixed(3)
					}
					return params.value.toFixed(0)
				},
			},
			{
				field: 'farmsAddedDate',
				headerName: 'Created At',
				flex: 1,
				minWidth: 150,
				renderCell: (params) =>
					getFormattedDate(
						params?.value,
						dateFormats.dd_MM_yyyy_with_time_in_bracket_AM_PM
					),
			},
		],
		[]
	)
	const farmsKMLColumn: GridColDef<GridValidRowModel>[] = useMemo(
		() => [
			{
				field: 'id',
				headerName: 'S. No',
				minWidth: 80,
				flex: 1,
				renderCell: (params) =>
					params.api.getRowIndexRelativeToVisibleRows(params.row.id) + 1,
			},
			{
				field: 'landmark',
				headerName: 'Farm Location',
				minWidth: 150,
				flex: 1,
				renderCell: (params) => {
					const location = params?.row?.location
					return (
						<Typography
							component={Link}
							to={getGoogleMapLink(location?.x, location?.y)}
							target='_blank'
							sx={{
								color: 'black',
								textDecoration: 'none',
								textTransform: 'capitalize',
							}}>
							{params?.value}
						</Typography>
					)
				},
			},
			{
				field: 'size',
				headerName: 'Farms Size (hect)',
				flex: 1,
				minWidth: 150,
			},
			{
				field: 'farmArea',
				headerName: 'KML File',
				minWidth: 150,
				flex: 1,
				renderCell: (params) => {
					if (params.value) {
						return (
							<>
								<Button
									variant='text'
									onClick={(e) => {
										e.preventDefault()
										const location = convertStringToArray(
											params.row.farmLocation
										)

										handleViewKMLFile(
											params?.row?.farmArea,
											{ x: location[0], y: location[1] },
											params?.row?.id
										)
									}}>
									View File
								</Button>
								<Download
									onClick={(event) => {
										event.stopPropagation()
										generateKML({
											...params.row,
										} as IFarm)
									}}
								/>
							</>
						)
					}
					return (
						<Button
							variant='text'
							onClick={(e) => {
								e.stopPropagation()
								const location = convertStringToArray(params.row.farmLocation)
								setSearchParams((urlParams) => {
									urlParams.set('farmId', params?.row?.id)
									urlParams.set('lat', location[0])
									urlParams.set('long', location[1])
									return urlParams
								})
								setShowMap(true)
							}}
							startIcon={<AddCircle />}
							sx={{
								alignSelf: 'end',
							}}>
							Add
						</Button>
					)
				},
			},
			{
				field: 'farmsAddedDate',
				headerName: 'Created At',
				flex: 1,
				minWidth: 150,
				renderCell: (params) =>
					getFormattedDate(
						params?.value,
						dateFormats.dd_MM_yyyy_with_time_in_bracket_AM_PM
					),
			},
		],
		[convertStringToArray, handleViewKMLFile, setSearchParams]
	)

	return (
		<StyledContainer>
			<TabContext value={tab}>
				<Stack direction='row' justifyContent='space-between'>
					<Stack direction='row' gap={theme.spacing(4)}>
						{farmsButtons.map((button) => (
							<Button
								key={button.value}
								variant={tab === button.value ? 'contained' : 'outlined'}
								onClick={button.handleClick}
								sx={{
									alignSelf: 'start',
									borderRadius: 5,
								}}>
								{button.displayText}
							</Button>
						))}
					</Stack>
					{tab === farmTabTypes.kml && (
						<Stack direction='row' alignItems='center'>
							<Checkbox
								value={kmlRequired}
								checked={kmlRequired}
								onChange={() => {
									setKmlRequired((prev) => !prev)
								}}
							/>
							<Typography>KML Required Farms</Typography>
						</Stack>
					)}
				</Stack>
				<TabPanel value={farmTabTypes.all}>
					<CustomDataGrid
						columns={farmsAllColumn}
						rows={getFarmsListQuery?.data ?? []}
						showPagination={false}
						showPaginationDetails={false}
					/>
				</TabPanel>

				<TabPanel value={farmTabTypes.kml}>
					<CustomDataGrid
						columns={farmsKMLColumn}
						rows={getKmlListQuery?.data ?? []}
						showPagination={false}
						showPaginationDetails={false}
					/>
					{showMap ? (
						<GoogleMapsDraw
							open={showMap}
							handleModalClose={() => {
								setSearchParams((params) => {
									params.delete('lat')
									params.delete('long')
									return params
								})
								setTab(farmTabTypes.kml)
								setShowMap(false)
								setFarmCoordinates([])
							}}
							handleSave={handleSave}
							initialPolygons={farmCoordinates}
						/>
					) : null}
				</TabPanel>
			</TabContext>
		</StyledContainer>
	)
}

const StyledContainer = styled(Stack)(({ theme }) => ({
	padding: theme.spacing(3, 5),
}))
