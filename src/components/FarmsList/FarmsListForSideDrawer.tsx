import { ActionInformationDrawer } from '@/components/ActionInformationDrawer'
import { ArtisanProList } from '@/components/ArtisanProsList'
import { Close, PlaceOutlined } from '@mui/icons-material'
import { Box, IconButton, Stack, styled, Typography } from '@mui/material'
import { useState } from 'react'
import { SetURLSearchParams, useNavigate } from 'react-router-dom'
import { AddKmlButton } from '../AddKmlButton'
import { handleViewKMLFile } from '@/utils/helper'
import { FarmerInfo } from '@/interfaces'
import { Nullable } from '@/types'
interface IProps {
	farmer: Nullable<FarmerInfo>
	handleClose: () => void
	setShowMap: React.Dispatch<React.SetStateAction<boolean>>
	setSearchParams: SetURLSearchParams
	setFarmCoordinates: React.Dispatch<
		React.SetStateAction<google.maps.LatLng[] | google.maps.LatLngLiteral[]>
	>
}

export const FarmsListForSideDrawer = ({
	farmer,
	handleClose,
	setShowMap,
	setSearchParams,
	setFarmCoordinates,
}: IProps) => {
	const [showArtisaProDrawer, setShowArtisaProDrawer] = useState<string | null>(
		null
	)

	const navigate = useNavigate()
	return (
		<StyledContainer>
			<Stack className='header'>
				<Typography variant='body2'>
					{farmer?.name}{' '}
					{farmer?.phoneNo ? `(${farmer?.countryCode}-${farmer?.phoneNo})` : ''}
				</Typography>
				<IconButton onClick={handleClose}>
					<Close />
				</IconButton>
			</Stack>

			<Stack className='container'>
				{farmer?.farms?.map((farm) => (
					<Box className='farm-li' key={`networks-${farm?.id}`}>
						<Stack flexDirection='row' alignItems='center'>
							<Typography textTransform='capitalize'>
								<b>Farm:</b>
								{'  '}
								{`${farm?.landmark}  (${farm?.fieldSize || 0} ${
									farm?.fieldSizeUnit
								})`}{' '}
							</Typography>
							<PlaceOutlined color='error' />
						</Stack>
						<AddKmlButton
							data={{
								name: farmer?.name,
								landmark: farmer?.address,
								coordinate: `(${farm?.location?.x},${farm?.location?.y})`,
								farmArea: farm?.farmArea || [],
								id: farmer?.id,
							}}
							farmId={farm?.id}
							handleViewKMLFile={(farmCoordinates, center, networkId) =>
								handleViewKMLFile({
									center,
									farmCoordinates,
									navigate,
									networkId,
									setFarmCoordinates,
									setShowMap: () => setShowMap(true),
								})
							}
							setSearchParams={setSearchParams}
							setShowMap={(bool: boolean) => setShowMap(bool)}
						/>
					</Box>
				))}
			</Stack>
			<ActionInformationDrawer
				open={!!showArtisaProDrawer}
				onClose={() => setShowArtisaProDrawer(null)}
				anchor='right'
				component={
					<ArtisanProList
						handleClose={() => setShowArtisaProDrawer(null)}
						id={showArtisaProDrawer || ''}
					/>
				}
			/>
		</StyledContainer>
	)
}

const StyledContainer = styled(Stack)(({ theme }) => ({
	// padding: theme.spacing(3, 5),
	justifyContent: 'space-around',
	'.header': {
		flexDirection: 'row',
		justifyContent: 'space-between',
		alignItems: 'center',
		padding: theme.spacing(4.5, 2),
		borderBottom: `${theme.spacing(0.125)} solid ${
			theme.palette.neutral['100']
		}`,
	},
	'.container': {
		padding: theme.spacing(2),
		'.farm-li': {
			display: 'flex',
			direction: 'row',
			justifyContent: 'space-between',
			alignItems: 'center',
		},
	},
}))
