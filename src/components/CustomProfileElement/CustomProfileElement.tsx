import { Nullable } from '@/types'
import { handleImageUpload } from '@/utils/helper'
import { CreateOutlined } from '@mui/icons-material'
import {
	Avatar,
	CircularProgress,
	FormHelperText,
	IconButton,
	Stack,
	Typography,
} from '@mui/material'
import {
	ChangeEvent,
	FC,
	useCallback,
	useEffect,
	useRef,
	useState,
} from 'react'
import { toast } from 'react-toastify'
interface IImage {
	id?: string
	url?: string
	path?: string
}

type TProps = {
	value?: Nullable<IImage> | undefined
	setValue?: (id: string, url: string) => void
	errorMessage?: string
	clearErrors?: () => void
}

export const CustomProfileElement: FC<TProps> = ({
	value,
	setValue,
	errorMessage,
	clearErrors,
}) => {
	const [isLoading, setIsLoading] = useState<boolean>(false)
	const [profileImage, setProfileImage] = useState<IImage>({
		id: '',
		url: '',
		path: '',
	})
	const ref = useRef<HTMLLabelElement | null>(null)

	const uploadProfile = useCallback(
		async (event: ChangeEvent<HTMLInputElement>) => {
			const file = event?.target?.files?.[0]
			if (!file) {
				throw new Error('Failed to get image')
			}
			setIsLoading(true)
			try {
				const data = await handleImageUpload(file)
				setValue?.(data.id, data.url)
				setProfileImage({ id: data.id, url: data.url, path: '' })
				clearErrors?.()
			} catch (err) {
				toast('Image upload failed')
			} finally {
				setIsLoading(false)
			}
		},
		[clearErrors, setValue]
	)

	useEffect(() => {
		if (!value) return
		setProfileImage({ id: value.id, url: value.url, path: '' })
	}, [value])

	return (
		<Stack alignItems='center' gap={1}>
			{isLoading ? (
				<Stack padding={2} borderRadius={1} bgcolor='custom.blue.A100'>
					<CircularProgress />
				</Stack>
			) : (
				<Stack
					ref={ref}
					htmlFor='profile-image-upload-input'
					component='label'
					position='relative'>
					<Avatar
						src={profileImage?.url || ''}
						sx={{
							width: 70,
							height: 70,
							border: errorMessage ? '1px solid red' : '1px solid black',
						}}
					/>

					<IconButton
						size='small'
						onClick={() => ref?.current?.click()}
						sx={{
							position: 'absolute',
							right: -15,
							bottom: -10,
							backgroundColor: 'grey.300',
							':hover': {
								backgroundColor: 'grey.300',
							},
						}}>
						<CreateOutlined fontSize='medium' color='inherit' />
					</IconButton>
					<input
						id='profile-image-upload-input'
						style={{
							width: '100%',
							height: '100%',
							cursor: 'pointer',
							position: 'absolute',
							opacity: 0,
						}}
						type='file'
						accept='image/*'
						onChange={(event) => uploadProfile(event)}
					/>
				</Stack>
			)}

			<FormHelperText error={Boolean(errorMessage)}>
				{errorMessage && (
					<Typography color='error' variant='caption'>
						{errorMessage}
					</Typography>
				)}
			</FormHelperText>
		</Stack>
	)
}
