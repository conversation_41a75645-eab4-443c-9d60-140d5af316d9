import { theme } from '@/lib/theme/theme'
import { Cancel } from '@mui/icons-material'
import { LoadingButton } from '@mui/lab'
import ImageOutlinedIcon from '@mui/icons-material/ImageOutlined'

import { MultipleTypeImageUploaderdDialog } from '.'
import { BatchProps, TemperatureImage, ListImageType, Payload } from '.'

import {
	Box,
	DialogActions,
	DialogContent,
	DialogTitle,
	IconButton,
	Input,
	InputLabel,
	MenuItem,
	Select,
	SelectChangeEvent,
	Stack,
	Typography,
} from '@mui/material'
import { ReactNode, useRef, useState } from 'react'
import { handleImageUpload } from '@/utils/helper'
import { toast } from 'react-toastify'

const options = [
	{
		value: 'chimneyImageIds',
		label: 'Chimney Image',
		fileType: 'chimney',
	},
	{
		value: 'quenchingImageIds',
		label: 'Quenching Image',
		fileType: 'quenching',
	},
	{
		value: 'processImageIds',
		label: 'Process Image',
		fileType: 'klin',
	},
	{
		value: 'temperatureImages',
		label: 'Temperature Image',
		fileType: 'crops',
	},
	{
		value: 'preQuenchingImageIds',
		label: 'Pre-Quenching Images',
		fileType: 'pre_quenching',
	},
	{
		value: 'biocharAdditionImageIds',
		label: 'Biochar Images',
		fileType: 'kiln_process_biochar_addition',
	},
]

// const MAX_FILE_SIZE = 10 * 1024 * 1024 // 10MB
const ERROR_MESSAGES = {
	NO_FILE: 'No file selected. Please choose a file to upload.',
	INVALID_TYPE: 'Unsupported file type. Please upload a valid image.',
	FILE_TOO_LARGE:
		'File size exceeds the 10MB limit. Please choose a smaller file.',
	UPLOAD_FAILURE: 'File upload failed. Please try again later.',
}

interface IProps {
	open: boolean
	handleClose: () => void
	dialogeElement?: ReactNode
	handleSaveClick: (payload: Payload) => void
	handleCancelClick: () => void
	title?: string | ReactNode
	label: string | ReactNode
	batchDetail?: BatchProps
}

export const MultipleTypeImageUploader = ({
	open,
	handleClose,
	dialogeElement,
	handleSaveClick,
	handleCancelClick,
	title,
	label,
}: IProps) => {
	const [selectedValues, setSelectedValues] = useState<string>(options[0].value)
	const [temperatureValue, setTemperatureValue] = useState<number>(20)
	const temperatureUnit = 'celsius' // Fixed it according to backend requirement
	const [listSelectedImages, setListSelectedImages] = useState<ListImageType[]>(
		[]
	)
	const [fileType, setFileType] = useState<string>(options[0].fileType)
	const fileInputRef = useRef<HTMLInputElement>(null)

	const handleIconButtonClick = () => {
		fileInputRef.current?.click()
	}

	const handleError = (error: { type: string; message: string }) => {
		toast.error(error.message)
	}

	const handleFileChange = async (
		event: React.ChangeEvent<HTMLInputElement>
	) => {
		try {
			if (!event.target.files || event.target.files.length === 0) {
				throw { type: 'NO_FILE', message: ERROR_MESSAGES.NO_FILE }
			}

			const file = event.target.files[0]

			// if (!ALLOWED_FILE_TYPES.includes(file.type)) {
			// 	throw { type: 'INVALID_TYPE', message: ERROR_MESSAGES.INVALID_TYPE }
			// }

			// if (file.size > MAX_FILE_SIZE) {
			// 	throw { type: 'FILE_TOO_LARGE', message: ERROR_MESSAGES.FILE_TOO_LARGE }
			// }
			const data = await handleImageUpload(file, fileType)
			if (!data || !data.id || !data.url) {
				throw {
					type: 'UPLOAD_FAILURE',
					message: ERROR_MESSAGES.UPLOAD_FAILURE,
				}
			}

			const selectedFile: ListImageType = {
				id: data.id,
				url: data.url,
			}
			setListSelectedImages((prevArray) => [...prevArray, selectedFile])
			event.target.value = ''
		} catch (error: any) {
			handleError(error)
		} finally {
			event.target.value = ''
		}
	}

	const handleRemoveImageFromSelected = (imgId: string) => {
		setListSelectedImages((prevArray) => {
			return prevArray.filter((item) => item.id !== imgId)
		})
	}

	const handleSelectChange = (event: SelectChangeEvent<string>) => {
		setSelectedValues(event.target.value as string)
		setFileType(
			options.find((ele) => ele.value === event.target.value)?.fileType ||
				'misc'
		)
	}

	const handleTemperatureValueChange = (event: any) => {
		const temperature = event.target.value
		setTemperatureValue(temperature)
	}

	const handleSave = () => {
		let data: TemperatureImage[] | string[]

		if (selectedValues === 'temperatureImages') {
			data = [
				{
					temperature: Number(temperatureValue),
					temperatureUnit,
					imageIds: listSelectedImages.map((i) => i.id),
				},
			]
		} else {
			data = listSelectedImages.map((item) => item.id)
		}

		// Final Payload
		const payload: Payload = {
			[selectedValues]: data,
		}

		handleSaveClick(payload)
	}

	return (
		<MultipleTypeImageUploaderdDialog
			open={open}
			onClose={handleClose}
			aria-labelledby='alert-dialog-title'
			aria-describedby='alert-dialog-description'
			fullWidth
			maxWidth='xs'
			PaperProps={{
				style: {
					padding: theme.spacing(1.2),
				},
			}}>
			<IconButton className='icon-button-align' onClick={handleClose}>
				<Cancel />
			</IconButton>
			<Box className='dialog-center'>
				{title ? <DialogTitle textAlign='center'>{title}</DialogTitle> : null}
			</Box>
			<DialogContent className='dialog-content-align'>
				{dialogeElement}
				<InputLabel>{label}</InputLabel>
				<Select
					value={selectedValues}
					onChange={handleSelectChange}
					className='select-menu-items'
					MenuProps={{
						PaperProps: {
							style: {
								maxHeight: 200,
							},
						},
						anchorOrigin: {
							vertical: 'bottom',
							horizontal: 'left',
						},
						transformOrigin: {
							vertical: 'top',
							horizontal: 'left',
						},
					}}>
					{options.map((option) => (
						<MenuItem key={option.value} value={option.value}>
							{option.label}
						</MenuItem>
					))}
				</Select>
				{selectedValues === 'temperatureImages' && (
					<Box className='align-item-box'>
						<Input
							id='temperature'
							type='number'
							sx={{
								width: '100%',
							}}
							onChange={handleTemperatureValueChange}
						/>
						<Typography className='temperature-align-center' component='span'>
							Celcius
						</Typography>
					</Box>
				)}
				<IconButton onClick={handleIconButtonClick}>
					<ImageOutlinedIcon className='upload-icon' />
					<input
						ref={fileInputRef}
						id='batchImageType'
						type='file'
						accept='.jpg, .jpeg, .png, .heic, .webp'
						style={{ display: 'none' }}
						onChange={handleFileChange}
					/>
				</IconButton>
				<Stack className='list-image-align'>
					{listSelectedImages.map((item) => {
						return (
							<Box key={item.id} position={'relative'}>
								{' '}
								<Box
									component={'img'}
									src={item.url}
									alt={`Image ${item.id}`}
									className='selected-image-align'
								/>
								<IconButton
									className='icon-image-cancel'
									onClick={() => {
										handleRemoveImageFromSelected(item.id)
									}}>
									<Cancel className='image-cancel-button' />
								</IconButton>
							</Box>
						)
					})}
				</Stack>
			</DialogContent>
			<DialogActions className='dialog-actions-align'>
				<LoadingButton
					variant='contained'
					onClick={() => {
						handleSave()
						// handleSaveClick()
					}}>
					Save
				</LoadingButton>
				<LoadingButton onClick={handleCancelClick}>Cancel</LoadingButton>
			</DialogActions>
		</MultipleTypeImageUploaderdDialog>
	)
}
