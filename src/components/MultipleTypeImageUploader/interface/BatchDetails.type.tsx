export interface BatchProps {
	artisianProId?: string | null | undefined
	csNetworkId?: string | null | undefined
	siteId?: string | null | undefined
	kilnId: string | undefined
	processId: string | undefined
}

export interface TemperatureImage {
	temperature: number
	temperatureUnit: string
	imageIds: string[]
}

export interface Processvideo {
    videoId: string | null | undefined
    thumbnailImageId: string | null | undefined
}

export type Payload = {
	[key: string]: TemperatureImage[] | string[] | Processvideo[]
}

export interface ListImageType {
	id: string
	url: string
}
