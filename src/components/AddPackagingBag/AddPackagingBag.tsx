import { yupResolver } from '@hookform/resolvers/yup'
import {
	FormControl,
	FormHelperText,
	IconButton,
	MenuItem,
	Stack,
	styled,
	Typography,
} from '@mui/material'
import { useCallback, useMemo } from 'react'
import { useForm } from 'react-hook-form'
import { useMutation, useQueryClient } from '@tanstack/react-query'
import { authAxios } from '@/contexts'
import { toast } from 'react-toastify'
import { LoadingButton } from '@mui/lab'
import { addPackagingBagSchema, TAddPackagingBag } from './schema'
import { useParams } from 'react-router-dom'
import { IArtisanProDetails, INetwork, IPackagingBag } from '@/interfaces'
import { Close } from '@mui/icons-material'
import { CustomFileUploader } from '../CustomFileUploader'
import { AxiosError } from 'axios'
import { CustomTextField } from '@/utils/components'

const acceptedImageTypes = ['png', 'jpg', 'jpeg', 'webp']
enum MeasuringType {
	volume = 'volume_based',
	weight = 'weight_based',
}
interface TProps {
	handleCloseDrawer: () => void
	editMode?: boolean
	bagDetails?: IPackagingBag
	csinkNetworkDetails?: INetwork
	subheading?: string
	addText?: string
	isCsink?: boolean
	artisanProDetails?: IArtisanProDetails
}

export const AddPackagingBag = ({
	handleCloseDrawer,
	editMode,
	bagDetails,
	addText = '',
	subheading,
	isCsink = true,
}: TProps) => {
	const queryClient = useQueryClient()

	const initialName = editMode ? bagDetails?.name : addText
	const initialValues = {
		name: initialName,
		bagType: bagDetails?.bagType ?? '',
		quantity: bagDetails?.quantity ?? 0,
		imageId: bagDetails?.imageURLs?.[0]?.id ?? '',
		measuringType: bagDetails?.measuringType ?? '',
	}

	const {
		register,
		formState: { errors },
		watch,
		setValue,
		clearErrors,
		handleSubmit,
	} = useForm<TAddPackagingBag>({
		defaultValues: initialValues,
		mode: 'all',
		resolver: yupResolver<TAddPackagingBag>(addPackagingBagSchema),
	})

	const { cSinkNetworkId, artisanProId } = useParams()

	const addPackagingBag = useMutation({
		mutationKey: ['addPackagingBag'],
		mutationFn: async ({
			name,
			bagType,
			quantity,
			imageId,
			measuringType,
		}: TAddPackagingBag) => {
			const formData = {
				name,
				bagType,
				measuringType,
				quantity,
				imageIds: [imageId],
				quantityUnit: measuringType === MeasuringType.volume ? 'litre' : 'kg',
			}
			const api = isCsink
				? `cs-network/${cSinkNetworkId}/bags`
				: `/artisian-pro/${artisanProId}/bags`
			return await authAxios.post(api, formData)
		},
		onError: (error: AxiosError) => {
			toast((error?.response?.data as { userToMessage: string })?.userToMessage)
		},
		onSuccess: ({ data }) => {
			toast(data?.message);
			queryClient.refetchQueries({ queryKey: ['getBagsList'] });
			handleCloseDrawer();
		},
	})

	const handleAddPackagingBag = useCallback(
		(values: TAddPackagingBag) => {
			addPackagingBag.mutate(values)
		},
		[addPackagingBag]
	)

	const editPackagingBag = useMutation({
		mutationKey: ['editPackagingBag', cSinkNetworkId, artisanProId],
		mutationFn: async (payload: TAddPackagingBag) => {
			const updatedPayload = {
				...payload,
				imageIds: [payload.imageId],
				quantityUnit:
					payload.measuringType === MeasuringType.volume ? 'litre' : 'kg',
			}
			const api = isCsink
				? `cs-network/${cSinkNetworkId}/bags/${bagDetails?.id}`
				: `/artisian-pro/${artisanProId}/bags/${bagDetails?.id}`
			return await authAxios.put(api, updatedPayload)
		},
		onError: (err: AxiosError) => {
			toast((err?.response?.data as { userToMessage: string })?.userToMessage)
		},
		onSuccess: ({ data }) => {
			toast(data?.message)
			queryClient.invalidateQueries({queryKey: ['getBagsList']});
			// queryClient.refetchQueries({ queryKey: ['getBagsList'] })
			handleCloseDrawer()
		},
	})

	const handleEditCSink = useCallback(
		(values: TAddPackagingBag) => {
			editPackagingBag.mutate(values)
		},
		[editPackagingBag]
	)

	const handleSave = useCallback(
		(values: TAddPackagingBag) =>
			editMode ? handleEditCSink(values) : handleAddPackagingBag(values),
		[editMode, handleEditCSink, handleAddPackagingBag]
	)

	const isLoading = useMemo(
		() => editPackagingBag?.isPending || addPackagingBag.isPending,
		[addPackagingBag.isPending, editPackagingBag?.isPending]
	)

	return (
		<StyleContainer>
			<Stack className='header'>
				<Stack
					direction='row'
					spacing={1}
					alignItems='center'
					width='100%'
					justifyContent='space-between'>
					<Stack>
						<Typography variant='h5'>
							{editMode ? 'Edit' : 'Add'} Bag
						</Typography>
						<Typography variant='subtitle1'>{subheading}</Typography>
					</Stack>
					<IconButton onClick={handleCloseDrawer}>
						<Close />
					</IconButton>
				</Stack>
			</Stack>
			<Stack className='container' component='form'>
				<Stack gap={5}>
					<CustomTextField
					schema={addPackagingBagSchema}
						fullWidth
						id='name'
						type='text'
						label='Enter Name '
						variant='outlined'
						error={!!errors.name?.message}
						helperText={errors?.name?.message}
						{...register('name')}
						
					/>
					<CustomTextField
					schema={addPackagingBagSchema}
						id='quantity'
						label='Enter quantity'
						autoComplete='off'
						variant='outlined'
						disabled={bagDetails?.inUse}
						error={!!errors.quantity?.message}
						helperText={errors?.quantity?.message}
						fullWidth
						
						{...register('quantity')}
					/>
					<FormControl>
						<CustomTextField
						schema={addPackagingBagSchema}
							select
							fullWidth
							value={watch('bagType')}
							label='Select the Packaging Bag Type'
							{...register('bagType')}
							id='bag-type'
							error={!!errors.bagType?.message}
							helperText={errors.bagType?.message}>
							<MenuItem value='metal'>Metal</MenuItem>
							<MenuItem value='sack'>Sack</MenuItem>
						</CustomTextField>
					</FormControl>
					<FormControl>
						<CustomTextField
						schema={addPackagingBagSchema}
							select
							fullWidth
							value={watch('measuringType')}
							label='Enter the Packaging Type'
							{...register('measuringType')}
							id='measuring-type'
							error={!!errors.measuringType?.message}
							helperText={errors.measuringType?.message}>
							<MenuItem value={MeasuringType.volume}>Volume</MenuItem>
							<MenuItem value={MeasuringType.weight}>Weight</MenuItem>
						</CustomTextField>
					</FormControl>
					<Stack rowGap={2}>
						<Typography variant='subtitle1'>Bag Image</Typography>
						<Stack rowGap={2} width='100%'>
							<CustomFileUploader
								imageUrl={bagDetails?.imageURLs?.[0]?.url}
								acceptFileTypes={acceptedImageTypes}
								heading='Add Bag image'
								sx={{
									height: { xs: 100, md: 150 },
									width: '100%',
								}}
								imageHeight={100}
								setUploadData={(data) => {
									setValue('imageId', data?.id)
									clearErrors('imageId')
								}}
								{...(editMode ? { mediaType: 'image' } : {})}
							/>
						</Stack>
						<FormHelperText error={Boolean(errors.imageId)}>
							{errors?.imageId?.message}
						</FormHelperText>
					</Stack>

					<LoadingButton
						loading={isLoading}
						disabled={isLoading}
						onClick={handleSubmit(handleSave)}
						variant='contained'>
						{editMode ? 'Save' : 'Add'}
					</LoadingButton>
				</Stack>
			</Stack>
		</StyleContainer>
	)
}

const StyleContainer = styled(Stack)(({ theme }) => ({
	gap: theme.spacing(2),
	'.header': {
		flexDirection: 'row',
		justifyContent: 'space-between',
		alignItems: 'center',
		padding: theme.spacing(4.5, 2),
		borderBottom: `${theme.spacing(0.125)} solid ${
			theme.palette.neutral['100']
		}`,
	},

	'.container': {
		padding: theme.spacing(2, 3),
		gap: theme.spacing(2.5),
	},
}))
