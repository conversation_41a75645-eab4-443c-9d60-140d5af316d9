import * as Yup from 'yup'
export const addPackagingBagSchema = Yup.object({
	name: Yup.string().required('Please enter bag name'),
	bagType: Yup.string().required('Please enter bag name'),
	imageId: Yup.string().required('Please Upload Image'),
	quantity: Yup.number()
		.typeError('Please enter quantity ')
		.required('Please enter quantity'),
	measuringType: Yup.string().required('Please enter measuring type'),
})

export type TAddPackagingBag = Yup.InferType<typeof addPackagingBagSchema>
