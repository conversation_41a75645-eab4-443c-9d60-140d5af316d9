import {
	<PERSON>,
	<PERSON>lapse,
	<PERSON>Item,
	<PERSON>ItemIcon,
	ListItemText,
	Stack,
	Tooltip,
	useTheme,
} from '@mui/material'

import ArrowDropDownRoundedIcon from '@mui/icons-material/ArrowDropDownRounded'
import ArrowRightRoundedIcon from '@mui/icons-material/ArrowRightRounded'
import FiberManualRecordRoundedIcon from '@mui/icons-material/FiberManualRecordRounded'
import { StyledListButton } from './SideDrawer.style'
import { useMemo, useState } from 'react'
import { CustomTooltip } from './CustomTooltip'
import { userRoles } from '@/utils/constant'
import { useAuthContext } from '@/contexts'

interface SideDrawerElementProps {
	id: string
	label: string
	icon: string
	url: string
	children: { id: string; label: string; url: string }[]
	isDrawerOpen: boolean
	openPanels: { [key: string]: boolean }
	activePanel: string[]
	handleToggle: (id: string) => void
	sidePanelItemClick: (args: { url: string }) => void
	isTabVisible: (tab: string) => boolean
}

interface SideDrawerMainHeadingsType {
	id: string
	label: string
	icon: string
	children: { id: string; label: string; url: string }[]
	isDrawerOpen: boolean
	openPanels: { [key: string]: boolean }
	activePanel: string[]
	sidePanelItemClick: (args: { url: string }) => void
	isTabVisible: (tab: string) => boolean
	tooltipOpen: string | null
	setTooltipOpen: React.Dispatch<React.SetStateAction<string | null>>
	isHeadingActive: boolean
	hasChildren: boolean
}

export const SideDrawerElement = ({
	id,
	label,
	icon,
	url,
	children,
	isDrawerOpen,
	openPanels,
	activePanel,
	handleToggle,
	sidePanelItemClick,
	isTabVisible,
}: SideDrawerElementProps) => {
	const theme = useTheme()
	const [tooltipOpen, setTooltipOpen] = useState<string | null>(null)
	const { userDetails } = useAuthContext()

	const isHeadingActive = useMemo(
		() => activePanel[2] === id,
		[activePanel, id]
	)
	const isChildActive = (childId: string) => activePanel[3] === childId

	const hasChildren = useMemo(() => children.length > 0, [children])

	const modifiedUrl = useMemo(() => {
		if (
			url === 'admin' &&
			![userDetails?.accountType as userRoles].includes(userRoles.Admin)
		) {
			return 'admin/user-management'
		}
		return url
	}, [url, userDetails?.accountType])

	if (!isTabVisible(url)) return null

	return (
		<ListItem
			key={id}
			disablePadding
			className='item-list'
			onMouseLeave={() => setTooltipOpen(null)}
			onMouseEnter={() => setTooltipOpen(id)}>
			<StyledListButton
				className={`list-button ${
					!activePanel[3] && isHeadingActive
						? 'selected-background'
						: 'unselected-background'
				}`}
				onClick={() => {
					handleToggle(id)
					sidePanelItemClick({ url: modifiedUrl })
				}}>
				<SideDrawerMainHeadings
					id={id}
					icon={icon}
					children={children}
					isDrawerOpen={isDrawerOpen}
					activePanel={activePanel}
					openPanels={openPanels}
					tooltipOpen={tooltipOpen}
					setTooltipOpen={setTooltipOpen}
					isTabVisible={isTabVisible}
					label={label}
					sidePanelItemClick={sidePanelItemClick}
					isHeadingActive={isHeadingActive}
					hasChildren={hasChildren}
				/>
			</StyledListButton>
			<Collapse className='collapse' in={openPanels[id]}>
				{isDrawerOpen && hasChildren && (
					<Stack className='children-list'>
						{children.map((child) =>
							isTabVisible(child?.url) ? (
								<StyledListButton
									key={child.id}
									className={`child-list-button ${
										isChildActive(child.id)
											? 'selected-background'
											: 'unselected-background'
									}`}
									onClick={() => sidePanelItemClick({ url: child.url })}>
									<Stack direction='row' alignItems='center' spacing={2}>
										<FiberManualRecordRoundedIcon
											className={`roundIcon ${
												isChildActive(child.id)
													? 'roundIcon-primary'
													: 'roundedIcon-grey'
											}`}
										/>
										<ListItemText
											className='child-item-text'
											primaryTypographyProps={{
												variant: 'subtitle1',
												color: isChildActive(child.id)
													? theme.palette.primary.light
													: theme.palette.neutral[500],
											}}
											primary={child.label}
										/>
									</Stack>
								</StyledListButton>
							) : null
						)}
					</Stack>
				)}
			</Collapse>
		</ListItem>
	)
}

const SideDrawerMainHeadings = ({
	id,
	icon,
	children,
	isDrawerOpen,
	activePanel,
	openPanels,
	tooltipOpen,
	setTooltipOpen,
	isTabVisible,
	label,
	sidePanelItemClick,
	isHeadingActive,
	hasChildren,
}: SideDrawerMainHeadingsType) => {
	const theme = useTheme()
	return (
		<Stack
			width='100%'
			direction='row'
			alignItems='center'
			className={` ${
				!activePanel[3] && isHeadingActive
					? 'selected-item-border'
					: 'unselected-item-border'
			}`}>
			{isDrawerOpen &&
				(hasChildren ? (
					isHeadingActive && openPanels[id] ? (
						<ArrowDropDownRoundedIcon className='arrow list-icon-active' />
					) : (
						<ArrowRightRoundedIcon
							className={isHeadingActive ? 'arrow list-icon-active' : 'arrow'}
						/>
					)
				) : (
					<Box className='dummy-arrow' />
				))}
			{isDrawerOpen ? (
				<ListItemIcon
					className={isHeadingActive ? 'list-icon-active' : 'list-icon'}>
					<Box
						component='img'
						className='icon'
						src={icon}
						height={20}
						width={20}
					/>
				</ListItemIcon>
			) : (
				<Tooltip
					open={tooltipOpen === id}
					componentsProps={{
						tooltip: {
							sx: {
								color: 'common.black',
								bgcolor: theme.palette.neutral[10],
							},
						},
					}}
					title={
						<CustomTooltip
							isTabVisible={isTabVisible}
							children={children}
							onlyLabel={label}
							handleClick={({ url }: { url: string }) => {
								sidePanelItemClick({ url })
								setTooltipOpen(null)
							}}
						/>
					}
					placement='right'
					arrow>
					<ListItemIcon
						className={isHeadingActive ? 'list-icon-active' : 'list-icon'}>
						<Box
							component='img'
							className='icon'
							src={icon}
							height={20}
							width={20}
							ml={2}
						/>
					</ListItemIcon>
				</Tooltip>
			)}
			<ListItemText
				primary={label}
				primaryTypographyProps={{
					variant: 'subtitle1',
					color: isHeadingActive
						? theme.palette.primary.light
						: theme.palette.neutral[500],
				}}
				className='item-list-text'
			/>
		</Stack>
	)
}
