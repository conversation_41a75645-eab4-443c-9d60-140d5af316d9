import admin from '@/assets/icons/admin.svg'
import artisan from '@/assets/icons/artisan-pro.svg'
import cSink from '@/assets/icons/c-sink.svg'
// import farmers from '@/assets/icons/farmer.svg'
import home from '@/assets/icons/home.svg'
import credits from '@/assets/icons/treeIcon.svg'
import production from '@/assets/icons/production.svg'
import { Avatar } from '@mui/material'
import { Logout } from '@mui/icons-material'
import { useAuthContext } from '@/contexts'
import { useMemo } from 'react'
import { userRoles } from '@/utils/constant'

export const useSideBarList = () => {
	const { userDetails } = useAuthContext()
	const list = useMemo(
		() => [
			{
				id: 'home',
				label: 'Home',
				icon: home,
				children: [],
				url: 'home',
			},
			{
				id: 'admin',
				label: 'Admin',
				icon: admin,
				url: 'admin',
				children: [
					// {
					// 	id: 'queries',
					// 	label: 'Queries',
					// 	url: 'admin/queries',
					// },
					{
						id: 'crm',
						label: 'CRM',
						url: 'admin/crm',
					},
					{
						id: 'projects',
						label: 'Website',
						url: 'admin/website',
					},
					{
						id: 'otp-bypass',
						label: 'SMS OTP Bypass',
						url: 'admin/otp-bypass',
					},
					{
						id: 'email-bypass',
						label: 'Email Bypass',
						url: 'admin/email-bypass',
					},
					{
						id: 'biomass',
						label: 'Biomass',
						url: 'admin/biomass',
					},

					{
						id: 'user-management',
						label: 'User Management',
						url: 'admin/user-management',
					},
					{
						id: 'entity-management',
						label: 'Entity Management',
						url: 'admin/entity-management',
					},
					{
						id: 'my-networks',
						label: 'My Networks',
						url: 'admin/my-networks',
					},
					{
						id: 'pending-task',
						label: 'Pending Task',
						url: 'admin/pending-task',
					},
					// {
					// 	id: 'add-constants',
					// 	label: 'Add Constants',
					// 	url: 'admin/add-constants',
					// },
					{
						id: 'settings',
						label: 'Settings',
						url: 'admin/settings',
					},
				],
			},
			{
				id: 'production',
				label: 'Production',
				icon: production,
				url:
					userDetails?.accountType == userRoles.compliance_manager
						? 'production/batches'
						: 'production',
				children:
					userDetails?.accountType == userRoles.compliance_manager
						? []
						: [
								{
									id: 'biomass',
									label: 'Biomass',
									url: 'production/biomass',
								},
								{
									id: 'batches',
									label: 'Batches',
									url: 'production/batches',
								},

								{
									id: 'inventory',
									label: 'Inventory',
									url: 'production/inventory',
								},
								{
									id: 'mixing',
									label: 'Mixing',
									url: 'production/mixing',
								},
								{
									id: 'packaging',
									label: 'Packaging',
									url: 'production/packaging',
								},
						  ],
			},
			{
				id: 'c-sink-network',
				label: 'C-Sink Network',
				icon: cSink,
				url: 'c-sink-network',
				children: [],
			},
			{
				id: 'c-sink-network',
				label: 'C-Sink Network',
				icon: cSink,
				url: `c-sink-network/${userDetails?.networkId}/details`,
				children: [],
			},
			{
				id: 'artisan-pro',
				label: 'Artisan Pros',
				icon: artisan,
				url: 'artisan-pro',
				children: [],
			},
			{
				id: 'artisan-pro',
				label: 'Artisan Pro',
				icon: artisan,
				url: `artisan-pro/${userDetails?.artisianProId}/details`,
				children: [],
			},
			// {
			// 	id: 'farmers',
			// 	label: 'Farmers',
			// 	icon: farmers,
			// 	url: 'farmers',
			// 	children: [],
			// },
			{
				id: 'credits',
				label: 'Credits',
				icon: credits,
				url: 'credits',
				children: [
					{
						id: 'projects',
						label: 'Projects',
						url: 'credits/projects',
					},
					{
						id: 'projects',
						label: 'Projects',
						url: `credits/projects/${userDetails?.csinkManagerDetails?.projectId}/details`,
					},
				],
			},
		],
		[
			userDetails?.accountType,
			userDetails?.artisianProId,
			userDetails?.csinkManagerDetails?.projectId,
			userDetails?.networkId,
		]
	)
	return list
}
export const bottomActionList = [
	{
		id: 'logout',
		label: 'Logout',
		icon: (
			<Logout
				sx={{
					height: 24,
					width: 24,
				}}
			/>
		),
		url: '',
	},
	{
		id: 'user',
		label: 'User',
		icon: (
			<Avatar
				sx={{
					height: 24,
					width: 24,
				}}
			/>
		),
		url: 'profile',
	},
]
