import {
	ListItem,
	ListItemButton,
	ListItemIcon,
	ListItemText,
	useTheme,
} from '@mui/material'
interface SideDrawerBottomElementProps {
	id: string
	setOpenLogoutConfirmation: React.Dispatch<React.SetStateAction<boolean>>
	icon: JSX.Element
	label: string
}

export const SideDrawerBottomElement = ({
	id,
	setOpenLogoutConfirmation,
	icon,
	label,
}: SideDrawerBottomElementProps) => {
	const theme = useTheme()
	return (
		<ListItem className='bottom-list-item' key={id} disablePadding>
			<ListItemButton
				className='bottom-list-button'
				onClick={() => {
					if (id === 'logout') return setOpenLogoutConfirmation(true)
				}}>
				<ListItemIcon className='bottom-list-icon'>{icon}</ListItemIcon>
				<ListItemText
					primary={label}
					primaryTypographyProps={{
						variant: 'subtitle2',
						color: theme?.palette?.neutral['500'],
					}}
				/>
			</ListItemButton>
		</ListItem>
	)
}
