import { Box, ListItemButton, alpha, styled } from '@mui/material'
import { filterValue } from '../../utils/constant'

interface IStyleProps {
	open?: boolean
}

export const StyledAppbar = styled(Box, {
	shouldForwardProp: (prop) => prop !== 'open',
})<IStyleProps>(({ theme, open }) => ({
	display: 'flex',
	'.app-bar': {
		position: 'fixed',
	},
	'.drawer': {
		'.MuiPaper-root': {
			width: open ? 250 : 60,
			overflow: 'hidden',
			transition: theme.transitions.create(['width'], {
				easing: theme.transitions.easing.sharp,
				duration: theme.transitions.duration.leavingScreen,
			}),
			...(open && {
				width: 250,
				transition: theme.transitions.create(['width'], {
					easing: theme.transitions.easing.sharp,
					duration: theme.transitions.duration.enteringScreen,
				}),
			}),
		},
		'.drawer-header': {
			alignItems: 'center',
			justifyContent: open ? 'space-between' : 'center',
			padding: theme.spacing(3.75, 3),
			'.logo-img': {
				height: 36,
				width: 136,
			},
			'.arrow-icon': {
				height: 18,
				width: 16,
			},
		},
	},
	'.tool-bar': {
		background: theme.palette.common.white,
		color: theme.palette.common.black,
		'.menu-icon-button': {
			marginRight: theme.spacing(0.625),
			display: open ? 'none' : 'inline-box',
		},
	},
	'.sidebar-item-container': {
		display: 'flex',
		flexDirection: 'column',
		justifyContent: 'space-between',
		height: '100%',
		paddingBottom: theme.spacing(2.5),
		overflowY: 'scroll',
		scrollbarWidth: 'none',
		'.item-list': {
			display: 'flex',
			flexDirection: 'column',
			alignItems: 'flex-start',
			'.selected-item-border': {
				paddingLeft: theme.spacing(open ? 2 : 0),
				borderLeft: `${theme.spacing(0.25)} solid ${
					theme.palette.primary.light
				}`,
			},
			'.unselected-item-border': {
				paddingLeft: theme.spacing(open ? 2 : 0),
				borderLeft: `${theme.spacing(0.25)} solid ${
					theme.palette.common.white
				}`,
			},
			'.list-button': {
				height: 56,
				width: '100%',
				display: 'flex',
				alignItems: 'center',
				gap: 6,
				padding: theme.spacing(0),
			},
			'.arrow': {
				color: theme.palette.neutral['300'],
			},
			'.dummy-arrow': {
				height: theme.spacing(3.5),
				width: theme.spacing(3.5),
			},
			'.collapse': {
				width: '100%',
				'.children-list': {
					alignItems: 'flex-start',
					width: '100%',
					'.child-item-text': {
						whiteSpace: 'nowrap',
						opacity: open ? 1 : 0,
					},
					'.child-list-button': {
						height: 40,
						width: '100%',
						paddingLeft: theme.spacing(6.7),
						gap: theme.spacing(3),
					},
				},
			},
		},

		'.item-list-text': {
			whiteSpace: 'nowrap',
			opacity: open ? 1 : 0,
		},
		'.bottom-list-button': {
			height: 56,
			width: '100%',
			display: 'flex',
			alignItems: 'center',
			gap: theme.spacing(2.6),
			paddingLeft: theme.spacing(2),
			'.bottom-list-icon': {
				minWidth: theme.spacing(3),
				color: theme.palette.neutral['300'],
				'.MuiBadge-badge': {
					margin: theme.spacing(-0.4, -0.3, 0, 0),
				},
			},
		},
	},
	'.main-component': {
		position: 'relative',
		minHeight: '100vh',
		overflowX: 'hidden',
		marginLeft: open ? 250 : 60,
		width: `calc( 100vw - ${open ? '270px' : '80px'} )`, // 270px = 250px + 20px (20px for scroll bar)
		flexGrow: 1,
		'.top-section': {
			flexDirection: 'row-reverse',
			'.btnOldVersion': {
				textTransform: 'capitalize',
				width: 200,
			},
		},
	},
}))

export const StyledListButton = styled(ListItemButton)(({ theme }) => ({
	'&.selected-background': {
		background: alpha(theme.palette.primary.light, 0.05),
	},
	'&.unselected-background': {
		background: theme.palette.common.white,
	},
	'.roundIcon': {
		fontSize: 6,
	},
	'.roundIcon-primary': {
		color: theme.palette.primary.main,
	},
	'.roundedIcon-grey': {
		color: theme.palette.neutral[500],
	},

	'.list-icon-active': {
		minWidth: 30,
		filter: filterValue,
	},
	'.list-icon': {
		minWidth: 30,
		filter: theme.palette.neutral['300'],
	},
}))
