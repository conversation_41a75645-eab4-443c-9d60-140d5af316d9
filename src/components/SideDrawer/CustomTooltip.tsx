import { ListItem, MenuItem, Typography } from '@mui/material'

interface IProps {
	onlyLabel?: string
	isTabVisible:(tab: string) => boolean
	children?: {
		id?: string
		url?: string
		label?: string
	}[]
	handleClick: ({ url }: { url: string }) => void
}

export const CustomTooltip = ({ onlyLabel, children, handleClick ,isTabVisible}: IProps) => {
	
	if (!children?.length)
		return <Typography variant='body1'>{onlyLabel}</Typography>
	return (
		<>
			<ListItem>
				<Typography variant='h5'>{onlyLabel}</Typography>
			</ListItem>
			{children?.map((item) =>isTabVisible(item?.url ?? '') ?(
				<MenuItem
					key={item?.id}
					sx={{ userSelect: 'none' }}
					onClick={(e) => {
						e.stopPropagation()
						handleClick({ url: item?.url ?? '/dashboard/home' })
					}}>
					{item?.label}
				</MenuItem>
			):null)}
		</>
	)
}
