import arrowLeft from '@/assets/icons/arrow-bar-left.svg'
import logo from '@/assets/icons/logo.svg'
import logoText from '@/assets/icons/logoText.svg'
import {
	Box,
	Button,
	Drawer,
	IconButton,
	List,
	Stack,
	Typography,
} from '@mui/material'
import { FC, Fragment, ReactNode, useCallback, useMemo, useState } from 'react'
import { useLocation, useNavigate } from 'react-router-dom'
import { bottomActionList, StyledAppbar, useSideBarList } from '.'
import { useAuthContext, useDrawerStore } from '@/contexts'
import {
	APP_DEV_URL,
	APP_PROD_URL,
	APP_STAGE_URL,
	isDevOrQA,
	userRoles,
} from '@/utils/constant'
import { Confirmation } from '../Confirmation'
import { useAuthStore } from '@/contexts/Auth/useAuthStore'
import { SideDrawerElement } from './SideDrawerElement'
import { SideDrawerBottomElement } from './SideDrawerBottomElement'
const { VITE_PUBLIC_APP_ENV } = import.meta.env

const Content = ({ isDrawerOpen }: { isDrawerOpen: boolean }) => {
	const navigate = useNavigate()
	const [openPanels, setOpenPanels] = useState<{ [key: string]: boolean }>({})
	const location = useLocation()
	const activePanel = location.pathname.split('/')
	const { logout, userDetails } = useAuthContext()
	const [OpenlogoutConfirmation, setOpenLogoutConfirmation] = useState(false)
	const sideBarList = useSideBarList()
	// const isRouteAllowed=useUser()

	const sidePanelItemClick = useCallback(
		({ url }: { url: string }) => {
			navigate(url)
		},
		[navigate]
	)

	const routeForCsinkManagerProjects = useMemo(() => {
		return userDetails?.csinkManagerDetails?.projectId
			? `credits/projects/${userDetails?.csinkManagerDetails?.projectId}/details`
			: 'credits/projects'
	}, [userDetails?.csinkManagerDetails?.projectId])

	const routeForArtisanProAdmin = !userDetails?.artisianProId
		? ''
		: Number(userDetails?.artisanProCount) > 1
		? 'artisan-pro'
		: `artisan-pro/${userDetails?.artisianProId}/details`

	const routeForcsinkNetworkAdmin = !userDetails?.networkId
		? ''
		: Number(userDetails?.csinkNetworkCount) > 1
		? 'c-sink-network'
		: `c-sink-network/${userDetails?.networkId}/details`

	const myNetworkRoute = isDevOrQA ? 'admin/my-networks' : ''

	const isTabVisible = useCallback(
		(tab: string) => {
			const PermissionsToShowSidebarTabsByUserRole: {
				[key: string]: string[]
			} = {
				[userRoles.Admin]: [
					'home',
					'admin',
					'admin/queries',
					'admin/crm',
					'admin/website',
					'admin/website/projects',
					'admin/projects',
					'admin/otp-bypass',
					'admin/email-bypass',

					// 'admin/geo-tag',

					// 'admin/pending-tasks',

					'admin/user-management',
					myNetworkRoute,
					'admin/entity-management',
					'admin/pending-task',
					// 'admin/add-constants',
					'admin/settings',
					'admin/biomass',
					'production',
					'production/biomass',
					'production/batches',
					'production/mixing',
					'production/packaging',
					'production/inventory',
					'c-sink-network',
					'artisan-pro',
					// 'farmers',
					'credits',
					'credits/projects',
				],
				[userRoles.BiomassAggregator]: [
					// 'contentManagement',
					'home',
					'admin',
					// 'admin/pending-tasks',

					'admin/user-management',
					'admin/entity-management',
					myNetworkRoute,
					'admin/settings',

					'production',
					'production/biomass',
					'production/batches',
					'c-sink-network',
					'artisan-pro',
					// 'farmers',
				],
				[userRoles.CsinkManager]: [
					'home',
					'admin',
					// 'admin/pending-tasks',
					'admin/user-management',
					'admin/otp-bypass',
					'admin/email-bypass',
					'admin/entity-management',
					myNetworkRoute,
					'admin/biomass',
					// 'admin/add-constants',
					'admin/settings',
					'production',
					'production/biomass',
					'production/batches',
					'c-sink-network',
					'artisan-pro',
					// 'farmers',
					'credits',
					routeForCsinkManagerProjects,
					// 'credits/projects',
				],
				[userRoles.CirconomyEmployee]: [
					'admin',
					'admin/queries',
					'admin/crm',
					'admin/projects',
					'admin/biomass',
				],
				[userRoles.Manager]: [
					'home',
					'admin',
					myNetworkRoute,
					'production',
					'production/biomass',
					'production/batches',
					routeForcsinkNetworkAdmin,
					routeForArtisanProAdmin,
				],
				[userRoles.cSinkNetwork]: [
					'home',
					'admin',
					// 'admin/pending-tasks',
					myNetworkRoute,
					'admin/user-management',
					'production',
					'production/biomass',
					'production/batches',
					routeForcsinkNetworkAdmin,
					// `c-sink-network/${userDetails?.networkId}/details`,
				],
				[userRoles.ArtisanPro]: [
					'home',
					'admin',
					// 'admin/pending-tasks',
					'admin/user-management',
					myNetworkRoute,
					'production',
					'production/biomass',
					'production/batches',
					routeForArtisanProAdmin,
					// `artisan-pro/${userDetails?.artisianProId}/details`,
				],
				[userRoles.artisanProNetworkManager]: [
					'home',
					'production',
					'production/biomass',
					'production/batches',
					'admin',
					'admin/entity-management',
					'admin/user-management',
					'artisan-pro',
				],
				[userRoles.compliance_manager]: [
					// 'home',
					// 'production',
					'production/batches',
				],
				[userRoles.ceres_auditor]: ['credits'],
			}
			return PermissionsToShowSidebarTabsByUserRole[
				userDetails?.accountType as userRoles
			]?.includes(tab)
		},
		[
			myNetworkRoute,
			routeForArtisanProAdmin,
			routeForcsinkNetworkAdmin,
			routeForCsinkManagerProjects,
			userDetails?.accountType,
		]
	)
	const handleToggle = (id: string) => {
		setOpenPanels((prev) => ({
			...prev,
			[id]: !prev[id],
		}))
	}
	return (
		<List className='sidebar-item-container'>
			<Box>
				{sideBarList.map((item, index) => (
					<SideDrawerElement
						key={`${item.id}-${index}`}
						{...item}
						isDrawerOpen={isDrawerOpen}
						openPanels={openPanels}
						handleToggle={handleToggle}
						activePanel={activePanel}
						sidePanelItemClick={sidePanelItemClick}
						isTabVisible={isTabVisible}
					/>
				))}
			</Box>
			<Box>
				{bottomActionList.map(({ id, label, icon }, index) => (
					<SideDrawerBottomElement
						key={`${id}-${index}`}
						id={id}
						label={label}
						icon={icon}
						setOpenLogoutConfirmation={setOpenLogoutConfirmation}
					/>
				))}
				{OpenlogoutConfirmation ? (
					<Confirmation
						confirmationText='Are you sure you want to logout?'
						open={OpenlogoutConfirmation}
						handleClose={() => setOpenLogoutConfirmation(false)}
						handleNoClick={() => setOpenLogoutConfirmation(false)}
						handleYesClick={() => {
							logout()
						}}
					/>
				) : null}
			</Box>
		</List>
	)
}

export const SideDrawer: FC<{ children: ReactNode }> = ({ children }) => {
	const { isDrawerOpen, setIsDrawerOpen } = useDrawerStore()
	const handleDrawer = useCallback(() => setIsDrawerOpen(), [setIsDrawerOpen])
	const { userDetails } = useAuthContext()
	const tokenStore = useAuthStore()
	const biomassAssignBanner = useMemo(() => {
		const showBanner = false
		showBanner
		userDetails?.accountType !== userRoles.Admin &&
			userDetails?.accountType !== userRoles.CirconomyEmployee &&
			!userDetails?.isBiomassAdded
		let bannerText = ''
		if (userDetails?.accountType === userRoles.CsinkManager) {
			bannerText = 'No biomass is Added, please add one'
		} else if (userDetails) {
			bannerText = 'No biomass is Added, please ask your admin to add one'
		}
		return {
			showBanner,
			bannerText,
		}
	}, [userDetails])

	const redirectUrlProvider = () => {
		const value = VITE_PUBLIC_APP_ENV || 'DEV'
		switch (value) {
			case 'PROD':
				return APP_PROD_URL
			case 'STAGE':
				return APP_STAGE_URL
			default:
				return APP_DEV_URL
		}
	}
	const handleLoginToOldVersion = () => {
		const redirectUrl = `${redirectUrlProvider()}login?token=${
			tokenStore?.token
		}`
		// Redirect to the second app's login page with the token in the URL
		window.location.href = redirectUrl
	}

	const showOlderVersionBtn = useMemo(() => {
		if (userDetails?.accountType) {
			return userDetails?.accountType === userRoles.Admin
		} else return false
	}, [userDetails?.accountType])

	return (
		<StyledAppbar open={isDrawerOpen}>
			<Drawer className='drawer' variant='permanent' open={isDrawerOpen}>
				<Stack className='drawer-header' direction='row'>
					{isDrawerOpen ? (
						<Fragment>
							<Box
								className='logo-img'
								component='img'
								src={logoText}
								alt='logo'
							/>
							<IconButton onClick={handleDrawer}>
								<Box
									component='img'
									src={arrowLeft}
									height={30}
									width={30}
									className='arrow-icon'
								/>
							</IconButton>
						</Fragment>
					) : (
						<IconButton onClick={handleDrawer} className='menu-icon-button'>
							<Box
								component='img'
								src={logo}
								height={30}
								width={30}
								className='arrow-icon'
							/>
						</IconButton>
					)}
				</Stack>
				<Content isDrawerOpen={isDrawerOpen} />
			</Drawer>
			<Box component='main' className='main-component'>
				<Stack className='top-section'>
					{showOlderVersionBtn ? (
						<Button
							variant='text'
							color='inherit'
							className='btnOldVersion'
							onClick={handleLoginToOldVersion}>
							<Typography variant='body2' textAlign={'center'}>
								Go back to <br />
								old version!
							</Typography>
						</Button>
					) : null}
					<Stack width={'100%'}>
						{['DEV', 'STAGE', 'QA'].includes(VITE_PUBLIC_APP_ENV) ? (
							<Stack
								width='100%'
								alignItems='center'
								sx={{
									padding: 1,
									backgroundColor:
										VITE_PUBLIC_APP_ENV === 'DEV'
											? 'error.main'
											: 'custom.pink.A500',
								}}>
								<Typography variant='body2'>
									{VITE_PUBLIC_APP_ENV === 'STAGE'
										? 'DEMO'
										: VITE_PUBLIC_APP_ENV}
								</Typography>
							</Stack>
						) : null}
						{biomassAssignBanner?.showBanner ? (
							<Box
								bgcolor={
									VITE_PUBLIC_APP_ENV === 'DEV'
										? 'error.main'
										: 'custom.pink.A500'
								}
								height={30}
								display='flex'
								justifyContent='center'
								alignItems='center'>
								<Typography textAlign='center' fontWeight='bold'>
									{biomassAssignBanner.bannerText}
								</Typography>
							</Box>
						) : null}
					</Stack>
				</Stack>

				{children}
			</Box>
		</StyledAppbar>
	)
}
