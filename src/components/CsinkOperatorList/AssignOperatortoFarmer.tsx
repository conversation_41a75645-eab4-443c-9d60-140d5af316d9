import { authAxios } from '@/contexts'
import { IAssignOperator, IEditOperator } from '@/interfaces'
import { theme } from '@/lib/theme/theme'
import { TModal } from '@/types'
import { userRoles } from '@/utils/constant'
import { Cancel } from '@mui/icons-material'
import {
	Dialog,
	IconButton,
	DialogTitle,
	DialogContent,
	Stack,
	DialogActions,
	Button,
	MenuItem,
	Select,
	FormControl,
	InputLabel,
	OutlinedInput,
} from '@mui/material'
import { useMutation, useQuery, useQueryClient } from '@tanstack/react-query'
import { AxiosError } from 'axios'
import { useState } from 'react'
import { useParams } from 'react-router-dom'
import { toast } from 'react-toastify'
interface IProps extends TModal {
	id: string
	onEditOperator: (operator: IAssignOperator) => void
}
export const AssignOperatortoFarmer = ({
	open,
	onClose,
	id,
	onEditOperator,
}: IProps) => {
	const [selectedOperator, setSelectedOperator] =
		useState<IEditOperator | null>(null)

	const queryClient = useQueryClient()
	const { cSinkNetworkId } = useParams()
	const fetchUnassignedOperators = useQuery({
		queryKey: ['fetchUnassignedOperators', cSinkNetworkId, id],
		queryFn: () => {
			return authAxios<{ count: number; operators: IEditOperator[] }>(
				`/cs-network/${cSinkNetworkId}/site/${id}/operators/v2`
			)
		},
		enabled: !!cSinkNetworkId && !!id,
	})
	const assignOperatorMutation = useMutation({
		mutationKey: ['AssignOperator', selectedOperator],
		mutationFn: async () => {
			const payload = {
				operatorIds: [selectedOperator?.id],
			}
			return authAxios.post(
				`/cs-network/${cSinkNetworkId}/site/${id}/operator/assign`,
				payload
			)
		},
		onSuccess: (data) => {
			toast(data?.data?.message)
			queryClient.refetchQueries({ queryKey: ['allCsinkManager'] })
			onClose()
			setSelectedOperator(null)
		},
		onError: (err: AxiosError) => {
			toast((err?.response?.data as { messageToUser: string })?.messageToUser)
		},
	})
	const handleAssignOperator = () => {
		if (!selectedOperator) return

		if (selectedOperator.accountType === userRoles.role_awaiting) {
			onClose()
			toast('Please edit the operator details before assigning.')
			onEditOperator({ ...selectedOperator, siteId: id })
		} else {
			assignOperatorMutation.mutate()
		}
	}

	return (
		
			<Dialog
				open={open}
				onClose={onClose}
				fullWidth
				sx={{
					'.MuiPaper-root': {
						padding: 2,
					},
				}}
				maxWidth='sm'>
				<IconButton sx={{ position: 'absolute', right: 10 }} onClick={onClose}>
					<Cancel />
				</IconButton>
				<DialogTitle textAlign='center'>Assign Operator</DialogTitle>
				<DialogContent>
					<Stack marginTop={3} rowGap={3}>
						<FormControl fullWidth>
							<InputLabel>Select the Operator</InputLabel>
							<Select
								id='selectOperator'
								label='Select the Operator'
								value={selectedOperator?.id || ''}
								input={
									<OutlinedInput
										label='Select Farmers'
										sx={{
											borderRadius: '8px',
										}}
									/>
								}
								MenuProps={{
									anchorOrigin: {
										vertical: 'bottom',
										horizontal: 'center',
									},
									PaperProps: {
										style: {
											maxHeight: theme.spacing(50),
										},
									},
								}}
								onChange={(e) => {
									const selectedId = e.target.value;
									const operators =
										fetchUnassignedOperators?.data?.data?.operators || [];
									const operator = operators.find(
										(op) => op.id === selectedId
									);
									setSelectedOperator(operator ?? null);
								}}>
								{fetchUnassignedOperators?.data?.data?.operators?.map(
									(operator) => (
										<MenuItem key={operator?.id} value={operator?.id}>
											{operator?.name}
										</MenuItem>
									)
								)}
							</Select>
						</FormControl>
					</Stack>
				</DialogContent>
				<DialogActions sx={{ display: 'flex', justifyContent: 'center' }}>
					<Button
						onClick={handleAssignOperator}
						disabled={assignOperatorMutation.isPending}
						variant='contained'>
						Save
					</Button>
				</DialogActions>
			</Dialog>
		
	)
}
