import { Avatar, IconButton, Stack, styled, Typography } from '@mui/material'
import { useQuery } from '@tanstack/react-query'
import { authAxios } from '@/contexts'
import { useParams } from 'react-router-dom'
import { ICsinkOperator, KilnOperator } from '@/interfaces'
import { Close } from '@mui/icons-material'

interface IProps {
	onClose: () => void
	operatorsData?: KilnOperator[]
	heading: string
}
export const CsinkOperatorList = ({
	onClose,
	heading,
	operatorsData,
}: IProps) => {
	const { cSinkNetworkId } = useParams()
	const operatorsQuery = useQuery({
		queryKey: ['OperatorsQuery', cSinkNetworkId],
		queryFn: () => {
			const data = authAxios.get<{
				operators: ICsinkOperator[]
			}>(`/cs-network/${cSinkNetworkId}/operator`)
			return data
		},

		enabled: !!cSinkNetworkId,
	})

	return (
		<StyleContainer>
			<Stack className='header'>
				<Typography variant='h4'>{heading}</Typography>
				<IconButton onClick={onClose}>
					<Close />
				</IconButton>
			</Stack>
			<Stack className='container'>
				{operatorsData
					? operatorsData?.map((operator) => (
						<Stack className='operator-container' key={operator?.id}>
							<Stack className='avatar-name'>
								<Avatar src={operator?.profileImage?.url} />
								<Stack flexDirection='column'>
									<Typography variant='subtitle1'>
										{operator?.name}
									</Typography>
									<Typography className='grey-text'>
										{operator.phoneNo &&
											`${operator?.countryCode} ${operator?.phoneNo}`}
										<br />
										{operator?.email}
									</Typography>
								</Stack>
							</Stack>
						</Stack>
					))
					: operatorsQuery?.data?.data?.operators?.map((operator) => (
						<Stack className='operator-container' key={operator?.id}>
							<Stack className='avatar-name'>
								<Avatar src={operator?.profileImage?.url} />
								<Stack flexDirection='column'>
									<Typography variant='subtitle1'>
										{operator?.name}
									</Typography>
									<Typography className='grey-text'>
										{operator?.countryCode && operator?.number && (
											<>
												{`${operator.countryCode} ${operator.number}`}
												<br />
											</>
										)}
										{operator?.email}
									</Typography>

								</Stack>
							</Stack>
							{!operatorsData && (
								<Stack>
									<Typography variant='subtitle1'>
										Farmers Assigned
									</Typography>
									{operator?.farmers?.map((i) => (
										<Typography className='grey-text' key={i?.id}>
											{`${i?.name} `}
											{i?.countryCode && i?.phoneNo && (
												<>
													{` (${i.countryCode} ${i.phoneNo})`}
												</>
											)}
										</Typography>
									))}
								</Stack>
							)}
						</Stack>
					))}
			</Stack>
		</StyleContainer>
	)
}
const StyleContainer = styled(Stack)(({ theme }) => ({
	gap: theme.spacing(2),
	'.header': {
		flexDirection: 'row',
		justifyContent: 'space-between',
		alignItems: 'center',
		padding: theme.spacing(4.5, 2),
		borderBottom: `${theme.spacing(0.125)} solid ${theme.palette.neutral['100']
			}`,
	},
	'.container': {
		padding: theme.spacing(2),
		gap: theme.spacing(1),
		maxHeight: 'calc( 100vh - 200px)',
		overflowY: 'auto',
	},
	'.operator-container': {
		padding: theme.spacing(1),
		flexDirection: 'row',
		justifyContent: 'space-between',
		borderBottom: `${theme.spacing(0.125)} solid ${theme.palette.neutral['100']
			}`,
		'.avatar-name': {
			flexDirection: 'row',
			alignItems: 'start',
			gap: theme.spacing(1),
		},
		'.grey-text': {
			color: theme.palette.neutral[300],
			fontSize: 13,
			fontWeight: 400,
		},
	},
}))
