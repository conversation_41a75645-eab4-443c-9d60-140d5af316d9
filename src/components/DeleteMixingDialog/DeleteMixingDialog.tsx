import * as React from 'react'
import Button from '@mui/material/Button'
import { styled } from '@mui/material/styles'
import Dialog from '@mui/material/Dialog'
import DialogTitle from '@mui/material/DialogTitle'
import DialogContent from '@mui/material/DialogContent'
import DialogActions from '@mui/material/DialogActions'
import { Stack } from '@mui/material'
const BootstrapDialog = styled(Dialog)(({ theme }) => ({
	'& .MuiDialogContent-root': {
		padding: theme.spacing(2),
	},
	'& .MuiDialogActions-root': {
		padding: theme.spacing(1),
	},
}))

export const DeleteMixingDialog = ({
	open,
	close,
	save,
	children,
}: {
	open: boolean
	close: () => void
	save: () => void
	children: React.ReactNode
}) => {
	return (
		<BootstrapDialog
			onClose={close}
			aria-labelledby='customized-dialog-title'
			open={open}
			maxWidth={'sm'}
			fullWidth>
			<DialogTitle sx={{ m: 0, p: 2 }} id='customized-dialog-title'>
			</DialogTitle>
			<DialogContent>{children}</DialogContent>
			<DialogActions>
				<Stack width="100%" direction="row" justifyContent="space-evenly">
					<Button onClick={close}>
						Close
					</Button>
					<Button variant='contained' autoFocus onClick={save}>
						Yes
					</Button>
				</Stack>
			</DialogActions>
		</BootstrapDialog>
	)
}
