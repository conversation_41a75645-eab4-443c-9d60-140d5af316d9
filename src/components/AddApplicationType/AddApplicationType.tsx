import {
	Box,
	Checkbox,
	IconButton,
	Stack,
	styled,
	Typography,
} from '@mui/material'
import { useState } from 'react'
import { LoadingButton } from '@mui/lab'
import CheckboxIcon from '@/assets/icons/checkboxIcon.svg'
import { useParams } from 'react-router-dom'
import {
	IArtisanProDetails,
	ICsinkApplicationType,
	IIPreferredBiomass,
	INetwork,
} from '@/interfaces'
import { Close } from '@mui/icons-material'
import {
	useMutation,
	useQuery,
	useQueryClient,
	UseQueryResult,
} from '@tanstack/react-query'
import { authAxios } from '@/contexts'
import { toast } from 'react-toastify'
import { AxiosError } from 'axios'
import { NoData } from '../NoData'
import { capitalizeFirstLetter } from '@/utils/helper'

interface TProps {
	handleCloseDrawer: () => void
	preferredBiomassList?: IIPreferredBiomass[]
	cSinkNetworkDetails?: INetwork
	artisanProDetails?: IArtisanProDetails
	subheading?: string
	isCsink?: boolean
	applicationTypeQuery?: UseQueryResult<
		ICsinkApplicationType[] | undefined,
		Error
	>
}

export const AddApplicationType = ({
	handleCloseDrawer,
	applicationTypeQuery,
	subheading,
	isCsink = true,
	artisanProDetails,
	cSinkNetworkDetails,
}: TProps) => {
	const { cSinkNetworkId, artisanProId } = useParams()
	const queryClient = useQueryClient()
	const data =
		applicationTypeQuery?.data
			?.map((item) => {
				if (!item?.id) return ''
				return item?.id
			})
			.filter((item) => !!item) ?? []

	const [selectedApplicationType, setSelectedAPplicationType] =
		useState<string[]>(data)

	const fetchAllApplicationTypeQuery = useQuery({
		queryKey: ['fetchAllApplicationTypeQuery'],
		queryFn: () => {
			const URI = isCsink
				? `/csink-manager/${cSinkNetworkDetails?.csinkManagerId}/application-types`
				: `/csink-manager/${artisanProDetails?.csinkManagerId}/application-types`
			return authAxios<ICsinkApplicationType[]>(URI)
		},
	})
	const addApplicationType = useMutation({
		mutationKey: ['addApplicationType', selectedApplicationType],
		mutationFn: async () => {
			const payload = {
				applicationTypeIds: selectedApplicationType,
			}
			const api = isCsink
				? `/cs-network/${cSinkNetworkId}/application-types/assign`
				: `/artisian-pro/${artisanProId}/application-types/assign`

			return await authAxios.put(api, payload)
		},
		onError: (error: AxiosError) => {
			toast((error?.response?.data as { messageToUser: string })?.messageToUser)
		},
		onSuccess: ({ data }) => {
			toast(data?.message)
			queryClient.refetchQueries({ queryKey: ['applicationTypeQuery'] })
			handleCloseDrawer()
		},
	})
	return (
		<StyleContainer>
			<Stack className='header'>
				<Stack
					direction='row'
					spacing={1}
					alignItems='center'
					width='100%'
					justifyContent='space-between'>
					<Stack>
						<Typography variant='h5'>Select Application Type</Typography>
						<Typography variant='subtitle1'> {subheading}</Typography>
					</Stack>
					<IconButton onClick={handleCloseDrawer}>
						<Close />
					</IconButton>
				</Stack>
			</Stack>
			<Stack className='container' component='form'>
				<Stack direction='column' gap={2} height='72dvh' overflow='auto'>
					{fetchAllApplicationTypeQuery?.data?.data?.length ? (
						fetchAllApplicationTypeQuery?.data?.data?.map((item) => {
							const isChecked = selectedApplicationType.includes(item?.id)
							return (
								<Stack
									key={item?.id}
									alignItems='center'
									direction='row'
									gap={1}>
									<Checkbox
										onClick={() => {
											setSelectedAPplicationType((p) => {
												if (p.includes(item?.id)) {
													return p.filter((v) => v !== item?.id)
												} else {
													return [...p, item?.id]
												}
											})
										}}
										checked={isChecked}
										checkedIcon={<Box component='img' src={CheckboxIcon} />}
									/>
									<Typography>{capitalizeFirstLetter(item?.type)}</Typography>
								</Stack>
							)
						})
					) : (
						<NoData size='small' />
					)}
				</Stack>

				<LoadingButton
					disabled={
						!fetchAllApplicationTypeQuery?.data?.data ||
						fetchAllApplicationTypeQuery?.data?.data?.length === 0
					}
					className='add_btn'
					onClick={() => addApplicationType.mutate()}
					variant='contained'>
					Add
				</LoadingButton>
			</Stack>
		</StyleContainer>
	)
}

const StyleContainer = styled(Stack)(({ theme }) => ({
	gap: theme.spacing(2),
	'.header': {
		flexDirection: 'row',
		justifyContent: 'space-between',
		alignItems: 'center',
		padding: theme.spacing(4.5, 2),
		borderBottom: `${theme.spacing(0.125)} solid ${
			theme.palette.neutral['100']
		}`,
	},

	'.container': {
		padding: theme.spacing(2, 3),
		gap: theme.spacing(2.5),
		justifyContent: 'space-between',
		paddingBottom: theme.spacing(1),
	},
}))
