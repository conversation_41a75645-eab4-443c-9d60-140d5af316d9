import { authAxios } from '@/contexts'
import { TModal } from '@/types'
import { Cancel } from '@mui/icons-material'
import {
	Button,
	Dialog,
	DialogActions,
	DialogContent,
	DialogTitle,
	IconButton,
	MenuItem,
	TextField,
	Typography,
} from '@mui/material'
import { useMutation } from '@tanstack/react-query'
import { FC, useState } from 'react'
import { toast } from 'react-toastify'
import { Confirmation } from '../Confirmation'

enum AppType {
	DMRV = 'dmrv',
	DMRV_LITE = 'dmrv_lite',
}

export const SelectApplicationTypeForLogOut: FC<
	TModal & {
		userId: string
		cb?: () => void
	}
> = ({ open, onClose, userId, cb }) => {
	const [selectedAppType, setSelectedAppType] = useState<AppType | ''>('')

	const [showConfirmationDialog, setShowConfirmationDialog] = useState(false)

	const logOutForApplicationType = useMutation({
		mutationKey: ['logOutForApplicationType', userId, selectedAppType],
		mutationFn: async () => {
			const { data } = await authAxios.delete(`/user/${userId}/sessions`, {
				data: { applicationType: selectedAppType },
			})
			return data
		},
		onError: (error: any) => {
			toast(error?.response?.data?.messageToUser || 'Something went wrong')
		},
		onSuccess: (data) => {
			toast(data?.message || 'Application type updated successfully')
			cb?.()
			onClose()
		},
	})

	return (
		<>
			{showConfirmationDialog && (
				<Confirmation
					confirmationText={
						<Typography>
							Are you sure you want to logout this user from{' '}
							{selectedAppType === AppType.DMRV ? 'Online' : 'Offline'} App
						</Typography>
					}
					open={showConfirmationDialog}
					handleClose={() => setShowConfirmationDialog(false)}
					handleNoClick={() => setShowConfirmationDialog(false)}
					handleYesClick={logOutForApplicationType.mutate}
				/>
			)}
			<Dialog open={open} onClose={onClose} fullWidth maxWidth='sm'>
				<IconButton
					sx={{ position: 'absolute', right: 5, top: 0 }}
					onClick={onClose}>
					<Cancel />
				</IconButton>
				<DialogTitle>Select Application Type</DialogTitle>
				<DialogContent>
					<TextField
						fullWidth
						select
						sx={{ mt: 2 }}
						label='Application Type'
						value={selectedAppType}
						onChange={(e) => setSelectedAppType(e.target.value as AppType)}>
						<MenuItem value={AppType.DMRV}>dMRV Online</MenuItem>
						<MenuItem value={AppType.DMRV_LITE}>dMRV Offline</MenuItem>
					</TextField>
				</DialogContent>
				<DialogActions sx={{ display: 'flex', justifyContent: 'center' }}>
					<Button
						variant='contained'
						onClick={() => setShowConfirmationDialog(true)}
						disabled={!selectedAppType}>
						Continue
					</Button>
				</DialogActions>
			</Dialog>
		</>
	)
}
