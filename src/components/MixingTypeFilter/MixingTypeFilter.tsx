import OutlinedInput from '@mui/material/OutlinedInput'
import InputLabel from '@mui/material/InputLabel'
import MenuItem from '@mui/material/MenuItem'
import FormControl from '@mui/material/FormControl'
import ListItemText from '@mui/material/ListItemText'
import Select, { SelectChangeEvent } from '@mui/material/Select'
import IconButton from '@mui/material/IconButton'
import ClearIcon from '@mui/icons-material/Clear'
import { useEffect, useRef } from 'react'
import { useSearchParams } from 'react-router-dom'
import { Nullable } from '@/types'

type MixProp = {
	mixName: Nullable<string>
	id: string
}

type BiomassFilterProps = {
	mixingType?: MixProp[]
	initialValue?: string
	onChangeSelect?: (param: {
		selectedOption: MixProp | null
		nsp: URLSearchParams
	}) => URLSearchParams
	filtersToReset?: string[]
}

export const MixingTypeFilter = ({
	mixingType = [],
	initialValue = '',
	onChangeSelect,
	filtersToReset = [],
}: BiomassFilterProps) => {
	const [searchParams, setSearchParams] = useSearchParams()
	const initialised = useRef(false)
	const queryKey = 'mixingIds'

	useEffect(() => {
		if (initialValue && !searchParams.get(queryKey)) {
			const nsp = new URLSearchParams(searchParams)
			nsp.set(queryKey, initialValue)

			let param = nsp
			if (onChangeSelect) {
				const selectedOption = mixingType?.find(
					(mix) => mix.id === initialValue
				)
				if (selectedOption) {
					param = onChangeSelect({ selectedOption, nsp })
				}
			}
			setSearchParams(param, { replace: true })
		}
		initialised.current = true
	}, [
		initialValue,
		queryKey,
		searchParams,
		setSearchParams,
		onChangeSelect,
		mixingType,
	])

	const currentValue = searchParams.get(queryKey)

	const handleChange = (event: SelectChangeEvent<string>) => {
		const value = event.target.value

		const nsp = new URLSearchParams(searchParams)
		filtersToReset.forEach((key) => {
			nsp.delete(key)
		})
		nsp.delete(queryKey)
		nsp.set(queryKey, value)

		let param = nsp
		if (onChangeSelect) {
			const selectedOption = mixingType?.find((mix) => mix.id === value)
			if (selectedOption) {
				param = onChangeSelect({ selectedOption, nsp })
			}
		}
		setSearchParams(param, { replace: true })
	}

	const handleClear = () => {
		// Clear the selected value and reset query params
		const nsp = new URLSearchParams(searchParams)
		nsp.delete(queryKey)
		filtersToReset.forEach((key) => {
			nsp.delete(key)
		})

		let param = nsp
		if (onChangeSelect) {
			param = onChangeSelect({ selectedOption: null, nsp })
		}
		setSearchParams(param, { replace: true })
	}

	return (
		<FormControl sx={{ width: 280 }}>
			<InputLabel id='biomass-select-label'>Mix Type</InputLabel>
			<Select
				labelId='biomass-select-label'
				id='biomass-select'
				value={currentValue || ''}
				size='small'
				onChange={handleChange}
				input={<OutlinedInput label='Mix Type' />}
				renderValue={(selected) => {
					const mix = mixingType?.find((mix) => mix.id === selected)
					return mix ? mix.mixName : ''
				}}
				MenuProps={{
					PaperProps: {
						style: {
							maxHeight: 48 * 4.5 + 9,
							width: 220,
						},
					},
				}}>
				{mixingType?.map((mix) => (
					<MenuItem key={mix.id} value={mix.id}>
						<ListItemText primary={mix.mixName} />
					</MenuItem>
				))}
			</Select>
			{/* Clear Button */}
			{currentValue && (
				<IconButton
					sx={{ position: 'absolute', top: 8, right: 36 , p: 0}}
					aria-label='clear'
					size='small'
					onClick={handleClear}>
					<ClearIcon sx={{height: 24, width: 24}}/>
				</IconButton>
			)}
		</FormControl>
	)
}
