import { Grid, styled } from '@mui/material'

interface Props {
	left: JSX.Element
	right: JSX.Element
	gridBreakpoints: number[]
	spacing?: number
}

export const TwoColumnLayout = ({
	left,
	right,
	gridBreakpoints,
	spacing = 0,
}: Props) => (
	<LayoutContainer container spacing={spacing}>
		<Grid item className='gridLeft' xs={gridBreakpoints[0]}>
			{left}
		</Grid>
		<Grid item className='gridRight' xs={gridBreakpoints[1]}>
			{right}
		</Grid>
	</LayoutContainer>
)

const LayoutContainer = styled(Grid)(() => ({
	justifyContent: 'space-between',
}))
