import {
	FormControl,
	FormHelperText,
	MenuItem,
	Stack,
	Typography,
} from '@mui/material'
import { FieldError, useFormContext } from 'react-hook-form'
import { MultipleFileUploader } from '../MultipleFileUploader'
import { theme } from '@/lib/theme/theme'
import { CustomProfileElement } from '../CustomProfileElement'
import { useMemo } from 'react'
import { PhoneInputComponent } from '../PhoneInput'
import { AnyObjectSchema } from 'yup'
import { CustomTextField } from '@/utils/components'

interface IProps {
	editMode?: boolean
	schema?: AnyObjectSchema
}

export const AddManagerDetailsFields = ({ editMode, schema }: IProps) => {
	const {
		register,
		clearErrors,
		formState: { errors },
		watch,
		setValue,
	} = useFormContext()
	const managerDetailsErrors: any = useMemo(
		() => errors?.managerDetails ?? [],
		[errors?.managerDetails]
	)

	return (
		<Stack gap={theme.spacing(3)}>
			{/* Methane Compensate Type */}
			<CustomProfileElement
				errorMessage={managerDetailsErrors?.profileImageId?.id?.message ?? ''}
				{...(editMode && !!watch(`managerDetails.profileImageId`)
					? { value: watch(`managerDetails.profileImageId`) }
					: {})}
				setValue={(id, url) =>
					setValue(`managerDetails.profileImageId`, {
						id,
						url,
					})
				}
			/>
			<CustomTextField
				schema={schema}
				id='managerDetailsName'
				label='Name'
				variant='outlined'
				fullWidth
				autoFocus={false}
				autoComplete='off'
				error={managerDetailsErrors?.name?.message ?? false}
				helperText={managerDetailsErrors?.name?.message ?? ''}
				{...register(`managerDetails.name`)}
			/>
			<CustomTextField
				schema={schema}
				id='managerDetailsEmail'
				label='Email'
				variant='outlined'
				fullWidth
				autoFocus={false}
				autoComplete='off'
				error={managerDetailsErrors?.email?.message ?? false}
				helperText={managerDetailsErrors?.email?.message ?? ''}
				{...register(`managerDetails.email`)}
			/>
			{watch(`managerDetails.phoneNo`) !== undefined ? (
				<FormControl>
					<PhoneInputComponent
						value={watch(`managerDetails.phoneNo`) ?? ''}
						handleOnChange={(value: string, dialCode: string) => {
							setValue(`managerDetails.countryCode`, `+${dialCode}`)
							setValue(`managerDetails.phoneNo`, value)
						}}
						dialCode={watch(`managerDetails.countryCode`)}
						getSelectedCountryDialCode={(dialCode) =>
							setValue(`managerDetails.countryCode`, dialCode)
						}
					/>
					<FormHelperText error={Boolean(managerDetailsErrors?.phoneNo)}>
						{managerDetailsErrors?.phoneNo && (
							<Typography color='error' variant='caption'>
								{(managerDetailsErrors?.phoneNo as FieldError)?.message}
							</Typography>
						)}
					</FormHelperText>
				</FormControl>
			) : null}
			{watch(`managerDetails.phoneNumber`) !== undefined &&
			watch(`managerDetails.phoneNumber`) !== null ? (
				<FormControl>
					<PhoneInputComponent
						value={watch(`managerDetails.phoneNumber`) ?? ''}
						handleOnChange={(value: string, dialCode: string) => {
							setValue(`managerDetails.countryCode`, `+${dialCode}`)
							setValue(`managerDetails.phoneNumber`, value)
						}}
						dialCode={watch(`managerDetails.countryCode`)}
						getSelectedCountryDialCode={(dialCode) =>
							setValue(`managerDetails.countryCode`, dialCode)
						}
					/>
					<FormHelperText error={Boolean(managerDetailsErrors?.phoneNumber)}>
						{managerDetailsErrors?.phoneNumber && (
							<Typography color='error' variant='caption'>
								{(managerDetailsErrors?.phoneNumber as FieldError)?.message}
							</Typography>
						)}
					</FormHelperText>
				</FormControl>
			) : null}
			<CustomTextField
				schema={schema}
				select
				id='managerDetailsTrained'
				{...register(`managerDetails.trained`)}
				value={watch(`managerDetails.trained`) ?? ''}
				label='Trained'
				error={managerDetailsErrors?.trained?.message ?? false}
				helperText={managerDetailsErrors?.trained?.message ?? ''}
				SelectProps={{
					MenuProps: {
						anchorOrigin: {
							vertical: 'bottom',
							horizontal: 'center',
						},
					},
				}}>
				<MenuItem value={'yes'}>Yes</MenuItem>
				<MenuItem value={'no'}>No</MenuItem>
			</CustomTextField>
			{watch(`managerDetails.trained`) === 'yes' ? (
				<FormControl>
					<Stack rowGap={2} width='100%'>
						<MultipleFileUploader
							sx={{
								height: { xs: 100, md: 150 },
								width: '100%',
							}}
							imageHeight={100}
							{...register(`managerDetails.trainingImages`)}
							data={watch(`managerDetails.trainingImages`)}
							training={true}
							heading='Add Training Image'
							setUploadData={(data) => {
								setValue(`managerDetails.trainingImages`, data)
								clearErrors(`managerDetails.trainingImages`)
							}}
						/>
					</Stack>
					<FormHelperText error={Boolean(managerDetailsErrors?.trainingImages)}>
						{managerDetailsErrors?.trainingImages && (
							<Typography color='error' variant='caption'>
								{(managerDetailsErrors?.trainingImages as FieldError)?.message}
							</Typography>
						)}
					</FormHelperText>
				</FormControl>
			) : null}
		</Stack>
	)
}
