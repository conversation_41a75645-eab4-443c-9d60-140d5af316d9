import { authAxios, useAuthContext } from '@/contexts'
import { BiomassTypeEnum, IAddBiomass, ICrop } from '@/types'
import { CustomTextField } from '@/utils/components'
import { yupResolver } from '@hookform/resolvers/yup'
import { Close } from '@mui/icons-material'
import {
	Button,
	FormControl,
	FormHelperText,
	IconButton,
	MenuItem,
	Stack,
	styled,
	TextField,
	Typography,
	useTheme,
} from '@mui/material'
import { useQueryClient } from '@tanstack/react-query'
import { FC, useCallback, useMemo } from 'react'
import { useController, useForm } from 'react-hook-form'
import { toast } from 'react-toastify'
import { CustomFileUploader } from '../CustomFileUploader'
import { addBiomass } from './schema'
import { userRoles } from '@/utils/constant'
interface IProps {
	handleClose: () => void
	editMode?: boolean
	biomassDetails?: ICrop | null
}

const ImageFileTypes = ['JPG', 'PNG', 'JPEG', 'HEIC']

export const AddBiomass: FC<IProps> = ({
	handleClose,
	editMode = false,
	biomassDetails,
}) => {
	const theme = useTheme()
	const { userDetails } = useAuthContext()
	const initialValues: IAddBiomass = useMemo(
		() => ({
			biomassType:
				biomassDetails?.biomassType || BiomassTypeEnum.agriculturalResidue,
			description: biomassDetails?.description || '',
			name: biomassDetails?.name || '',
			biomassImageId: biomassDetails?.imagePathUrl?.id || '',
			season: biomassDetails?.seasonValue || 'all_seasons',
			density: biomassDetails?.cropDensity || null,
		}),
		[biomassDetails]
	)

	const QueryClient = useQueryClient()
	const {
		register,
		handleSubmit,
		formState: { errors },
		setValue,
		watch,
		clearErrors,
		control,
	} = useForm<IAddBiomass>({
		defaultValues: initialValues,
		mode: 'all',
		resolver: yupResolver<IAddBiomass>(addBiomass),
	})
	const biomassTypeName = useController({
		name: 'name',
		control: control,
	})

	const handleAddCrop = useCallback(
		async (values: IAddBiomass) => {
			try {
				const cropDetails =
					values.biomassType === 'crop'
						? {
								season: values.season,
						  }
						: {
								season: 'all_seasons',
						  }
				const formBody = {
					type: values.biomassType,
					name: values.name,
					imageId: values.biomassImageId,
					description: values.description,
					density: values.density,
					...(editMode ? { season: values.season } : cropDetails),
				}
				const { data } = editMode
					? await authAxios.put(
							`/crops/${biomassDetails?.id}/crop-details`,
							formBody
					  )
					: await authAxios.post(`/crops`, formBody)
				handleClose()
				QueryClient.refetchQueries({ queryKey: ['getBiomassTypes'] })

				toast(data?.message)
			} catch (err: any) {
				toast(err.response?.data?.messageToUser)
			}
		},
		[QueryClient, biomassDetails?.id, editMode, handleClose]
	)

	return (
		<StyleContainer>
			<Stack className='header'>
				<Stack
					direction='row'
					alignItems='center'
					width='100%'
					justifyContent='space-between'>
					<Typography variant='h4'>
						{editMode ? 'Edit' : 'Add'} Biomass/Crop
					</Typography>
					<IconButton onClick={handleClose}>
						<Close />
					</IconButton>
				</Stack>
			</Stack>

			<Stack
				className='container'
				component='form'
				onSubmit={handleSubmit(handleAddCrop)}
				rowGap={2}
				mt={1}>
				<Stack direction='column' gap={5}>
					<Stack rowGap={2} width='100%'>
						<FormControl>
							<TextField
								select
								label='Biomass Type'
								{...register('biomassType')}
								value={watch('biomassType')}
								id='select-biomass-type'
								disabled={editMode}
								error={!!errors.biomassType?.message}
								helperText={errors.biomassType?.message}>
								<MenuItem value={BiomassTypeEnum.agriculturalResidue}>
									Agricultural Residue
								</MenuItem>
								<MenuItem value={BiomassTypeEnum.invasiveSpecies}>
									Invasive Species
								</MenuItem>
								<MenuItem value={BiomassTypeEnum.other}>Others</MenuItem>
							</TextField>
						</FormControl>
						<CustomTextField
							id='name'
							label='Enter name'
							variant='outlined'
							schema={addBiomass}
							{...register('name', {
								onChange: (e) => {
									const input = e.target.value
									// Only allow letters and spaces
									if (/^[A-Za-z\s]*$/.test(input)) {
										setValue('name', input)
										return // update state only if input is valid
									}
									setValue('name', biomassTypeName?.field?.value) // revert to previous valid state
								},
							})}
							fullWidth
							error={!!errors.name?.message}
							helperText={errors.name?.message}
							disabled={
								editMode && userDetails?.accountType !== userRoles.Admin
							}
						/>

						<CustomTextField
							id='density'
							label='Enter Biomass density'
							hideNumberArrows={true}
							variant='outlined'
							schema={addBiomass}
							{...register('density')}
							fullWidth
							type='number'
							InputProps={{
								endAdornment: (
									<Typography color='grey.800' pl={1}>
										t/m<sup>3</sup>
									</Typography>
								),
								inputProps: {
									step: 0.*********,
								},
							}}
							error={!!errors.density?.message}
							helperText={errors.density?.message}
						/>

						<TextField
							label='Description'
							variant='outlined'
							multiline
							rows={5}
							{...register('description')}
							fullWidth
							error={!!errors.description?.message}
							helperText={errors.description?.message}
						/>
						<Stack rowGap={2}>
							<Typography variant='body2'>
								Add Biomass/Crop Image <sup>*</sup>
							</Typography>
							<Stack rowGap={2} width='100%'>
								<CustomFileUploader
									imageUrl={biomassDetails?.imagePathUrl?.url}
									acceptFileTypes={ImageFileTypes}
									heading='Upload or Drag the image '
									sx={{
										height: { xs: 100, md: 150 },
										width: '100%',
									}}
									imageHeight={100}
									setUploadData={({ id }) => {
										setValue('biomassImageId', id)
										clearErrors('biomassImageId')
									}}
									{...(editMode ? { mediaType: 'image' } : {})}
								/>
							</Stack>
							<FormHelperText error={Boolean(errors.biomassImageId)}>
								{errors?.biomassImageId?.message}
							</FormHelperText>
						</Stack>
					</Stack>
					<Stack
						direction='row'
						justifyContent='space-between'
						gap={2}
						className='buttonContainer'>
						<Button
							onClick={handleClose}
							sx={{ border: ` 1px solid ${theme.palette.primary.main}` }}>
							Cancel
						</Button>{' '}
						<Button variant='contained' type='submit'>
							Save
						</Button>
					</Stack>
				</Stack>
			</Stack>
		</StyleContainer>
	)
}

const StyleContainer = styled(Stack)(({ theme }) => ({
	gap: theme.spacing(2),
	'.header': {
		flexDirection: 'row',
		justifyContent: 'space-between',
		alignItems: 'center',
		padding: theme.spacing(4.5, 2),
		borderBottom: `${theme.spacing(0.125)} solid ${
			theme.palette.neutral['100']
		}`,
	},
	'.container': {
		padding: theme.spacing(2, 4),
		// overflowY: 'scroll',
		gap: theme.spacing(5),
		'.formcontrol': {
			gap: theme.spacing(0.6),

			'.label': {
				color: theme.palette.neutral[500],
			},
		},
		'.buttonContainer button': {
			width: theme.spacing(30),
			height: theme.spacing(4.5),
			padding: theme.spacing(1, 2.5),
		},
	},
}))
