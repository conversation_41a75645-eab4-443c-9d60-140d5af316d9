import { IBioCharContentDetail } from '@/types'
import { yupResolver } from '@hookform/resolvers/yup'
import { Close } from '@mui/icons-material'
import { LoadingButton } from '@mui/lab'
import {
	Button,
	FormHelperText,
	IconButton,
	InputAdornment,
	MenuItem,
	Stack,
	styled,
	TextField,
	Typography,
	useTheme,
} from '@mui/material'
import { FC, useCallback, useMemo } from 'react'
import { useForm } from 'react-hook-form'
import { AddBioCharContent, TAddBioCharContent } from './schema'
import { useMutation, useQueryClient } from '@tanstack/react-query'
import { MultipleFileUploader } from '../MultipleFileUploader'
import { authAxios } from '@/contexts'
import { IMedia } from '@/interfaces'
import { toast } from 'react-toastify'

interface ITechnology {
	label: string
	value: string
}

export const AddOrEditBiocharContentDetail: FC<{
	bioCharDetail?: IBioCharContentDetail | null
	type: 'add' | 'edit'
	handleClose: () => void
	biomassId: string
}> = ({ bioCharDetail, type, handleClose, biomassId }) => {
	const theme = useTheme()
	const QueryClient = useQueryClient()

	const initialValues: TAddBioCharContent = {
		organicCarbonPercentage: String(
			bioCharDetail?.organicCarbonPercentage || ''
		),
		inorganicCarbonPercentage: String(
			bioCharDetail?.inorganicCarbonPercentage || ''
		),
		ph: String(bioCharDetail?.ph || ''),
		waterHoldingCapacity: String(bioCharDetail?.waterHoldingCapacity || ''),
		density: String(bioCharDetail?.density || ''),
		HByC: String(bioCharDetail?.HByC || ''),
		ash: String(bioCharDetail?.ash || ''),
		carbonPercentage: String(bioCharDetail?.carbonPercentage || ''),
		hContent: String(bioCharDetail?.hContent || ''),
		nContent: String(bioCharDetail?.nContent || ''),
		sContent: String(bioCharDetail?.sContent || ''),
		technology: bioCharDetail?.technology || '',
		report: [
			...(bioCharDetail?.labReportDocs ?? []).map((i) => ({
				...i,
				fileName: i?.path,
			})),
			...(bioCharDetail?.labReportImages ?? []).map((i) => ({
				...i,
				fileName: i?.path,
			})),
		],
	}

	const {
		register,
		formState: { errors },
		setValue,
		handleSubmit,
		watch,
		clearErrors,
	} = useForm<TAddBioCharContent>({
		defaultValues: initialValues,
		resolver: yupResolver<TAddBioCharContent>(AddBioCharContent),
		mode: 'all',
	})

	const fieldList = [
		{ id: 'carbonPercentage', label: 'Total Carbon', unit: '%' },
		{ id: 'organicCarbonPercentage', label: 'Organic Carbon', unit: '%' },
		{ id: 'inorganicCarbonPercentage', label: 'InOrganic Carbon', unit: '%' },
		{ id: 'density', label: 'Bulk Density', unit: 't/m3' },
		{ id: 'ph', label: 'pH', unit: '' },
		{ id: 'HByC', label: 'H/C Ratio', unit: '' },
		{ id: 'ash', label: 'Ash Content', unit: '%' },
		{ id: 'hContent', label: 'Hydrogen', unit: '%' },
		{ id: 'nContent', label: 'Nitrogen', unit: '%' },
		{ id: 'sContent', label: 'Sulphur', unit: '%' },
		{ id: 'waterHoldingCapacity', label: 'Water Holding Capacity', unit: '%' },
	]

	const editOrAddBioCharContent = useMutation({
		mutationKey: [
			type === 'edit' ? 'editBioCharContent' : 'addBioCharContent',
			bioCharDetail?.id,
			biomassId,
		],
		mutationFn: async (item: TAddBioCharContent) => {
			const payload = [
				{
					temperature: Number(600),
					organicCarbonPercentage: Number(item?.organicCarbonPercentage),
					inorganicCarbonPercentage: Number(item?.inorganicCarbonPercentage),
					ph: Number(item?.ph),
					waterHoldingCapacity: Number(item?.waterHoldingCapacity),
					density: Number(item?.density),
					HByC: Number(item?.HByC),
					ash: Number(item?.ash),
					carbonPercentage: Number(item?.carbonPercentage),
					hContent: Number(item?.hContent),
					nContent: Number(item?.nContent),
					sContent: Number(item?.sContent),
					technology: item?.technology || '',
					labReportImages: (
						extractImageAndDocs(item.report)?.imageMedia ?? []
					).map((i) => i.id),
					labReportDocs: (extractImageAndDocs(item.report)?.docMedia ?? []).map(
						(i) => i.id
					),
				},
			]
			return await authAxios.put(
				type === 'edit'
					? `crops/${biomassId}/carbon-percentage/${bioCharDetail?.id}`
					: `crops/${biomassId}`,
				type === 'add' ? payload : payload[0]
			)
		},
		onSuccess: ({ data }) => {
			toast(data?.message)
			handleClose()
			QueryClient.refetchQueries({
				queryKey: [`bioCharContentDetail-${biomassId}`],
			})
		},
		onError: (error: any) => {
			toast(error?.response?.data?.messageToUser)
		},
	})

	const handleEditOrAddBioCharContent = useCallback(
		(item: TAddBioCharContent) => editOrAddBioCharContent.mutate(item),
		[editOrAddBioCharContent]
	)

	const isLoading = useMemo(
		() => editOrAddBioCharContent?.isPending,
		[editOrAddBioCharContent?.isPending]
	)

	const technologies: ITechnology[] = [
		{
			label: 'RWTH',
			value: 'RWTH',
		},
		{
			label: 'Kon Tiki',
			value: 'kontikki',
		},
	]

	return (
		<StyleContainer>
			<Stack className='header'>
				<Stack
					direction='row'
					spacing={1}
					alignItems='center'
					width='100%'
					justifyContent='space-between'>
					<Typography variant='h4'>Add Biochar Details</Typography>
					<IconButton onClick={handleClose}>
						<Close />
					</IconButton>
				</Stack>
			</Stack>
			<Stack className='container'>
				<Stack mt={2} rowGap={2}>
					<Stack gap={2}>
						{fieldList.map(({ id, label, unit }) => (
							<Stack key={id} direction='row' gap={1}>
								<StyledNumericTextField
									fullWidth
									id={id}
									type='number'
									{...register(id as keyof TAddBioCharContent)}
									label={label}
									placeholder={`Enter ${label}`}
									variant='outlined'
									error={!!errors?.[id as keyof TAddBioCharContent]?.message}
									helperText={errors?.[id as keyof TAddBioCharContent]?.message}
									{...(unit
										? {
												InputProps: {
													endAdornment: (
														<InputAdornment position='end'>
															<Typography>{unit}</Typography>
														</InputAdornment>
													),
												},
										  }
										: {})}
								/>
							</Stack>
						))}
						<TextField
							select
							fullWidth
							label='Enter Technology'
							// {...register('technology',{onChange:(event)=>{
							// 	setValue('technology', event.target.value)
							// 	clearErrors('technology')
							// }})}
							{...register('technology')}
							value={watch('technology')}
							onChange={(event) => {
								setValue('technology', event.target.value)
								clearErrors('technology')
							}}
							error={!!errors.technology?.message}
							helperText={errors.technology?.message}>
							{technologies?.map(({ value, label }) => (
								<MenuItem key={value} value={value}>
									{label}
								</MenuItem>
							))}
						</TextField>
						<Stack gap={1}>
							<MultipleFileUploader
								heading='Lab Test Report'
								sx={{
									height: { xs: 100, md: 166 },
									width: '100%',
								}}
								data={watch('report')}
								imageHeight={100}
								setUploadData={(data) => {
									setValue('report', data)
									clearErrors('report')
								}}
								training={false}
							/>
							<FormHelperText error={Boolean(errors.report)}>
								{errors?.report?.message}
							</FormHelperText>
						</Stack>
						<Stack
							direction='row'
							justifyContent='space-between'
							mb={10}
							gap={2}
							className='buttonContainer'>
							<Button
								onClick={handleClose}
								sx={{ border: ` 1px solid ${theme.palette.primary.main}` }}>
								Cancel
							</Button>
							<LoadingButton
								loading={isLoading}
								disabled={isLoading}
								onClick={handleSubmit(handleEditOrAddBioCharContent)}
								variant='contained'>
								{type === 'edit' ? 'Save' : 'Add'}
							</LoadingButton>
						</Stack>
					</Stack>
				</Stack>
			</Stack>
		</StyleContainer>
	)
}

const StyleContainer = styled(Stack)(({ theme }) => ({
	gap: theme.spacing(2),
	'.header': {
		flexDirection: 'row',
		justifyContent: 'space-between',
		alignItems: 'center',
		padding: theme.spacing(4.5, 2),
		borderBottom: `${theme.spacing(0.125)} solid ${
			theme.palette.neutral['100']
		}`,
	},
	'.container': {
		padding: theme.spacing(2, 4),
		// overflowY: 'scroll',
		gap: theme.spacing(4),
		'.buttonContainer button': {
			width: theme.spacing(30),
			height: theme.spacing(4.5),
			padding: theme.spacing(1, 2.5),
		},
	},
}))

const StyledNumericTextField = styled(TextField)(() => ({
	// For Chrome, Safari, Edge, and any WebKit browsers
	'& input::-webkit-outer-spin-button, & input::-webkit-inner-spin-button': {
		appearance: 'none',
		margin: 0,
		display: 'none', // Hide the arrows
	},
	// For Firefox
	'& input[type=number]': {
		'-moz-appearance': 'textfield', // Disable the spinner in Firefox
		appearance: 'textfield', // Ensure this applies to other browsers that support appearance
	},
}))

const extractImageAndDocs = (mediaList: IMedia[]) => {
	const ImageTypes = ['png', 'jpg', 'jpeg', 'webp']

	const DocsTypes = ['pdf', 'doc', 'docx', 'txt', 'ppt', 'pptx', 'xls', 'xlsx']
	const imageMedia = mediaList.filter(
		(media) =>
			media?.fileName &&
			ImageTypes.includes(media?.fileName?.split('.').pop() || '')
	)

	const docMedia = mediaList.filter(
		(media) =>
			media?.fileName &&
			DocsTypes.includes(media?.fileName.split('.').pop() || '')
	)
	return {
		imageMedia,
		docMedia,
	}
}
