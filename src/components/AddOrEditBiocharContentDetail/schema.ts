import * as Yup from 'yup'

export const EditBioCharContent = Yup.object({
	temperature: Yup.number().required('Please enter temperature'),
	carbonPercentage: Yup.number().required('Please enter carbon percentage'),
	density: Yup.number().required('Please enter density'),
	ash: Yup.number().required('Please enter ash content'),
	hContent: Yup.number().required('Please enter H content'),
	nContent: Yup.number().required('Please enter N content'),
	technology: Yup.string().required('Please enter technology'),
	report: Yup.array()
		.required('Please upload report')
		.min(1, 'Please upload atleast one report'),
	HByC: Yup.number().required('Please enter H/C ratio'),
	sContent: Yup.number().required('Please enter S content'),
	waterHoldingCapacity: Yup.number().required(
		'Please enter water holding capacity'
	),
	inorganicCarbonPercentage: Yup.number().required(
		'Please enter inOrganic carbon'
	),
	organicCarbonPercentage: Yup.number().required('Please enter organic carbon'),
	ph: Yup.number().required('Please enter pH'),
})

export const AddBioCharContent = Yup.object({
	carbonPercentage: Yup.string().required('Please enter carbon percentage'),
	density: Yup.string().required('Please enter density'),
	ash: Yup.string(),
	hContent: Yup.string(),
	nContent: Yup.string(),
	technology: Yup.string(),
	report: Yup.array()
		.required('Please upload report')
		.min(1, 'Please upload atleast one report'),
	HByC: Yup.string(),
	sContent: Yup.string(),
	waterHoldingCapacity: Yup.string(),
	inorganicCarbonPercentage: Yup.string(),
	organicCarbonPercentage: Yup.string(),
	ph: Yup.string(),
})

export type TAddBioCharContent = Yup.InferType<typeof AddBioCharContent>
export type TEditBioCharContent = Yup.InferType<typeof EditBioCharContent>
