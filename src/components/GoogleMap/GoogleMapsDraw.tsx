import { Close, Delete } from '@mui/icons-material'
import { Button, CircularProgress, Dialog, Stack } from '@mui/material'
import {
	DrawingManagerF,
	GoogleMap,
	LoadScriptProps,
	MarkerF,
	PolygonF,
	useJsApiLoader,
} from '@react-google-maps/api'
import { useCallback, useEffect, useRef, useState } from 'react'
import { useSearchParams } from 'react-router-dom'

interface IProps {
	open: boolean
	handleModalClose: () => void
	initialPolygons?: google.maps.LatLng[] | google.maps.LatLngLiteral[]
	handleSave: (...rest: any[]) => Promise<void>
	disableDrawingMarker?: boolean
}

const containerStyle: React.CSSProperties = {
	width: '100%',
	height: '1000px',
}

const deleteIconStyle: React.CSSProperties = {
	cursor: 'pointer',
	height: 28,
	width: 28,
	backgroundColor: 'common.white',
	position: 'absolute',
	top: 5,
	left: '52%',
	zIndex: 2,
	justifyContent: 'center',
	alignItems: 'center',
}

const polygonOptions: google.maps.PolygonOptions = {
	fillOpacity: 0.3,
	fillColor: '#ff0000',
	strokeColor: '#ff0000',
	strokeWeight: 2,
	draggable: true,
	editable: true,
}

const libraries: LoadScriptProps['libraries'] = ['places', 'drawing', 'maps']

const { VITE_GOOGLE_MAPS_API_KEY } = import.meta.env

const emptyCoords = [{ lat: 0, lng: 0 }] as unknown as google.maps.LatLng[]

export const GoogleMapsDraw = ({
	open,
	handleModalClose,
	initialPolygons,
	handleSave,
	disableDrawingMarker = false,
}: IProps) => {
	const polygonRefs = useRef<google.maps.Polygon | null>(null)
	const drawingManagerRef = useRef<google.maps.drawing.DrawingManager | null>(
		null
	)
	const isEditingPolygon = !!initialPolygons?.length

	const [searchParams] = useSearchParams()

	const { isLoaded } = useJsApiLoader({
		googleMapsApiKey: VITE_GOOGLE_MAPS_API_KEY,
		libraries,
	})

	const [polygons, setPolygons] = useState<google.maps.LatLng[]>(
		initialPolygons?.length
			? (initialPolygons as google.maps.LatLng[])
			: emptyCoords
	)

	const location: google.maps.LatLngLiteral = {
		lat: Number(searchParams.get('lat')),
		lng: Number(searchParams.get('long')),
	}

	const [center, setCenter] = useState<google.maps.LatLngLiteral | undefined>(
		location
	)
	const [markerLocation, setMarkerLocation] =
		useState<google.maps.LatLngLiteral>(location)

	const handleGetCurrentLocation = useCallback(() => {
		navigator.geolocation.getCurrentPosition((position) => {
			setMarkerLocation({
				lat: position.coords.latitude,
				lng: position.coords.longitude,
			})
			setCenter({
				lat: position.coords.latitude,
				lng: position.coords.longitude,
			})
		})
	}, [])
	
	useEffect(() => {
		if (!open) return
		if (initialPolygons?.length) {
			const firstPoint = initialPolygons[0] as google.maps.LatLngLiteral
			setCenter({ lat: firstPoint.lat, lng: firstPoint.lng })
			setPolygons(initialPolygons as unknown as google.maps.LatLng[])
		} else {
			setCenter({
				lat: Number(searchParams.get('lat')),
				lng: Number(searchParams.get('long')),
			})
		}
	}, [initialPolygons, open, searchParams])

	// Zoom out when coordinates are (0,0)
	useEffect(() => {
		if (!isLoaded) return
		if (
			Number(searchParams.get('lat')) === 0 &&
			Number(searchParams.get('long')) === 0
		)
			handleGetCurrentLocation()
	}, [handleGetCurrentLocation, isLoaded, searchParams])
	const drawingManagerOptions: google.maps.drawing.DrawingManagerOptions = {
		polygonOptions,
		drawingControl: true,
		drawingControlOptions: {
			position: window.google?.maps?.ControlPosition?.TOP_CENTER,
			drawingModes: [window.google?.maps?.drawing?.OverlayType?.POLYGON],
		},
	}

	const onLoadPolygon = useCallback((polygon: google.maps.Polygon) => {
		polygonRefs.current = polygon
	}, [])

	const onLoadDrawingManager = useCallback(
		(drawingManager: google.maps.drawing.DrawingManager) => {
			drawingManagerRef.current = drawingManager
		},
		[]
	)

	const onOverlayComplete = useCallback(($overlayEvent: any) => {
		drawingManagerRef?.current?.setDrawingMode(null)
		if (!$overlayEvent.overlay) return
		if ($overlayEvent.type === window.google.maps.drawing.OverlayType.POLYGON) {
			const newPolygon = $overlayEvent.overlay
				.getPath()
				.getArray()
				.map((latLng: google.maps.LatLng) => ({
					lat: latLng.lat(),
					lng: latLng.lng(),
				}))

			const startPoint = newPolygon[0]
			newPolygon.push(startPoint)
			$overlayEvent.overlay?.setMap(null)
			setPolygons(newPolygon)
		}
	}, [])

	const onDeleteDrawing = useCallback(() => {
		setPolygons([])
	}, [])

	const onEditPolygon = useCallback(() => {
		const polygonRef = polygonRefs.current
		if (polygonRef) {
			const coordinates = polygonRef
				.getPath()
				.getArray()
				.map((latLng: google.maps.LatLng) => ({
					lat: latLng.lat(),
					lng: latLng.lng(),
				}))
			setPolygons(coordinates as any)
		}
	}, [])

	const handleMapUnmount = useCallback(() => {
		polygonRefs.current = null
		drawingManagerRef.current = null
	}, [])

	const handleSaveClick = useCallback(async () => {
		const formattedPolygons =
			polygons?.length > 1
				? polygons.map((polygon) => ({
						x: polygon.lat,
						y: polygon.lng,
				  }))
				: null

		await handleSave(formattedPolygons)
		handleModalClose()
		setPolygons([])
	}, [handleModalClose, handleSave, polygons])

	return (
		<Dialog open={open} fullScreen>
			{isLoaded ? (
				<>
					{drawingManagerRef.current && (
						<Stack
							onClick={onDeleteDrawing}
							title='Delete shape'
							sx={deleteIconStyle}>
							<Delete fontSize='small' color='action' />
						</Stack>
					)}
					<GoogleMap
						zoom={15}
						center={center}
						onUnmount={handleMapUnmount}
						mapContainerStyle={containerStyle}
						onTilesLoaded={() => setCenter(undefined)}>
						{!disableDrawingMarker && (
							<DrawingManagerF
								onUnmount={handleMapUnmount}
								onLoad={onLoadDrawingManager}
								onOverlayComplete={onOverlayComplete}
								options={drawingManagerOptions}
							/>
						)}
						<MarkerF position={markerLocation} />

						<PolygonF
							onLoad={(polygon) => onLoadPolygon(polygon)}
							onMouseUp={isEditingPolygon ? () => {} : () => onEditPolygon()}
							onDragEnd={isEditingPolygon ? () => {} : () => onEditPolygon()}
							options={polygonOptions}
							onUnmount={handleMapUnmount}
							paths={polygons}
							draggable={!isEditingPolygon}
							editable={!isEditingPolygon}
						/>

						<Button
							variant='text'
							sx={{
								position: 'absolute',
								zIndex: 2,
								top: 10,
								right: 110,
								backgroundColor: 'common.white',
								borderRadius: 0,
								'&:hover': {
									backgroundColor: 'common.white',
								},
							}}
							onClick={handleSaveClick}>
							Save
						</Button>
						<Stack
							sx={{
								position: 'absolute',
								right: 60,
								top: 10,
								cursor: 'pointer',
								backgroundColor: 'common.white',
								width: 40,
								height: 40,
								justifyContent: 'center',
								alignItems: 'center',
							}}
							onClick={handleModalClose}>
							<Close color='action' />
						</Stack>
					</GoogleMap>
				</>
			) : (
				<Stack justifyContent='center' alignItems='center' py={10} width='100%'>
					<CircularProgress size={100} />
				</Stack>
			)}
		</Dialog>
	)
}
