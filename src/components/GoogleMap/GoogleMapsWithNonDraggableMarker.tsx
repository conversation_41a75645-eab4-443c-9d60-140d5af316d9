import { GpsFixed } from '@mui/icons-material'
import { Box, CircularProgress, IconButton, useTheme } from '@mui/material'
import {
	GoogleMap,
	GoogleMapProps,
	LoadScriptProps,
	MarkerF,
	StandaloneSearchBox,
	useLoadScript,
} from '@react-google-maps/api'
import { useCallback, useEffect, useRef, useState } from 'react'
import { toast } from 'react-toastify'

type Coordinates = { lat: number; lng: number }

interface IProps extends GoogleMapProps {
	center: Coordinates
	zoom?: number
	setMapCenter: (lat: number, lng: number) => void
	mapContainerStyle: React.CSSProperties
	dragEnabled?: boolean
}

const { VITE_GOOGLE_MAPS_API_KEY } = import.meta.env

const libraries: LoadScriptProps['libraries'] = ['places']

export const GoogleMapsWithNonDraggableMarker = ({
	center,
	zoom = 5,
	dragEnabled = true,
	setMapCenter,
	...props
}: IProps) => {
	const theme = useTheme()
	const { isLoaded } = useLoadScript({
		id: 'google-map-script',
		googleMapsApiKey: VITE_GOOGLE_MAPS_API_KEY,
		libraries,
	})

	const refMap = useRef<any>()
	const [searchBox, setSearchBox] = useState<any>(null)

	const [markerPosition, setMarkerPosition] = useState<Coordinates>({
		lat: center.lat,
		lng: center.lng,
	})

	const handleGetCurrentLocation = useCallback(() => {
		navigator.geolocation.getCurrentPosition(
			(position) => {
				setMapCenter(position.coords.latitude, position.coords.longitude)
				setMarkerPosition({
					lat: position.coords.latitude,
					lng: position.coords.longitude,
				})
			},
			(error) => {
				toast(error.message)
			}
		)
	}, [setMapCenter])

	const onPlacesChanged = useCallback(() => {
		const place = searchBox?.getPlaces()[0]
		setMarkerPosition({
			lat: place.geometry.location.lat(),
			lng: place.geometry.location.lng(),
		})
		setMapCenter(place.geometry.location.lat(), place.geometry.location.lng())
		if (refMap) {
			refMap?.current?.setZoom(18)
		}
	}, [searchBox, setMapCenter])

	const onSBLoad = (ref: any) => {
		setSearchBox(ref)
	}

	const handleOnLoad = (map: any) => {
		if (isLoaded) {
			refMap.current = map
			setMarkerPosition({ lat: center.lat, lng: center.lng })
		}
	}

	const handleCenterChanged = useCallback(() => {
		if (refMap?.current) {
			const newCenter = refMap?.current?.getCenter()
			setMapCenter(newCenter?.lat(), newCenter?.lng())
		}
	}, [setMapCenter])

	useEffect(() => {
		if (
			refMap?.current &&
			markerPosition.lat === 0 &&
			markerPosition.lng === 0
		) {
			setMarkerPosition({ lat: center.lat, lng: center.lng })
		}
	}, [center.lat, center.lng, markerPosition.lat, markerPosition.lng])

	if (!isLoaded) return <CircularProgress />

	return (
		<GoogleMap
			zoom={zoom}
			onLoad={handleOnLoad}
			center={markerPosition}
			{...props}
			onCenterChanged={handleCenterChanged}
			options={{
				mapTypeControl: false,
				draggable: dragEnabled,
			}}>
			<IconButton
				sx={{
					position: 'absolute',
					zIndex: 2,
					backgroundColor: 'common.white',
					color: theme.palette.grey[700],
					borderRadius: '50%',
					m: 1.5,
					':hover': {
						backgroundColor: 'common.white',
					},
				}}
				onClick={handleGetCurrentLocation}>
				<GpsFixed />
			</IconButton>

			<StandaloneSearchBox onPlacesChanged={onPlacesChanged} onLoad={onSBLoad}>
				<Box
					sx={{
						width: `60%`,
						position: 'relative',
						top: '10px',
						left: '20%',
						'input::-webkit-search-cancel-button': {
							cursor: 'pointer',
							fontSize: '20px',
						},
					}}>
					<input
						type='search'
						placeholder='Search Google Maps'
						style={{
							width: '100%',
							boxSizing: 'border-box',
							border: `1px solid transparent`,
							height: `40px`,
							padding: `0 12px`,
							borderRadius: `3px`,
							boxShadow: `0 2px 6px rgba(0, 0, 0, 0.3)`,
							fontSize: `14px`,
							outline: `none`,
							textOverflow: `ellipses`,
							position: 'absolute',
						}}
					/>
				</Box>
			</StandaloneSearchBox>
			<MarkerF position={center} />
		</GoogleMap>
	)
}
