// import { CancelOutlined, TaskAlt } from '@mui/icons-material'
import {
	Button,
	Dialog,
	DialogActions,
	Paper,
	styled,
	Typography,
} from '@mui/material'
import { FC, useCallback, useMemo, useState } from 'react'
import { Confirmation } from '../Confirmation'
import { useMutation, useQueryClient } from '@tanstack/react-query'
import { authAxios, useAuthContext } from '@/contexts'
import { toast } from 'react-toastify'
import { useParams } from 'react-router-dom'
import { AddRejectionReason } from '../AddRejectionReason'
import { AxiosError } from 'axios'
import { BatchStatusTypeEnum, userRoles } from '@/utils/constant'
import { BatchstatusType } from '@/interfaces'
import { CancelOutlined } from '@mui/icons-material'
import { ApproveRejectedInformation } from '@/pages/dashboard/Production/Batches/BatchDetails/components'

type AcceptRejectProp = {
	status: BatchstatusType
	approvalName: string
	reason?: string
	approvalDate: Date
	open: boolean
	siteId?: string
	artisanProId?: string
	kilnId?: string
	networkId?: string
	onClose: () => void
	anchorEl?: HTMLElement | null
}

interface IComponentProps {
	type: string
	status: BatchstatusType
	isRejected: boolean
	isCompensate: boolean
	approvalName?: string
	approvalDate?: Date
	siteId?: string
	artisanProId?: string
	kilnId?: string
	networkId?: string
	reason?: string
	setShowConfirmationDialog: (
		value: React.SetStateAction<'approved' | 'compensate' | null>
	) => void
	setShowRejectionDialog: (
		value: React.SetStateAction<'reject' | 'force-reject' | null>
	) => void
}
export const AcceptOrReject: FC<AcceptRejectProp> = ({
	open,
	onClose,
	status,
	reason,
	approvalName,
	networkId,
	kilnId,
	siteId,
	artisanProId,
	approvalDate,
}) => {
	const { id: batchId } = useParams()
	const { userDetails } = useAuthContext()
	const [showConfirmationDialog, setShowConfirmationDialog] = useState<
		'approved' | 'compensate' | null
	>(null)
	const [showRejectionDialog, setShowRejectionDialog] = useState<
		'reject' | 'force-reject' | null
	>(null)
	const queryClient = useQueryClient()

	const isRejected =
		status === BatchStatusTypeEnum.Rejected ||
		status === BatchStatusTypeEnum.AdminRejected

	const isCompensate = status === BatchStatusTypeEnum.Compensated

	const approveBatch = useMutation({
		mutationKey: ['approveBatch'],
		mutationFn: async (status: 'approved' | 'compensate' | null) => {
			const payload = {
				status: status,
			}
			await authAxios.put(`kiln-process/${batchId}/assess`, payload)
		},
		onError: (error: AxiosError) => {
			toast((error?.response?.data as { messageToUser: string })?.messageToUser)
		},
		onSuccess: () => {
			toast(`successfully accepted the batch`)
			setShowConfirmationDialog(null)
			onClose()
			queryClient.refetchQueries({ queryKey: ['batchDetails', batchId] })
		},
	})
	const showAcceptRejectButtons = useMemo(() => {
		switch (status) {
			case BatchStatusTypeEnum.AdminRejected:
			case BatchStatusTypeEnum.AdminApproved:
			case BatchStatusTypeEnum.Compensated:
				return 'details'
			case BatchStatusTypeEnum.Rejected:
				return 'rejected'
			case BatchStatusTypeEnum.Approved:
				if (
					userDetails?.accountType === userRoles.Admin ||
					userDetails?.accountType === userRoles.CsinkManager
				)
					return 'accept-reject-buttons'
				return 'details'
			case BatchStatusTypeEnum.NotAssessed:
				return 'accept-reject-buttons'
			case BatchStatusTypeEnum.PartiallyCompleted:
			case BatchStatusTypeEnum.Started:
				if (
					userDetails?.accountType === userRoles.Admin ||
					userDetails?.accountType === userRoles.CsinkManager
				)
					return 'forcefully-reject'
				return ''
			default:
				return ''
		}
	}, [status, userDetails?.accountType])

	const handleApproveBatch = useCallback(
		async (status: 'approved' | 'compensate' | null) => {
			approveBatch.mutate(status)
		},
		[approveBatch]
	)
	return (
		<>
			{showConfirmationDialog ? (
				<Confirmation
					open={!!showConfirmationDialog}
					handleClose={() => setShowConfirmationDialog(null)}
					handleNoClick={() => {
						setShowConfirmationDialog(null)
					}}
					confirmationText={
						<Typography>
							{showConfirmationDialog === 'approved'
								? 'Are you sure you want to accept this process?'
								: 'Are you sure you want to compensate this process?'}
						</Typography>
					}
					handleYesClick={() => handleApproveBatch(showConfirmationDialog)}
				/>
			) : null}
			{showRejectionDialog ? (
				<AddRejectionReason
					forceReject={showRejectionDialog === 'force-reject'}
					networkId={networkId}
					kilnId={kilnId}
					siteId={siteId}
					artisanProId={artisanProId}
					open={!!showRejectionDialog}
					onClose={() => setShowRejectionDialog(null)}
					handleCloseForRejection={() => {
						setShowRejectionDialog(null)
						onClose()
					}}
				/>
			) : null}

			<Dialog
				open={open}
				onClose={onClose}
				maxWidth='xs'
				sx={{
					'.MuiDialog-paper': {
						borderRadius: 4,
						position: 'absolute',
						top: 60,
						right: 210,
						margin: 8,
						backgroundColor: 'grey.300',
					},
					'& .MuiBackdrop-root': {
						backgroundColor: 'transparent',
					},
				}}>
				<StyledPaper>
					<RenderComponentType
						isRejected={isRejected}
						isCompensate={isCompensate}
						setShowConfirmationDialog={(value) =>
							setShowConfirmationDialog(value)
						}
						setShowRejectionDialog={(value) => setShowRejectionDialog(value)}
						type={showAcceptRejectButtons}
						status={status}
						approvalDate={approvalDate}
						approvalName={approvalName}
						reason={reason}
					/>
				</StyledPaper>
			</Dialog>
		</>
	)
}

const RenderComponentType: FC<IComponentProps> = ({
	type,
	status,
	isRejected,
	isCompensate,
	approvalName,
	approvalDate,
	reason,
	setShowRejectionDialog,
	setShowConfirmationDialog,
}) => {
	const { userDetails } = useAuthContext()
	const isCompensateButtonVisible = useMemo(() => {
		if (
			[userRoles.Admin, userRoles.CsinkManager, userRoles.compliance_manager].includes(
				userDetails?.accountType as userRoles
			) &&
			[
				BatchStatusTypeEnum.NotAssessed,
				BatchStatusTypeEnum.Approved,
				BatchStatusTypeEnum.Rejected,
			].includes(status as BatchStatusTypeEnum)
		) {
			return true
		} else {
			return false
		}
	}, [status, userDetails?.accountType])

	let statusLabelName = ''
	let statusLabelDate = ''

	switch (true) {
		case isRejected:
			statusLabelName = 'Rejected By: '
			statusLabelDate = 'Rejected '
			break
		case isCompensate:
			statusLabelName = 'Compensate By: '
			statusLabelDate = 'Compensated '
			break
		default:
			statusLabelName = 'Approved By: '
			statusLabelDate = 'Approved '
			break
	}

	switch (type) {
		case 'details':
			return (
				<ApproveRejectedInformation
					statusLabelName={statusLabelName}
					approvalName={approvalName || ''}
					statusLabelDate={statusLabelDate}
					approvalDate={approvalDate}
					reason={reason || ''}
					isRejected={isRejected}
				/>
			)
		case 'accept-reject-buttons':
			return (
				<DialogActions>
					<ButtonsContainer>
						<Button
							variant='contained'
							color='primary'
							onClick={() => setShowRejectionDialog('reject')}
							className='button'>
							Reject
						</Button>
						<Button
							variant='contained'
							color='success'
							className='button'
							onClick={() => setShowConfirmationDialog('approved')}>
							Approve
						</Button>
						{isCompensateButtonVisible && (
							<Button
								variant='contained'
								className='compensateButton'
								onClick={() => setShowConfirmationDialog('compensate')}>
								Compensate
							</Button>
						)}
					</ButtonsContainer>
				</DialogActions>
			)
		case 'forcefully-reject':
			return (
				<DialogActions>
					<RejectButton
						variant='contained'
						color='primary'
						startIcon={<CancelOutlined />}
						onClick={() => setShowRejectionDialog('force-reject')}>
						Forcefully reject
					</RejectButton>
				</DialogActions>
			)
		case 'rejected':
			return (
				<>
					{isCompensateButtonVisible ? (
						<DialogActions>
							<ButtonsContainer>
								<Button
									variant='contained'
									className='compensateButton'
									onClick={() => setShowConfirmationDialog('compensate')}>
									Compensate
								</Button>
							</ButtonsContainer>
						</DialogActions>
					) : (
						<ApproveRejectedInformation
							statusLabelName={statusLabelName}
							approvalName={approvalName || ''}
							statusLabelDate={statusLabelDate}
							approvalDate={approvalDate}
							reason={reason || ''}
							isRejected={isRejected}
						/>
					)}
				</>
			)
	}
}

const ButtonsContainer = styled(Button)(({ theme }) => ({
	display: 'flex',
	my: -1,
	gap: theme.spacing(4),
	justifyContent: 'center',
	ml: 3,
	alignItems: 'center',

	'.button': {
		textTransform: 'none',
		borderRadius: 12,
		padding: theme.spacing(0.125, 5),
	},
	'.compensateButton': {
		textTransform: 'none',
		borderRadius: 12,
		padding: theme.spacing(0.125, 5),
		backgroundColor: '#FFE0BE',
		color: theme.palette.text.secondary,
		'&:hover': {
			backgroundColor: '#ffd4a3',
			transform: 'scale(1.02)',
		},
	},
}))

const RejectButton = styled(Button)(() => ({
	justifyContent: 'flex-start',
	alignItems: 'center',
	borderRadius: 12,
}))

const StyledPaper = styled(Paper)(({ theme }) => ({
	backgroundColor: theme.palette.grey[300],
	borderRadius: theme.spacing(2),
}))
