import { Box, CircularProgress, styled } from '@mui/material'
import React, { PropsWithChildren } from 'react'

type TProps = {
	loading: boolean
}

export const LoadingWrapper: React.FC<PropsWithChildren<TProps>> = ({
	children,
	loading,
}) => {
	if (loading)
		return (
			<StyledBox>
				<CircularProgress />
			</StyledBox>
		)
	return children
}

const StyledBox = styled(Box)(() => ({
	height: '100%',
	width: '100%',
	display: 'flex',
	justifyContent: 'center',
	alignItems: 'center',
}))
