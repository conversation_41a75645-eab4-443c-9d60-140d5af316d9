import { Nullable } from '@/types'

export interface ILogin {
	email: string
	password: string
}

export interface IUserContext {
	id: string
	accountType: string
	apNetworkId: string
	csinkManagerId: string
	csinkNetworkCount?: number
	artisanProName?: string
	apNetworkManagerId: string
	biomassAggregatorName?: string
	biomassAggregatorIsCsink?: boolean
	apNetworkName?: string
	name?: string
	biomassAggregatorShortName?: string
	apNetworkShortName?: string
	biomassAggregatorId: string
	cSinkNetworkManagerId?: string
	networkId: string
	artisianProId: string
	email: string
	employeeDeactivated: boolean | null // Assuming it can be a boolean value or null
	networkName: string | null
	networkShortName: string | null
	artisanProShortName: string | null
	artisanProMaximumMoisture: Nullable<number>
	artisanProMinimumTemperature: Nullable<number>
	artisanProMinimumImagesVideos: Nullable<number>
	creditsToRegister: Nullable<number>
	csinkNetworkMaximumMoisture: Nullable<number>
	csinkNetworkMinimumTemperature: Nullable<number>
	csinkNetworkMinimumImagesVideos: Nullable<number>
	isBiomassAdded?: boolean
	csinkManagerName: Nullable<string>
	artisanProIds?: string[]
	csinkNetworkIds?: string[]
	artisanProCount?: Nullable<number>
	companyId?: string
	csinkManagerDetails?: CSinkManagerDetails
}

export interface CSinkManagerDetails {
	id: string
	name: string
	biomassReference: BiomassReference[]
	location: string
	locationName: string
	shortName: string
	projectId: string
}

export interface BiomassReference {
	id: string
	biomassName: string
	biomassQuantity: number
	biomassTypeId: string
	createdById: string
	createdByName: string
	fieldSize: number
	fieldSizeUnit: string
}

export interface IAuthContext {
	login: (payload: ILogin) => Promise<unknown>
	logout: () => Promise<unknown>
	userDetails?: IUserContext
	isLoggedIn: boolean
	getUserDetails: () => Promise<unknown>
}
