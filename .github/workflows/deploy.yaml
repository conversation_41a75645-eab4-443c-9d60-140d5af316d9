name: Deploy to Firebase
on:
  push:
    branches:
      - dev
      - main
      - stage
      - qa

jobs:
  firebase-build:
    name: Firebase Build
    runs-on: ubuntu-latest
    timeout-minutes: 15
    steps:
      - name: Set branch environments
        run: |-

          if [[ "${{github.base_ref}}" == "dev" || "${{github.ref}}" == "refs/heads/dev" ]]; then
             echo "CMD=deploy-dev" >> "$GITHUB_ENV"
             echo "VITE_BASE_URL=${{ secrets.VITE_BASE_URL }}" >> "$GITHUB_ENV"
             echo "VITE_IMAGE_PROXY_URL=${{ secrets.VITE_IMAGE_DEV_URL }}" >> "$GITHUB_ENV"
             echo "VITE_GOOGLE_MAPS_API_KEY=${{ secrets.VITE_GOOGLE_MAPS_API_KEY_DEV }}" >> "$GITHUB_ENV"
             echo "VITE_PUBLIC_APP_ENV=DEV" >> "$GITHUB_ENV"

          fi

          if [[ "${{github.base_ref}}" == "qa" || "${{github.ref}}" == "refs/heads/qa" ]]; then
             echo "CMD=deploy-qa" >> "$GITHUB_ENV"
             echo "VITE_BASE_URL=${{ secrets.VITE_QA_URL }}" >> "$GITHUB_ENV"
             echo "VITE_IMAGE_PROXY_URL=${{ secrets.VITE_IMAGE_QA_URL }}" >> "$GITHUB_ENV"
             echo "VITE_GOOGLE_MAPS_API_KEY=${{ secrets.VITE_GOOGLE_MAPS_API_KEY_DEV }}" >> "$GITHUB_ENV"
             echo "VITE_PUBLIC_APP_ENV=QA" >> "$GITHUB_ENV"

          fi

          if [[ "${{github.base_ref}}" == "stage" || "${{github.ref}}" == "refs/heads/stage" ]]; then
             echo "CMD=deploy-stage" >> "$GITHUB_ENV"
             echo "VITE_BASE_URL=${{ secrets.VITE_STAGE_URL }}" >> "$GITHUB_ENV"
             echo "VITE_IMAGE_PROXY_URL=${{ secrets.VITE_IMAGE_STAGE_URL }}" >> "$GITHUB_ENV"
             echo "VITE_GOOGLE_MAPS_API_KEY=${{ secrets.VITE_GOOGLE_MAPS_API_KEY_STAGE }}" >> "$GITHUB_ENV"
             echo "VITE_PUBLIC_APP_ENV=STAGE" >> "$GITHUB_ENV"
          fi

          if [[ "${{github.base_ref}}" == "main" || "${{github.ref}}" == "refs/heads/main" ]]; then
             echo "CMD=deploy-prod" >> "$GITHUB_ENV"
             echo "VITE_BASE_URL=${{ secrets.VITE_PROD_URL }}" >> "$GITHUB_ENV"
             echo "VITE_IMAGE_PROXY_URL=${{ secrets.VITE_IMAGE_PROD_URL }}" >> "$GITHUB_ENV"
             echo "VITE_GOOGLE_MAPS_API_KEY=${{ secrets.VITE_GOOGLE_MAPS_API_KEY_MAIN }}" >> "$GITHUB_ENV"
             echo "VITE_PUBLIC_APP_ENV=PROD" >> "$GITHUB_ENV"
          fi
      - name: Checkout Repo
        uses: actions/checkout@v3
      - name: Setup Node 22
        uses: actions/setup-node@v3
        with:
          node-version: 22.0.0
          cache: 'npm'
      - name: Install Dependencies
        run: npm install
      - name: Install Firebase CLI
        run: npm install -g firebase-tools
      - name: Build and deploy
        run: npm run ${{ env.CMD }}
        env:
          VITE_BASE_URL: ${{ env.VITE_BASE_URL }}
          VITE_IMAGE_PROXY_URL: ${{ env.VITE_IMAGE_PROXY_URL }}
          VITE_GOOGLE_MAPS_API_KEY: ${{ env.VITE_GOOGLE_MAPS_API_KEY }}
          FIREBASE_TOKEN: ${{ secrets.FIREBASE_TOKEN }}
